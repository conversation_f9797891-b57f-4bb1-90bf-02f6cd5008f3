package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.ReturnStk;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ReturnStkMapper extends Mapper<ReturnStk> {

    List<ReturnStk> getList(Page page, ReturnStk record);

    /**
     * 获取上一条退库单ID
     *
     * @param currentId 当前退库单ID
     * @return 上一条退库单ID
     */
    String getPrevId(@Param("whId") String whId, @Param("currentId") String currentId);

    /**
     * 获取下一条退库单ID
     *
     * @param currentId 当前退库单ID
     * @return 下一条退库单ID
     */
    String getNextId(@Param("whId") String whId, @Param("currentId") String currentId);
}