package cn.trasen.ams.material.bean.accountPeriod;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.accountPeriod
 * @className: WarehouseCheckOut
 * @author: chenbin
 * @description: 仓库结算状态
 * @date: 2025/7/23 08:53
 * @version: 1.0
 */
@Data
public class WarehouseCheckout {

    @ApiModelProperty(value = "仓库ID")
    private String warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "结算状态")
    private String checkoutStatus;

    @ApiModelProperty(value = "计算状态翻译")
    private String checkoutStatusShow;
}
