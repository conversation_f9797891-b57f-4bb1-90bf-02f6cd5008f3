package cn.trasen.ams.material.bean.warehouseRela;

import cn.trasen.ams.material.model.WarehouseRela;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.warehouseRela
 * @className: WarehouseRelaVsEmpResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/7/23 15:14
 * @version: 1.0
 */
@Data
public class WarehouseRelaVsEmpResp extends WarehouseRela {

    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工名称")
    private String name;

    @ApiModelProperty(value = "科室名称")
    private String orgName;

    @ApiModelProperty(value = "启用状态")
    private String isEnable;

    @ApiModelProperty(value = "启用状态翻译")
    private String isEnableShow;
}
