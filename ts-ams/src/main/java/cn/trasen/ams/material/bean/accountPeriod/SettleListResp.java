package cn.trasen.ams.material.bean.accountPeriod;

import cn.trasen.ams.material.model.AccountingPeriod;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.accountPeriod
 * @className: SettleListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/8/13 15:39
 * @version: 1.0
 */
@Data
public class SettleListResp extends AccountingPeriod {

    @ApiModelProperty(value = "结算状态")
    private String stat;

    @ApiModelProperty(value = "结算状态显示")
    private String statShow;

    @ApiModelProperty(value = "结算仓库ID")
    private String whId;

    @ApiModelProperty(value = "结算仓库名称")
    private String whName;

    @ApiModelProperty(value = "备注")
    private String remark;
}
