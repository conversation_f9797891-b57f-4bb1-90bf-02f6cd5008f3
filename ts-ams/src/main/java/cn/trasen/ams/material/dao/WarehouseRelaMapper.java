package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaVsEmpResp;
import cn.trasen.ams.material.model.WarehouseRela;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WarehouseRelaMapper extends Mapper<WarehouseRela> {

    List<WarehouseRelaVsEmpResp> selectWarehouseRelaEmpList(WarehouseRelaVsEmpResp record);

    List<WarehouseRela> getWhRelaListByWhIdAndModelId(@Param("whIdList") List<String> whIdList, @Param("modelIdList") List<String> modelIdList);
}