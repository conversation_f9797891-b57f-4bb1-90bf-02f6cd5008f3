package cn.trasen.ams.material.bean.returnOrder;

import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.ams.material.model.Batch;
import cn.trasen.ams.material.model.ReturnDtl;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.returnOrder
 * @className: ReturnDtlResp
 * @author: chenbin
 * @description: 物资退货单明细扩展响应体
 * @date: 2025/8/7 16:30
 * @version: 1.0
 */
@Data
public class ReturnDtlResp extends ReturnDtl {
    @ApiModelProperty(value = "物资编码")
    private String flowNo;

    @ApiModelProperty(value = "物资名称")
    private String name;

    @ApiModelProperty(value = "物资分类名称")
    private String categoryName;

    @ApiModelProperty(value = "规格型号")
    private String model;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位显示")
    private String unitShow;

    @ApiModelProperty(value = "参考单价")
    private BigDecimal originPrice;

    @ApiModelProperty(value = "注册证编号")
    private String regNo;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "生产厂家名称")
    private String manufacturerName;

    @ApiModelProperty(value = "库存数量")
    private Integer stock;

    /**
     * 生产批号
     */
    @Transient
    @Column(name = "prod_no")
    @ApiModelProperty(value = "生产批号")
    private String prodNo;

    /**
     * 生产日期
     */
    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "prod_date")
    @ApiModelProperty(value = "生产日期")
    private Date prodDate;

    /**
     * 失效日期
     */
    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "expire_date")
    @ApiModelProperty(value = "失效日期")
    private Date expireDate;

    @Transient
    @ApiModelProperty(value = "批次信息")
    private List<Batch> batchList;

    @ApiModelProperty(value = "入库数量")
    private Integer inNum;

    @ApiModelProperty(value = "待退货数量")
    private Integer outNum;

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = CommonUtil.dcmDgtFmt(originPrice);
    }
}
