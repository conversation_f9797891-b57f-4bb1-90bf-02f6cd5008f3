package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.Inb;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface InbMapper extends Mapper<Inb> {
    List<Inb> getList(Page page, Inb inb);

    /**
     * 获取上一条记录ID
     *
     * @param currentId 当前记录ID
     * @return 上一条记录ID
     */
    String getPrevId(@Param("whId") String whId, @Param("currentId") String currentId);

    /**
     * 获取下一条记录ID
     *
     * @param currentId 当前记录ID
     * @return 下一条记录ID
     */
    String getNextId(@Param("whId") String whId, @Param("currentId") String currentId);

    void updateReturnStat();
}