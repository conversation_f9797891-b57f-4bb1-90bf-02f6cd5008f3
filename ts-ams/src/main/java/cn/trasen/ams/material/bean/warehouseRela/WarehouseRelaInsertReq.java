package cn.trasen.ams.material.bean.warehouseRela;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.warehouse
 * @className: WarehouseRelaInsertReq
 * @author: chenbin
 * @description: 仓库关系
 * @date: 2025/7/23 13:41
 * @version: 1.0
 */
@Data
public class WarehouseRelaInsertReq {

    @ApiModelProperty(value = "关系类型")
    private String modelType;

    @NotNull(message = "仓库ID必填")
    @ApiModelProperty(value = "仓库ID")
    private String warehouseId;

    @NotNull(message = "仓库ID必填")
    @ApiModelProperty(value = "关系ID集合")
    private List<String> relaIdList;

}
