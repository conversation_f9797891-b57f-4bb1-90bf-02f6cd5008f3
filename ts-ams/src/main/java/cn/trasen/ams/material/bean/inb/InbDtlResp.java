package cn.trasen.ams.material.bean.inb;

import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.ams.material.model.InbDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.inb
 * @className: InbDtlResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/8/1 11:04
 * @version: 1.0
 */
@Data
public class InbDtlResp extends InbDtl {
    @ApiModelProperty(value = "物资编码")
    private String flowNo;

    @ApiModelProperty(value = "物资名称")
    private String name;

    @ApiModelProperty(value = "物资分类名称")
    private String categoryName;

    @ApiModelProperty(value = "规格型号")
    private String model;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位显示")
    private String unitShow;

    @ApiModelProperty(value = "参考单价")
    private BigDecimal originPrice;

    @ApiModelProperty(value = "注册证编号")
    private String regNo;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "厂家名称")
    private String manufacturerName;

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = CommonUtil.dcmDgtFmt(originPrice);
    }
}
