package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.sku.MSkuStock;
import cn.trasen.ams.material.model.MSku;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface MSkuMapper extends Mapper<MSku> {
    List<MSku> getList(Page page, MSku record);

    List<MSkuStock> getStockList(Page page, MSkuStock record);

    List<MSkuStock> getStockListNoPage(MSkuStock record);

    List<MSku> getListNoPage(MSku record);
}