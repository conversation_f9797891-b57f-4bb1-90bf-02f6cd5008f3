package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.accountPeriod.SettleListResp;
import cn.trasen.ams.material.bean.accountPeriod.WarehouseCheckout;
import cn.trasen.ams.material.model.AccountingPeriod;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import io.lettuce.core.dynamic.annotation.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface AccountingPeriodMapper extends Mapper<AccountingPeriod> {
    List<WarehouseCheckout> getWarehouseCheckout(@Param("apid") String apid);

    /**
     * 获取结算记录列表
     *
     * @param record 查询条件
     * @return 结算记录列表
     */
    List<SettleListResp> getSettleRecordList(Page page, SettleListResp record);
}