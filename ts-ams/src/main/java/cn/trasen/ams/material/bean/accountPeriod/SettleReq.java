package cn.trasen.ams.material.bean.accountPeriod;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.accountPeriod
 * @className: SettleReq
 * @author: chenbin
 * @description: 会计期间结算请求体
 * @date: 2025/8/13 10:08
 * @version: 1.0
 */

@Data
public class SettleReq {

    @NotNull(message = "会计期间必填")
    @ApiModelProperty(value = "会计期间ID")
    private String id;

    @NotNull(message = "仓库必选")
    @ApiModelProperty(value = "仓库ID合集")
    private List<String> whIdList;

    @ApiModelProperty(value = "备注")
    private String remark;

    @NotNull(message = "结算状态必填")
    @ApiModelProperty(value = "结算状态 (0:取消结算, 1:结算)")
    private String stat;


}
