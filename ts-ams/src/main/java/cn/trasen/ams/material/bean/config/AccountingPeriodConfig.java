package cn.trasen.ams.material.bean.config;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import cn.trasen.ams.device.constant.SkuConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.config
 * @className: AccountingPeriodConfig
 * @author: chenbin
 * @description: 会计区间配置
 * @date: 2025/7/22 16:35
 * @version: 1.0
 */
@Data
public class AccountingPeriodConfig {

    @NotNull(message = "是否自然月必填")
    @ConstValid(constant = {CommonConst.YES, CommonConst.NO}, message = "是否自然月值错误")
    @ApiModelProperty(value = "是否自然月")
    private String isNatureMonth;

    @ApiModelProperty(value = "开始日")
    private String startDay;


    @NotNull(message = "是否自动生成必填")
    @ConstValid(constant = {CommonConst.YES, CommonConst.NO}, message = "是否自动生成值错误")
    @ApiModelProperty(value = "是否自动生成")
    private String isAutoGen;

}
