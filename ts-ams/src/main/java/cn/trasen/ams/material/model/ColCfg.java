package cn.trasen.ams.material.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;
import org.springframework.validation.annotation.Validated;

@Table(name = "m_col_cfg")
@Setter
@Getter
@Validated
public class ColCfg {
    @Id
    private String id;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    @Column(name = "model_type")
    @ApiModelProperty(value = "业务类型")
    private String modelType;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    @Column(name = "model_id")
    @ApiModelProperty(value = "业务ID")
    private String modelId;

    /**
     * 字段编码
     */
    @NotNull(message = "字段编码不能为空")

    @Column(name = "col_code")
    @ApiModelProperty(value = "字段编码")
    private String colCode;

    /**
     * 字段名称
     */
    @NotNull(message = "字段名称不能为空")
    @Column(name = "col_name")
    @ApiModelProperty(value = "字段名称")
    private String colName;

    /**
     * 是否只读 0 否 1 是
     */
    @NotNull(message = "是否只读不能为空")
    @ApiModelProperty(value = "是否只读 0 否 1 是")
    private String readonly;

    /**
     * 是否显示 0 否 1 是
     */
    @NotNull(message = "是否显示不能为空")
    @Column(name = "is_show")
    @ApiModelProperty(value = "是否显示 0 否 1 是")
    private String isShow;

    /**
     * 是否必填 0 否 1 是
     */
    @NotNull(message = "是否必填不能为空")
    @Column(name = "is_required")
    @ApiModelProperty(value = "是否必填 0 否 1 是")
    private String isRequired;

    /**
     * 字段排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "字段排序")
    private Integer seqNo;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}