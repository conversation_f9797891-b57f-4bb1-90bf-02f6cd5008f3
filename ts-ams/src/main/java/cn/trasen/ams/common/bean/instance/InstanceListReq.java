package cn.trasen.ams.common.bean.instance;

import lombok.Data;

import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/5/28 10:33
 * @version: 1.0
 */
@Data
public class InstanceListReq {

    private String definitionId;
    private String status;

    private Map<String, String> params;
    private Map<String, String> query;

    private Map<String, String> mainParams;

    private String defField;
    private String join;
    private String defWhere;
    private String orderBy;


}
