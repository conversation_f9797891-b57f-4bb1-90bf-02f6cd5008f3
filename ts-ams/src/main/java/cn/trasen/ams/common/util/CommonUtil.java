package cn.trasen.ams.common.util;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.ConfigConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.core.utils.SpringContextUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.util
 * @className: Comm
 * @author: chenbin
 * @description: 工具类
 * @date: 2024/9/10 17:35
 * @version: 1.0
 */


public class CommonUtil {

    // 配置拼音格式为无声调小写
    private static final HanyuPinyinOutputFormat PINYIN_FORMAT;

    static {
        PINYIN_FORMAT = new HanyuPinyinOutputFormat();
        PINYIN_FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        PINYIN_FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    }


    /**
     * @param array1:
     * @param array2:
     * @param key:
     * @return String
     * <AUTHOR>
     * @description 从两个数组中查找对应的值
     * @date 2024/9/11 09:07
     */
    public static String constFmt(String[] array1, String[] array2, String key) throws Exception {
        // 检查数组的长度是否相等
        if (array1.length != array2.length) {
            throw new Exception("Array sizes must be equal");
        }

        // 创建两个映射表
        Map<String, String> map1 = new HashMap<>();
        Map<String, String> map2 = new HashMap<>();

        // 填充映射表
        for (int i = 0; i < array1.length; i++) {
            map1.put(array1[i], array2[i]);
            map2.put(array2[i], array1[i]);
        }

        // 从映射表中查找结果
        String res = map1.get(key);

        if (res == null) {
            res = map2.get(key);
        }

        return res;
    }

    public static String convertCamelToSnake(String camelCase) {
        StringBuilder snakeCase = new StringBuilder();

        for (int i = 0; i < camelCase.length(); i++) {
            char currentChar = camelCase.charAt(i);

            if (Character.isUpperCase(currentChar) && i > 0) {
                // 如果当前字符是大写字母且不是第一个字符，则在前面加下划线
                snakeCase.append("_");
            }

            snakeCase.append(Character.toLowerCase(currentChar));
        }

        return snakeCase.toString();
    }

    public static Date string2Date(String dateString) {
        String pattern = "yyyy-MM-dd"; // 日期格式

        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);

        try {
            Date date = dateFormat.parse(dateString); // 将字符串解析为Date对象
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    public static String dateFormate(Date date, String format) {
        try {
            DateFormat dateFormat = new SimpleDateFormat(format);
            return dateFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static String getCurDate(String tpl) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        if (tpl == null) {
            // 定义日期时间格式化器
            tpl = "yyyyMMdd";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(tpl);
        LocalDateTime now = LocalDateTime.now();  // 用 LocalDateTime，带时分秒
        return now.format(formatter);
    }

    public static String getAbsolutePath(String resourceName) {
        try {
            // 使用ClassPathResource加载资源文件
            Resource resource = new ClassPathResource(resourceName);
            File file = resource.getFile();
            return file.getAbsolutePath();
        } catch (IOException e) {
            // 处理异常，例如打印错误日志或抛出自定义异常
            e.printStackTrace();
            return null; // 或者抛出自定义异常，视情况而定
        }
    }

    /*
     * @param str:  字符串
     * @return Integer
     * <AUTHOR>
     * @description 字符串转整数
     * @date 2024/3/26 17:43
     */
    public static Integer String2Integer(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return 0;
        }
    }

    public static List<String> string2List(String str) {
        return Arrays.asList(str.split(","));
    }


    public static String list2String(List<String> list) {
        return String.join(",", list);
    }


    /*
     * @param input: 输入字符串
     * @return int
     * <AUTHOR>
     * @description 提取字符串中的第一个数字
     * @date 2024/3/26 17:43
     */
    public static int extractFirstNumber(String input) {
        StringBuilder sb = new StringBuilder();
        boolean foundDigit = false;

        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (Character.isDigit(ch)) {
                sb.append(ch);
                foundDigit = true;
            } else if (foundDigit) {
                // 如果已经找到数字，并且当前字符不是数字，则退出循环
                break;
            }
        }
        if (sb.length() == 0) {
            return 0;
        }

        return Integer.parseInt(sb.toString());
    }

    public static BigDecimal removeZero(BigDecimal num) {
        try {
            return new BigDecimal(num.stripTrailingZeros().toPlainString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }


    /**
     * @param str: 字符串
     * @return boolean
     * @description 判断字符串能否转成数字
     * @date 2024/3/26 17:43
     */
    public static boolean tryDoubleRet(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String divide(String a, String b, int scale) {
        String ret;
        try {
            ret = new BigDecimal(a).divide(new BigDecimal(b), scale, BigDecimal.ROUND_HALF_UP).toString();
        } catch (Exception e) {
            ret = new BigDecimal(0).divide(new BigDecimal(1), scale, BigDecimal.ROUND_HALF_UP).toString();
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * @param input:
     * @param pattern:
     * @return String
     * <AUTHOR>
     * @description 传入类似于2024-06 获取首末日期
     * @date 2024/6/19 16:10
     */
    public static String[] getMonthRange(String input, String pattern) {
        // Define the date formatter based on the input pattern
        DateTimeFormatter formatter;
        try {
            formatter = DateTimeFormatter.ofPattern(pattern);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date pattern: " + pattern, e);
        }

        YearMonth yearMonth;
        try {
            DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM");
            // Parse the input string to get the YearMonth object
            yearMonth = YearMonth.parse(input, formatter1);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date input: " + input, e);
        }

        // Get the first and last day of the month with time
        LocalDateTime firstDay = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime lastDay = yearMonth.atEndOfMonth().atTime(23, 59, 59);

        // Format the dates back to strings using the same pattern
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern);
        String firstDayStr = firstDay.format(outputFormatter);
        String lastDayStr = lastDay.format(outputFormatter);

        return new String[]{firstDayStr, lastDayStr};
    }

    /**
     * @param input:
     * @param pattern:
     * @return String
     * <AUTHOR>
     * @description 获取年的首末日期
     * @date 2024/6/20 10:04
     */

    public static String[] getYearRange(String input, String pattern) {
        // Define the date formatter based on the input pattern
        DateTimeFormatter formatter;
        try {
            formatter = DateTimeFormatter.ofPattern(pattern);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date pattern: " + pattern, e);
        }

        Year year;
        try {
            DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy");
            // Parse the input string to get the Year object
            year = Year.parse(input, yearFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date input: " + input, e);
        }

        // Get the first and last day of the year with time
        LocalDateTime firstDay = year.atDay(1).atStartOfDay();
        LocalDateTime lastDay = year.atDay(year.length()).atTime(23, 59, 59);

        // Format the dates back to strings using the same pattern

        String firstDayStr = firstDay.format(formatter);
        String lastDayStr = lastDay.format(formatter);

        return new String[]{firstDayStr, lastDayStr};
    }

    /**
     * 获取字符串的全拼
     *
     * @param input 字符串
     * @return 转换后的全拼字符串
     */
    public static String toPinyinFull(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (isChineseCharacter(c)) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, PINYIN_FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0]); // 取第一个拼音
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    // 打印
                    e.printStackTrace();

                }
            } else {
                result.append(c); // 非汉字直接拼接
            }
        }
        return result.toString();
    }

    /**
     * 获取字符串的首拼
     *
     * @param input 字符串
     * @return 转换后的首拼字符串
     */
    public static String toPinyinFirst(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (isChineseCharacter(c)) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, PINYIN_FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0].charAt(0)); // 取第一个拼音的首字母
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                result.append(c); // 非汉字直接拼接
            }
        }
        return result.toString();
    }

    /**
     * 判断字符是否是汉字
     *
     * @param c 字符
     * @return 如果是汉字则返回 true，否则返回 false
     */
    private static boolean isChineseCharacter(char c) {
        return String.valueOf(c).matches("[\u4E00-\u9FA5]");
    }


    /**
     * @param input:
     * @return String
     * <AUTHOR>
     * @description TODO
     * @date 2025/2/17 15:41
     */
    public static String escapeSql(String input) {
        return input.replaceAll("([';])+|(--)+", "");
    }

    public static String howLong(Date date, int level) {
        if (date == null || level < 1 || level > 6) {
            throw new IllegalArgumentException("日期不能为空，级别必须在1到6之间");
        }

        // 将 Date 转换为 LocalDateTime
        LocalDateTime inputDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime now = LocalDateTime.now();

        // 计算时间差
        Duration duration = Duration.between(inputDate, now);
        long totalSeconds = Math.abs(duration.getSeconds());

        long years = totalSeconds / (365 * 24 * 60 * 60);
        long months = (totalSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60);
        long days = (totalSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60);
        long hours = (totalSeconds % (24 * 60 * 60)) / (60 * 60);
        long minutes = (totalSeconds % (60 * 60)) / 60;
        long seconds = totalSeconds % 60;

        // 根据级别拼接结果
        StringBuilder result = new StringBuilder();
        String[] units = {"年", "月", "天", "时", "分", "秒"};
        long[] values = {years, months, days, hours, minutes, seconds};

        for (int i = 0; i < level && i < units.length; i++) {
            if (values[i] > 0) {
                result.append(values[i]).append(units[i]);
            }
        }

        return result.toString();
    }

    /**
     * @param num:
     * @return BigDecimal
     * <AUTHOR>
     * @description 精度转换
     * @date 2025/8/28 17:59
     */
    public static BigDecimal dcmDgtFmt(BigDecimal num) {
        // 取出保留小数点的配置
        if (num == null || num.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        ApplicationContext context = SpringContextUtil.getApplicationContext();
        DictService dictService = context.getBean(DictService.class);
        Integer digits = CommonConst.DECIMAL_DIGITS_DEF;
        try {
            digits = Integer.valueOf(dictService.cgetValueByCode(ConfigConst.M_BASE_SETTING, CommonConst.DECIMAL_DIGITS));
        } catch (Exception e) {
            // 什么都不做
        }
        // 防止字典配置有误差
        if (digits <= 0 || digits > 6) {
            digits = CommonConst.DECIMAL_DIGITS_DEF;
        }
        BigDecimal fmt = num.setScale(digits, BigDecimal.ROUND_HALF_UP);
        return fmt;
    }

}
