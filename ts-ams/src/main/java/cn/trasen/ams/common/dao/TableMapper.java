package cn.trasen.ams.common.dao;

import cn.trasen.homs.bean.base.DictItemResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TableMapper {
    /**
     * @param table:
     * @param pk:
     * @param id:
     * @return int
     * <AUTHOR>
     * @description 请不要直接从controller 调用，让前台的动态参数入参到本函数，会出现严重注入安全问题
     * @date 2023/12/1 10:12
     */

    int hasById(@Param("table") String table, @Param("pk") String pk, @Param("id") String id, @Param("ignoreIsDelete") String ignoreIsDelete);

    /**
     * @param table:
     * @param pk:
     * @param id:
     * @return int
     * <AUTHOR>
     * @description 请不要直接从controller 调用，让前台的动态参数入参到本函数，会出现严重注入安全问题
     * @date 2023/12/1 10:13
     */
    int hasByIdList(@Param("table") String table, @Param("pk") String pk, @Param("id") String[] id, @Param("ignoreIsDelete") String ignoreIsDelete);

    List<DictItemResp> getDictItemByTypeCode(@Param("typeCode") String typeCode, @Param("ssoOrgCode") String ssoOrgCode);
}
