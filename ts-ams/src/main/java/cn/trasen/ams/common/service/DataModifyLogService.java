package cn.trasen.ams.common.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.DataModifyLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DataModifyLogService
 * @Description TODO
 * @date 2025年7月24日 下午6:39:31
 */
public interface DataModifyLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    Integer save(DataModifyLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    Integer update(DataModifyLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DataModifyLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    DataModifyLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DataModifyLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    DataSet<DataModifyLog> getDataSetList(Page page, DataModifyLog record);

    void dataFmt(DataModifyLog record);

    void writeLog(String tableName, String pkValue, String flowNo, String name, String type, Object o, Object n, String search);
}
