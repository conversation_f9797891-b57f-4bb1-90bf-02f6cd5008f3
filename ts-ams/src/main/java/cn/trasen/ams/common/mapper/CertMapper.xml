<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.CertMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.common.model.Cert">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sys_type" jdbcType="CHAR" property="sysType" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="birth_date" jdbcType="DATE" property="birthDate" />
    <result column="valid_over_date" jdbcType="DATE" property="validOverDate" />
    <result column="is_forever" jdbcType="CHAR" property="isForever" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="file_set" jdbcType="VARCHAR" property="fileSet" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>