package cn.trasen.ams.common.service.impl;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.trasen.homs.form.model.DpTableField;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import cn.trasen.ams.device.constant.PurchaseConst;
import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.bean.instance.InstanceListReq;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.dao.AMSInstanceMapper;
import cn.trasen.ams.common.model.AMSDpFormTemplate;
import cn.trasen.ams.common.model.AMSEmployee;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfo;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfoFieldSet;
import cn.trasen.ams.common.model.AMSOrganization;
import cn.trasen.ams.common.service.AMSInstanceService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.service.impl
 * @className: AMSInstanceServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2025/5/27 15:18
 * @version: 1.0
 */
@Service
public class AMSInstanceServiceImpl implements AMSInstanceService {

    @Autowired
    private AMSInstanceMapper mapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 根据ID列表获取实例定义信息列表
     *
     * @param idList ID列表
     * @return 实例定义信息列表，如果ID列表为空则返回空列表
     */
    @Override
    public List<AMSInstanceDefinitionInfo> getInstanceDefinitionListByIdList(List<String> idList) {
        if (idList != null && !idList.isEmpty()) {
            return mapper.getInstanceDefinitionListByIdList(idList);
        }
        return Collections.emptyList();
    }

    /**
     * 获取采购类型列表
     *
     * @return 采购类型对应的实例定义信息列表
     * @throws RuntimeException 如果采购流程定义字典未配置
     */
    @Override
    public List<AMSInstanceDefinitionInfo> getPurchaseTypeList() {
        Map<String, String> map = dictService.cgetCV(PurchaseConst.PURCHASE_INS_DEFINITION_LIST);
        // 获取map 中的所有 值 放进 List<String>
        if (map == null || map.isEmpty()) {
            throw new RuntimeException("采购流程定义字典未配置");
        }
        // 将map的值转换为List<String>
        List<String> purchaseTypeList = map.values().stream().collect(Collectors.toList());
        List<AMSInstanceDefinitionInfo> definitionList = getInstanceDefinitionListByIdList(purchaseTypeList);
        return definitionList;
    }

    /**
     * 根据模板ID获取实例定义字段集列表
     *
     * @param formId 模板ID
     * @return 实例定义字段集列表
     */
    @Override
    public List<AMSInstanceDefinitionInfoFieldSet> getInstanceDefinitionFieldSetListByTemplateId(String formId) {
        return mapper.getInstanceDefinitionFieldSetListByTemplateId(formId);
    }

    @Override
    public List<DpTableField> getSonFormField(String tableId) {
        return mapper.getSonFormField(tableId);
    }

    /**
     * 根据定义ID和状态列表获取实例数量
     *
     * @param definitionId 定义ID
     * @param withStatus   状态列表
     * @return 状态与实例数量的映射
     * @throws RuntimeException 如果状态请求值非法或用户未登录
     */
    @Override
    public Map<String, Integer> getInstancesWithStatus(String definitionId, List<String> withStatus) {
        Set<String> statusSet = new HashSet<>(Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007", "10008"));
        for (String s : withStatus) {
            if (!statusSet.contains(s)) {
                throw new RuntimeException("非法的状态请求值");
            }
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }

        String userCode = user.getUsercode();

        Map<String, Integer> res = new HashMap<>();

        for (String status : withStatus) {
            Integer nums = mapper.getNumsByStatus(userCode, definitionId, status, getDeptCodeSet());
            res.put(status, nums);
        }

        return res;
    }

    @Override
    public Map<String, Integer> getSonFormsWithStatus(String definitionId, List<String> withStatus) {
        Set<String> statusSet = new HashSet<>(Arrays.asList(PurchaseConst.PURCHASE_PAGE_STATUS_APPLY, "10010"));
        for (String s : withStatus) {
            if (!statusSet.contains(s)) {
                throw new RuntimeException("非法的状态请求值");
            }
        }

        // 查出来对应的表ID
        AMSInstanceDefinitionInfo definitionInfo = mapper.selectInstanceDefinitionById(definitionId);

        if (definitionInfo == null) {
            throw new RuntimeException("流程不存在");
        }

        String tableName = getSonFormTableName(definitionInfo.getFormId());

        if (tableName == null || tableName.isEmpty()) {
            throw new RuntimeException("没有找到对应的表单数据");
        }

        Map<String, Integer> res = new HashMap<>();

        for (String status : withStatus) {
            Integer nums = mapper.getSonFormsWithStatus(definitionId, status, tableName, getDeptCodeSet());
            res.put(status, nums);
        }

        return res;
    }

    /**
     * 获取当前用户的部门代码
     *
     * @return 部门代码，如果用户是管理员则返回 "admin"
     */
    public String getDeptCodeSet() {

        PermissionResp permissionResp = permissionService.getPermission(PermissionConst.业务类型_采购申请);

        if (PermissionConst.TYPE_ALL.equals(permissionResp.getType())) {
            return null;
        }

        // 这个地方不可能有sku_type

        String deptCodeSet = "";
        List<String> deptCodeList = permissionResp.getDeptIdList();
        if (CollectionUtils.isEmpty(deptCodeList)) {
            String deptCode = getUserParttimeOrgId(UserInfoHolder.getCurrentUserId());
            if (deptCode == null || "".equals(deptCode)) {
                deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
            }
            deptCodeSet = "'" + deptCode + "'";
        } else {

            StringBuilder sb = new StringBuilder();
            for (String deptCode : deptCodeList) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("'").append(deptCode).append("'");
            }
            deptCodeSet = sb.toString();
        }
        return deptCodeSet;
    }

    /**
     * 根据员工ID获取兼职组织ID
     *
     * @param employeeId 员工ID
     * @return 兼职组织ID
     */
    public String getUserParttimeOrgId(String employeeId) {
        return mapper.getUserParttimeOrgId(employeeId);
    }

    /**
     * 根据组织ID列表获取员工编码列表
     *
     * @param orgIdList 组织ID列表
     * @return 员工编码列表，如果组织ID列表为空则返回空列表
     */
    @Override
    public List<String> getEmployeeCodeListByOrgIdList(List<String> orgIdList) {
        // 返回空列表
        List<String> emptyList = Collections.emptyList();

        if (orgIdList == null || orgIdList.isEmpty()) {
            return emptyList;
        }

        // 获取员工列表
        List<AMSEmployee> employeeList = mapper.getEmployeeListByOrgIdList(orgIdList);
        if (employeeList == null || employeeList.isEmpty()) {
            return emptyList;
        }

        // 提取员工编号
        return employeeList.stream().map(AMSEmployee::getEmployeeNo).collect(Collectors.toList());
    }

    /**
     * 根据组织ID获取子组织ID列表
     *
     * @param orgId 组织ID
     * @return 子组织ID列表，如果组织ID为空或无子组织则返回空列表
     */
    @Override
    public List<String> getOrganizationIdListByOrgId(String orgId) {
        if (orgId != null && !orgId.isEmpty()) {
            List<AMSOrganization> organizationList = mapper.getOrganizationListByOrgId(orgId);

            if (organizationList == null || organizationList.size() == 0) {
                return Collections.emptyList();
            }

            return organizationList.stream().map(AMSOrganization::getOrganizationId).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public DataSet<Map<String, Object>> getInstanceList(Page page, InstanceListReq record) {

        String definitionId = record.getDefinitionId();
        String status = record.getStatus();
        Map<String, String> query = record.getQuery();
        Map<String, String> params = record.getParams();
        String sidx = "";
        String sord = "";
        if (query != null) {
            sidx = query.get("sidx");
            sord = query.get("sord");
        }


        Set<String> statusSet = new HashSet<>(Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007", "10008"));

        if (!statusSet.contains(status)) {
            throw new RuntimeException("非法的状态请求值");
        }

        // 查出来对应的表ID
        AMSInstanceDefinitionInfo definitionInfo = mapper.selectInstanceDefinitionById(definitionId);

        if (definitionInfo == null) {
            throw new RuntimeException("流程不存在");
        }

        AMSDpFormTemplate dpFormTemplate = mapper.selectDpFormTemplateById(definitionInfo.getFormId());

        if (dpFormTemplate == null) {
            throw new RuntimeException("没有找到对应的表单数据");
        }

        String base = "SELECT DISTINCT\n"
                + "    wii.`WF_INSTANCE_ID`,\n"
                + "    wii.WF_DEFINITION_ID,\n"
                + "    wii.IS_DELETED                         AS WF_IS_DELETED,\n"
                + "    wii.CREATE_USER                        AS WF_CREATE_USER,\n"
                + "    wii.CREATE_DATE                        AS WF_CREATE_DATE,\n"
                + "    wii.UPDATE_USER                        AS WF_UPDATE_USER,\n"
                + "    wii.UPDATE_DATE                        AS WF_UPDATE_DATE,\n"
                + "    wii.CREATE_USER_NAME                   AS WF_CREATE_USER_NAME,\n"
                + "    wii.UPDATE_USER_NAME                   AS WF_UPDATE_USER_NAME,\n"
                + "    wii.VERSION                            AS WF_VERSION,\n"
                + "    wii.WORKFLOW_NO                        AS WF_WORKFLOW_NO,\n"
                + "    wii.WORKFLOW_NAME                      AS WF_WORKFLOW_NAME,\n"
                + "    wii.BUSINESS_ID                        AS WF_BUSINESS_ID,\n"
                + "    wii.CURRENT_STEP_NO                    AS WF_CURRENT_STEP_NO,\n"
                + "    wii.CURRENT_STEP_NAME                  AS WF_CURRENT_STEP_NAME,\n"
                + "    wii.CURRENT_ASSIGNEE_NO                AS WF_CURRENT_ASSIGNEE_NO,\n"
                + "    wii.CURRENT_ASSIGNEE_NAME              AS WF_CURRENT_ASSIGNEE_NAME,\n"
                + "    wii.WF_FINISHED_DATE,\n"
                + "    wii.PARENT_ID                          AS WF_PARENT_ID,\n"
                + "    wii.LAUNCH_DEPT_CODE                   AS WF_LAUNCH_DEPT_CODE,\n"
                + "    wii.LAUNCH_DEPT_NAME                   AS WF_LAUNCH_DEPT_NAME,\n"
                + "    wii.LAUNCH_COMPANY_CODE                AS WF_LAUNCH_COMPANY_CODE,\n"
                + "    wii.LAUNCH_COMPANY_NAME                AS WF_LAUNCH_COMPANY_NAME,\n"
                + "    wii.SUMMARY                            AS WF_SUMMARY,\n"
                + "    wii.STATUS                             AS WF_STATUS,\n"
                + "    wii.WORKFLOW_NUMBER                    AS WF_WORKFLOW_NUMBER,\n"
                + "    wii.WORKFLOW_TITLE                     AS WF_WORKFLOW_TITLE,\n"
                + "    wii.HANDLE_ALLOTTED_TIME               AS WF_HANDLE_ALLOTTED_TIME,\n"
                + "    wii.URGENCY_LEVEL                      AS WF_URGENCY_LEVEL,\n"
                + "    wii.HANDLE_MARKED_WORDS                AS WF_HANDLE_MARKED_WORDS,\n"
                + "    wii.IS_PRESS                           AS WF_IS_PRESS,\n"
                + "    wii.print                              AS WF_PRINT,\n"
                + "    wii.choice_id                          AS WF_CHOICE_ID,\n"
                + "    wii.sso_org_code                       AS WF_SSO_ORG_CODE,\n"
                + "    wii.sso_org_name                      AS WF_SSO_ORG_NAME,\n"
                + "    wii.form_version                       AS WF_FORM_VERSION,\n"
                + "    wii.CHILD_BUSINESS                     AS WF_CHILD_BUSINESS,\n"
                + "    wt.TASK_ID                             AS WF_TASK_ID,\n"
                + "    zdyt.*,\n"
                + "    (\n"
                + "        SELECT GROUP_CONCAT(ASSIGNEE_NAME SEPARATOR ', ')\n"
                + "        FROM ts_base_oa.wf_task\n"
                + "        WHERE WF_INSTANCE_ID = wii.`WF_INSTANCE_ID`\n"
                + "    ) AS WF_UNSURE_ASSIGNEE_NAME_LIST\n"
                + "FROM ts_base_oa.wf_instance_info wii\n"
                + "INNER JOIN ts_base_oa." + dpFormTemplate.getTableName() + " zdyt\n"
                + "    ON zdyt.`WORKFLOW_ID` = wii.`WF_INSTANCE_ID`\n"
                + "LEFT JOIN ts_base_oa.wf_task wt\n"
                + "  ON wt.`WF_INSTANCE_ID` = wii.`WF_INSTANCE_ID`";

        base += "  LEFT join ts_base_oa.wf_definition_info wdi on wii.`WF_DEFINITION_ID` = wdi.`WF_DEFINITION_ID`\n"
                + "        LEFT join ts_base_oa.wf_form_classify_info wfci on wdi.`WORKFLOW_CLASSIFY` = wfci.`wf_form_classify_id`";
        if (record.getJoin() != null && !record.getJoin().isEmpty()) {
            // 如果有join条件，则拼接到SQL中
            base += " " + record.getJoin();
        }

        List<AMSInstanceDefinitionInfoFieldSet> fileSetList = getInstanceDefinitionFieldSetListByTemplateId(dpFormTemplate.getId());

        // 包含上面SQL查询的所有WF_前缀字段
        String mainRule = "WF_IS_DELETED,WF_CREATE_USER,WF_CREATE_DATE,WF_UPDATE_USER,WF_UPDATE_DATE,WF_CREATE_USER_NAME,WF_UPDATE_USER_NAME,WF_VERSION,WF_WORKFLOW_NO,WF_WORKFLOW_NAME,WF_BUSINESS_ID,WF_CURRENT_STEP_NO,WF_CURRENT_STEP_NAME,WF_CURRENT_ASSIGNEE_NO,WF_CURRENT_ASSIGNEE_NAME,WF_FINISHED_DATE,WF_PARENT_ID,WF_LAUNCH_DEPT_CODE,WF_LAUNCH_DEPT_NAME,WF_LAUNCH_COMPANY_CODE,WF_LAUNCH_COMPANY_NAME,WF_SUMMARY,WF_STATUS,WF_WORKFLOW_NUMBER,WF_WORKFLOW_TITLE,WF_HANDLE_ALLOTTED_TIME,WF_URGENCY_LEVEL,WF_HANDLE_MARKED_WORDS,WF_IS_PRESS,WF_PRINT,WF_CHOICE_ID,WF_SSO_ORG_CODE,WF_SSO_ORG_NAME,WF_FORM_VERSION,WF_CHILD_BUSINESS";
        String specialRule = "WF_INSTANCE_ID,WF_DEFINITION_ID,WF_FINISHED_DATE"; // 特例字段

        String dynamicRule = fileSetList.stream().map(AMSInstanceDefinitionInfoFieldSet::getFieldName).collect(Collectors.joining(",")); // 表单字段
        String orderRule = "desc,asc";

        // 检测排序方式 和排序字段是否规范
        List<String> mainRuleList = Arrays.asList(mainRule.split(","));
        List<String> dynamicRuleList = Arrays.asList(dynamicRule.split(","));

        List<String> orderRuleList = Arrays.asList(orderRule.split(","));

        String orderBy = record.getOrderBy();

        // 如果子业务没有传入排序
        if (StringUtil.isEmpty(orderBy)) {
            // 默认倒序
            if (!orderRuleList.contains(sord)) {
                sord = "desc";
            }
            if (mainRuleList.contains(sidx)) {
                sidx = sidx.replace("WF_", "");
                orderBy = String.format(" ORDER BY `wii`.`%s` %s", sidx, sord);
            } else if (dynamicRuleList.contains(sidx)) {
                orderBy = String.format(" ORDER BY `zdyt`.`%s` %s", sidx, sord);
            } else {
                orderBy = " order by `wii`.`CREATE_DATE` desc";
            }
        }

        // 拆分参数成 主要查询 和 动态查询两个部分
        Map<String, String> mainParams = record.getMainParams() == null ? new HashMap<>() : record.getMainParams();
        Map<String, String> dynamicParams = new HashMap<>();

        // 拆分参数
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            if (mainRuleList.contains(k)) {
                mainParams.put(k, v);
            } else if (dynamicRuleList.contains(k)) {
                dynamicParams.put(k, v);
            }
        }

        Map<String, String> mainFieldType = getMainFieldMap();

        Map<String, String> dynamicFieldType = fileSetList.stream().filter(f -> f.getFieldName() != null && f.getFieldType() != null) // 可选：防止空指针
                .collect(Collectors.toMap(AMSInstanceDefinitionInfoFieldSet::getFieldName, AMSInstanceDefinitionInfoFieldSet::getFieldType));

        // 构建where请求 循环mainParams
        StringBuilder where = new StringBuilder("WHERE 1=1 ");

        // 循环mainFieldType
        for (Map.Entry<String, String> entry : mainFieldType.entrySet()) {
            String field = entry.getKey();
            String type = entry.getValue();
            String finalField = field.replace("WF_", ""); // 去掉WF_前缀
            // 检测mainParams中是否存在该字段
            if (mainParams.containsKey(field)) {
                String value = mainParams.get(field);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                switch (type) {
                    case "input":
                        // like
                    case "textarea":
                        // like
                        where.append(String.format("AND wii.`%s` LIKE '%%%s%%' ", finalField, value));
                        break;
                    case "number":
                        // where.append(String.format("AND wii.`%s` = '%s' ", finalField, value));
                        // between
                        List<String> numberSet = Arrays.asList(value.split(","));
                        if (numberSet == null || numberSet.size() < 2) {
                            break;
                        }
                        String startNumber = numberSet.get(0);
                        String endNumber = numberSet.get(1);
                        where.append(String.format("AND wii.`%s` BETWEEN '%s' AND '%s' ", finalField, startNumber, endNumber));
                        break;
                    case "date":
                        // between
                        List<String> dateSet = Arrays.asList(value.split(","));
                        if (dateSet == null || dateSet.size() < 2) {
                            break;
                        }

                        String start = dateSet.get(0);
                        String end = dateSet.get(1);
                        where.append(String.format("AND wii.`%s` BETWEEN '%s' AND '%s' ", finalField, start, end));

                        break;
                    case "select":
                        where.append(String.format("AND wii.`%s` = '%s' ", finalField, value));
                        break;
                    case "radio":
                        where.append(String.format("AND wii.`%s` = '%s' ", finalField, value));
                        break;
                    case "checkbox":
                        // LIKE
                        where.append(String.format("AND wii.`%s` LIKE '%%%s%%' ", finalField, value));
                        break;
                    default:
                        where.append(String.format("AND wii.`%s` = '%s' ", finalField, value));
                        break;
                }
            }
        }

        // 循环dynamicFieldType
        for (Map.Entry<String, String> entry : dynamicFieldType.entrySet()) {
            String field = entry.getKey();
            String type = entry.getValue();
            // 检测dynamicParams中是否存在该字段
            if (dynamicParams.containsKey(field)) {
                String value = dynamicParams.get(field);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                switch (type) {
                    case "input":
                        // like
                        where.append(String.format("AND zdyt.`%s` LIKE '%%%s%%' ", field, value));
                        break;
                    case "textarea":
                        // like
                        where.append(String.format("AND zdyt.`%s` LIKE '%%%s%%' ", field, value));
                        break;
                    case "number":
                        List<String> numberSet = Arrays.asList(value.split(","));
                        if (numberSet == null || numberSet.size() < 2) {
                            break;
                        }
                        String startNumber = numberSet.get(0);
                        String endNumber = numberSet.get(1);
                        where.append(String.format("AND zdyt.`%s` BETWEEN '%s' AND '%s' ", field, startNumber, endNumber));
                        break;
                    case "date":
                        // between
                        List<String> dateSet = Arrays.asList(value.split(","));
                        if (dateSet == null || dateSet.size() < 2) {
                            break;
                        }
                        String start = dateSet.get(0);
                        String end = dateSet.get(1);

                        where.append(String.format("AND zdyt.`%s` BETWEEN '%s' AND '%s' ", field, start, end));

                        break;
                    case "select":
                        where.append(String.format("AND zdyt.`%s` = '%s' ", field, value));
                        break;
                    case "radio":
                        where.append(String.format("AND zdyt.`%s` = '%s' ", field, value));
                        break;
                    case "checkbox":
                        // LIKE
                        where.append(String.format("AND zdyt.`%s` LIKE '%%%s%%' ", field, value));
                        break;
                    default:
                        where.append(String.format("AND zdyt.`%s` = '%s' ", field, value));
                        break;
                }
            }
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String userCode = user.getUsercode();
        String deptCodeSet = getDeptCodeSet();

        // 流程类型
        where.append(" and wdi.`WF_DEFINITION_ID` = '" + definitionId + "'");
        String delete1 = " and wii.`is_deleted` = 'N'";
        String delete2 = " and wt.`is_deleted` = 'N'";
        switch (status) {
            case "10000":
                where.append(" and wii.`WF_DEFINITION_ID` = '" + definitionId + "'");
                where.append(" and wii.`CREATE_USER` = '" + userCode + "'");
                where.append(" and wii.`status` in ('2','3','4','5','6')");
                where.append(delete1);
                break;
            case "10001":
                where.append(" and wii.`CREATE_USER` = '" + userCode + "'");
                where.append(" and wii.`status` = '1'");
                where.append(delete1);
                where.append(delete2);

                break;
            case "10002":
                where.append(" and wii.`CREATE_USER` = '" + userCode + "'");
//                where.append(" and wt.`ASSIGNEE_NO` != '" + userCode + "'");
                where.append(" and wii.`status` = '1'");
                where.append(delete1);
                where.append(delete2);
                break;
            case "10003":
                where.append(" and wii.`CREATE_USER` = '" + userCode + "'");
                where.append(" and wii.`status` = '0'");
                where.append(delete1);
                break;
            case "10004":
                where.append(" and wii.`status` = '1' and wii.`CURRENT_STEP_NAME` != '重新提交'");
                where.append(" and wt.`ASSIGNEE_NO` = '" + userCode + "'");
                where.append(delete1);
                where.append(delete2);
                break;
            case "10005":
                base += " left join ts_base_oa.wf_task_his wth on wii.`WF_INSTANCE_ID` = wth.`WF_INSTANCE_ID`";
                where.append(" and wii.`status` in ('1','2','5')");
                where.append(" and wth.`ASSIGNEE_NO` = '" + userCode + "'");
                where.append(delete1);
                //where.append(delete2);
                break;
            case "10006":
                base += "  left join ts_base_oa.wf_copy_user wcu on wii.`WF_INSTANCE_ID` = wcu.`WF_INSTANCE_ID`";
                where.append(" and wcu.`COPYTO_USER_CODE` = '" + userCode + "'");
                where.append(delete1);
                //where.append(delete2);
                break;
            case "10007":
                where.append(" and wii.`status` = '1'");
                if (deptCodeSet != null) {
                    where.append(" and wii.`LAUNCH_DEPT_CODE` in (" + deptCodeSet + ")");
                }
                where.append(delete1);
                break;
            case "10008":
                where.append(" and wii.`status` in ('2','3','4','5','6')");
                if (deptCodeSet != null) {
                    where.append(" and wii.`LAUNCH_DEPT_CODE` in (" + deptCodeSet + ")");
                }
                where.append(delete1);
                break;
        }
        // 拼接sql语句
        String sql = base + where.toString() + orderBy;
        List<Map<String, Object>> records = mapper.getInstanceList(page, sql);
        // 查询wf_task_his取出来相关的信息进行匹配

        for (Map<String, Object> map : records) {

            String instanceId = (String) map.get("WF_INSTANCE_ID");

            List<Map<String,String>> hisTaskList = mapper.getTaskHisListByInstanceId(instanceId);

            // 转换日期格式
            for (String key : map.keySet()) {

                if(!CollectionUtils.isEmpty(hisTaskList)){
                    StringBuffer remarksb = new StringBuffer();
                    String approvalFiledKey = null;
                    for (Map<String, String> map2 : hisTaskList) {
                        String approvalFiled = (String) map2.get("approval_filed");
                        String remark = (String) map2.get("remark");
                        String finished_date  = map2.get("finished_date");
                        String act_assignee_name = (String) map2.get("act_assignee_name");

                        if(key.equals(approvalFiled)){
                            approvalFiledKey = approvalFiled;
                            remarksb.append(remark + "【" + act_assignee_name + "-" + finished_date + "】");
                        }

                        //map.merge(approvalFiled, remark + "【" + act_assignee_name + "-" + finished_date + "】", (oldValue, newValue) -> oldValue + ", " + newValue);
                    }
                    if(StringUtils.isNotBlank(approvalFiledKey)){
                        map.put(approvalFiledKey,remarksb.toString());
                    }
                }

                if (map.get(key) instanceof java.util.Date) {

                    map.put(key, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(map.get(key)));
                }
            }
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Override
    public DataSet<Map<String, Object>> getInstanceSonFromList(Page page, InstanceListReq record) {

        String definitionId = record.getDefinitionId();
        String status = record.getStatus();
        Map<String, String> query = record.getQuery();
        Map<String, String> params = record.getParams();
        String defWhere = record.getDefWhere();
        String defField = record.getDefField();

        String sidx = "";
        String sord = "";
        if (query != null) {
            sidx = query.get("sidx");
            sord = query.get("sord");
        }
        Set<String> statusSet = new HashSet<>(
                // TODO 这里可以扩展状态 后续再弄
                Arrays.asList(PurchaseConst.PURCHASE_PAGE_STATUS_APPLY));

        if (!statusSet.contains(status)) {
            throw new RuntimeException("非法的状态请求值");
        }

        // 查出来对应的表ID
        AMSInstanceDefinitionInfo definitionInfo = mapper.selectInstanceDefinitionById(definitionId);

        if (definitionInfo == null) {
            throw new RuntimeException("流程不存在");
        }

        String tableName = getSonFormTableName(definitionInfo.getFormId());

        AMSDpFormTemplate dpFormTemplate = mapper.selectDpFormTemplateById(definitionInfo.getFormId());

        if (dpFormTemplate == null) {
            throw new RuntimeException("没有找到对应的表单数据");
        }

        String base = "SELECT DISTINCT\n"
                + "    wii.`WF_INSTANCE_ID`,\n"
                + "    wii.WF_DEFINITION_ID,\n"
                + "    wii.IS_DELETED                         AS WF_IS_DELETED,\n"
                + "    wii.CREATE_USER                        AS WF_CREATE_USER,\n"
                + "    wii.CREATE_DATE                        AS WF_CREATE_DATE,\n"
                + "    wii.UPDATE_USER                        AS WF_UPDATE_USER,\n"
                + "    wii.UPDATE_DATE                        AS WF_UPDATE_DATE,\n"
                + "    wii.CREATE_USER_NAME                   AS WF_CREATE_USER_NAME,\n"
                + "    wii.UPDATE_USER_NAME                   AS WF_UPDATE_USER_NAME,\n"
                + "    wii.VERSION                            AS WF_VERSION,\n"
                + "    wii.WORKFLOW_NO                        AS WF_WORKFLOW_NO,\n"
                + "    wii.WORKFLOW_NAME                      AS WF_WORKFLOW_NAME,\n"
                + "    wii.BUSINESS_ID                        AS WF_BUSINESS_ID,\n"
                + "    wii.CURRENT_STEP_NO                    AS WF_CURRENT_STEP_NO,\n"
                + "    wii.CURRENT_STEP_NAME                  AS WF_CURRENT_STEP_NAME,\n"
                + "    wii.CURRENT_ASSIGNEE_NO                AS WF_CURRENT_ASSIGNEE_NO,\n"
                + "    wii.CURRENT_ASSIGNEE_NAME              AS WF_CURRENT_ASSIGNEE_NAME,\n"
                + "    wii.WF_FINISHED_DATE,\n"
                + "    wii.PARENT_ID                          AS WF_PARENT_ID,\n"
                + "    wii.LAUNCH_DEPT_CODE                   AS WF_LAUNCH_DEPT_CODE,\n"
                + "    wii.LAUNCH_DEPT_NAME                   AS WF_LAUNCH_DEPT_NAME,\n"
                + "    wii.LAUNCH_COMPANY_CODE                AS WF_LAUNCH_COMPANY_CODE,\n"
                + "    wii.LAUNCH_COMPANY_NAME                AS WF_LAUNCH_COMPANY_NAME,\n"
                + "    wii.SUMMARY                            AS WF_SUMMARY,\n"
                + "    wii.STATUS                             AS WF_STATUS,\n"
                + "    wii.WORKFLOW_NUMBER                    AS WF_WORKFLOW_NUMBER,\n"
                + "    wii.WORKFLOW_TITLE                     AS WF_WORKFLOW_TITLE,\n"
                + "    wii.HANDLE_ALLOTTED_TIME               AS WF_HANDLE_ALLOTTED_TIME,\n"
                + "    wii.URGENCY_LEVEL                      AS WF_URGENCY_LEVEL,\n"
                + "    wii.HANDLE_MARKED_WORDS                AS WF_HANDLE_MARKED_WORDS,\n"
                + "    wii.IS_PRESS                           AS WF_IS_PRESS,\n"
                + "    wii.print                              AS WF_PRINT,\n"
                + "    wii.choice_id                          AS WF_CHOICE_ID,\n"
                + "    wii.sso_org_code                       AS WF_SSO_ORG_CODE,\n"
                + "    wii.sso_org_name                       AS WF_SSO_ORG_NAME,\n"
                + "    wii.form_version                       AS WF_FORM_VERSION,\n"
                + "    wii.CHILD_BUSINESS                     AS WF_CHILD_BUSINESS,\n"
                + "    zdyt.* \n" + defField + " \n FROM ts_base_oa.wf_instance_info wii\n"
                + "INNER JOIN ts_base_oa." + tableName + " zdyt\n"
                + "    ON zdyt.`WORKFLOW_ID` = wii.`WF_INSTANCE_ID`";
//                "LEFT JOIN ts_base_oa.wf_task wt\n" +
//                "    ON wt.`WF_INSTANCE_ID` = wii.`WF_INSTANCE_ID`";

        base += "  LEFT join ts_base_oa.wf_definition_info wdi on wii.`WF_DEFINITION_ID` = wdi.`WF_DEFINITION_ID`\n";
//                "        LEFT join ts_base_oa.wf_form_classify_info wfci on wdi.`WORKFLOW_CLASSIFY` = wfci.`wf_form_classify_id`";
        if (record.getJoin() != null && !record.getJoin().isEmpty()) {
            // 如果有join条件，则拼接到SQL中
            base += " " + record.getJoin();
        }

        List<DpTableField> fileSetList = getSonFormField(getSonFromTableId(definitionInfo.getFormId()));

        // 包含上面SQL查询的所有WF_前缀字段
        String mainRule = "WF_IS_DELETED,WF_CREATE_USER,WF_CREATE_DATE,WF_UPDATE_USER,WF_UPDATE_DATE,WF_CREATE_USER_NAME,WF_UPDATE_USER_NAME,WF_VERSION,WF_WORKFLOW_NO,WF_WORKFLOW_NAME,WF_BUSINESS_ID,WF_CURRENT_STEP_NO,WF_CURRENT_STEP_NAME,WF_CURRENT_ASSIGNEE_NO,WF_CURRENT_ASSIGNEE_NAME,WF_FINISHED_DATE,WF_PARENT_ID,WF_LAUNCH_DEPT_CODE,WF_LAUNCH_DEPT_NAME,WF_LAUNCH_COMPANY_CODE,WF_LAUNCH_COMPANY_NAME,WF_SUMMARY,WF_STATUS,WF_WORKFLOW_NUMBER,WF_WORKFLOW_TITLE,WF_HANDLE_ALLOTTED_TIME,WF_URGENCY_LEVEL,WF_HANDLE_MARKED_WORDS,WF_IS_PRESS,WF_PRINT,WF_CHOICE_ID,WF_SSO_ORG_CODE,WF_SSO_ORG_NAME,WF_FORM_VERSION,WF_CHILD_BUSINESS";
        String specialRule = "WF_INSTANCE_ID,WF_DEFINITION_ID,WF_FINISHED_DATE"; // 特例字段

        String dynamicRule = fileSetList.stream().map(DpTableField::getFieldName).collect(Collectors.joining(",")); // 表单字段
        String orderRule = "desc,asc";

        // 检测排序方式 和排序字段是否规范
        List<String> mainRuleList = Arrays.asList(mainRule.split(","));
        List<String> dynamicRuleList = Arrays.asList(dynamicRule.split(","));

        List<String> orderRuleList = Arrays.asList(orderRule.split(","));

        String orderBy = record.getOrderBy();

        // 如果子业务没有传入排序
        if (StringUtil.isEmpty(orderBy)) {
            // 默认倒序
            if (!orderRuleList.contains(sord)) {
                sord = "desc";
            }
            if (mainRuleList.contains(sidx)) {
                sidx = sidx.replace("WF_", "");
                orderBy = String.format(" ORDER BY `wii`.`%s` %s", sidx, sord);
            } else if (dynamicRuleList.contains(sidx)) {
                orderBy = String.format(" ORDER BY `zdyt`.`%s` %s", sidx, sord);
            } else {
                orderBy = " order by `wii`.`CREATE_DATE` desc ,zdyt.`ID` ";
            }
        }

        // 拆分参数成 主要查询 和 动态查询两个部分
        Map<String, String> mainParams = record.getMainParams() == null ? new HashMap<>() : record.getMainParams();
        Map<String, String> dynamicParams = new HashMap<>();

        // 为空的容错
        if (params == null) {
            params = new HashMap<>();
        }

        // 拆分参数
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            if (mainRuleList.contains(k)) {
                mainParams.put(k, v);
            } else if (dynamicRuleList.contains(k)) {
                dynamicParams.put(k, v);
            }
        }

        Map<String, String> mainFieldType = getMainFieldMap();

        Map<String, String> dynamicFieldType = fileSetList.stream().filter(f -> f.getFieldName() != null && f.getFieldType() != null) // 可选：防止空指针
                .collect(Collectors.toMap(DpTableField::getFieldName, DpTableField::getFieldType));

        // 构建where请求 循环mainParams
        StringBuilder where = new StringBuilder("WHERE 1=1 " + defWhere);

        // 循环mainFieldType
        for (Map.Entry<String, String> entry : mainFieldType.entrySet()) {
            String field = entry.getKey();
            String type = entry.getValue();
            String finalField = field.replace("WF_", ""); // 去掉WF_前缀
            // 检测mainParams中是否存在该字段
            if (mainParams.containsKey(field)) {
                String value = mainParams.get(field);
                if (value == null || value.isEmpty()) {
                    continue;
                }
                // finalField 字符串包含了 DATE 则使用between and 查询
                if (finalField.toUpperCase().contains("DATE")) {
                    List<String> dateSet = Arrays.asList(value.split(","));
                    if (dateSet == null || dateSet.size() < 2) {
                        break;
                    }
                    String start = dateSet.get(0);
                    String end = dateSet.get(1);
                    where.append(String.format("AND wii.`%s` BETWEEN '%s' AND '%s' ", finalField, start, end));
                } else {
                    // 全部like TODO 这里可能有性能瓶颈 不考虑那么多
                    where.append(String.format("AND wii.`%s` LIKE '%%%s%%' ", finalField, value));
                }

            }
        }

        // 循环dynamicFieldType
        for (Map.Entry<String, String> entry : dynamicFieldType.entrySet()) {
            String field = entry.getKey();
            String type = entry.getValue();
            // 检测dynamicParams中是否存在该字段
            if (dynamicParams.containsKey(field)) {
                String value = dynamicParams.get(field);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                // finalField 字符串包含了 DATE 则使用between and 查询
                if (type.toUpperCase().contains("DATE")) {
                    List<String> dateSet = Arrays.asList(value.split(","));
                    if (dateSet == null || dateSet.size() < 2) {
                        break;
                    }
                    String start = dateSet.get(0);
                    String end = dateSet.get(1);
                    where.append(String.format("AND zdyt.`%s` BETWEEN '%s' AND '%s' ", field, start, end));
                }else{
                    where.append(String.format("AND zdyt.`%s` LIKE '%%%s%%' ", field, value));
                }
            }
        }

        String deptCodeSet = getDeptCodeSet();

        // 流程类型
        where.append(" and wdi.`WF_DEFINITION_ID` = '" + definitionId + "'");
        String delete1 = " and wii.`is_deleted` = 'N'";
        switch (status) {
            case PurchaseConst.PURCHASE_PAGE_STATUS_APPLY:
                where.append(" and wii.`status` in ('2','3','4','5','6')");
                if (deptCodeSet != null) {
                    where.append(" and wii.`LAUNCH_DEPT_CODE` in (" + deptCodeSet + ")");
                }
                where.append(delete1);
                break;
        }
        // 拼接sql语句
        String sql = base + where.toString() + orderBy;
        System.out.println(sql);
        List<Map<String, Object>> records = mapper.getInstanceList(page, sql);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public String getMainFormTableName(String formId) {
        AMSDpFormTemplate dpFormTemplate = mapper.selectDpFormTemplateById(formId);
        return dpFormTemplate.getTableName();
    }

    @Override
    public List<AMSInstanceDefinitionInfoFieldSet> getMainFormFieldFromFormId(String formId) {
        List<AMSInstanceDefinitionInfoFieldSet> formTemplates = getInstanceDefinitionFieldSetListByTemplateId(formId);
        return formTemplates;
    }

    @Override
    public String getSonFromTableId(String formId) {
        // 根据formId查询到主表字段配置
        List<AMSInstanceDefinitionInfoFieldSet> formTemplates = mapper.getInstanceDefinitionFieldSetListByTemplateId(formId);

        // 循环找到 fieldType 为 "childForm" 的字段 break；得到tableId
        String tableId = "";
        for (AMSInstanceDefinitionInfoFieldSet fieldSet : formTemplates) {
            if ("childForm".equals(fieldSet.getFieldType())) {
                // 根据tableId查询子表字段配置
                tableId = fieldSet.getTableId();
            }
        }
        return tableId;

    }

    @Override
    public String getSonFormTableName(String formId) {
        // 根据formId查询到主表字段配置

        String tableId = getSonFromTableId(formId);

        if (tableId.isEmpty()) {
            throw new RuntimeException("没有找到子表字段配置");
        }

        return mapper.getSonFormTableName(tableId);
        // 根据tableId查询子表字段配置
    }

    @Override
    public List<DpTableField> getSonFormFieldFromFormId(String formId) {
        // 根据formId查询到主表字段配置
        List<AMSInstanceDefinitionInfoFieldSet> formTemplates = getInstanceDefinitionFieldSetListByTemplateId(formId);
        // 循环找到 fieldType 为 "childForm" 的字段 break；得到tableId
        for (AMSInstanceDefinitionInfoFieldSet fieldSet : formTemplates) {
            if ("childForm".equals(fieldSet.getFieldType())) {
                // 根据tableId查询子表字段配置
                List<DpTableField> sonFormFields = getSonFormField(fieldSet.getTableId());
                return sonFormFields;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public AMSInstanceDefinitionInfo getInstanceDefinitionById(String definitionId) {
        return mapper.selectInstanceDefinitionById(definitionId);
    }

    private Map<String, String> getMainFieldMap() {
        Map<String, String> mainFieldType = new HashMap<>();
        mainFieldType.put("WF_INSTANCE_ID", "input");
        mainFieldType.put("WF_DEFINITION_ID", "input");
        mainFieldType.put("WF_IS_DELETED", "input");
        mainFieldType.put("WF_CREATE_USER", "input");
        mainFieldType.put("WF_CREATE_DATE", "date");
        mainFieldType.put("WF_UPDATE_USER", "input");
        mainFieldType.put("WF_UPDATE_DATE", "date");
        mainFieldType.put("WF_CREATE_USER_NAME", "input");
        mainFieldType.put("WF_UPDATE_USER_NAME", "input");
        mainFieldType.put("WF_VERSION", "input");
        mainFieldType.put("WF_WORKFLOW_NO", "input");
        mainFieldType.put("WF_WORKFLOW_NAME", "input");
        mainFieldType.put("WF_BUSINESS_ID", "input");
        mainFieldType.put("WF_CURRENT_STEP_NO", "input");
        mainFieldType.put("WF_CURRENT_STEP_NAME", "input");
        mainFieldType.put("WF_CURRENT_ASSIGNEE_NO", "input");
        mainFieldType.put("WF_CURRENT_ASSIGNEE_NAME", "input");
        mainFieldType.put("WF_FINISHED_DATE", "date");
        mainFieldType.put("WF_PARENT_ID", "input");
        mainFieldType.put("WF_LAUNCH_DEPT_CODE", "input");
        mainFieldType.put("WF_LAUNCH_DEPT_NAME", "input");
        mainFieldType.put("WF_LAUNCH_COMPANY_CODE", "input");
        mainFieldType.put("WF_LAUNCH_COMPANY_NAME", "input");
        mainFieldType.put("WF_SUMMARY", "input");
        mainFieldType.put("WF_STATUS", "input");
        mainFieldType.put("WF_WORKFLOW_NUMBER", "input");
        mainFieldType.put("WF_WORKFLOW_TITLE", "input");
        mainFieldType.put("WF_HANDLE_ALLOTTED_TIME", "input");
        mainFieldType.put("WF_URGENCY_LEVEL", "input");
        mainFieldType.put("WF_HANDLE_MARKED_WORDS", "input");
        mainFieldType.put("WF_IS_PRESS", "input");
        mainFieldType.put("WF_PRINT", "input");
        mainFieldType.put("WF_CHOICE_ID", "input");
        mainFieldType.put("WF_SSO_ORG_CODE", "input");
        mainFieldType.put("WF_SSO_ORG_NAME", "input");
        mainFieldType.put("WF_FORM_VERSION", "input");
        mainFieldType.put("WF_CHILD_BUSINESS", "input");
        mainFieldType.put("WF_TASK_ID", "input");
        return mainFieldType;
    }

    private static String parseformTemplate2String(String input) {
        // 去除字符串中的方括号和双引号
        String cleanedInput = input.replace("[", "").replace("]", "").replace("\"", "");

        // 使用逗号分割字符串
        String[] parts = cleanedInput.split(",");

        // 使用StringBuilder拼接字符串
        StringBuilder resultBuilder = new StringBuilder();
        for (String part : parts) {
            resultBuilder.append(part.trim()).append(",");
        }

        // 删除最后一个逗号
        if (resultBuilder.length() > 0) {
            resultBuilder.deleteCharAt(resultBuilder.length() - 1);
        }

        return resultBuilder.toString();
    }

}
