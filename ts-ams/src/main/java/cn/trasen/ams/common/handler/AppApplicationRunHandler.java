package cn.trasen.ams.common.handler;

import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.service.ColCfgService;
import cn.trasen.ams.material.service.impl.ColCfgServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.handler
 * @className: AppApplicationRunHandler
 * @author: chenbin
 * @description: TODO
 * @date: 2025/9/3 16:21
 * @version: 1.0
 */
@Component
public class AppApplicationRunHandler implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(ColCfgServiceImpl.class);

    @Autowired
    private ColCfgService colCfgService;

    @Autowired
    private WarehouseService warehouseService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.warn("AMS 应用启动任务执行开始");

        // 执行启动任务
        executeTask("物资字段配置初始化", () -> colCfgService.genDefData());
        executeTask("物资仓库初始化", () -> warehouseService.genDefData());

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;
        logger.warn("AMS 应用启动任务执行完成, 总耗时: {}ms", totalDuration);
    }

    /**
     * 执行启动任务的通用方法
     * @param taskName 任务名称
     * @param task 任务执行逻辑
     */
    private void executeTask(String taskName, Runnable task) {
        long taskStartTime = System.currentTimeMillis();
        logger.info("开始执行任务: {}", taskName);
        
        try {
            task.run();
            long taskDuration = System.currentTimeMillis() - taskStartTime;
            logger.info("任务完成: {}, 耗时: {}ms", taskName, taskDuration);
        } catch (Exception e) {
            long taskDuration = System.currentTimeMillis() - taskStartTime;
            logger.error("任务失败: {}, 耗时: {}ms, 错误: {}", taskName, taskDuration, e.getMessage(), e);
            throw e;
        }
    }
}
