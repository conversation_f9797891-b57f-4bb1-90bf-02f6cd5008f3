package cn.trasen.ams.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * POI工具类 - 提供Excel和Word文档的操作功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/9/2
 */
@Slf4j
public class PoiHelper {

    public static List<List<String>> readSimpleExcel(InputStream in, Integer headLineNum) throws Exception {
        List<List<String>> results = new ArrayList<>();
        if (in == null) {
            throw new IllegalArgumentException("流不存在");
        }
        Workbook wb = WorkbookFactory.create(in);
        Sheet sheet = wb.getSheetAt(0);
        int rowCnt = sheet.getPhysicalNumberOfRows();
        int totalCols = 0;

        // 先确定总列数（假设表头在第 headLineNum-1 行）
        if (headLineNum != null && headLineNum > 0) {
            Row headRow = sheet.getRow(headLineNum - 1);
            if (headRow != null) {
                totalCols = headRow.getPhysicalNumberOfCells();
            }
        } else if (rowCnt > 0) {
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                totalCols = firstRow.getPhysicalNumberOfCells();
            }
        }

        // 从第 headLineNum 行开始遍历数据
        for (int r = headLineNum != null ? headLineNum : 0; r < rowCnt; r++) {
            Row row = sheet.getRow(r);
            if (row == null || isRowEmpty(row)) {
                continue;
            }
            List<String> list = new ArrayList<>();
            for (int c = 0; c < totalCols; c++) {
                list.add(getCellVal(row.getCell(c)));
            }
            results.add(list);
        }
        return results;
    }

    private static boolean isRowEmpty(Row row) {
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
                return false;
            }
        }
        return true;
    }

    static String getCellVal(Cell cell) {
        String cellVal = "";
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        DecimalFormat dft = new DecimalFormat("##.##");
        if (cell != null) {
            switch (cell.getCellTypeEnum()) {
                case NUMERIC: // 数值/日期
                    if (DateUtil.isCellDateFormatted(cell)) { // 日期型
                        cellVal = df.format(cell.getDateCellValue());
                    } else { // 数值型
                        if (String.valueOf(cell.getNumericCellValue()).contains(".")) {
                            cellVal = dft.format(cell.getNumericCellValue());
                        } else {
                            cellVal = String.valueOf(cell.getNumericCellValue());
                        }
                    }
                    break;
                case STRING: // 字符串
                    cellVal = cell.getStringCellValue();
                    break;
                case FORMULA: // 公式
                    try {
                        cellVal = cell.getStringCellValue();
                    } catch (IllegalStateException e) {
                        cellVal = dft.format(cell.getNumericCellValue());
                    }
                    break;
                case BLANK: // 空白
                    cellVal = "";
                    break;
                case BOOLEAN: // 布尔值
                    cellVal = String.valueOf(cell.getBooleanCellValue());
                    break;
                case ERROR: // 错误
                    cellVal = "error";
                default:
                    cellVal = "defaultVal";
                    break;
            }
        }
        return cellVal.trim();
    }
}
