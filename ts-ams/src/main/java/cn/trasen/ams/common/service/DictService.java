package cn.trasen.ams.common.service;

import cn.trasen.homs.bean.base.DictItemResp;

import java.util.List;
import java.util.Map;

public interface DictService {


    List<DictItemResp> cgetKL(String code);

    Map<String, String> cgetKS(String code);

    Map<String, String> cgetSV(String code);

    Map<String, String> cgetCV(String code);

    Map<String, DictItemResp> cgetKI(String code);

    String cgetNameByValue(String code, String value);

    String cgetValueByName(String code, String name);

    String cgetValueByCode(String code, String itemCode);
}
