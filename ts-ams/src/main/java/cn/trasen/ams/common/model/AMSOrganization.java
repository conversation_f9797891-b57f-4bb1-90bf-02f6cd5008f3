package cn.trasen.ams.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.model
 * @className: Organization
 * @author: chenbin
 * @description:
 * @date: 2024/1/30 10:03
 * @version: 1.0
 */
@Table(name = "comm_organization")
@Setter
@Getter
public class AMSOrganization {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "organization_id")
    @ApiModelProperty(value = "主键ID")
    private String organizationId;

    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private String code;

    /**
     * 组织机构名称
     */
    @Excel(name = "科室名称")
    @ApiModelProperty(value = "组织机构名称")
    private String name;

    /**
     * 树ID
     */
    @Column(name = "tree_ids")
    @ApiModelProperty(value = "树ID")
    private String treeIds;

    /**
     * 父类ID
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 树结构中级别
     */
    @Excel(name = "科室等级")
    @Column(name = "org_level")
    @ApiModelProperty(value = "树结构中级别")
    private Integer orgLevel;

    /**
     * 排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序")
    private Integer seqNo;

    /**
     * 组织机构类型
     */
    @Column(name = "org_flag")
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;

    /**
     * 管理人员代码
     */
    @Column(name = "manager_code")
    @ApiModelProperty(value = "管理人员代码")
    private String managerCode;

    /**
     * 管理人员名称
     */
    @Column(name = "manager_name")
    @ApiModelProperty(value = "管理人员名称")
    private String managerName;

    /**
     * 分管领导
     */
    @Column(name = "assigne_manager_code")
    @ApiModelProperty(value = "分管领导")
    private String assigneManagerCode;

    /**
     * 分管领导名称
     */
    @Column(name = "assigne_manager_name")
    @ApiModelProperty(value = "分管领导名称")
    private String assigneManagerName;

    /**
     * 部门职责
     */
    @ApiModelProperty(value = "部门职责")
    private String responsibility;

    /**
     * 定编人数
     */
    @Column(name = "personnel_allocation")
    @ApiModelProperty(value = "定编人数")
    private Integer personnelAllocation;

    /**
     * 报表排序
     */
    @Column(name = "report_sort")
    @ApiModelProperty(value = "报表排序")
    private Integer reportSort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 三甲医生标准人数
     */
    @Column(name = "doctor_sta")
    @ApiModelProperty(value = "三甲医生标准人数")
    private String doctorSta;

    /**
     * 三甲护士标准人数
     */
    @Column(name = "nurse_sta")
    @ApiModelProperty(value = "三甲护士标准人数")
    private String nurseSta;

    /**
     * 三甲医床比
     */
    @Column(name = "doctor_three")
    @ApiModelProperty(value = "三甲医床比")
    private String doctorThree;

    /**
     * 三甲护床比
     */
    @Column(name = "nurse_three")
    @ApiModelProperty(value = "三甲护床比")
    private String nurseThree;


    /**
     * 电话号码
     */
    private String tel;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    @Column(name = "custom_code")
    private String customCode;

}
