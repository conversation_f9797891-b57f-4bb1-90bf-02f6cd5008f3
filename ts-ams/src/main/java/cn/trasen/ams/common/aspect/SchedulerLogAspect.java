package cn.trasen.ams.common.aspect;

import cn.trasen.ams.common.annotation.sch.SchedulerLog;
import cn.trasen.ams.common.model.SchedulerExecutionLog;
import cn.trasen.ams.common.service.SchedulerExecutionLogService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.aspect
 * @className: SchedulerLogAspect
 * @author: chenbin
 * @description: 定时器执行日志切面
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
@Slf4j
@Aspect
@Component
public class SchedulerLogAspect {

    @Autowired
    private SchedulerExecutionLogService schedulerExecutionLogService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 环绕通知，记录定时器执行日志
     */
    @Around("@annotation(schedulerLog)")
    public Object around(ProceedingJoinPoint joinPoint, SchedulerLog schedulerLog) throws Throwable {
        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 创建执行记录
        SchedulerExecutionLog executionLog = new SchedulerExecutionLog();
        executionLog.setStartTime(new Date());
        executionLog.setStatus("RUNNING");
        executionLog.setMethodName(method.getName());
        executionLog.setClassName(method.getDeclaringClass().getName());
        executionLog.setThreadName(Thread.currentThread().getName());

        // 设置定时器名称和分组
        String schedulerName = schedulerLog.schedulerName();
        if (schedulerName.isEmpty()) {
            schedulerName = method.getName();
        }
        executionLog.setSchdName(schedulerName);
        executionLog.setSchdGrp(schedulerLog.schedulerGroup());

        // 记录输入参数
        if (schedulerLog.logInput()) {
            try {
                executionLog.setInput(objectMapper.writeValueAsString(joinPoint.getArgs()));
            } catch (JsonProcessingException e) {
                log.warn("序列化输入参数失败: {}", e.getMessage());
                executionLog.setInput("序列化失败");
            }
        }

        // 记录系统指标
        if (schedulerLog.logMetrics()) {
            recordSystemMetrics(executionLog);
        }

        // 保存初始记录
        String logId = null;
        try {
            schedulerExecutionLogService.save(executionLog);
            logId = executionLog.getId();
        } catch (Exception e) {
            log.error("保存定时器执行记录失败: {}", e.getMessage(), e);
        }

        long startTime = System.currentTimeMillis();
        Object result = null;
        boolean isSuccess = false;

        try {
            // 执行原方法
            result = joinPoint.proceed();
            isSuccess = true;
            return result;
        } catch (Exception e) {
            // 记录异常信息
            executionLog.setErrMsg(e.getMessage());
            executionLog.setStatus("FAILED");
            throw e;
        } finally {
            // 更新执行记录
            updateExecutionLog(executionLog, logId, startTime, result, isSuccess, schedulerLog);
        }
    }

    /**
     * 记录系统指标
     */
    private void recordSystemMetrics(SchedulerExecutionLog executionLog) {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            executionLog.setMemUsage(usedMemory);

            // 简单的CPU使用率计算（这里可以根据需要集成更精确的监控）
            executionLog.setCpuUsage(BigDecimal.ZERO);
        } catch (Exception e) {
            log.warn("记录系统指标失败: {}", e.getMessage());
        }
    }

    /**
     * 更新执行记录
     */
    private void updateExecutionLog(SchedulerExecutionLog executionLog, String logId,
                                    long startTime, Object result, boolean isSuccess,
                                    SchedulerLog schedulerLog) {
        try {
            if (logId != null) {
                // 重新查询记录
                SchedulerExecutionLog existingLog = schedulerExecutionLogService.selectById(logId.toString());
                if (existingLog != null) {
                    // 更新执行结果
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;

                    existingLog.setEndTime(new Date());
                    existingLog.setExecDur(duration);

                    if (isSuccess) {
                        existingLog.setStatus("SUCCESS");
                    }

                    // 检查是否超时
                    if (duration > schedulerLog.timeoutThreshold()) {
                        existingLog.setStatus("TIMEOUT");
                        if (existingLog.getErrMsg() == null) {
                            existingLog.setErrMsg("执行超时，耗时: " + duration + "ms");
                        }
                    }

                    // 记录输出结果
                    if (schedulerLog.logOutput() && result != null) {
                        try {
                            existingLog.setOutput(objectMapper.writeValueAsString(result));
                        } catch (JsonProcessingException e) {
                            log.warn("序列化输出结果失败: {}", e.getMessage());
                            existingLog.setOutput("序列化失败");
                        }
                    }

                    // 更新记录
                    schedulerExecutionLogService.update(existingLog);
                }
            }
        } catch (Exception e) {
            log.error("更新定时器执行记录失败: {}", e.getMessage(), e);
        }
    }
} 