package cn.trasen.ams.common.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "c_schd_exec_log")
@Setter
@Getter
public class SchedulerExecutionLog {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 定时器名称
     */
    @Column(name = "schd_name")
    @ApiModelProperty(value = "定时器名称")
    private String schdName;

    /**
     * 定时器分组
     */
    @Column(name = "schd_grp")
    @ApiModelProperty(value = "定时器分组")
    private String schdGrp;

    /**
     * 执行方法名
     */
    @Column(name = "method_name")
    @ApiModelProperty(value = "执行方法名")
    private String methodName;

    /**
     * 执行类名
     */
    @Column(name = "class_name")
    @ApiModelProperty(value = "执行类名")
    private String className;

    /**
     * 开始执行时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "开始执行时间")
    private Date startTime;

    /**
     * 结束执行时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "结束执行时间")
    private Date endTime;

    /**
     * 执行时长(毫秒)
     */
    @Column(name = "exec_dur")
    @ApiModelProperty(value = "执行时长(毫秒)")
    private Long execDur;

    /**
     * 执行状态：SUCCESS/FAILED/RUNNING
     */
    @ApiModelProperty(value = "执行状态：SUCCESS/FAILED/RUNNING")
    private String status;

    /**
     * 执行线程名
     */
    @Column(name = "thread_name")
    @ApiModelProperty(value = "执行线程名")
    private String threadName;

    /**
     * 内存使用量(字节)
     */
    @Column(name = "mem_usage")
    @ApiModelProperty(value = "内存使用量(字节)")
    private Long memUsage;

    /**
     * CPU使用率(%)
     */
    @Column(name = "cpu_usage")
    @ApiModelProperty(value = "CPU使用率(%)")
    private BigDecimal cpuUsage;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    /**
     * 错误信息
     */
    @Column(name = "err_msg")
    @ApiModelProperty(value = "错误信息")
    private String errMsg;

    /**
     * 输入参数(JSON格式)
     */
    @Column(name = "input")
    @ApiModelProperty(value = "输入参数(JSON格式)")
    private String input;

    /**
     * 输出结果(JSON格式)
     */
    @Column(name = "output")
    @ApiModelProperty(value = "输出结果(JSON格式)")
    private String output;
}