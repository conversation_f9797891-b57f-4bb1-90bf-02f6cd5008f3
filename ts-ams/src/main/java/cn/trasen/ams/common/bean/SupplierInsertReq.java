package cn.trasen.ams.common.bean;

import cn.trasen.ams.common.model.Cert;
import cn.trasen.ams.common.model.Supplier;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.bean
 * @className: SupplierInsertReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/7/25 17:47
 * @version: 1.0
 */
@Data
@Validated
public class SupplierInsertReq {

    @Valid
    @NotNull(message = "供应商信息不能为空")
    @ApiModelProperty(value = "供应商")
    private Supplier supplier;
    
    @ApiModelProperty(value = "证件信息")
    private List<Cert> certList;

}
