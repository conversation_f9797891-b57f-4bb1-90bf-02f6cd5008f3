<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.TableMapper">
    <select id="hasById" resultType="int">
        select count(1)
        from ${table}
        <choose>
            <when test="ignoreIsDelete != null and ignoreIsDelete ==&quot;N&quot;">
                where is_deleted = 'N'
            </when>
            <otherwise>
                where 1 = 1
            </otherwise>
        </choose>

        <choose>
            <when test="pk != null and pk !=''">
                and ${pk}
            </when>
            <otherwise>
                and id
            </otherwise>
        </choose>
        = #{id, jdbcType=VARCHAR}
    </select>
    <select id="hasByIdList" resultType="int">
        select count(1) from ${table}
        <if test="id != null and id.length > 0">
            <choose>
                <when test="ignoreIsDelete != null and ignoreIsDelete ==&quot;N&quot;">
                    where is_deleted = 'N'
                </when>
                <otherwise>
                    where 1 = 1
                </otherwise>
            </choose>
            <choose>
                <when test="pk != null and pk !=''">
                    and ${pk}
                </when>
                <otherwise>
                    and id
                </otherwise>
            </choose>
            in
            <foreach item="itemId" index="index" collection="id" open="(" separator="," close=")">
                #{itemId, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="hasDict" resultType="int">
        select count(1)
        from COMM_DICT_ITEM cdi
        left join COMM_DICT_TYPE cdt on cdi.DIC_TYPE_ID = cdt.ID
        where cdt.TYPE_CODE = #{typeCode}
          and cdt.SYS_CODE = #{sysCode}
          and cdi.ITEM_NAME_VALUE = #{val}
          and cdt.IS_DELETED = 'N'
          and cdi.IS_DELETED = 'N'
          and cdi.IS_ENABLE = '1'
        <!-- and cdi.sso_org_code = #{ssoOrgCode} -->
  </select>

  <select id="getDictItemByTypeCode" resultType="cn.trasen.homs.bean.base.DictItemResp">
      select t1.*, t2.`TYPE_CODE` as dict_type_code, t2.`TYPE_NAME` as dict_type_name
      from `ts_base_oa`.`comm_dict_item` t1
               left join `ts_base_oa`.`comm_dict_type` t2 on t1.`DIC_TYPE_ID` = t2.`ID`
      where t2.TYPE_CODE = #{typeCode}
        <!-- and t1.sso_org_code = #{ssoOrgCode} -->
          and t1.`IS_DELETED` = 'N'
          and t1.`is_enable` = '1'
    </select>
</mapper>