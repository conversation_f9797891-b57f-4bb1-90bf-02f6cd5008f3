package cn.trasen.ams.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.config
 * @className: SchedulerLogConfig
 * @author: chenbin
 * @description: 定时器日志配置
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
@Configuration
@EnableAspectJAutoProxy
public class SchedulerLogConfig {
    // 配置已通过注解启用
} 