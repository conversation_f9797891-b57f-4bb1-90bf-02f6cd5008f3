package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.service.TableService;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: DictServiceImpl
 * @author: chenbin
 * @description: 字典服务实现类，提供字典数据的获取和转换功能
 * @date: 2024/12/12 13:53
 * @version: 1.0
 */
@Service
public class DictServiceImpl implements DictService {
    @Autowired
    private TableService tableService;

    @Autowired
    private RedisService redisService;

    /**
     * 根据字典类型代码获取字典项列表
     * 
     * @param code 字典类型代码
     * @return 字典项列表
     * @throws BusinessException 当字典远程读取失败或字典配置未配置时抛出异常
     * @description 从Redis缓存中获取字典数据，如果缓存不存在则从数据库获取并缓存24小时
     */
    @Override
    public List<DictItemResp> cgetKL(String code) {

        List<DictItemResp> dictItemResps;
        String cacheKey = "dict:" + code;
        try {
            dictItemResps = (List<DictItemResp>) redisService.fetch(cacheKey, () -> {
//                PlatformResult<List<DictItemResp>> pr = dictItemFeignService.getDictItemByTypeCode(code);
//                return pr.getObject();
                return tableService.getDictItemByTypeCode(code);
            }, 60 * 60 * 24);
        } catch (Exception e) {
            throw new BusinessException("【" + code + "】字典远程读取失败", e);
        }

        if (dictItemResps == null || dictItemResps.size() <= 0) {
            redisService.clearFetch(cacheKey);
            throw new BusinessException("【" + code + "】字典配置未配置");
        }

        return dictItemResps;

    }

    /**
     * 根据字典类型代码获取字典值到显示名称的映射
     * 
     * @param code 字典类型代码
     * @return Map<String, String> 字典值到显示名称的映射，key为字典值，value为显示名称
     * @description 将字典项列表转换为Map格式，便于通过字典值快速获取对应的显示名称
     */
    @Override
    public Map<String, String> cgetKS(String code) {

        List<DictItemResp> dictItemResps = cgetKL(code);

        Map<String, String> dictMap = new HashMap<>();

        // 把字典值list 转换成 map
        for (DictItemResp dir : dictItemResps) {
            dictMap.put(dir.getItemNameValue(), dir.getItemName());
        }

        return dictMap;
    }

    /**
     * 根据字典类型代码获取显示名称到字典值的映射
     * 
     * @param code 字典类型代码
     * @return Map<String, String> 显示名称到字典值的映射，key为显示名称，value为字典值
     * @description 将字典项列表转换为Map格式，便于通过显示名称快速获取对应的字典值
     */
    @Override
    public Map<String, String> cgetSV(String code) {

        List<DictItemResp> dictItemResps = cgetKL(code);

        Map<String, String> dictMap = new HashMap<>();

        // 把字典值list 转换成 map
        for (DictItemResp dir : dictItemResps) {
            dictMap.put(dir.getItemName(), dir.getItemNameValue());
        }
        return dictMap;
    }

    /**
     * 根据字典类型代码获取字典编码到字典值的映射
     * 
     * @param code 字典类型代码
     * @return Map<String, String> 字典编码到字典值的映射，key为字典编码，value为字典值
     * @description 将字典项列表转换为Map格式，便于通过字典编码快速获取对应的字典值
     */
    @Override
    public Map<String, String> cgetCV(String code) {

        List<DictItemResp> dictItemResps = cgetKL(code);

        Map<String, String> dictMap = new HashMap<>();

        // 把字典值list 转换成 map
        for (DictItemResp dir : dictItemResps) {
            dictMap.put(dir.getItemCode(), dir.getItemNameValue());
        }
        return dictMap;
    }

    /**
     * 根据字典类型代码获取字典编码到字典对象的映射
     * 
     * @param code 字典类型代码
     * @return Map<String, DictItemResp> 字典编码到字典对象的映射，key为字典编码，value为完整的字典项对象
     * @description 将字典项列表转换为Map格式，便于通过字典编码快速获取完整的字典项信息
     */
    @Override
    public Map<String, DictItemResp> cgetKI(String code) {

        List<DictItemResp> dictItemResps = cgetKL(code);

        Map<String, DictItemResp> dictMap = new HashMap<>();

        // 把字典值list 转换成 map
        for (DictItemResp dir : dictItemResps) {
            dictMap.put(dir.getItemCode(), dir);
        }

        return dictMap;

    }

    /**
     * 根据字典类型代码和字典值获取对应的显示名称
     * 
     * @param code 字典类型代码
     * @param value 字典值，支持单个值或逗号分隔的多个值
     * @return String 对应的显示名称，多个值用逗号分隔
     * @description 支持单个字典值转换和多个字典值批量转换，如果输入为null则返回空字符串
     */
    @Override
    public String cgetNameByValue(String code, String value) {
        if (value == null) {
            return "";
        }
        // 升级版 如果 value 包含, 则返回多个值
        if (value.contains(",")) {
            String[] values = value.split(",");
            List<String> valueList = new ArrayList<>();
            for (String v : values) {
                Map<String, String> dictMap = cgetKS(code);
                valueList.add(dictMap.get(v));
            }
            return String.join(",", valueList);
        } else {
            Map<String, String> dictMap = cgetKS(code);
            return dictMap.get(value);
        }

    }

    /**
     * 根据字典类型代码和显示名称获取对应的字典值
     * 
     * @param code 字典类型代码
     * @param name 显示名称，支持单个名称或逗号分隔的多个名称
     * @return String 对应的字典值，多个值用逗号分隔
     * @description 支持单个显示名称转换和多个显示名称批量转换，如果输入为null则返回空字符串
     */
    @Override
    public String cgetValueByName(String code, String name) {
        if (name == null) {
            return "";
        }
        // 升级版 如果 value 包含, 则返回多个值
        if (name.contains(",")) {
            String[] names = name.split(",");
            List<String> valueList = new ArrayList<>();
            for (String n : names) {
                Map<String, String> dictMap = cgetSV(code);
                valueList.add(dictMap.get(n));
            }
            return String.join(",", valueList);
        } else {
            Map<String, String> dictMap = cgetSV(code);
            return dictMap.get(name);
        }
    }

    @Override
    public String cgetValueByCode(String code, String itemCode) {
        if (itemCode == null) {
            return "";
        }
        // 升级版 如果 itemCode 包含, 则返回多个值
        if (itemCode.contains(",")) {
            String[] itemCodes = itemCode.split(",");
            List<String> valueList = new ArrayList<>();
            for (String ic : itemCodes) {
                Map<String, String> dictMap = cgetCV(code);
                valueList.add(dictMap.get(ic));
            }
            return String.join(",", valueList);
        } else {
            Map<String, String> dictMap = cgetCV(code);
            return dictMap.get(itemCode);
        }
    }
}
