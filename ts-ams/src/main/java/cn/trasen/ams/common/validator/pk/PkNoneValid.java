package cn.trasen.ams.common.validator.pk;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PkNoneValidator.class)
@Documented

public @interface PkNoneValid {
    String message() default "关联对象ID存在数据";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String table();

    String pk() default "id";

    String ignoreIsDelete() default "N";
}
