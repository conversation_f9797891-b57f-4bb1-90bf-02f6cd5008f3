package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: PermissionServiceImpl
 * @author: chenbin
 * @description: 公用数据权限服务
 * @date: 2025/4/24 15:49
 * @version: 1.0
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private DictService dictService;

    @Autowired
    private RedisService redisService;


    private int cacheSeconds = 3;

    /**
     * @param orgRang:
     * @return List<List>
     * <AUTHOR>
     * @description 拆分用户的科室权限串，分离出auth 和 deptId
     * @date 2025/4/25 11:30
     */
    private List<List> getAuthAndDeptId(String orgRang) {
        orgRang = orgRang.trim();

        // 去掉括号
        if (orgRang.startsWith("(") && orgRang.endsWith(")")) {
            orgRang = orgRang.substring(1, orgRang.length() - 1);
        }

        // 分割并去掉引号
        String[] parts = orgRang.replace("'", "").split(",");

        // 默认权限列表
        Set<String> authSet = new HashSet<>(Arrays.asList(PermissionConst.SSO_AUTH_SELF,
                PermissionConst.SSO_AUTH_SELF_DEPT,
                PermissionConst.SSO_AUTH_SELF_SUB_DEPT));

        // 初始化两个列表
        List<String> authList = new ArrayList<>();
        List<String> deptIdList = new ArrayList<>();

        // 分类
        for (String part : parts) {
            part = part.trim();
            if (authSet.contains(part)) {
                authList.add(part);
            } else {
                deptIdList.add(part);
            }
        }
        return Arrays.asList(authList, deptIdList);
    }

    @Override
    public PermissionResp getPermission(String business) {

        // 获取所有相关的科室ID 类似 ('SELF','SELF_DEPT','SELF_SUB_DEPT','432816904746987520')
        String orgRang = UserInfoHolder.getOrgRang();
        // 拆分出默认权限与科室ID
        List<List> authAndDeptIdList = getAuthAndDeptId(orgRang);

        List<String> authList = authAndDeptIdList.get(0);
        List<String> deptIdList = authAndDeptIdList.get(1);

        // 获取所有的角色名称 类似 PTYH,ZCSB_ADMIN
        String roleCode = UserInfoHolder.getCurrentUserInfo().getSysRoleCode();

        // 获取AMS_ROLE_SKU_TYPE
        Map<String, String> roleSkuTypeMap = dictService.cgetCV(PermissionConst.ROLE_SKU_TYPE);

        // 拆分roleCode 遍历，获取所有的sku_type
        // 通过字典拉取 所有的角色列表与对应的sku_type，然后通过拉取用户角色进行匹配进行装载
        List<String> roleList = Arrays.asList(roleCode.split(","));

        Map<String, String> businessMap = dictService.cgetCV(PermissionConst.BUSINESS_ROLE);

        Boolean hasAll = false;

        // 检测是否拥有最高权限
        for (String role : roleList) {
            String skuType = roleSkuTypeMap.get(role);
            if (PermissionConst.TYPE_ALL.equals(skuType)) {
                hasAll = true;
            }
        }

        Map<String, List<String>> userBusinessMap = new HashMap<>();
        // 对 businessMap 进行遍历
        for (Map.Entry<String, String> entry : businessMap.entrySet()) {
            String businessStr = entry.getKey();
            String authStr = entry.getValue();
            // 如果业务与当前业务相同，则进行拆分

            String[] roleArray = authStr.split(",");
            List<String> skuTypeList = new ArrayList<>();

            for (String role : roleArray) {
                role = role.trim();
                // 检测当前用户是否存在这个角色
                if (roleList.contains(role)) {
                    String st = roleSkuTypeMap.get(role);
                    if (!StringUtil.isEmpty(st)) {
                        // 这个地方st 可能是1,2,3 这样的格式
                        // 需要进行拆分并添加到skuTypeList
                        String[] stArray = st.split(",");
                        skuTypeList.addAll(Arrays.asList(stArray));
                    }
                }
            }

            // 装载到userBusinessMap中
            userBusinessMap.put(businessStr, skuTypeList);

        }

        // 确定权限类型的逻辑
        // 如果 hasAll 为 true，则权限类型为 ALL
        // 如果 userBusinessMap['业务'] 不为空，则权限类型为 SKU_TYPE
        // 如果 roleList 仅包含一个元素且为 SELF，则权限类型为 SELF
        // 其他情况，权限类型为 DEPT

        PermissionResp permessionResp = new PermissionResp();

        String type;
        if (hasAll) {
            type = PermissionConst.TYPE_ALL;
        } else if (!CollectionUtils.isEmpty(userBusinessMap) && userBusinessMap.get(business) != null && userBusinessMap.get(business).size() > 0) {
            type = PermissionConst.TYPE_SKU_TYPE;
            permessionResp.setSkuTypeList(userBusinessMap.get(business));
        } else if (authList.size() == 1 && authList.contains(PermissionConst.SSO_AUTH_SELF)) {
            type = PermissionConst.TYPE_SELF;
        } else {
            type = PermissionConst.TYPE_DEPT;
        }

        // 获取用户对应业务的SKU_TYPE

        permessionResp.setType(type);
        permessionResp.setBusinessMap(userBusinessMap);
        permessionResp.setAuthList(authList);
        permessionResp.setRoleList(roleList);
        permessionResp.setDeptIdList(deptIdList);
        permessionResp.setUserId(UserInfoHolder.getCurrentUserInfo().getUsercode());


        return permessionResp;
    }

    @Override
    public PermissionResp cgetPermission(String business) {

        String key = "permission:" + business + ":" + UserInfoHolder.getCurrentUserInfo().getUsercode();

        PermissionResp resp;
        try {
            resp = (PermissionResp) redisService.fetch(key, () -> {
                // 如果缓存不存在，则调用 getPermission 方法获取数据
                PermissionResp permission = getPermission(business);
                return permission;
            }, cacheSeconds);
        } catch (Exception e) {
            throw new RuntimeException("获取权限失败", e);
        }


        return resp;
    }

    /**
     * @param sql:
     * @param field:
     * @param values:
     * @return void
     * <AUTHOR>
     * @description 构造in条件
     * @date 2025/5/8 14:11
     */
    @Override
    public void appendInCondition(StringBuffer sql, String field, List<String> values) {
        if (!CollectionUtils.isEmpty(values)) {
            String inCondition = values.stream().map(value -> "'" + value + "'").collect(Collectors.joining(", "));
            sql.append(" and ").append(field).append(" in (").append(inCondition).append(")");
        }
    }

    /**
     * @param sql:
     * @param field:
     * @param value:
     * @return void
     * <AUTHOR>
     * @description 构造 = 条件
     * @date 2025/5/9 10:38
     */
    @Override
    public void appendEqualCondition(StringBuffer sql, String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            sql.append(" and ").append(field).append(" = '").append(value).append("'");
        }
    }


    /**
     * @param business:
     * @param sql:
     * @return void
     * <AUTHOR>
     * @description 根据字符串构造权限条件
     * @date 2025/5/9 14:47
     */
    @Override
    public void appendPermissionSql(String business, StringBuffer sql) {
        String finalBusiness;
        switch (business) {
            case PermissionConst.二级业务类型_资产台账列表:
                finalBusiness = PermissionConst.业务类型_台账管理;
                appendSql4Comm(finalBusiness, sql, "t2.sku_type", "t1.belong_to_org_id", "t1.create_user");
                break;
            case PermissionConst.二级业务类型_资产处置单创建选择资产:
                finalBusiness = PermissionConst.业务类型_审核资产处置单;
                appendSql4DeviceSelect(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_资产转移单创建选择资产:
                finalBusiness = PermissionConst.业务类型_审核资产转移单;
                appendSql4DeviceSelect(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_资产出库单选择资产:
                finalBusiness = PermissionConst.业务类型_审核出库单;
                appendSql4DeviceSelect(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_资产退回入库单选择资产:
                finalBusiness = PermissionConst.业务类型_审核入库单;
                appendSql4DeviceSelect(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_入库单列表:
                finalBusiness = PermissionConst.业务类型_审核入库单;
                appendCondition4inboundOrderList(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_安装验收其他入库登记列表:
                finalBusiness = PermissionConst.业务类型_安装验收;
                appendSql4DeviceSelectWithoutOrg(finalBusiness, sql);
                break;
            case PermissionConst.二级业务类型_安装验收采购入库登记列表:
                finalBusiness = PermissionConst.业务类型_安装验收;
                appendSql4Comm(finalBusiness, sql, "po.sku_type", "po.dept_id", "po.create_user");
                break;
            case PermissionConst.二级业务类型_安装验收已登记:
                finalBusiness = PermissionConst.业务类型_安装验收;
                appendSql4SignoffedList(finalBusiness, sql);
                break;
        }
    }


    /**
     * @param business:
     * @param criteria:
     * @return void
     * <AUTHOR>
     * @description 根据example构造权限条件
     * @date 2025/5/9 14:47
     */
    @Override
    public void appendPermissionCondition(String business, Example.Criteria criteria) {
        String finalBusiness;
        switch (business) {
            case PermissionConst.二级业务类型_出库单列表:
                finalBusiness = PermissionConst.业务类型_审核出库单;
                appendCondition4outboundOrderList(finalBusiness, criteria);
                break;
            case PermissionConst.二级业务类型_资产处置单列表:
                finalBusiness = PermissionConst.业务类型_审核资产处置单;
                appendCondition4Comm(finalBusiness, criteria, "skuType", "deptId", "createUser");
                break;
            case PermissionConst.二级业务类型_盘点列表:
                finalBusiness = PermissionConst.业务类型_资产盘点;
                appendCondition4inventoryPlanList(finalBusiness, criteria);
                break;
            case PermissionConst.二级业务类型_资产转移单列表:
                finalBusiness = PermissionConst.业务类型_审核资产转移单;
                appendCondition4Comm(finalBusiness, criteria, "skuType", "deptId", "createUser");
                break;
        }
    }


    private void appendCondition4inventoryPlanList(String business, Example.Criteria criteria) {

        // 权限问题 与 所属问题
        // 如果用户是设备盘点专员，则只看到设备的计划
        // 如果用户是资产盘点专员，则只看到资产的计划
        // 如果拥有两个这样的角色，那么都可以查看，这里用IN
        // 如果是管理员则都可以查看

        PermissionResp permissionResp = cgetPermission(business);
        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            case PermissionConst.TYPE_SKU_TYPE:
                criteria.andIn("skuType", permissionResp.getSkuTypeList());
                break;
            case PermissionConst.TYPE_DEPT:
                criteria.andCondition("(engineer_id_set like '%" + permissionResp.getUserId() + "%' or dept_id in (" + permissionResp.getDeptIdList().stream().map(deptId -> "'" + deptId + "'").collect(Collectors.joining(",")) + "))");
                break;
            case PermissionConst.TYPE_SELF:
                criteria.andCondition("(engineer_id_set like '%" + permissionResp.getUserId() + "%' or create_user = '" + permissionResp.getUserId() + "' )");
                break;
        }

    }


    /**
     * @param business:
     * @param sql:
     * @return void
     * <AUTHOR>
     * @description 构造入库单的权限条件
     * @date 2025/5/9 16:10
     */
    private void appendCondition4inboundOrderList(String business, StringBuffer sql) {
        PermissionResp permissionResp = cgetPermission(business);
        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            case PermissionConst.TYPE_SKU_TYPE:
                appendInCondition(sql, "t1.sku_type", permissionResp.getSkuTypeList());
                break;
            case PermissionConst.TYPE_DEPT:
                appendInCondition(sql, "t1.dept_id", permissionResp.getDeptIdList());
                break;
            case PermissionConst.TYPE_SELF:
                appendEqualCondition(sql, "t1.create_user", UserInfoHolder.getCurrentUserInfo().getUsercode());
                break;
        }
    }

    /**
     * @param business:
     * @param criteria:
     * @return void
     * <AUTHOR>
     * @description 构造出库单的权限条件
     * @date 2025/5/9 15:59
     */
    private void appendCondition4outboundOrderList(String business, Example.Criteria criteria) {
        PermissionResp permissionResp = cgetPermission(business);

        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            case PermissionConst.TYPE_SKU_TYPE:
                criteria.andIn("skuType", permissionResp.getSkuTypeList());
                break;
            case PermissionConst.TYPE_DEPT:
                criteria.andIn("deptId", permissionResp.getDeptIdList());
                break;
            case PermissionConst.TYPE_SELF:
                criteria.andEqualTo("createUser", permissionResp.getUserId());
                break;
        }
    }


    private void appendCondition4Comm(String business, Example.Criteria criteria, String field1, String field2, String field3) {

        PermissionResp permissionResp = cgetPermission(business);
        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            case PermissionConst.TYPE_SKU_TYPE:
                criteria.andIn(field1, permissionResp.getSkuTypeList());
                break;
            case PermissionConst.TYPE_DEPT:
                criteria.andIn(field2, permissionResp.getDeptIdList());
                break;
            case PermissionConst.TYPE_SELF:
                criteria.andEqualTo(field3, UserInfoHolder.getCurrentUserInfo().getUsercode());
                break;
        }

    }

    /**
     * @param business:
     * @param sql:
     * @return void
     * <AUTHOR>
     * @description 通用sql构造
     * @date 2025/5/9 16:00
     */
    private void appendSql4Comm(String business, StringBuffer sql, String field1, String field2, String field3) {
        PermissionResp permissionResp = cgetPermission(business);

        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            case PermissionConst.TYPE_SKU_TYPE:
                appendInCondition(sql, field1, permissionResp.getSkuTypeList());
                break;
            case PermissionConst.TYPE_DEPT:
                appendInCondition(sql, field2, permissionResp.getDeptIdList());
                break;
            case PermissionConst.TYPE_SELF:
                appendEqualCondition(sql, field3, UserInfoHolder.getCurrentUserInfo().getUsercode());
                break;
        }
    }


    private void appendSql4SignoffedList(String business, StringBuffer sql) {
        PermissionResp permissionResp = cgetPermission(business);
        // 只考虑管理员 和 部门
        switch (permissionResp.getType()) {
            case PermissionConst.TYPE_ALL:
                break;
            default:
                appendInCondition(sql, "t1.dept_id", permissionResp.getDeptIdList());
                break;
        }
    }


    /**
     * @param business:
     * @param sql:
     * @return void
     * <AUTHOR>
     * @description 资产选择的sql构造 （一般用于职能不能管理类选择资产业务）
     * @date 2025/5/9 15:46
     */
    private void appendSql4DeviceSelectWithoutOrg(String business, StringBuffer sql) {
        PermissionResp permissionResp = cgetPermission(business);
        if (PermissionConst.TYPE_ALL.equals(permissionResp.getType())) {
            // 全部权限，无需追加条件
            return;
        }
        // 由于资产都是出入库的时候创建的，所以不存在self权限
        if (PermissionConst.TYPE_SKU_TYPE.equals(permissionResp.getType())) {
            appendInCondition(sql, "t2.sku_type", permissionResp.getSkuTypeList());
        } else {
            appendInCondition(sql, "t1.belong_to_org_id", permissionResp.getDeptIdList());
        }
    }

    /**
     * @param business:
     * @param sql:
     * @return void
     * <AUTHOR>
     * @description 资产选择的sql构造 （一般用于临床需要选择资产做业务）
     * @date 2025/5/9 15:46
     */
    private void appendSql4DeviceSelect(String business, StringBuffer sql) {
        PermissionResp permissionResp = cgetPermission(business);
        if (PermissionConst.TYPE_ALL.equals(permissionResp.getType())) {
            // 全部权限，无需追加条件
            return;
        }
        // 由于资产都是出入库的时候创建的，所以不存在self权限
        if (PermissionConst.TYPE_SKU_TYPE.equals(permissionResp.getType())) {
            // t2.sku_type 在 getSkuTypeList 或者  "t1.belong_to_org_id", permissionResp.getDeptIdList()
            StringBuffer sqlTemp1 = new StringBuffer();
            appendInCondition(sqlTemp1, "t2.sku_type", permissionResp.getSkuTypeList());

            StringBuffer sqlTemp2 = new StringBuffer();
            appendInCondition(sqlTemp2, "t1.belong_to_org_id", permissionResp.getDeptIdList());

            // 替换sqlTemp1 的 and 为 空
            // 替换sqlTemp2 的 and 为 or
            String sqlTemp1Str = sqlTemp1.toString().replaceFirst(" and ", "");
            String sqlTemp2Str = sqlTemp2.toString().replaceFirst(" and ", "");
            sql.append(" and (").append(sqlTemp1Str).append(" or ").append(sqlTemp2Str).append(")");

        } else {
            appendInCondition(sql, "t1.belong_to_org_id", permissionResp.getDeptIdList());
        }
    }
}
