package cn.trasen.ams.common.dao;

import cn.trasen.ams.common.model.*;
import cn.trasen.homs.bpm.model.WfTaskHis;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.form.model.DpTableField;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;


/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.dao
 * @className: AMSInstanceMapper
 * @author: chenbin
 * @description: TODO
 * @date: 2025/5/27 15:21
 * @version: 1.0
 */

public interface AMSInstanceMapper extends Mapper<AMSInstanceInfo> {
    List<AMSInstanceDefinitionInfo> getInstanceDefinitionListByIdList(@Param("idList") List<String> idList);

    List<AMSInstanceDefinitionInfoFieldSet> getInstanceDefinitionFieldSetListByTemplateId(@Param("id") String id);

    List<DpTableField> getSonFormField(@Param("id") String id);

    Integer getNumsByStatus(@Param("userCode") String userCode, @Param("definitionId") String definitionId, @Param("status") String status, @Param("deptCode") String deptCode);

    Integer getSonFormsWithStatus(@Param("definitionId") String definitionId, @Param("status") String status, @Param("tableName") String tablename, @Param("deptCode") String deptCode);

    List<AMSOrganization> getOrganizationListByOrgId(@Param("orgId") String orgId);

    List<AMSEmployee> getEmployeeListByOrgIdList(@Param("orgIdList") List<String> orgIdList);

    String getUserParttimeOrgId(@Param("employeeId") String employeeId);

    AMSInstanceDefinitionInfo selectInstanceDefinitionById(String definitionId);

    AMSDpFormTemplate selectDpFormTemplateById(String templateId);

    List<Map<String, Object>> getInstanceList(Page page, @Param("sql") String sql);

    String getSonFormTableName(@Param("tableId") String tableId);

    List<Map<String, String>> getTaskHisListByInstanceId(@Param("instanceId") String instanceId);

}
