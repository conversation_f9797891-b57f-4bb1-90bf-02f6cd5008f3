package cn.trasen.ams.common.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.device.constant.SkuConst;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "c_warehouse")
@Setter
@Getter
public class Warehouse {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 仓库代码
     */
    @Column(name = "wh_code")
    @ApiModelProperty(value = "仓库代码")
    private String whCode;

    /**
     * 所属系统
     */
    @Column(name = "sys_type")
    @ApiModelProperty(value = "所属系统")
    private String sysType;

    /**
     * 仓库名称
     */
    @NotNull(message = "仓库名称不能为空")
    @ApiModelProperty(value = "仓库名称")
    private String name;

    /**
     * 库房级别
     */
    @DictExistValid(code = "AMS_WAREHOUSE_LEVEL", message = "库房级别不合法")
    @NotNull(message = "库房级别不能为空")
    @ApiModelProperty(value = "库房级别")
    private String level;

    @Transient
    @ApiModelProperty(value = "库房级别翻译")
    private String levelShow;

    /**
     * 是否开启货位管理
     */
    @ConstValid(constant = {CommonConst.YES, CommonConst.NO}, message = "是否开启货位管理值不合法")
    @Column(name = "is_loc")
    @ApiModelProperty(value = "是否开启货位管理")
    private String isLoc;

    @Transient
    @ApiModelProperty(value = "是否开启货位管理翻译")
    private String isLocShow;

    /**
     * 首拼
     */
    @ApiModelProperty(value = "首拼")
    private String sp;

    /**
     * 全拼
     */
    @ApiModelProperty(value = "全拼")
    private String qp;


    @ApiModelProperty(value = "出库方式")
    @Column(name = "stk_mtd")
    private String stkMtd;


    @ApiModelProperty(value = "盘点方式")
    @Column(name = "inv_mtd")
    private String invMtd;

    /**
     * 是否启用
     */
    @ConstValid(constant = {CommonConst.YES, CommonConst.NO}, message = "是否启用值不合法")
    @ApiModelProperty(value = "是否启用")
    private String status;

    @Transient
    @ApiModelProperty(value = "是否启用翻译")
    private String statusShow;

    /**
     * 排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序")
    private Integer seqNo;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}