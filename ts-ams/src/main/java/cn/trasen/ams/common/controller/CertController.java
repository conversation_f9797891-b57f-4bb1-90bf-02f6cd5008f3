package cn.trasen.ams.common.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Cert;
import cn.trasen.ams.common.service.CertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CertController
 * @Description TODO
 * @date 2025年7月25日 下午5:20:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CertController")
public class CertController {

	private transient static final Logger logger = LoggerFactory.getLogger(CertController.class);

	@Autowired
	private CertService certService;

	/**
	 * @Title saveCert
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月25日 下午5:20:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/common/cert/save")
	public PlatformResult<String> saveCert(@RequestBody Cert record) {
		try {
			certService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCert
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月25日 下午5:20:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/common/cert/update")
	public PlatformResult<String> updateCert(@RequestBody Cert record) {
		try {
			certService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCertById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<Cert>
	 * @date 2025年7月25日 下午5:20:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/common/cert/{id}")
	public PlatformResult<Cert> selectCertById(@PathVariable String id) {
		try {
			Cert record = certService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCertById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月25日 下午5:20:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/common/cert/delete/{id}")
	public PlatformResult<String> deleteCertById(@PathVariable String id) {
		try {
			certService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCertList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Cert>
	 * @date 2025年7月25日 下午5:20:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/common/cert/list")
	public DataSet<Cert> selectCertList(Page page, Cert record) {
		return certService.getDataSetList(page, record);
	}
}
