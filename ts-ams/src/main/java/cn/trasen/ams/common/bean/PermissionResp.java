package cn.trasen.ams.common.bean;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.bean
 * @className: PermessionResp
 * @author: chenbin
 * @description: 用户数据权限
 * @date: 2025/4/24 09:01
 * @version: 1.0
 */

@Data
public class PermissionResp {

    // 权限类型 all | sku_type | dept | self
    private String type;

    // 业务所对应的 skuType 列表
    private Map<String, List<String>> businessMap;


    private List<String> authList;

    private List<String> roleList;

    private List<String> skuTypeList;
    
    // 所有能查看的科室列表
    private List<String> deptIdList;

    // 用户id
    private String userId;


}
