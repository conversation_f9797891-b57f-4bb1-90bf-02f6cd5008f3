package cn.trasen.ams.common.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.bean
 * @className: inportErrRow
 * @author: chenbin
 * @description: 导入错误行
 * @date: 2024/9/10 10:39
 * @version: 1.0
 */

@Data
public class ImportErrRow {

    @ApiModelProperty(value = "错误行下标")
    private int index;

    @ApiModelProperty(value = "错误行数据")
    private String columnName;

    @ApiModelProperty(value = "错误行数据")
    private String columnValue;

    @ApiModelProperty(value = "错误信息")
    private String errMsg;
    
}
