<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.ManufacturerMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.common.model.Manufacturer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sord" jdbcType="VARCHAR" property="sord" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <insert id="batchInsert">
    <![CDATA[
			INSERT INTO c_manufacturer
			(
				id,
				name,
			    sp,
			    qp,
				sord,
				create_date,
				create_user,
				create_user_name,
				update_date,
				update_user,
				update_user_name,
			    sso_org_code,
			    sso_org_name,
				is_deleted,
                dept_id,
                dept_name
			)
			VALUES
		]]>
      <foreach collection="list" item="item" index="index" separator=",">
          <![CDATA[
			(
				#{item.id},
				#{item.name},
			    #{item.sp},
			    #{item.qp},
				#{item.sord},
				#{item.createDate},
				#{item.createUser},
				#{item.createUserName},
				#{item.updateDate},
				#{item.createUser},
				#{item.updateUser},
			    #{item.ssoOrgCode},
				#{item.ssoOrgName},
				#{item.isDeleted},
				#{item.deptId},
				#{item.deptName}
			)
			]]>
      </foreach>
  </insert>
</mapper>