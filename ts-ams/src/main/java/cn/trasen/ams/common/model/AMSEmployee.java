package cn.trasen.ams.common.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.model
 * @className: User
 * @author: chenbin
 * @description: TODO
 * @date: 2024/1/29 17:52
 * @version: 1.0
 */
@Table(name = "cust_emp_base")
@Setter
@Getter
public class AMSEmployee {

    @Id
    @Column(name = "employee_id")
    @ApiModelProperty(value = "ID")
    private String employeeId;


    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;


    @Column(name = "employee_name")

    @ApiModelProperty(value = "名称")
    private String employeeName;

    @Column(name = "org_id")
    @ApiModelProperty(value = "组织ID")
    private String orgId;

}
