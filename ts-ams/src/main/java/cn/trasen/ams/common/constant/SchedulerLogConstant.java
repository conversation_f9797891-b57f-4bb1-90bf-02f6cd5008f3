package cn.trasen.ams.common.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.constant
 * @className: SchedulerLogConstant
 * @author: chenbin
 * @description: 定时器日志常量
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
public class SchedulerLogConstant {

    /**
     * 执行状态常量
     */
    public static final String STATUS_RUNNING = "RUNNING";
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_TIMEOUT = "TIMEOUT";

    /**
     * 默认超时时间（毫秒）
     */
    public static final long DEFAULT_TIMEOUT = 30000L;

    /**
     * 默认分组
     */
    public static final String DEFAULT_GROUP = "DEFAULT";

    /**
     * 系统分组
     */
    public static final String GROUP_SYSTEM = "系统维护";
    public static final String GROUP_DATA_SYNC = "数据同步";
    public static final String GROUP_MONITOR = "系统监控";
    public static final String GROUP_REALTIME = "实时监控";
    public static final String GROUP_BACKUP = "数据备份";
    public static final String GROUP_CLEANUP = "数据清理";

    /**
     * 常见定时器名称
     */
    public static final String SCHEDULER_DATA_SYNC = "数据同步定时器";
    public static final String SCHEDULER_STATUS_CHECK = "状态检查定时器";
    public static final String SCHEDULER_CLEANUP = "清理任务定时器";
    public static final String SCHEDULER_BACKUP = "数据备份定时器";
    public static final String SCHEDULER_HEALTH_CHECK = "健康检查定时器";
    public static final String SCHEDULER_LOG_CLEANUP = "日志清理定时器";
} 