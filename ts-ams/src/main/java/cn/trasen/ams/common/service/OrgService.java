package cn.trasen.ams.common.service;

import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.bean.base.OrganizationListSimpleRes;

import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service
 * @className: OrgService
 * @author: chenbin
 * @description: TODO
 * @date: 2024/9/12 15:14
 * @version: 1.0
 */

public interface OrgService {

    List<OrganizationListSimpleRes> getOrgListSimple();

    List<HrmsOrganizationResp> getOrgList();

    List<HrmsOrganizationResp> cgetOrgList();

    Map<String, String> getOrgMap();

    Map<String, String> cgetOrgMap();

    String cgetOrgNameById(String orgId);
}
