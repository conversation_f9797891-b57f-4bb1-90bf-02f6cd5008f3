package cn.trasen.ams.common.service.impl;

import java.util.*;

import cn.trasen.ams.common.bean.ManufacturerInsertReq;
import cn.trasen.ams.common.constant.CertConst;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Brand;
import cn.trasen.ams.common.model.Cert;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.common.service.BrandService;
import cn.trasen.ams.common.service.CertService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.ManufacturerMapper;
import cn.trasen.ams.common.model.Manufacturer;
import cn.trasen.ams.common.service.ManufacturerService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ManufacturerServiceImpl
 * @Description 厂家业务实现
 * @date 2024年9月2日 下午4:51:25
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ManufacturerServiceImpl implements ManufacturerService {

    @Autowired
    private ManufacturerMapper mapper;

    @Autowired
    private CertService certService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public String save(Manufacturer record) {

        checkNameRepeat(record.getSysType(), record.getName(), "add");
        autoFillColumn(record);
        String id = IdGeneraterUtils.nextId();
        record.setId(id);
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        mapper.insertSelective(record);

        return id;
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Manufacturer record) {
        Assert.hasText(record.getId(), "ID不能为空.");
        checkNameRepeat(record.getSysType(), record.getName(), "update");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        autoFillColumn(record);
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void insert(ManufacturerInsertReq record) {
        // 保存厂家
        Manufacturer manufacturer = record.getManufacturer();
        save(manufacturer);

        // 处理证件信息
        processCertificates(manufacturer, record.getCertList());

        // 处理品牌信息
        processBrands(manufacturer, record.getBrandList());
    }

    @Transactional(readOnly = false)
    @Override
    public void edit(ManufacturerInsertReq record) {
        // 更新厂家
        Manufacturer manufacturer = record.getManufacturer();
        update(manufacturer);

        // 处理证件信息
        processCertificates(manufacturer, record.getCertList());

        // 处理品牌信息
        processBrands(manufacturer, record.getBrandList());
    }

    /**
     * 处理厂家证件信息
     *
     * @param manufacturer 厂家对象
     * @param certList     证件列表
     */
    private void processCertificates(Manufacturer manufacturer, List<Cert> certList) {
        // 先清空之前的证件数据
        certService.deleteByModelId(manufacturer.getId());

        // 如果没有提交新的证件信息，直接返回
        if (CollectionUtils.isEmpty(certList)) {
            return;
        }

        // 设置证件的必要属性
        certList.forEach(cert -> {
            cert.setId(IdGeneraterUtils.nextId()); // 防止逻辑删除造成的key 重复
            cert.setSysType(CommonConst.SYS_TYPE_WZ);
            cert.setModelId(manufacturer.getId());
            cert.setModelType(CertConst.MODEL_TYPE_MANUFACTURER);
        });

        // 批量写入新的证件数据
        certService.batchInsert(certList);
    }

    /**
     * 处理厂家品牌信息
     *
     * @param manufacturer 厂家对象
     * @param brandList    品牌列表
     */
    private void processBrands(Manufacturer manufacturer, List<Brand> brandList) {
        // 先清空之前的品牌数据
        brandService.deleteByManufacturerId(manufacturer.getId());

        // 如果没有提交新的品牌信息，直接返回
        if (CollectionUtils.isEmpty(brandList)) {
            return;
        }

        // 设置品牌的必要属性
        brandList.forEach(brand -> {
            brand.setId(IdGeneraterUtils.nextId()); // 防止逻辑删除造成的key 重复
            brand.setSysType(CommonConst.SYS_TYPE_WZ);
            brand.setManufacturerId(manufacturer.getId());
        });

        // 批量写入新的品牌数据
        brandService.batchInsert(brandList);
    }

    private void autoFillColumn(Manufacturer record) {
        if (record.getId() == null) {
            record.setStatus(CommonConst.YES);
        }
        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {

        Assert.hasText(id, "ID不能为空.");

        Manufacturer record = new Manufacturer();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Manufacturer selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Manufacturer> getDataSetList(Page page, Manufacturer record) {
        Example example = new Example(Manufacturer.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        example.setOrderByClause("sord asc , create_date desc");

        if (!StringUtil.isEmpty(record.getName())) {
            // 创建 OR 条件
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition).orLike("sp", likeCondition).orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }

        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        List<Manufacturer> records = mapper.selectByExampleAndRowBounds(example, page);
        // dataFmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param record:
     * @return List<Manufacturer>
     * <AUTHOR>
     * @description 查询列表不分页，支持导出或者select数据需要
     * @date 2024/9/11 08:53
     */

    @Override
    public List<Manufacturer> getList(Manufacturer record) {
        Example example = new Example(Manufacturer.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        example.setOrderByClause("sord asc , create_date desc");

        if (!StringUtil.isEmpty(record.getName())) {
            // 创建 OR 条件
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition).orLike("sp", likeCondition).orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }

        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        List<Manufacturer> records = mapper.selectByExample(example);
        // dataFmt
        records.forEach(this::dataFmt);

        return records;
    }

    /**
     * @param list:
     * @return Integer
     * <AUTHOR>
     * @description 批量导入数据
     * @date 2024/9/11 08:53
     */

    @Transactional(readOnly = false)
    @Override
    public Integer importByExcel(List<Manufacturer> list, String sysType) {

        if (null == list || list.size() <= 0) {
            return 0;
        }
        // 这里是批量提交，先要对list中 Manufacturer 重复的name 进行过滤
        Set<String> set = fetchAllManufacturerName(sysType);

        List<Manufacturer> cleanList = new ArrayList<>();
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        Set<String> selfSet = new HashSet<>();

        for (Manufacturer manufacturer : list) {
            if (set.contains(manufacturer.getName())) {
                continue;
            }

            if (selfSet.contains(manufacturer.getName())) {
                continue;
            }

            selfSet.add(manufacturer.getName());

            autoFillColumn(manufacturer);

            manufacturer.setId(IdGeneraterUtils.nextId());
            manufacturer.setCreateDate(new Date());
            manufacturer.setUpdateDate(new Date());
            manufacturer.setIsDeleted("N");
            manufacturer.setSysType(sysType);
            if (user != null) {
                manufacturer.setCreateUser(user.getUsercode());
                manufacturer.setCreateUserName(user.getUsername());
                manufacturer.setUpdateUser(user.getUsercode());
                manufacturer.setUpdateUserName(user.getUsername());
                manufacturer.setSsoOrgCode(user.getCorpcode());
                manufacturer.setSsoOrgName(user.getOrgName());
                manufacturer.setDeptId(user.getDeptId());
                manufacturer.setDeptName(user.getDeptname());
            }
            cleanList.add(manufacturer);
        }

        // 批量插入
        int size = cleanList.size();
        int batchSize = 1000;

        for (int i = 0; i < size; i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, size);
            List<Manufacturer> subList = cleanList.subList(i, end);
            mapper.batchInsert(subList);
        }

        return size;
    }

    /**
     * @param :
     * @return Set
     * <AUTHOR>
     * @description 获取所有厂商名称，用于导入时校验
     * @date 2024/9/11 08:54
     */

    public Set fetchAllManufacturerName(String sysType) {
        Example example = new Example(Manufacturer.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("sysType", sysType);
        List<Manufacturer> list = mapper.selectByExample(example);
        Set<String> set = new HashSet<>();
        for (Manufacturer manufacturer : list) {
            set.add(manufacturer.getName());
        }
        return set;
    }

    /**
     * @param :
     * @return Map
     * <AUTHOR>
     * @description 获取所有的厂商名称，主要用来逻辑过滤用，与反向查ID
     * @date 2024/9/11 08:54
     */

    public Map fetchAllManufacturerName2Map(String sysType) {
        Example example = new Example(Manufacturer.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("sysType", sysType);

        List<Manufacturer> list = mapper.selectByExample(example);
        Map<String, String> map = new HashMap<>();
        for (Manufacturer manufacturer : list) {
            map.put(manufacturer.getName(), manufacturer.getId());
        }
        return map;
    }

    /**
     * @param name:
     * @param type:
     * @return void
     * <AUTHOR>
     * @description 校验厂商名称是否重复
     * @date 2024/9/11 08:54
     */

    private void checkNameRepeat(String sysType, String name, String type) {
        Example example = new Example(Manufacturer.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("name", name);
        criteria.andEqualTo("sysType", sysType);

        int count = mapper.selectCountByExample(example);

        if (count > 0 && "add".equals(type)) {
            throw new RuntimeException("厂商名称重复.");
        }

        if (count > 1 && "update".equals(type)) {
            throw new RuntimeException("厂商名称重复.");
        }
    }

    public void dataFmt(Manufacturer record) {
        record.setEnterpriseTypeShow(dictService.cgetNameByValue(CommonConst.ENTERPRISE_TYPE, record.getEnterpriseType()));
        record.setStatusShow(dictService.cgetNameByValue(CommonConst.IS_ENABLE, record.getStatus()));
    }
}
