package cn.trasen.ams.common.validator.pk;

import cn.trasen.ams.common.service.TableService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @projectName: xtbg
 * @package: cn.trasen.hrms.performance.validator.pk
 * @className: PkNoneValidator
 * @author: chenbin
 * @description: 用于需要关联的索引数据不存在的时候 （范围稍微窄一点点，比如某个表一对一，类似于外键设计）
 * @date: 2023/12/7 08:56
 * @version: 1.0
 */

public class PkNoneValidator implements ConstraintValidator<PkNoneValid, String> {


    private String table;

    private String pk;
    private String ignoreIsDelete;

    @Autowired
    private TableService tableService;


    @Override
    public void initialize(PkNoneValid constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        table = constraintAnnotation.table();
        pk = constraintAnnotation.pk();
        ignoreIsDelete = constraintAnnotation.ignoreIsDelete();
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(s)) {
            // 值的必填判断 不走这里 如果没值 则不验证
            return true;
        }
        return !tableService.has(table, pk, s, ignoreIsDelete);
    }
}
