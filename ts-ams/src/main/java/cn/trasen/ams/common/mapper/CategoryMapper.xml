<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.CategoryMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.common.model.Category">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="tree_ids" jdbcType="VARCHAR" property="treeIds"/>
        <result column="is_enable" jdbcType="CHAR" property="isEnable"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="devices" jdbcType="INTEGER" property="devices"/>
        <result column="seq_no" jdbcType="INTEGER" property="seqNo"/>
        <result column="is_leaf_node" jdbcType="CHAR" property="isLeafNode"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted"/>
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
    </resultMap>
    <update id="changeSonNode">
        update c_category
        set tree_ids = REPLACE(tree_ids, #{oldTreeIds}, #{newTreeIds}),
            level    = level + #{levelChange}
        where tree_ids like CONCAT(#{parentId}, ',%')
    </update>
</mapper>