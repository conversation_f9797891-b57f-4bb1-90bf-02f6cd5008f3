package cn.trasen.ams.common.validator.dict;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD ,ElementType.PARAMETER,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DictExistValidator.class)
@Documented

public @interface DictExistValid {
    String message() default "字典不合法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String code();
}
