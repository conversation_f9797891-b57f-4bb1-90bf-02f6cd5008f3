package cn.trasen.ams.common.scheduler;

import cn.trasen.ams.common.annotation.sch.SchedulerLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.scheduler
 * @className: ExampleScheduler
 * @author: chenbin
 * @description: 示例定时器类
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
@Slf4j
//@Component
public class ExampleScheduler {

    /**
     * 示例定时器1 - 数据同步
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    @SchedulerLog(
        schedulerName = "数据同步定时器",
        schedulerGroup = "数据同步",
        timeoutThreshold = 60000L
    )
    public void dataSyncJob() {
        log.info("开始执行数据同步定时器");
        try {
            // 模拟业务逻辑
            Thread.sleep(2000);
            log.info("数据同步定时器执行完成");
        } catch (InterruptedException e) {
            log.error("数据同步定时器执行异常", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 示例定时器2 - 状态检查
     */
    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟执行一次
    @SchedulerLog(
        schedulerName = "状态检查定时器",
        schedulerGroup = "系统监控",
        logInput = false,
        logOutput = false
    )
    public void statusCheckJob() {
        log.info("开始执行状态检查定时器");
        try {
            // 模拟业务逻辑
            Thread.sleep(1000);
            log.info("状态检查定时器执行完成");
        } catch (InterruptedException e) {
            log.error("状态检查定时器执行异常", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 示例定时器3 - 清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @SchedulerLog(
        schedulerName = "清理任务定时器",
        schedulerGroup = "系统维护",
        timeoutThreshold = 300000L // 5分钟超时
    )
    public void cleanupJob() {
        log.info("开始执行清理任务定时器");
        try {
            // 模拟长时间运行的业务逻辑
            Thread.sleep(5000);
            log.info("清理任务定时器执行完成");
        } catch (InterruptedException e) {
            log.error("清理任务定时器执行异常", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 示例定时器4 - 快速任务
     */
    @Scheduled(cron = "0 */1 * * * ?") // 每分钟执行一次
    @SchedulerLog(
        schedulerName = "快速任务定时器",
        schedulerGroup = "实时监控",
        logMetrics = false // 不记录系统指标
    )
    public void quickJob() {
        log.info("开始执行快速任务定时器 - {}", new Date());
        // 快速执行，无需等待
    }
} 