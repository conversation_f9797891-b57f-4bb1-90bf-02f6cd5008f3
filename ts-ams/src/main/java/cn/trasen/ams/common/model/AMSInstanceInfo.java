package cn.trasen.ams.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.model
 * @className: AMSInstanceInfo
 * @author: chenbin
 * @description: TODO
 * @date: 2025/5/27 15:24
 * @version: 1.0
 */

public class AMSInstanceInfo {
    /**
     * 流程实例ID
     */
    @Id
    @Column(name = "wf_instance_id")
    @ApiModelProperty(value = "流程实例ID")
    private String wfInstanceId;

    /**
     * 流程定义ID
     */
    @Column(name = "wf_definition_id")
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer version;

    /**
     * 流程编号
     */
    @Column(name = "workflow_no")
    @ApiModelProperty(value = "流程编号")
    private String workflowNo;

    /**
     * 流程名称
     */
    @Column(name = "workflow_name")
    @ApiModelProperty(value = "流程名称")
    private String workflowName;

    /**
     * 业务ID
     */
    @Column(name = "business_id")
    @ApiModelProperty(value = "业务ID")
    private String businessId;

    /**
     * 当前节点
     */
    @Column(name = "current_step_no")
    @ApiModelProperty(value = "当前节点")
    private String currentStepNo;

    /**
     * 当前节点名称
     */
    @Column(name = "current_step_name")
    @ApiModelProperty(value = "当前节点名称")
    private String currentStepName;

    /**
     * 处理人编号
     */
    @Column(name = "current_assignee_no")
    @ApiModelProperty(value = "处理人编号")
    private String currentAssigneeNo;

    /**
     * 当前节点处理人名称
     */
    @Column(name = "current_assignee_name")
    @ApiModelProperty(value = "当前节点处理人名称")
    private String currentAssigneeName;

    /**
     * 流程完成时间
     */
    @Column(name = "wf_finished_date")
    @ApiModelProperty(value = "流程完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wfFinishedDate;

    /**
     * 父流程ID
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父流程ID")
    private String parentId;

    /**
     * 发起人机构代码
     */
    @Column(name = "launch_dept_code")
    @ApiModelProperty(value = "发起人机构代码")
    private String launchDeptCode;

    /**
     * 发起人机构名称
     */
    @Column(name = "launch_dept_name")
    @ApiModelProperty(value = "发起人机构名称")
    private String launchDeptName;

    /**
     * 发起人所属公司
     */
    @Column(name = "launch_company_code")
    @ApiModelProperty(value = "发起人所属公司")
    private String launchCompanyCode;

    /**
     * 发起人所属公司名称
     */
    @Column(name = "launch_company_name")
    @ApiModelProperty(value = "发起人所属公司名称")
    private String launchCompanyName;

    /**
     * 流程摘要
     */
    @ApiModelProperty(value = "流程摘要")
    private String summary;

    /**
     * 流程状态(0:草稿;1:运行中;2:已完成;3:强制结束;4-撤销)
     */
    @ApiModelProperty(value = "流程状态(0:草稿;1:运行中;2:已完成;3:强制结束;4-撤销)")
    private Integer status;

    /**
     * 流程序号
     */
    @Column(name = "workflow_number")
    @ApiModelProperty(value = "流程序号")
    private String workflowNumber;

    /**
     * 流程标题
     */
    @Column(name = "workflow_title")
    @ApiModelProperty(value = "流程标题")
    private String workflowTitle;

    /**
     * 办理日期限
     */
    @Column(name = "handle_allotted_time")
    @ApiModelProperty(value = "办理日期限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date handleAllottedTime;

    /**
     * 紧急级别 1-一般；2-加急；3-急件；4-特急
     */
    @Column(name = "urgency_level")
    @ApiModelProperty(value = "紧急级别 1-一般；2-加急；3-急件；4-特急")
    private Integer urgencyLevel;

    /**
     * 办理提示语
     */
    @Column(name = "handle_marked_words")
    @ApiModelProperty(value = "办理提示语")
    private String handleMarkedWords;

    /**
     * 是否催办 1-是；2-否
     */
    @Column(name = "is_press")
    @ApiModelProperty(value = "是否催办 1-是；2-否")
    private Integer isPress;

    @Column(name = "form_version")
    @ApiModelProperty(value = "表单版本")
    private Integer formVersion;
}
