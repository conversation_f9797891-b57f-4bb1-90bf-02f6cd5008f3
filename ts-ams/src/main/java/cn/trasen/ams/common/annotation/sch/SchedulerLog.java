package cn.trasen.ams.common.annotation.sch;

import java.lang.annotation.*;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.annotation
 * @className: SchedulerLog
 * @author: chenbin
 * @description: 定时器执行日志注解
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SchedulerLog {
    
    /**
     * 定时器名称
     */
    String schedulerName() default "";
    
    /**
     * 定时器分组
     */
    String schedulerGroup() default "";
    
    /**
     * 是否记录输入参数
     */
    boolean logInput() default true;
    
    /**
     * 是否记录输出结果
     */
    boolean logOutput() default true;
    
    /**
     * 是否记录系统指标（内存、CPU）
     */
    boolean logMetrics() default true;
    
    /**
     * 超时阈值（毫秒），超过此时间记录为超时
     */
    long timeoutThreshold() default 30000L;
} 