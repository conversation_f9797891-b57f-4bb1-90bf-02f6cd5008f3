<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.AMSInstanceMapper">
    <select id="getInstanceDefinitionListByIdList" resultType="cn.trasen.ams.common.model.AMSInstanceDefinitionInfo">
        SELECT
        *
        FROM ts_base_oa.wf_definition_info
        WHERE WF_DEFINITION_ID IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getInstanceDefinitionFieldSetListByTemplateId"
            resultType="cn.trasen.ams.common.model.AMSInstanceDefinitionInfoFieldSet">
        select *
        from ts_base_oa.toa_field_set
        where template_id = #{id}
    </select>
    <select id="getSonFormField" resultType="cn.trasen.homs.form.model.DpTableField">
        select *
        from ts_base_oa.dp_table_field
        where table_id = #{id} order by seq
    </select>
    <sql id="joinClassify">
        LEFT join ts_base_oa.wf_definition_info wdi on t1.`WF_DEFINITION_ID` = wdi.`WF_DEFINITION_ID`
        LEFT join ts_base_oa.wf_form_classify_info wfci on wdi.`WORKFLOW_CLASSIFY` = wfci.`wf_form_classify_id`
    </sql>
    <sql id="joinDefinitionWhere">
        and wdi.`WF_DEFINITION_ID` =
        #{definitionId}
    </sql>
    <select id="getNumsByStatus" resultType="java.lang.Integer">
        <choose>
            <when test="status == '10000'">
                select count(*) from ts_base_oa.wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`CREATE_USER` = #{userCode}
                and t1.`status` in ('2','3','4','5','6')
                <include refid="joinDefinitionWhere"/>
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10001'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                left join ts_base_oa.wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10002'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                left join ts_base_oa.wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10003'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` = '0'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10004'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                left join ts_base_oa.wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1' and t1.`CURRENT_STEP_NAME` != '重新提交'
                <include refid="joinDefinitionWhere"/>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
            </when>
            <when test="status == '10005'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                left join ts_base_oa.wf_task_his t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` in ('1','2','5')
                <include refid="joinDefinitionWhere"/>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
            </when>
            <when test="status == '10006'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from ts_base_oa.wf_instance_info t1
                left join ts_base_oa.wf_copy_user t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t2.`COPYTO_USER_CODE` = #{userCode}
                <include refid="joinDefinitionWhere"/>
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10007'">
                select count(*) from ts_base_oa.wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <if test="deptCode != null">
                    and t1.`LAUNCH_DEPT_CODE` in (${deptCode})
                </if>
                <include refid="joinDefinitionWhere"/>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10008'">
                select count(*) from ts_base_oa.wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` in ('2','3','4','5','6')
                <if test="deptCode != null">
                    and t1.`LAUNCH_DEPT_CODE` in (${deptCode})
                </if>
                <include refid="joinDefinitionWhere"/>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                and t1.`is_deleted` = 'N'
            </when>
        </choose>
    </select>
    <select id="getSonFormsWithStatus" resultType="java.lang.Integer">
        <choose>
            <when test="status == '10009'">
                select count(*) from ts_base_oa.wf_instance_info wii left join ts_base_oa.${tableName} zdyt ON
                zdyt.`WORKFLOW_ID` = wii.`WF_INSTANCE_ID`
                where wii.`status` in ('2','3','4','5','6')
                and wii.`WF_DEFINITION_ID` = #{definitionId}
                and wii.`is_deleted` = 'N'
                <if test="deptCode != null">
                    and t1.`LAUNCH_DEPT_CODE` in (${deptCode})
                </if>
            </when>
        </choose>

    </select>
    <select id="getOrganizationListByOrgId" resultType="cn.trasen.ams.common.model.AMSOrganization">
        SELECT *
        FROM ts_base_oa.comm_organization
        WHERE tree_ids LIKE CONCAT('%', #{orgId}, '%')
    </select>
    <select id="getEmployeeListByOrgIdList" resultType="cn.trasen.ams.common.model.AMSEmployee">
        SELECT *
        FROM ts_base_oa.cust_emp_base
        WHERE org_id IN
        <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </select>
    <select id="getUserParttimeOrgId" resultType="java.lang.String">
        SELECT org_id
        FROM ts_base_oa.comm_organization_parttime
        WHERE employee_id = #{employeeId}
          and is_default = '1'
          and is_deleted = 'N' limit 1
    </select>
    <select id="selectInstanceDefinitionById" resultType="cn.trasen.ams.common.model.AMSInstanceDefinitionInfo">
        SELECT *
        FROM ts_base_oa.wf_definition_info
        WHERE WF_DEFINITION_ID = #{id}
    </select>
    <select id="selectDpFormTemplateById" resultType="cn.trasen.ams.common.model.AMSDpFormTemplate">
        SELECT *
        FROM ts_base_oa.DP_FORM_TEMPLATE
        WHERE id = #{id}
    </select>
    <select id="getInstanceList" resultType="java.util.Map" parameterType="java.lang.String">
        ${sql}
    </select>
    <select id="getSonFormTableName" parameterType="String" resultType="String">
        SELECT TABLE_NAME
        FROM ts_base_oa.dp_table
        WHERE ID = #{tableId}
    </select>
    <select id="getTaskHisListByInstanceId" resultType="Map" parameterType="String">
        select remark,approval_filed,DATE_FORMAT(FINISHED_DATE,'%Y-%m-%d %H:%i') as
            finished_date,act_assignee_name from ts_base_oa.wf_task_his
        where wf_instance_id = #{instanceId} and is_deleted = 'N' and remark is not null and approval_filed is not
            null
        order by approval_filed

    </select>
</mapper>