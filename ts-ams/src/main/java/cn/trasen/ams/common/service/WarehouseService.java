package cn.trasen.ams.common.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Warehouse;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WarehouseService
 * @Description TODO
 * @date 2025年7月18日 下午3:56:24
 */
public interface WarehouseService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    Integer save(Warehouse record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    Integer update(Warehouse record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Warehouse
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    Warehouse selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Warehouse>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    DataSet<Warehouse> getDataSetList(Page page, Warehouse record);

    List<Warehouse> getListNoPage(Warehouse record);

    Warehouse getZcsbDefWarehouse();

    void dataFmt(Warehouse record);

    DataSet<Warehouse> getDataSetByIdList(Page page, List<String> idList);


    void genDefData();
}
