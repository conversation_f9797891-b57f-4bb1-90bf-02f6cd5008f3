<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.common.dao.DataModifyLogMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.common.model.DataModifyLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="row_pk_flow_no" jdbcType="VARCHAR" property="rowPkFlowNo" />
    <result column="row_name" jdbcType="VARCHAR" property="rowName" />
    <result column="row_modify_type" jdbcType="CHAR" property="rowModifyType" />
    <result column="row_pk_value" jdbcType="VARCHAR" property="rowPkValue" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="row_json_old" jdbcType="LONGVARCHAR" property="rowJsonOld" />
    <result column="row_json_new" jdbcType="LONGVARCHAR" property="rowJsonNew" />
  </resultMap>
</mapper>