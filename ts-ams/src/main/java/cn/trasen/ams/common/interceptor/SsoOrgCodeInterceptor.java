package cn.trasen.ams.common.interceptor;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MySQL SQL拦截器 - 自动添加机构代码条件
 * 自动为SELECT语句添加sso_org_code条件，实现数据隔离
 *
 * <AUTHOR>
 * @date 2025/7/15 10:26
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class SsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(SsoOrgCodeInterceptor.class);

    // 正则表达式模式 - 优化版本
    private static final Pattern ORG_CODE_PATTERN = Pattern.compile(
            "\\b(\\w+\\.)?`?sso_org_code`?\\s*[=<>!]|\\b(\\w+\\.)?`?sso_org_code`?\\s+LIKE|\\b(\\w+\\.)?`?sso_org_code`?\\s+IS\\s+(?:NOT\\s+)?NULL",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern WHERE_PATTERN = Pattern.compile("\\bWHERE\\b", Pattern.CASE_INSENSITIVE);

    // 新增：更精确的FROM子句模式
    private static final Pattern FROM_PATTERN = Pattern.compile(
            "\\bFROM\\s+([\\w\\.`]+)(?:\\s+(?:AS\\s+)?([\\w]+))?\\s+",
            Pattern.CASE_INSENSITIVE
    );

    // SQL关键字
    private static final String[] SQL_KEYWORDS = {
            " WHERE ", " GROUP BY ", " ORDER BY ", " HAVING ", " LIMIT ", " UNION "
    };

    // 需要排除的表
    private Set<String> excludeTables = new HashSet<>(Arrays.asList("d_category22"));

    // 需要添加sso_org_code条件的表前缀
    private Set<String> includeTablePrefixes = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;

    // 常量
    private static final String WHERE_KEYWORD = "WHERE";
    private static final String FROM_KEYWORD = "FROM";
    private static final String SELECT_KEYWORD = "SELECT";
    private static final String AND_KEYWORD = "AND";
    private static final String AS_KEYWORD = "AS";
    private static final String ORG_CODE_FIELD = "sso_org_code";

    // MySQL 关键字集合
    private static final Set<String> MYSQL_KEYWORDS = new HashSet<>(Arrays.asList(
            "SELECT", "FROM", "WHERE", "AND", "OR", "GROUP", "BY", "ORDER", "HAVING", "LIMIT", "AS",
            "INNER", "LEFT", "RIGHT", "OUTER", "JOIN", "ON", "UNION", "ALL", "DISTINCT", "TOP",
            "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP", "INDEX", "TABLE", "DATABASE",
            "SCHEMA", "VIEW", "PROCEDURE", "FUNCTION", "TRIGGER", "CASCADE", "RESTRICT", "SET",
            "VALUES", "INTO", "DEFAULT", "NULL", "NOT", "EXISTS", "BETWEEN", "LIKE", "IN", "IS"
    ));

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (enableLogging) {
            log.info("=== MySQL拦截器开始执行 ===");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            String modifiedSql = processSql(originalSql);

            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(boundSql, modifiedSql, statementHandler);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.info("=== MySQL拦截器执行完成，耗时: {}ms ===", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("MySQL拦截器执行异常", e);
            throw e;
        }
    }

    /**
     * 处理SQL语句
     */
    private String processSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            if (enableLogging) {
                log.debug("SQL为空，跳过处理");
            }
            return sql;
        }

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null) {
                if (enableLogging) {
                    log.warn("无法获取用户信息，跳过SQL修改");
                }
                return sql;
            }

            if (user.getCorpcode() == null || user.getCorpcode().trim().isEmpty()) {
                if (enableLogging) {
                    log.warn("用户机构代码为空，跳过SQL修改");
                }
                return sql;
            }

            SqlAnalysisResult analysis = analyzeSelectSql(sql);
            if (analysis == null) {
                if (enableLogging) {
                    log.debug("无法解析SELECT语句，跳过修改");
                }
                return sql;
            }

            if (!shouldAddOrgCodeCondition(analysis.getMainTable())) {
                if (enableLogging) {
                    log.debug("表 {} 不需要添加机构代码条件，跳过修改", analysis.getMainTable());
                }
                return sql;
            }

            if (hasOrgCodeInWhereClause(sql)) {
                if (enableLogging) {
                    log.debug("WHERE子句中已包含sso_org_code条件，跳过修改");
                }
                return sql;
            }

            String modifiedSql = addOrgCodeCondition(sql, analysis, user.getCorpcode());

            if (enableLogging && !sql.equals(modifiedSql)) {
                log.info("SQL修改完成 - 主表: {}, 机构代码: {}", analysis.getMainTable(), user.getCorpcode());
                log.debug("修改后SQL: {}", modifiedSql);
            }

            return modifiedSql;

        } catch (Exception e) {
            log.error("处理SQL时发生异常: {}", e.getMessage(), e);
            return sql;
        }
    }

    /**
     * 应用SQL修改 - 安全版本
     */
    private void applySqlModification(BoundSql boundSql, String modifiedSql, StatementHandler statementHandler) {
        try {
            // 使用反射安全地修改SQL
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);

            // 保存原始SQL以便恢复
            String originalSql = (String) sqlField.get(boundSql);

            try {
                sqlField.set(boundSql, modifiedSql);

                // 验证修改是否成功
                String currentSql = (String) sqlField.get(boundSql);
                if (modifiedSql.equals(currentSql)) {
                    log.info("SQL修改成功");
                } else {
                    log.warn("SQL修改验证失败，恢复原SQL");
                    sqlField.set(boundSql, originalSql);
                }

            } catch (Exception e) {
                log.error("修改SQL字段失败，恢复原SQL", e);
                sqlField.set(boundSql, originalSql);
            }

        } catch (NoSuchFieldException e) {
            log.warn("BoundSql类没有sql字段，尝试其他方式: {}", e.getMessage());
            tryAlternativeSqlModification(boundSql, modifiedSql, statementHandler);
        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
        }
    }

    /**
     * 尝试其他SQL修改方式
     */
    private void tryAlternativeSqlModification(BoundSql boundSql, String modifiedSql, StatementHandler statementHandler) {
        try {
            // 尝试通过MetaObject修改
            org.apache.ibatis.reflection.MetaObject metaObject = org.apache.ibatis.reflection.SystemMetaObject.forObject(boundSql);
            if (metaObject.hasGetter("sql")) {
                metaObject.setValue("sql", modifiedSql);
                log.info("通过MetaObject修改SQL成功");
                return;
            }

            log.error("所有SQL修改方法都失败了");

        } catch (Exception e) {
            log.error("尝试其他SQL修改方式失败", e);
        }
    }

    /**
     * SQL分析结果类
     */
    private static class SqlAnalysisResult {
        private final String mainTable;
        private final String tableAlias;

        public SqlAnalysisResult(String mainTable, String tableAlias) {
            this.mainTable = mainTable;
            this.tableAlias = tableAlias;
        }

        public String getMainTable() {
            return mainTable;
        }

        public String getTableAlias() {
            return tableAlias;
        }
    }

    /**
     * 分析SELECT语句
     */
    private SqlAnalysisResult analyzeSelectSql(String sql) {
        if (sql == null || sql.trim().isEmpty() || !isSelectStatement(sql)) {
            return null;
        }

        try {
            int fromIndex = findMainFromClause(sql);
            if (fromIndex == -1) {
                return null;
            }

            String fromClause = extractFromClause(sql, fromIndex);
            if (fromClause == null) {
                return null;
            }

            // 使用改进的FROM子句解析
            String[] tableInfo = parseTableInfo(fromClause);
            if (tableInfo == null) {
                return null;
            }

            String mainTable = tableInfo[0];
            String tableAlias = tableInfo[1];

            if (enableLogging) {
                log.debug("SELECT语句分析 - 主表: {}, 别名: {}", mainTable, tableAlias);
            }

            return new SqlAnalysisResult(mainTable, tableAlias);

        } catch (Exception e) {
            log.warn("解析SELECT语句失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断是否为SELECT语句
     */
    private boolean isSelectStatement(String sql) {
        if (sql == null) return false;

        String trimmedSql = sql.trim();
        if (trimmedSql.isEmpty()) return false;

        // 忽略注释和空白
        String cleanSql = removeSqlComments(trimmedSql).trim();
        return cleanSql.toUpperCase().startsWith(SELECT_KEYWORD);
    }

    /**
     * 移除SQL注释
     */
    private String removeSqlComments(String sql) {
        // 移除单行注释
        String noSingleLineComments = sql.replaceAll("--.*?\\n", " ");
        // 移除多行注释
        return noSingleLineComments.replaceAll("/\\*.*?\\*/", " ");
    }

    /**
     * 提取FROM子句
     */
    private String extractFromClause(String sql, int fromIndex) {
        String afterFrom = sql.substring(fromIndex + FROM_KEYWORD.length()).trim();

        // 找到FROM子句的结束位置
        int endIndex = findClauseEnd(afterFrom, 0);
        if (endIndex == -1) {
            return afterFrom;
        }

        return afterFrom.substring(0, endIndex).trim();
    }

    /**
     * 解析表信息
     */
    private String[] parseTableInfo(String fromClause) {
        if (fromClause == null || fromClause.isEmpty()) {
            return null;
        }

        String[] parts = fromClause.split("\\s+");
        if (parts.length == 0) {
            return null;
        }

        String tableName = parts[0];
        String alias = null;

        // 处理可能的数据库前缀
        tableName = getPureTableName(tableName);

        // 查找别名
        for (int i = 1; i < parts.length; i++) {
            if (AS_KEYWORD.equalsIgnoreCase(parts[i]) && i + 1 < parts.length) {
                alias = parts[i + 1];
                break;
            } else if (!isMySqlKeyword(parts[i]) && alias == null) {
                // 可能是直接别名
                alias = parts[i];
                break;
            }
        }

        return new String[]{tableName, alias};
    }

    /**
     * 找到主FROM子句位置
     */
    private int findMainFromClause(String sql) {
        String cleanSql = removeSqlComments(sql);
        String upperSql = cleanSql.toUpperCase();
        int currentIndex = 0;

        while (true) {
            int fromIndex = upperSql.indexOf(FROM_KEYWORD, currentIndex);
            if (fromIndex == -1) {
                break;
            }

            // 确保前面是空白或SELECT等关键字
            if (isValidFromPosition(cleanSql, fromIndex)) {
                return fromIndex;
            }

            currentIndex = fromIndex + FROM_KEYWORD.length();
        }

        return -1;
    }

    /**
     * 检查FROM位置是否有效
     */
    private boolean isValidFromPosition(String sql, int position) {
        if (position == 0) return false;

        // 检查前面字符是否为空白或SELECT关键字
        char prevChar = sql.charAt(position - 1);
        if (!Character.isWhitespace(prevChar)) {
            return false;
        }

        // 检查是否在子查询中
        return !isInSubquery(sql, position);
    }

    /**
     * 判断是否在子查询中
     */
    private boolean isInSubquery(String sql, int position) {
        int parenLevel = 0;

        for (int i = 0; i < position; i++) {
            char c = sql.charAt(i);
            if (c == '(') {
                parenLevel++;
            } else if (c == ')') {
                parenLevel--;
            }
        }

        return parenLevel > 0;
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        if (excludeTables.contains(pureTableName.toLowerCase())) {
            return false;
        }

        return includeTablePrefixes.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 检查WHERE子句中是否已包含sso_org_code条件
     */
    private boolean hasOrgCodeInWhereClause(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        // 移除注释避免误匹配
        String cleanSql = removeSqlComments(sql);

        Matcher matcher = ORG_CODE_PATTERN.matcher(cleanSql);
        boolean found = matcher.find();

        if (found && enableLogging) {
            log.debug("找到sso_org_code条件，跳过SQL修改");
        }

        return found;
    }

    /**
     * 添加机构代码条件 - 安全版本
     */
    private String addOrgCodeCondition(String sql, SqlAnalysisResult analysis, String orgCode) {
        // 输入验证
        if (sql == null || sql.trim().isEmpty()) {
            log.warn("SQL为空，无法添加机构代码条件");
            return sql;
        }

        if (analysis == null) {
            log.warn("SQL分析结果为空，无法添加机构代码条件");
            return sql;
        }

        if (orgCode == null || orgCode.trim().isEmpty()) {
            log.warn("机构代码为空，无法添加机构代码条件");
            return sql;
        }

        // 验证机构代码格式（防止SQL注入）
        if (!isValidOrgCode(orgCode)) {
            log.warn("机构代码格式无效: {}", orgCode);
            return sql;
        }

        String orgCodeField = buildOrgCodeField(analysis);
        String condition = orgCodeField + " = '" + orgCode.replace("'", "''") + "'";

        if (enableLogging) {
            log.debug("addOrgCodeCondition - orgCodeField: '{}', condition: '{}'", orgCodeField, condition);
        }

        int whereIndex = findMainWhereClause(sql);

        if (whereIndex == -1) {
            return addWhereClause(sql, condition);
        } else {
            return addAndCondition(sql, whereIndex, condition);
        }
    }

    /**
     * 验证机构代码格式
     */
    private boolean isValidOrgCode(String orgCode) {
        if (orgCode == null || orgCode.trim().isEmpty()) {
            return false;
        }

        // 只允许字母、数字、下划线和短横线
        return orgCode.matches("^[a-zA-Z0-9_\\-]+$");
    }

    /**
     * 构建机构代码字段名
     */
    private String buildOrgCodeField(SqlAnalysisResult analysis) {
        String pureTableName = getPureTableName(analysis.getMainTable());

        if (enableLogging) {
            log.debug("buildOrgCodeField - mainTable: '{}', tableAlias: '{}', pureTableName: '{}'",
                    analysis.getMainTable(), analysis.getTableAlias(), pureTableName);
        }

        // 优先使用别名
        if (analysis.getTableAlias() != null && !analysis.getTableAlias().trim().isEmpty()) {
            String result = analysis.getTableAlias() + "." + ORG_CODE_FIELD;
            if (enableLogging) {
                log.debug("buildOrgCodeField - 使用别名: '{}'", result);
            }
            return result;
        }

        // 使用表名
        String result = pureTableName + "." + ORG_CODE_FIELD;
        if (enableLogging) {
            log.debug("buildOrgCodeField - 使用表名: '{}'", result);
        }
        return result;
    }

    /**
     * 查找主查询的WHERE子句位置
     */
    private int findMainWhereClause(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return -1;
        }

        String cleanSql = removeSqlComments(sql);
        String upperSql = cleanSql.toUpperCase();
        int currentIndex = 0;

        while (true) {
            int whereIndex = upperSql.indexOf(WHERE_KEYWORD, currentIndex);
            if (whereIndex == -1) {
                break;
            }

            // 确保WHERE前面是空白
            if (whereIndex > 0 && !Character.isWhitespace(cleanSql.charAt(whereIndex - 1))) {
                currentIndex = whereIndex + WHERE_KEYWORD.length();
                continue;
            }

            if (!isInSubquery(cleanSql, whereIndex)) {
                if (enableLogging) {
                    log.debug("findMainWhereClause - 返回WHERE位置: {}", whereIndex);
                }
                return whereIndex;
            }

            currentIndex = whereIndex + WHERE_KEYWORD.length();
        }

        if (enableLogging) {
            log.debug("findMainWhereClause - 未找到WHERE子句");
        }
        return -1;
    }

    /**
     * 添加WHERE子句
     */
    private String addWhereClause(String sql, String condition) {
        String cleanSql = removeSqlComments(sql);
        String upperSql = cleanSql.toUpperCase();

        // 找到最后一个FROM子句之后的位置
        int fromIndex = upperSql.lastIndexOf(FROM_KEYWORD);
        if (fromIndex == -1) {
            return sql;
        }

        // 找到FROM子句结束位置
        int fromEndIndex = findClauseEnd(cleanSql.substring(fromIndex), FROM_KEYWORD.length()) + fromIndex;
        if (fromEndIndex == -1) {
            fromEndIndex = cleanSql.length();
        }

        String beforeClause = sql.substring(0, fromEndIndex);
        String afterClause = sql.substring(fromEndIndex);

        return beforeClause + " WHERE " + condition + afterClause;
    }

    /**
     * 查找子句结束位置
     */
    private int findClauseEnd(String sqlFragment, int startIndex) {
        if (sqlFragment == null || sqlFragment.length() <= startIndex) {
            return -1;
        }

        String fragment = sqlFragment.substring(startIndex);
        String upperFragment = fragment.toUpperCase();

        int minIndex = Integer.MAX_VALUE;
        for (String keyword : SQL_KEYWORDS) {
            int index = upperFragment.indexOf(keyword.trim());
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }

        return minIndex == Integer.MAX_VALUE ? fragment.length() : minIndex + startIndex;
    }

    /**
     * 添加AND条件 - 改进版本
     */
    private String addAndCondition(String sql, int whereIndex, String condition) {
        if (sql == null || whereIndex < 0 || whereIndex >= sql.length()) {
            return sql;
        }

        String beforeWhere = sql.substring(0, whereIndex);
        String afterWhere = sql.substring(whereIndex + WHERE_KEYWORD.length());

        // 跳过空白
        int contentStart = 0;
        while (contentStart < afterWhere.length() && Character.isWhitespace(afterWhere.charAt(contentStart))) {
            contentStart++;
        }

        String whereContent = afterWhere.substring(contentStart);

        // 检查是否需要添加括号（如果已有复杂条件）
        boolean needsParentheses = whereContent.toUpperCase().contains(" OR ") ||
                whereContent.contains("(");

        if (needsParentheses) {
            return beforeWhere + WHERE_KEYWORD + " (" + condition + ") AND (" + whereContent + ")";
        } else {
            return beforeWhere + WHERE_KEYWORD + " " + condition + " AND " + whereContent;
        }
    }

    /**
     * 获取纯表名
     */
    private static String getPureTableName(String tableName) {
        if (tableName == null) {
            return null;
        }

        String pureTableName = tableName.trim();

        // 处理数据库.表名格式
        int dotIndex = pureTableName.indexOf('.');
        if (dotIndex != -1) {
            pureTableName = pureTableName.substring(dotIndex + 1);
        }

        // 处理反引号
        if (pureTableName.startsWith("`") && pureTableName.endsWith("`") && pureTableName.length() > 1) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 判断是否为MySQL关键字
     */
    private boolean isMySqlKeyword(String word) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }
        return MYSQL_KEYWORDS.contains(word.toUpperCase());
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            this.enableSqlModification = Boolean.parseBoolean(
                    properties.getProperty("enableSqlModification", "true"));
            this.enableLogging = Boolean.parseBoolean(
                    properties.getProperty("enableLogging", "true"));

            // 支持动态配置包含/排除表
            String excludeTablesStr = properties.getProperty("excludeTables");
            if (excludeTablesStr != null) {
                this.excludeTables = new HashSet<>(Arrays.asList(excludeTablesStr.split(",")));
            }

            String includePrefixesStr = properties.getProperty("includeTablePrefixes");
            if (includePrefixesStr != null) {
                this.includeTablePrefixes = new HashSet<>(Arrays.asList(includePrefixesStr.split(",")));
            }

            log.info("多机构拦截器配置 - 启用SQL修改: {}, 启用日志: {}", enableSqlModification, enableLogging);
            log.info("排除表: {}", excludeTables);
            log.info("包含表前缀: {}", includeTablePrefixes);
        }
    }
}