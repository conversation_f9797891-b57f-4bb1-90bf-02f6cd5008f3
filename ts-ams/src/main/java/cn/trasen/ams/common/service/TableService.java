package cn.trasen.ams.common.service;

import cn.trasen.homs.bean.base.DictItemResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service
 * @className: DictService
 * @author: chenbin
 * @description: TODO
 * @date: 2024/9/3 16:45
 * @version: 1.0
 */

public interface TableService {
    /**
     * @param table:
     * @param pk:
     * @param id:
     * @return boolean
     * <AUTHOR>
     * @description 检测传入的ID是否合法 !!!!!! 不允许从前台传入table pk 参数，造成安全问题  !!!!!!
     * @date 2023/12/1 17:18
     */
    boolean has(String table, String pk, String id, String ignoreIsDelete);

    /**
     * @param table:
     * @param pk:
     * @param id:
     * @return boolean
     * <AUTHOR>
     * @description 查询ID集是否都合法 !!!!!! 不允许从前台传入table pk 参数，造成安全问题  !!!!!!
     * @date 2023/12/1 17:17
     */
    boolean has(String table, String pk, String[] id, String ignoreIsDelete);

    List<DictItemResp> getDictItemByTypeCode(String typeCode);
}
