package cn.trasen.ams.common.service.impl;


import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.ConfigConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.base.DictItemFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.ams.common.model.Config;
import cn.trasen.ams.common.service.ConfigService;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ConfigServiceImpl
 * @Description TODO
 * @date 2025年1月8日 下午2:19:37
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ConfigServiceImpl implements ConfigService {

    @Autowired
    private DictService dictService;

    @Autowired
    private DictItemFeignService dictItemFeignService;


    /**
     * 验证Config对象的所有必填属性
     *
     * @param record Config对象
     * @throws IllegalArgumentException 当必填属性为空时抛出异常
     */
    void validate(Config record) {
        // 参数对象不能为空
        if (record == null) {
            throw new RuntimeException("配置对象不能为空");
        }

        if (StringUtil.isEmpty(record.getSysType())) {
            throw new RuntimeException("所属系统不能为空");
        }
        if (StringUtil.isEmpty(record.getItemCode())) {
            throw new RuntimeException("字典项编码不能为空");
        }
        if (StringUtil.isEmpty(record.getItemName())) {
            throw new RuntimeException("字典项名称不能为空");
        }

        if (record.getRemark() == null) {
            throw new RuntimeException("备注不能为空");
        }

    }


    @Transactional(readOnly = false)
    @Override
    public void setConfig(Config record) {
        // 验证输入参数
        validate(record);

        record.setTypeCode(getTypeCodeBySysType(record.getSysType()));

        // @RequestParam String typeCode, @RequestParam String itemCode, @RequestParam String itemName, @RequestParam String itemValue, @RequestParam String remark
        PlatformResult ret = dictItemFeignService.setDictItemByTypeCode(record.getTypeCode(), record.getItemCode(), record.getItemName(), record.getItemValue(), // itemValue is not used in this context
                record.getRemark());

        if (!ret.isSuccess()) {
            throw new RuntimeException("设置配置失败: " + ret.getMessage());
        }
    }

    @Override
    public String getConfig(Config record) {
        String typeCode = getTypeCodeBySysType(record.getSysType());
        String value = dictService.cgetValueByCode(typeCode, record.getItemCode());
        return value;
    }

    private String getTypeCodeBySysType(String sysType) {
        switch (sysType) {
            case CommonConst.SYS_TYPE_WZ:
                return ConfigConst.M_BASE_SETTING;
            case CommonConst.SYS_TYPE_ZCSB:
                return ConfigConst.D_BASE_SETTING;
            default:
                throw new RuntimeException("未知系统类型: " + sysType);
        }
    }

}