package cn.trasen.ams.common.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

@Table(name = "c_category")
@Setter
@Getter
public class Category {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;


    @ApiModelProperty(value = "分类类型")
    @Column(name = "sys_type")
    private String sysType;
    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String code;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    // 首拼
    @ApiModelProperty(value = "首拼")
    private String sp;

    // 全拼
    @ApiModelProperty(value = "全拼")
    private String qp;

    /**
     * 父类ID
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 父类
     */
    @Column(name = "parent_code")
    @ApiModelProperty(value = "父类")
    private String parentCode;

    @Transient
    @ApiModelProperty(value = "父类名称")
    private String parentName;

    /**
     * 树ID
     */
    @Column(name = "tree_ids")
    @ApiModelProperty(value = "树ID")
    private String treeIds;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 树结构中级别
     */
    @ApiModelProperty(value = "树结构中级别")
    private Integer level;

    /**
     * 设备数量
     */
    @ApiModelProperty(value = "设备数量")
    private Integer devices;

    /**
     * 排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序")
    private Integer seqNo;


    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    @Column(name = "is_leaf_node")
    @ApiModelProperty(value = "是否叶节点 0 非叶节点 1 叶节点")
    private String isLeafNode;

    @Transient
    @ApiModelProperty(value = "序号(导出数据用)")
    private String no;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}