package cn.trasen.ams.common.util;

import cn.trasen.ams.common.model.SchedulerExecutionLog;
import cn.trasen.ams.common.service.SchedulerExecutionLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.util
 * @className: SchedulerLogUtil
 * @author: chenbin
 * @description: 定时器日志工具类
 * @date: 2025/1/27 10:00
 * @version: 1.0
 */
@Slf4j
@Component
public class SchedulerLogUtil {

    @Autowired
    private SchedulerExecutionLogService schedulerExecutionLogService;

    /**
     * 手动记录定时器执行日志
     *
     * @param schedulerName  定时器名称
     * @param schedulerGroup 定时器分组
     * @param methodName     方法名
     * @param className      类名
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param status         执行状态
     * @param errorMessage   错误信息
     * @param inputParams    输入参数
     * @param outputResult   输出结果
     */
    public void recordSchedulerLog(String schedulerName, String schedulerGroup,
                                   String methodName, String className,
                                   Date startTime, Date endTime, String status,
                                   String errorMessage, String inputParams, String outputResult) {
        try {
            SchedulerExecutionLog log = new SchedulerExecutionLog();
            log.setSchdName(schedulerName);
            log.setSchdGrp(schedulerGroup);
            log.setMethodName(methodName);
            log.setClassName(className);
            log.setStartTime(startTime);
            log.setEndTime(endTime);
            log.setStatus(status);
            log.setErrMsg(errorMessage);
            log.setInput(inputParams);
            log.setOutput(outputResult);
            log.setThreadName(Thread.currentThread().getName());

            // 计算执行时长
            if (startTime != null && endTime != null) {
                long duration = endTime.getTime() - startTime.getTime();
                log.setExecDur(duration);
            }

            schedulerExecutionLogService.save(log);
        } catch (Exception e) {
            log.error("手动记录定时器日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录成功的定时器执行
     */
    public void recordSuccess(String schedulerName, String schedulerGroup,
                              String methodName, String className,
                              Date startTime, Date endTime,
                              String inputParams, String outputResult) {
        recordSchedulerLog(schedulerName, schedulerGroup, methodName, className,
                startTime, endTime, "SUCCESS", null, inputParams, outputResult);
    }

    /**
     * 记录失败的定时器执行
     */
    public void recordFailure(String schedulerName, String schedulerGroup,
                              String methodName, String className,
                              Date startTime, Date endTime,
                              String errorMessage, String inputParams) {
        recordSchedulerLog(schedulerName, schedulerGroup, methodName, className,
                startTime, endTime, "FAILED", errorMessage, inputParams, null);
    }

    /**
     * 记录超时的定时器执行
     */
    public void recordTimeout(String schedulerName, String schedulerGroup,
                              String methodName, String className,
                              Date startTime, Date endTime,
                              String inputParams) {
        recordSchedulerLog(schedulerName, schedulerGroup, methodName, className,
                startTime, endTime, "TIMEOUT", "执行超时", inputParams, null);
    }
} 