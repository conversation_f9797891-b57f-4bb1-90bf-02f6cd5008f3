package cn.trasen.ams.common.validator.dict;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;


@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ConstValidator.class)
@Documented
public @interface ConstValid {
    String message() default "不存在的常量值";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    // 常量集合
    String[] constant() default {};
}
