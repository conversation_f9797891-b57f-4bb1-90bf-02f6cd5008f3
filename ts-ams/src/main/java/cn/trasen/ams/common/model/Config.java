package cn.trasen.ams.common.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Data
public class Config {


    @ApiModelProperty(value = "所属系统")
    private String sysType;

    @ApiModelProperty(value = "字典主编码")
    private String typeCode;

    @ApiModelProperty(value = "字典项编码")
    private String itemCode;

    @ApiModelProperty(value = "字典项值")
    private String itemValue;

    @ApiModelProperty(value = "字典项名称")
    private String itemName;


    @ApiModelProperty(value = "字典项备注")
    private String remark;

}