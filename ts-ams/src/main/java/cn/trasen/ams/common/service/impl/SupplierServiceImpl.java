package cn.trasen.ams.common.service.impl;

import java.util.*;

import cn.trasen.ams.common.bean.SupplierInsertReq;
import cn.trasen.ams.common.constant.CertConst;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Cert;
import cn.trasen.ams.common.service.CertService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.SupplierMapper;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.common.service.SupplierService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SupplierServiceImpl
 * @Description 供应商实现
 * @date 2024年9月3日 下午5:35:59
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SupplierServiceImpl implements SupplierService {

    @Autowired
    private SupplierMapper mapper;

    @Autowired
    private CertService certService;

    @Autowired
    private DictService dictService;

    @Autowired
    private RedisService redisService;


    private String cacheKeyPrefix = "m:service:SupplierService:";

    @Transactional(readOnly = false)
    @Override
    public Integer save(Supplier record) {

        // 重复性检查
        checkNameRepeat(record.getSysType(), record.getName(), "add");
        // 自动填充字段
        autoFillColumn(record);

        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        // 清理缓存
        redisService.clearPrefix(cacheKeyPrefix);
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Supplier record) {

        Assert.hasText(record.getId(), "ID不能为空.");
        // 重复性检查
        checkNameRepeat(record.getSysType(), record.getName(), "update");

        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        // 自动填充字段
        autoFillColumn(record);

        // 清理缓存
        redisService.clearPrefix(cacheKeyPrefix);

        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void insert(SupplierInsertReq record) {
        // 保存供应商
        Supplier supplier = record.getSupplier();
        save(supplier);
        // 处理证件信息
        processCertificates(supplier, record.getCertList());
    }

    @Transactional(readOnly = false)
    @Override
    public void edit(SupplierInsertReq record) {
        // 更新供应商
        Supplier supplier = record.getSupplier();
        update(supplier);
        // 处理证件信息
        processCertificates(supplier, record.getCertList());
    }

    /**
     * 处理供应商证件信息
     *
     * @param supplier 供应商对象
     * @param certList 证件列表
     */
    private void processCertificates(Supplier supplier, List<Cert> certList) {
        // 先清空之前的证件数据
        certService.deleteByModelId(supplier.getId());

        // 如果没有提交新的证件信息，直接返回
        if (CollectionUtils.isEmpty(certList)) {
            return;
        }

        // 设置证件的必要属性
        certList.forEach(cert -> {
            cert.setId(IdGeneraterUtils.nextId()); // 防止逻辑删除造成的key 重复
            cert.setSysType(CommonConst.SYS_TYPE_WZ);
            cert.setModelId(supplier.getId());
            cert.setModelType(CertConst.MODEL_TYPE_SUPPLIER);
        });

        // 批量写入新的证件数据
        certService.batchInsert(certList);
    }

    private void autoFillColumn(Supplier record) {
        if (record.getId() == null) {
            record.setStatus(CommonConst.YES);
        }
        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Supplier record = new Supplier();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Supplier selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Supplier> getDataSetList(Page page, Supplier record) {
        Example example = new Example(Supplier.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getSysType() != null) {
            criteria.andLike("sysType", record.getSysType());
        }

        example.orderBy("level").asc();

        if (!StringUtil.isEmpty(record.getName())) {
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition).orLike("sp", likeCondition).orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }

        if (!StringUtil.isEmpty(record.getCreditCode())) {
            criteria.andLike("creditCode", record.getCreditCode());
        }

        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        // order by
        example.setOrderByClause("create_date desc");
        List<Supplier> records = mapper.selectByExampleAndRowBounds(example, page);
        // dataFmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param record:
     * @return List<Supplier>
     * <AUTHOR>
     * @description 获取列表，不分页，支持导出或者select数据需要
     * @date 2024/9/11 09:00
     */

    @Override
    public List<Supplier> getList(Supplier record) {
        Example example = new Example(Supplier.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }
        example.orderBy("level").asc();

        if (record.getSysType() != null) {
            criteria.andLike("sysType", record.getSysType());
        }

        if (!StringUtil.isEmpty(record.getName())) {
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition).orLike("sp", likeCondition).orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }


        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        if (!StringUtil.isEmpty(record.getCreditCode())) {
            criteria.andLike("creditCode", record.getCreditCode());
        }
        // order by
        example.setOrderByClause("create_date desc");

        List<Supplier> records = mapper.selectByExample(example);
        // dataFmt
        records.forEach(this::dataFmt);
        return records;
    }

    /**
     * @param list:
     * @return Integer
     * <AUTHOR>
     * @description 批量导入数据
     * @date 2024/9/11 09:05
     */
    @Transactional(readOnly = false)
    @Override
    public Integer importByExcel(List<Supplier> list, String sysType) {
        if (null == list || list.size() <= 0) {
            return 0;
        }
        // 这里是批量提交，先要对list中 Manufacturer 重复的name 进行过滤
        Set<String> set = fetchAllSupplierName(sysType);

        List<Supplier> cleanList = new ArrayList<>();
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        // 对list 本身名称重复也要过滤

        Set<String> selfSet = new HashSet<>();

        for (Supplier supplier : list) {
            if (set.contains(supplier.getName())) {
                continue;
            }

            if (selfSet.contains(supplier.getName())) {
                continue;
            }

            selfSet.add(supplier.getName());

            supplier.setId(IdGeneraterUtils.nextId());
            supplier.setCreateDate(new Date());
            supplier.setUpdateDate(new Date());
            supplier.setIsDeleted("N");
            supplier.setSysType(sysType);

            autoFillColumn(supplier);

            if (user != null) {
                supplier.setCreateUser(user.getUsercode());
                supplier.setCreateUserName(user.getUsername());
                supplier.setUpdateUser(user.getUsercode());
                supplier.setUpdateUserName(user.getUsername());
                supplier.setSsoOrgCode(user.getCorpcode());
                supplier.setSsoOrgName(user.getOrgName());
                supplier.setDeptId(user.getDeptId());
                supplier.setDeptName(user.getDeptname());
            }
            cleanList.add(supplier);
        }

        // 批量插入
        int size = cleanList.size();
        int batchSize = 1000;

        for (int i = 0; i < size; i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, size);
            List<Supplier> subList = cleanList.subList(i, end);
            mapper.batchInsert(subList);
        }

        return size;
    }

    /**
     * @param :
     * @return Set
     * <AUTHOR>
     * @description 获取所有的供应商名称，用于导入时校验
     * @date 2024/9/11 09:05
     */
    private Set fetchAllSupplierName(String sysType) {
        Example example = new Example(Supplier.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("sysType", sysType);
        example.setOrderByClause("create_date desc");
        List<Supplier> list = mapper.selectByExample(example);
        Set<String> set = new HashSet<>();
        for (Supplier supplier : list) {
            set.add(supplier.getName());
        }
        return set;
    }


    /**
     * @param name:
     * @param type:
     * @return void
     * <AUTHOR>
     * @description 检查名称是否重复
     * @date 2024/9/11 09:05
     */
    private void checkNameRepeat(String sysType, String name, String type) {
        Example example = new Example(Supplier.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("name", name);
        criteria.andEqualTo("sysType", sysType);

        int count = mapper.selectCountByExample(example);

        if (count > 0 && "add".equals(type)) {
            throw new RuntimeException("供应商名称重复.");
        }

        if (count > 1 && "update".equals(type)) {
            throw new RuntimeException("供应商名称重复.");
        }
    }

    public void dataFmt(Supplier record) {
        record.setEnterpriseTypeShow(dictService.cgetNameByValue(CommonConst.ENTERPRISE_TYPE, record.getEnterpriseType()));
        record.setStatusShow(dictService.cgetNameByValue(CommonConst.IS_ENABLE, record.getStatus()));
    }

    @Override
    public Map<String, Supplier> cgetSupplierMap(String sysType) {

        try {
            Map<String, Supplier> supplierMap = (Map<String, Supplier>) redisService.fetch(cacheKeyPrefix + "SupplierMap:" + sysType, () -> {
                Example example = new Example(Supplier.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                if (sysType != null) {
                    criteria.andEqualTo("sysType", sysType);
                }
                example.setOrderByClause("create_date desc");
                List<Supplier> list = mapper.selectByExample(example);
                Map<String, Supplier> map = new HashMap<>();
                for (Supplier supplier : list) {
                    map.put(supplier.getName(), supplier);
                }
                return map;
            }, 3600);
            return supplierMap;
        } catch (Exception e) {
            throw new RuntimeException("获取供应商大map失败" + e.getMessage());
        }
    }
}
