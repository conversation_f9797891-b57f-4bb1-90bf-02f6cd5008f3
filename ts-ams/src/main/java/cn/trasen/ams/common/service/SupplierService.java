package cn.trasen.ams.common.service;

import cn.trasen.ams.common.bean.SupplierInsertReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Supplier;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SupplierService
 * @Description TODO
 * @date 2024年9月3日 下午5:35:59
 */
public interface SupplierService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    Integer save(Supplier record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    Integer update(Supplier record);


    void insert(SupplierInsertReq record);

    void edit(SupplierInsertReq record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Supplier
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    Supplier selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Supplier>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    DataSet<Supplier> getDataSetList(Page page, Supplier record);


    List<Supplier> getList(Supplier record);

    Integer importByExcel(List<Supplier> list, String sysType);

    void dataFmt(Supplier record);

    Map<String, Supplier> cgetSupplierMap(String sysType);
}
