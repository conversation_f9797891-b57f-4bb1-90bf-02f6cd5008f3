package cn.trasen.ams.common.validator.pk;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD ,ElementType.PARAMETER,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PkExistValidator.class)
@Documented

public @interface PkExistValid {
    String message() default "关联对象的Id不合法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String table();

    String pk() default "id";

    String ignoreIsDelete() default "N";
}
