package cn.trasen.ams.common.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.DataModifyLog;
import cn.trasen.ams.common.service.DataModifyLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DataModifyLogController
 * @Description TODO
 * @date 2025年7月24日 下午6:39:31
 */
@RestController
@Api(tags = "DataModifyLogController")
public class DataModifyLogController {

    private transient static final Logger logger = LoggerFactory.getLogger(DataModifyLogController.class);

    @Autowired
    private DataModifyLogService dataModifyLogService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveDataModifyLog
     * @Description 新增
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    @ApiOperation(value = "业务数据变更记录新增", notes = "业务数据变更记录新增")
    @PostMapping("/api/common/dataModifyLog/save")
    public PlatformResult<String> saveDataModifyLog(@RequestBody DataModifyLog record) {
        try {
            dataModifyLogService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateDataModifyLog
     * @Description 编辑
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    @ApiOperation(value = "业务数据变更记录编辑", notes = "业务数据变更记录编辑")
    @PostMapping("/api/common/dataModifyLog/update")
    public PlatformResult<String> updateDataModifyLog(@RequestBody DataModifyLog record) {
        try {
            dataModifyLogService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<DataModifyLog>
     * @Title selectDataModifyLogById
     * @Description 根据ID查询
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    @ApiOperation(value = "业务数据变更记录详情", notes = "业务数据变更记录详情")
    @GetMapping("/api/common/dataModifyLog/{id}")
    public PlatformResult<DataModifyLog> selectDataModifyLogById(@PathVariable String id) {
        try {
            DataModifyLog record = dataModifyLogService.selectById(id);
            dataModifyLogService.dataFmt(record);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteDataModifyLogById
     * @Description 根据ID删除
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    @ApiOperation(value = "业务数据变更记录删除", notes = "业务数据变更记录删除")
    @PostMapping("/api/common/dataModifyLog/delete/{id}")
    public PlatformResult<String> deleteDataModifyLogById(@PathVariable String id) {
        try {
            dataModifyLogService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<DataModifyLog>
     * @Title selectDataModifyLogList
     * @Description 查询列表
     * @date 2025年7月24日 下午6:39:31
     * <AUTHOR>
     */
    @ApiOperation(value = "业务数据变更记录列表", notes = "业务数据变更记录列表")
    @GetMapping("/api/common/dataModifyLog/list")
    public DataSet<DataModifyLog> selectDataModifyLogList(Page page, DataModifyLog record) {
        return dataModifyLogService.getDataSetList(page, record);
    }
}
