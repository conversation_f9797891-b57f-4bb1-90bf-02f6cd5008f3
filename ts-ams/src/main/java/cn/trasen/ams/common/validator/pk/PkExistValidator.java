package cn.trasen.ams.common.validator.pk;

import cn.trasen.ams.common.service.TableService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @projectName: xtbg
 * @package: cn.trasen.hrms.performance.validator.pk
 * @className: PkExistValidator
 * @author: chenbin
 * @description: 用来检测某个主键是否合法
 * @date: 2023/12/6 16:30
 * @version: 1.0
 */

public class PkExistValidator implements ConstraintValidator<PkExistValid, Object> {

    private String table;

    private String pk;

    private String ignoreIsDelete;

    @Autowired
    private TableService tableService;

    @Override
    public void initialize(PkExistValid constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        table = constraintAnnotation.table();
        pk = constraintAnnotation.pk();
        ignoreIsDelete = constraintAnnotation.ignoreIsDelete();
    }

    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        // 检测 o 是string 还是 String[] ，其他全部返回 true
        if (o instanceof String) {
            if (StringUtils.isBlank(o.toString())) {
                // 值的必填判断 不走这里 如果没值 则不验证
                return true;
            }

            String value = o.toString();
            // 检查是否包含逗号分隔的多个ID TODO 这里只是简单处理一下，实际上遇到大规模ID 这里性能会很差，后期可以考虑hasId 支持 而不是直接这里循环
            if (value.contains(",")) {
                String[] ids = value.split(",");
                // 去除空白并过滤空字符串
                for (String id : ids) {
                    String trimmedId = id.trim();
                    if (StringUtils.isNotBlank(trimmedId)) {
                        if (!tableService.has(table, pk, trimmedId, ignoreIsDelete)) {
                            return false;
                        }
                    }
                }
                return true;
            } else {
                return tableService.has(table, pk, value, ignoreIsDelete);
            }
        } else if (o instanceof String[]) {
            if (((String[]) o).length <= 0) {
                return true;
            }
            return tableService.has(table, pk, (String[]) o, ignoreIsDelete);
        }
        return true;
    }
}
