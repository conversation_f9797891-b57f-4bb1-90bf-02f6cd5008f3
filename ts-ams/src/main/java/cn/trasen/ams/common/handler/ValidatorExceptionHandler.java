package cn.trasen.ams.common.handler;

import cn.trasen.homs.core.utils.PlatformResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * @projectName: xtbg
 * @package: cn.trasen.hrms.performance.handler
 * @className: ValidatorExceptionHandler
 * @author: chenbin
 * @description: handler validate 的错误信息
 * @date: 2024/09/02 17:30
 * @version: 1.0
 */
@RestControllerAdvice(basePackages = {
        "cn.trasen.ams.device.controller",
        "cn.trasen.ams.material.controller",
        "cn.trasen.ams.common.controller"
})
@Order(Ordered.HIGHEST_PRECEDENCE)
@Configuration
public class ValidatorExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(ValidatorExceptionHandler.class);

    /**
     * 参数效验异常处理器 ｜ 通常是验证注解写在类属性上
     *
     * @param e 参数验证异常
     * @return ResponseInfo
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public PlatformResult<String> parameterExceptionHandler(MethodArgumentNotValidException e) {
        logger.error("", e);
        // 获取异常信息
        BindingResult exceptions = e.getBindingResult();
        // 判断异常中是否有错误信息，如果存在就使用异常中的消息，否则使用默认消息
        if (exceptions.hasErrors()) {
            List<ObjectError> errors = exceptions.getAllErrors();
            if (!errors.isEmpty()) {
                // 这里列出了全部错误参数，按正常逻辑，只需要第一条错误即可
                FieldError fieldError = (FieldError) errors.get(0);
                return PlatformResult.failure(fieldError.getDefaultMessage());
            }
        }
        return PlatformResult.failure();
    }

    /**
     * @param e:
     * @return PlatformResult<String>
     * <AUTHOR>
     * @description 兼容Java Bean Validation（JSR 380）规范 ｜ 通常是把@Validated写到了类上面
     * @date 2023/12/7 10:13
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(ConstraintViolationException.class)
    public PlatformResult<String> BeanExceptionHandler(ConstraintViolationException e) {
        logger.error("", e);
        // 获取异常信息
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : constraintViolations) {
            return PlatformResult.failure(violation.getMessage());
        }
        return PlatformResult.failure();
    }

    /**
     * @param e:
     * @return PlatformResult<String>
     * <AUTHOR>
     * @description 增加RuntimeExceptionHandler处理器
     * @date 2023/12/7 10:13
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(RuntimeException.class)
    public PlatformResult<String> RuntimeExceptionHandler(RuntimeException e) {
        logger.error("", e);
        // 获取异常信息
        String message = e.getMessage();
        return PlatformResult.failure(message);
    }
}






