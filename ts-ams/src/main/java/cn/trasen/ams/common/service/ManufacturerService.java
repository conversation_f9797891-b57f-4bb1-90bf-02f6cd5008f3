package cn.trasen.ams.common.service;

import cn.trasen.ams.common.bean.ManufacturerInsertReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Manufacturer;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ManufacturerService
 * @Description TODO
 * @date 2024年9月2日 下午4:51:25
 */
public interface ManufacturerService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月2日 下午4:51:25
     * <AUTHOR>
     */
    String save(Manufacturer record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月2日 下午4:51:25
     * <AUTHOR>
     */
    Integer update(Manufacturer record);

    void insert(ManufacturerInsertReq record);

    void edit(ManufacturerInsertReq record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月2日 下午4:51:25
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Manufacturer
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月2日 下午4:51:25
     * <AUTHOR>
     */
    Manufacturer selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Manufacturer>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月2日 下午4:51:25
     * <AUTHOR>
     */
    DataSet<Manufacturer> getDataSetList(Page page, Manufacturer record);


    List<Manufacturer> getList(Manufacturer record);

    Integer importByExcel(List<Manufacturer> list, String sysType);

    Set fetchAllManufacturerName(String sysType);

    Map fetchAllManufacturerName2Map(String sysType);

    void dataFmt(Manufacturer record);

}
