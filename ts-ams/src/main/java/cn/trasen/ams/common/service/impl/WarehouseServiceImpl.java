package cn.trasen.ams.common.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.WarehouseConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.utils.StringUtil;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.WarehouseMapper;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WarehouseServiceImpl
 * @Description TODO
 * @date 2025年7月18日 下午3:56:24
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class WarehouseServiceImpl implements WarehouseService {

    @Autowired
    private WarehouseMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private DictService dictService;

    private String defZcsbWarehouseId = "100";

    @Transactional(readOnly = false)
    @Override
    public Integer save(Warehouse record) {

        autoFillColumn(record);

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Warehouse record) {
        autoFillColumn(record);
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    private String genWhCode() {
        return serialNoGenService.genByDate("WZCK");
    }

    private String genWhCodeNew(Warehouse record) {
        // 查询当前级别的仓库数量
        Example example = new Example(Warehouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sysType", record.getSysType());
        criteria.andEqualTo("level", record.getLevel());

        Integer count = mapper.selectCountByExample(example);
        // 生成新的仓库编码 级别 + 三位流水号
        String level = record.getLevel() != null ? record.getLevel() : "1"; // 默认级别为0
        String whCode = level + String.format("%03d", count + 1);
        return whCode;
    }

    private void autoFillColumn(Warehouse record) {

        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }

        // 新增生成flowNo
        if (record.getId() == null) {
            record.setWhCode(genWhCodeNew(record));

            // 默认不开启库存库位
            if (record.getIsLoc() == null) {
                record.setIsLoc(CommonConst.NO);
            }

            // 默认启用
            if (record.getStatus() == null) {
                record.setStatus(CommonConst.YES);
            }

        }


    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Warehouse record = new Warehouse();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Warehouse selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Warehouse> getDataSetList(Page page, Warehouse record) {
        Example example = new Example(Warehouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        if (!StringUtil.isEmpty(record.getLevel())) {
            criteria.andEqualTo("level", record.getLevel());
        }

        example.setOrderByClause("seq_no asc, create_date desc");

        // 动态添加查询条件：name like 或 sp like 或 qp like
        if (!StringUtil.isEmpty(record.getName())) {
            // 创建 OR 条件
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition).orLike("sp", likeCondition).orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }


        List<Warehouse> records = mapper.selectByExampleAndRowBounds(example, page);

        // dataFmt
        records.forEach(this::dataFmt);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<Warehouse> getListNoPage(Warehouse record) {
        Example example = new Example(Warehouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sysType", record.getSysType());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        return mapper.selectByExample(example);
    }

    @Override
    public Warehouse getZcsbDefWarehouse() {
        Example example = new Example(Warehouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("id", defZcsbWarehouseId);
        // 这个条件的意义主要是让 sso拦截器 不追加条件
        criteria.andEqualTo("ssoOrgCode", -10000);
        return mapper.selectOneByExample(example);
    }


    public void dataFmt(Warehouse record) {
        record.setStatusShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getStatus()));
        record.setIsLocShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getIsLoc()));
        record.setLevelShow(dictService.cgetNameByValue(WarehouseConst.WAREHOUSE_LEVEL, record.getLevel()));
    }

    @Override
    public DataSet<Warehouse> getDataSetByIdList(Page page, List<String> idList) {
        Example example = new Example(Warehouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if (!CollectionUtils.isEmpty(idList)) {
            criteria.andIn("id", idList);
        }
        example.setOrderByClause("seq_no asc, create_date desc");
        List<Warehouse> records = mapper.selectByExampleAndRowBounds(example, page);
        // dataFmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void genDefData() {
        // defZcsbWarehouseId
        Warehouse record = mapper.selectByPrimaryKey(defZcsbWarehouseId);
        if (record == null) {
            record = new Warehouse();
            record.setId(defZcsbWarehouseId);
            record.setWhCode(defZcsbWarehouseId);
            record.setName("资产设备默认仓库");
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            record.setLevel(WarehouseConst.WAREHOUSE_LEVEL_DEF);
            record.setIsLoc(CommonConst.NO);
            record.setStatus(CommonConst.YES);
            record.setSeqNo(1);
            this.save(record);
        }
    }
}
