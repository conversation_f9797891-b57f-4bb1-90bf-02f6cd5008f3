package cn.trasen.ams.common.service.impl;

import java.util.*;

import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.BrandMapper;
import cn.trasen.ams.common.model.Brand;
import cn.trasen.ams.common.service.BrandService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BrandServiceImpl
 * @Description 品牌业务实现
 * @date 2024年9月3日 下午3:22:26
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class BrandServiceImpl implements BrandService {

    @Autowired
    private BrandMapper mapper;


    @Transactional(readOnly = false)
    @Override
    public String save(Brand record) {

        checkNameRepeat(record.getSysType(), record.getName(), "add");
        String id = IdGeneraterUtils.nextId();
        record.setId(id);
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        autoFillColumn(record);

        mapper.insertSelective(record);

        return id;
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Brand record) {
        Assert.hasText(record.getId(), "ID不能为空.");
        checkNameRepeat(record.getSysType(), record.getName(), "update");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        autoFillColumn(record);
        return mapper.updateByPrimaryKeySelective(record);
    }


    private void autoFillColumn(Brand record) {
        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Brand record = new Brand();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Brand selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Brand> getDataSetList(Page page, Brand record) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        // 添加固定的查询条件
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        // 设置排序条件
        example.setOrderByClause("sord asc, create_date desc");

        // 动态添加查询条件：name like 或 sp like 或 qp like
        if (!StringUtil.isEmpty(record.getName())) {
            // 创建 OR 条件
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition)
                    .orLike("sp", likeCondition)
                    .orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }
        List<Brand> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param record:
     * @return List<Brand>
     * <AUTHOR>
     * @description 查询列表不分页，支持导出或者select数据需要
     * @date 2024/9/11 08:50
     */
    @Override
    public List<Brand> getList(Brand record) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        example.setOrderByClause("sord asc , create_date desc");

        if (record.getSysType() != null) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        if (!StringUtil.isEmpty(record.getName())) {
            Example.Criteria orCriteria = example.createCriteria();
            String likeCondition = "%" + record.getName() + "%";

            orCriteria.andLike("name", likeCondition)
                    .orLike("sp", likeCondition)
                    .orLike("qp", likeCondition);

            // 将 OR 条件添加到 Example 中
            example.and(orCriteria);
        }

        List<Brand> records = mapper.selectByExample(example);
        return records;
    }

    /**
     * @param list:
     * @return Integer
     * <AUTHOR>
     * @description 批量导入数据
     * @date 2024/9/11 08:50
     */

    @Transactional(readOnly = false)
    @Override
    public Integer importByExcel(List<Brand> list, String sysType) {
        if (null == list || list.size() <= 0) {
            return 0;
        }
        // 这里是批量提交，先要对list中 Manufacturer 重复的name 进行过滤
        Set<String> set = fetchAllBrandName(sysType);

        List<Brand> cleanList = new ArrayList<>();
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        // 对list 本身名称重复也要过滤

        Set<String> selfSet = new HashSet<>();

        for (Brand brand : list) {
            if (set.contains(brand.getName())) {
                continue;
            }

            if (selfSet.contains(brand.getName())) {
                continue;
            }

            selfSet.add(brand.getName());

            brand.setId(IdGeneraterUtils.nextId());
            brand.setCreateDate(new Date());
            brand.setUpdateDate(new Date());
            brand.setIsDeleted("N");
            brand.setSysType(sysType);

            autoFillColumn(brand);

            if (user != null) {
                brand.setCreateUser(user.getUsercode());
                brand.setCreateUserName(user.getUsername());
                brand.setUpdateUser(user.getUsercode());
                brand.setUpdateUserName(user.getUsername());
                brand.setSsoOrgCode(user.getCorpcode());
                brand.setSsoOrgName(user.getOrgName());
                brand.setDeptId(user.getDeptId());
                brand.setDeptName(user.getDeptname());

            }
            cleanList.add(brand);
        }

        // 批量插入
        int size = cleanList.size();
        int batchSize = 1000;

        for (int i = 0; i < size; i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, size);
            List<Brand> subList = cleanList.subList(i, end);
            mapper.batchInsert(subList);
        }

        return size;
    }

    /**
     * @param :
     * @return Set
     * <AUTHOR>
     * @description 查询所有的品牌名称，主要用来逻辑过滤用
     * @date 2024/9/11 08:51
     */

    public Set fetchAllBrandName(String sysType) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sysType", sysType);

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Brand> list = mapper.selectByExample(example);
        Set<String> set = new HashSet<>();
        for (Brand brand : list) {
            set.add(brand.getName());
        }
        return set;
    }

    /**
     * @param :
     * @return Map<String, String>
     * <AUTHOR>
     * @description 也是用来过滤，与反响查询ID
     * @date 2024/9/11 08:51
     */

    public Map<String, String> fetchAllBrandName2Map(String sysType) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("sysType", sysType);

        List<Brand> list = mapper.selectByExample(example);
        Map<String, String> map = new HashMap<>();
        for (Brand brand : list) {
            map.put(brand.getName(), brand.getId());
        }
        return map;
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByManufacturerId(String manufacturerId) {
        // 逻辑删除
        Brand record = new Brand();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("manufacturerId", manufacturerId);
        mapper.updateByExampleSelective(record, example);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchInsert(List<Brand> list) {
        // 数据不多，循环插入
        for (Brand brand : list) {
            save(brand);
        }
    }

    @Override
    public List<Brand> getListByManufacturerId(String manufacturerId) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("manufacturerId", manufacturerId);
        example.setOrderByClause("sord asc, create_date desc");
        return mapper.selectByExample(example);
    }

    /**
     * @param name:
     * @param type:
     * @return void
     * <AUTHOR>
     * @description 用于新增或者修改时候的名称重复校验
     * @date 2024/9/11 08:52
     */
    private void checkNameRepeat(String sysType, String name, String type) {
        Example example = new Example(Brand.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("name", name);
        criteria.andEqualTo("sysType", sysType);

        int count = mapper.selectCountByExample(example);

        if (count > 0 && "add".equals(type)) {
            throw new RuntimeException("品牌名称重复.");
        }

        if (count > 1 && "update".equals(type)) {
            throw new RuntimeException("品牌名称重复.");
        }
    }
}
