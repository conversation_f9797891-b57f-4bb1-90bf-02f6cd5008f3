package cn.trasen.ams.common.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.SupplierConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.validation.annotation.Validated;

@Table(name = "c_supplier")
@Setter
@Getter
@Validated
public class Supplier {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 所属系统
     */
    @Column(name = "sys_type")
    @ApiModelProperty(value = "所属系统")
    private String sysType;


    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    @Size(max = 50, message = "供应商名称长度不能超过50")
    @NotNull(message = "供应商名称不能为空")
    @ApiModelProperty(value = "供应商名称")
    private String name;

    // 首拼
    @Column(name = "sp")
    @Size(max = 50, message = "首拼长度不能超过50")
    @ApiModelProperty(value = "首拼")
    private String sp;

    // 全拼
    @Column(name = "qp")
    @Size(max = 255, message = "全拼长度不能超过255")
    @ApiModelProperty(value = "全拼")
    private String qp;

    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码")
    @Column(name = "credit_code")
    @Size(max = 50, message = "统一社会信用代码长度不能超过50")
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 企业性质
     */
//    @DictExistValid(code = CommonConst.ENTERPRISE_TYPE, message = "企业性质不合法")
    @Excel(name = "企业性质")
    @Column(name = "enterprise_type")
    @Size(max = 2, message = "企业性质长度不能超过2")
    @ApiModelProperty(value = "企业性质")
    private String enterpriseType;

    @Transient
    @ApiModelProperty(value = "企业性质显示")
    private String enterpriseTypeShow;

    /**
     * 法定代表人
     */
    @Excel(name = "法定代表人")
    @Column(name = "legal_person")
    @Size(max = 50, message = "法定代表人长度不能超过50")
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    @Column(name = "bank")
    @Size(max = 50, message = "开户银行长度不能超过50")
    @ApiModelProperty(value = "开户银行")
    private String bank;

    /**
     * 银行账号
     */
    @Excel(name = "银行账号")
    @Column(name = "bank_card_no")
    @Size(max = 50, message = "银行账号长度不能超过50")
    @ApiModelProperty(value = "银行账号")
    private String bankCardNo;

    /**
     * 供应商级别
     */
    @Excel(name = "供应商级别")
    @Column(name = "level")
    @Size(max = 10, message = "供应商级别长度不能超过10")
    @ConstValid(constant = {SupplierConst.LEVEL_1, SupplierConst.LEVEL_2, SupplierConst.LEVEL_3, SupplierConst.LEVEL_4, SupplierConst.LEVEL_5}, message = "供应商级别不正确")
    @ApiModelProperty(value = "供应商级别")
    private String level;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @Column(name = "contactor")
    @Size(max = 20, message = "联系人长度不能超过20")
    @ApiModelProperty(value = "联系人")
    private String contactor;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @Column(name = "mobile")
    @Size(max = 20, message = "联系电话长度不能超过20")
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 供应商地址
     */
    @Excel(name = "联系地址")
    @Column(name = "addr")
    @Size(max = 50, message = "联系地址长度不能超过50")
    @ApiModelProperty(value = "联系地址")
    private String addr;

    /**
     * 邮政编码
     */
    @Excel(name = "邮政编码")
    @Column(name = "postal_code")
    @Size(max = 10, message = "邮政编码长度不能超过10")
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    /**
     * 传真号码
     */
    @Excel(name = "传真号码")
    @Column(name = "fax_no")
    @Size(max = 20, message = "传真号码长度不能超过20")
    @ApiModelProperty(value = "传真号码")
    private String faxNo;

    /**
     * 附件
     */
    @Column(name = "files")
    @Size(max = 500, message = "附件长度不能超过500")
    @ApiModelProperty(value = "附件")
    private String files;


    @DictExistValid(code = CommonConst.YES_OR_NO, message = "状态不合法")
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String status;

    @Transient
    @ApiModelProperty(value = "状态显示")
    private String statusShow;


    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @Column(name = "note")
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}