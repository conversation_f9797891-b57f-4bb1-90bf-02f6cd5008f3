package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: RedisServiceImpl
 * @author: chenbin
 * @description: 简单包装一层redis
 * @date: 2024/9/12 15:31
 * @version: 1.0
 */

@Service
public class RedisServiceImpl implements RedisService {

    public String cacheKeyPrefix = "ams:cache:";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 存储对象
    public void set(String key, Object object, long timeout) {
        redisTemplate.opsForValue().set(key, object, timeout, TimeUnit.SECONDS);
    }

    // 获取对象
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }


    private String key4sso(String key) {
        String orgCode = "def";
        // 多机构支持

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                orgCode = user.getCorpcode();
            }
        } catch (Exception e) {
            // 什么都不做
        }
        return cacheKeyPrefix + orgCode + ":" + key;

    }

    @Override
    public Object fetch(String key, Callable<Object> callback, long timeout) {


        // 增加前缀，避免key冲突，好维护，好debug
        key = key4sso(key);

        Object object = get(key);
        if (object == null) {
            try {
                object = callback.call();
            } catch (Exception e) {
                throw new BusinessException("callback.call error", e);
            }
            if (object != null) {
                set(key, object, timeout);
            }
        }
        return object;
    }

    @Override
    public void clearFetch(String key) {
        key = key4sso(key);
        redisTemplate.delete(key);
    }

    @Override
    public void clearPrefix(String prefix) {
        prefix = key4sso(prefix + "*");
        // 清除前缀的缓存
        try {
            redisTemplate.delete(redisTemplate.keys(prefix));
        } catch (Exception e) {
            throw new BusinessException("清除缓存失败", e);
        }
    }

    @Override
    public void clearAll() {
        // 清除所有的缓存
        try {
            redisTemplate.delete(redisTemplate.keys(key4sso("*")));
        } catch (Exception e) {
            throw new BusinessException("清除缓存失败", e);
        }
    }

}
