package cn.trasen.ams.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "c_cert")
@Setter
@Getter
public class Cert {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 所属系统
     */
    @Column(name = "sys_type")
    @ApiModelProperty(value = "所属系统")
    private String sysType;

    // model_type
    @ApiModelProperty(value = "模型类型")
    private String modelType;
    // model_id

    @ApiModelProperty(value = "模型ID")
    private String modelId;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String type;

    @Transient
    @ApiModelProperty(value = "证件类型显示")
    private String typeShow;

    /**
     * 证件编号
     */
    @Column(name = "cert_no")
    @ApiModelProperty(value = "证件编号")
    private String certNo;

    /**
     * 发证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "birth_date")
    @ApiModelProperty(value = "发证日期")
    private Date birthDate;

    /**
     * 有效期至
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "valid_over_date")
    @ApiModelProperty(value = "有效期至")
    private Date validOverDate;

    /**
     * 是否长期
     */
    @Column(name = "is_forever")
    @ApiModelProperty(value = "是否长期")
    private String isForever;

    @Transient
    @ApiModelProperty(value = "是否长期显示")
    private String isForeverShow;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private String status;

    @Transient
    @ApiModelProperty(value = "是否有效显示")
    private String statusShow;

    /**
     * 附件
     */
    @Column(name = "file_set")
    @ApiModelProperty(value = "附件")
    private String fileSet;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}