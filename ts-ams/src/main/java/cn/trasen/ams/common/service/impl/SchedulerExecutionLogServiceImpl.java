package cn.trasen.ams.common.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.SchedulerExecutionLogMapper;
import cn.trasen.ams.common.model.SchedulerExecutionLog;
import cn.trasen.ams.common.service.SchedulerExecutionLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SchedulerExecutionLogServiceImpl
 * @Description TODO
 * @date 2025年6月21日 下午4:16:57
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SchedulerExecutionLogServiceImpl implements SchedulerExecutionLogService {

    @Autowired
    private SchedulerExecutionLogMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(SchedulerExecutionLog record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }
        record.setCreatedTime(new Date());

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(SchedulerExecutionLog record) {
        record.setUpdatedTime(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        SchedulerExecutionLog record = new SchedulerExecutionLog();
        record.setId(id);
 
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public SchedulerExecutionLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<SchedulerExecutionLog> getDataSetList(Page page, SchedulerExecutionLog record) {
        Example example = new Example(SchedulerExecutionLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<SchedulerExecutionLog> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }
}
