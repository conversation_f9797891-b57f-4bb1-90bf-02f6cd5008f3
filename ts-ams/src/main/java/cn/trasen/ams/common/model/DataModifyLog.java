package cn.trasen.ams.common.model;

import io.swagger.annotations.*;

import java.util.Date;
import java.util.List;
import javax.persistence.*;

import lombok.*;

@Table(name = "c_mod_log")
@Setter
@Getter
public class DataModifyLog {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 表名称
     */
    @Column(name = "table_name")
    @ApiModelProperty(value = "Q:表名称")
    private String tableName;

    /**
     * 主键行流水号
     */
    @Column(name = "row_pk_flow_no")
    @ApiModelProperty(value = "Q:主键行流水号")
    private String rowPkFlowNo;

    /**
     * 主键行名称
     */
    @Column(name = "row_name")
    @ApiModelProperty(value = "Q:主键行名称")
    private String rowName;

    /**
     * 主键行修改类型
     */
    @Column(name = "row_modify_type")
    @ApiModelProperty(value = "Q:主键行修改类型")
    private String rowModifyType;


    @Transient
    @ApiModelProperty(value = "主键行修改类型显示")
    private String rowModifyTypeShow;

    /**
     * 表主键
     */
    @Column(name = "row_pk_value")
    @ApiModelProperty(value = "表主键")
    private String rowPkValue;


    @ApiModelProperty(value = "查询构造")
    private String search;

    @Transient
    @ApiModelProperty(value = "Q:search查询集合")
    private List<String> searchList;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 老数据
     */
    @Column(name = "row_json_old")
    @ApiModelProperty(value = "老数据")
    private String rowJsonOld;


    /**
     * 新数据
     */
    @Column(name = "row_json_new")
    @ApiModelProperty(value = "新数据")
    private String rowJsonNew;


    @Column(name = "row_diff")
    @ApiModelProperty(value = "行数据差异")
    private String rowDiff;
}