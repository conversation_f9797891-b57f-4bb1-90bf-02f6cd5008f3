package cn.trasen.ams.common.validator.right;

import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.List;

@Aspect
@Component
@Slf4j
public class RightValidatorAspect {

    @Resource
    private PermissionService permissionService;

    @Pointcut("@annotation(cn.trasen.ams.common.validator.right.RightValid)")
    public void rightCheckPointcut() {
    }

    @Before("rightCheckPointcut()")
    public void before(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        RightValid annotation = method.getAnnotation(RightValid.class);
        if (annotation == null) {
            return;
        }

        String action = annotation.action();
        String message = annotation.message();

        PermissionResp permissionResp = permissionService.cgetPermission(action);

        if (permissionResp == null || permissionResp.getBusinessMap() == null) {
            throw new RuntimeException(message);
        }

        // 超级管理员直接放行
        if (PermissionConst.TYPE_ALL.equals(permissionResp.getType())) {
            return;
        }

        List<String> skuTypeList = permissionResp.getBusinessMap().get(action);
        if (skuTypeList == null || skuTypeList.isEmpty()) {
            throw new RuntimeException(message);
        }
    }
}
