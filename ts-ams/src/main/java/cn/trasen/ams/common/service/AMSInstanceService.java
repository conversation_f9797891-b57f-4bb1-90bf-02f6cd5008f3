package cn.trasen.ams.common.service;

import cn.trasen.ams.common.bean.instance.InstanceListReq;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfo;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfoFieldSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.form.model.DpTableField;

import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.service
 * @className: InstanceService
 * @author: chenbin
 * @description: 防止容器内service命名一致的冲突加上AMS
 * @date: 2025/5/27 15:05
 * @version: 1.0
 */

public interface AMSInstanceService {
    List<AMSInstanceDefinitionInfo> getInstanceDefinitionListByIdList(List<String> idList);

    List<AMSInstanceDefinitionInfo> getPurchaseTypeList();

    // TODO 命名一致性太差
    List<AMSInstanceDefinitionInfoFieldSet> getInstanceDefinitionFieldSetListByTemplateId(String formId);

    List<DpTableField> getSonFormField(String tableId);

    Map<String, Integer> getInstancesWithStatus(String definitionId, List<String> withStatus);

    Map<String, Integer> getSonFormsWithStatus(String definitionId, List<String> withStatus);

    // TODO 这两个方法不应该存在这里，应该挪走
    List<String> getEmployeeCodeListByOrgIdList(List<String> orgIdList);

    // TODO 这两个方法不应该存在这里，应该挪走
    List<String> getOrganizationIdListByOrgId(String orgId);

    DataSet<Map<String, Object>> getInstanceList(Page page, InstanceListReq record);

    DataSet<Map<String, Object>> getInstanceSonFromList(Page page, InstanceListReq record);

    String getMainFormTableName(String formId);

    // TODO 这个模型应该直接基础包的
    List<AMSInstanceDefinitionInfoFieldSet> getMainFormFieldFromFormId(String formId);

    String getSonFromTableId(String formId);

    String getSonFormTableName(String formId);
    
    List<DpTableField> getSonFormFieldFromFormId(String formId);

    AMSInstanceDefinitionInfo getInstanceDefinitionById(String definitionId);

}
