package cn.trasen.ams.common.validator.dict;

import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * <AUTHOR>
 * @description 验证字典值是否有效
 * @date 2023/12/6 15:51
 */
public class DictExistValidator implements ConstraintValidator<DictExistValid, Object> {

    // 系统编码
    private String code;
    @Autowired
    DictService dictService;

    @Override
    public void initialize(DictExistValid constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        code = constraintAnnotation.code();

    }

    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        if (!(o instanceof String)) {
            return false;
        }
        if (StringUtils.isBlank(o.toString())) {
            // 为空的情况不在检测真实性范围内，直接返回true
            return true;
        }
        // 增加一层缓存
        try {
            List<DictItemResp> dictItemResps = dictService.cgetKL(code);

            if (dictItemResps == null || dictItemResps.size() <= 0) {
                return false;
            }

            for (DictItemResp dictItemResp : dictItemResps) {
//                if (dictItemResp.getItemCode().equals(o.toString())) {
//                    return true;
//                }
                if (dictItemResp.getItemNameValue().equals(o.toString())) {
                    return true;
                }
            }
            return false;

        } catch (Exception e) {
            throw new BusinessException("获取字典项失败", e);
        }

    }
}
