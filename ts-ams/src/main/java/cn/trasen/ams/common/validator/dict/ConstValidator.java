package cn.trasen.ams.common.validator.dict;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @projectName: xtbg
 * @package: cn.trasen.hrms.performance.validator.dict
 * @className: ConstantValidator
 * @author: chenbin
 * @description: 主要用来检测传入的变量是否在约定的常量数组内【只支持String类型常量】
 * @date: 2023/12/7 15:03
 * @version: 1.0
 */

public class ConstValidator implements ConstraintValidator<ConstValid, String> {

    private String[] constant;

    @Override
    public void initialize(ConstValid constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        constant = constraintAnnotation.constant();
    }

    @Override
    public boolean isValid(String o, ConstraintValidatorContext constraintValidatorContext) {

        if (o == null) {
            return true;
        }

        for (String s : constant) {
            if (s.equals(o)) {
                return true;
            }
        }
        return false;
    }
}
