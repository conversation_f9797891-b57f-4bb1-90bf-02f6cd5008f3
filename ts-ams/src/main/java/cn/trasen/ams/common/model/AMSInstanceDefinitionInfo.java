package cn.trasen.ams.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.model
 * @className: AMSDefinitionInfo
 * @author: chenbin
 * @description: TODO
 * @date: 2025/5/27 15:09
 * @version: 1.0
 */
@Data
public class AMSInstanceDefinitionInfo {
    /**
     * 流程定义ID
     */
    @Id
    @Column(name = "wf_definition_id")
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 流程编号
     */
    @Column(name = "workflow_no")
    @ApiModelProperty(value = "流程编号")
    private String workflowNo;

    /**
     * 流程名称
     */
    @Column(name = "workflow_name")
    @ApiModelProperty(value = "流程名称")
    private String workflowName;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer version;

    /**
     * 是否最新版本(1:是；0否)
     */
    @Column(name = "is_last_version")
    @ApiModelProperty(value = "是否最新版本(1:是；0否)")
    private Integer isLastVersion;


    @Column(name = "is_hide_content")
    @ApiModelProperty(value = "是否对发起人隐藏(0:否；1是)")
    private Integer isHideContent;


    @Column(name = "show_print_watermark")
    @ApiModelProperty(value = "打印是否显示水印(0:否；1是)")
    private Integer showPrintWatermark;

    @Column(name = "is_external")
    @ApiModelProperty(value = "是否第三方发起的流程(0:否；1是)")
    private Integer isExternal;

    @Column(name = "notify_start_url")
    @ApiModelProperty(value = "流程发起回调url")
    private String notifyStartUrl;

    @Column(name = "return_url")
    @ApiModelProperty(value = "流程退回回调url")
    private String returnUrl;

    /**
     * 流程图XML
     */
    @ApiModelProperty(value = "流程图XML")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 流程完成后通知地址
     */
    @Column(name = "notify_url")
    @ApiModelProperty(value = "流程完成后通知地址")
    private String notifyUrl;

    /**
     * 流程PAGE URL
     */
    @Column(name = "page_url")
    @ApiModelProperty(value = "pageUrl")
    private String pageUrl;

    /**
     * 自定义服务
     */
    @Column(name = "custom_service")
    @ApiModelProperty(value = "自定义服务")
    private String customService;

    /**
     * 抄送人员
     */
    @Column(name = "copy_to_user")
    @ApiModelProperty(value = "抄送人员")
    private String copyToUser;

    /**
     * 抄送人员姓名
     */
    @Column(name = "copy_to_user_name")
    @ApiModelProperty(value = "抄送人员姓名")
    private String copyToUserName;

    /**
     * 开发配置 1-开启移动端；2-结束归档；3-启用电子签名;4-外部流程 5-启用动态签名
     */
    @Column(name = "exploit_configuration")
    @ApiModelProperty(value = "开发配置 1-开启移动端；2-结束归档；3-启用电子签名;4-外部流程 5-启用动态签名 6-不允许重复发起")
    private String exploitConfiguration;

    /**
     * 归档类型
     */
    @Column(name = "archive_type")
    @ApiModelProperty(value = "归档类型")
    private String archiveType;

    /**
     * 流程分类
     */
    @Column(name = "workflow_classify")
    @ApiModelProperty(value = "流程分类")
    private String workflowClassify;

    /**
     * 流程标题模板
     */
    @Column(name = "workflow_title_temp")
    @ApiModelProperty(value = "流程标题模板")
    private String workflowTitleTemp;

    /**
     * 流程图片URL
     */
    @Column(name = "wf_picture_url")
    @ApiModelProperty(value = "流程图片URL")
    private String wfPictureUrl;

    /**
     * PC端-发起页面Url
     */
    @Column(name = "initiate_page_url")
    @ApiModelProperty(value = "发起页面Url")
    private String initiatePageUrl;

    /**
     * 手机端-发起页面Url
     */
    @Column(name = "mobile_initiate_page_url")
    @ApiModelProperty(value = "发起页面Url")
    private String mobileInitiatePageUrl;

    /**
     * PC端-审批页面Url
     */
    @Column(name = "examine_page_url")
    @ApiModelProperty(value = "审批页面Url")
    private String examinePageUrl;

    /**
     * 手机端-审批页面Url
     */
    @Column(name = "mobile_examine_page_url")
    @ApiModelProperty(value = "审批页面Url")
    private String mobileExaminePageUrl;

    /**
     * PC端-查看详情页面Url
     */
    @Column(name = "detail_page_url")
    @ApiModelProperty(value = "查看详情页面Url")
    private String detailPageUrl;

    /**
     * 手机端-查看详情页面Url
     */
    @Column(name = "mobile_detail_page_url")
    @ApiModelProperty(value = "查看详情页面Url")
    private String mobileDetailPageUrl;

    /**
     * 是否为普通流程 N-否；Y-是
     */
    @Column(name = "is_normal")
    @ApiModelProperty(value = "是否为普通流程 N-否；Y-是")
    private String isNormal;


    @Column(name = "is_cancel")
    @ApiModelProperty(value = "发起人是否可以撤销 0否1是")
    private String isCancel;

    /**
     * 云模板ID
     */
    @Column(name = "CLOUD_TEMPLATE_ID")
    @ApiModelProperty(value = "云模板ID")
    private String cloudTemplateId;

    /**
     * 表单ID
     */
    @Column(name = "FORM_ID")
    @ApiModelProperty(value = "表单ID")
    private String formId;

    @Column(name = "cron")
    @ApiModelProperty(value = "cron表达式")
    private String cron;

    @Column(name = "recipient")
    @ApiModelProperty(value = "消息接收人")
    private String recipient;

    @Column(name = "recipient_name")
    @ApiModelProperty(value = "消息接收人名称")
    private String recipientName;

    @Column(name = "message_content")
    @ApiModelProperty(value = "消息文本")
    private String messageContent;


    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序号")
    private String seqNo;


    /**
     * 状态 1-启用；2-禁用
     */
    @ApiModelProperty(value = "状态 1-启用；2-禁用")
    private Integer status;
}
