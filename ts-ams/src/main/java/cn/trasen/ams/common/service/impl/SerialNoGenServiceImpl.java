package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.service.StepperService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: SerialNoGenServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 16:41
 * @version: 1.0
 */
@Service
public class SerialNoGenServiceImpl implements SerialNoGenService {

    @Autowired
    private StepperService stepperService;

    private String keyPrefix = "ams:serialNo:"; // Redis键前缀


    private String key4sso(String key) {
        String orgCode = "def";
        // 多机构支持
        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                orgCode = user.getCorpcode();
            }
        } catch (Exception e) {
            // 什么都不做
        }
        return keyPrefix + orgCode + ":" + key;

    }

    @Override
    public String genByDate(String key) {
        key = key + CommonUtil.getCurDate("yyyyMMdd");
        long step = stepperService.step(key, 86400);
        String code = String.format("%s%04d", key, step);
        return code;
    }

    @Override
    public String genByDate(String key, String tpl, int length) {
        // 参数校验
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("key不能为空");
        }

        if (tpl == null || tpl.trim().isEmpty()) {
            tpl = "yyyyMMdd"; // 默认日期格式
        }

        if (length <= 0) {
            length = 4; // 默认序列号长度
        }

        // 根据模板格式化当前日期
        String dateStr = formatCurrentDate(tpl);

        // 构建Redis键，包含日期信息以确保每天重新计数
        String redisKey = key4sso(key + "-" + dateStr);

        // 计算过期时间：根据日期模板确定合适的过期时间
        int expireSeconds = calculateExpireSeconds(tpl);

        // 获取步进序列号
        long step = stepperService.step(redisKey, expireSeconds);

        // 生成最终的序列号：key + 格式化日期 + 补零的序列号
        String serialNo = String.format("%s%s%0" + length + "d", key, dateStr, step);

        return serialNo;
    }


    @Override
    public String genCommonNo(String key, int length) {
        long step = stepperService.step(key, 0);
        String code = String.format("%s%0" + length + "d", key, step);
        return code;
    }

    @Override
    public String genAssetNo() {
        // 获取当前年份 只保留后两位
        String year = CommonUtil.getCurDate("yy");
        String key = "ASSET_NO" + year;
        // 防止闰月？好像阳历根本不需要关心，但是这个数据很小，多存一段时间也无所谓
        long step = stepperService.step(key, 86400 * 400);
        // 5位数字
        String code = String.format("%s%06d", year, step);
        return code;
    }

    /**
     * 根据模板格式化当前日期
     *
     * @param tpl 日期模板，如 "yyyyMMdd", "yyyy-MM-dd", "yyMM" 等
     * @return 格式化后的日期字符串
     */
    private String formatCurrentDate(String tpl) {
        try {
            return CommonUtil.getCurDate(tpl);
        } catch (Exception e) {
            // 如果模板格式有误，使用默认格式
            return CommonUtil.getCurDate("yyyyMMdd");
        }
    }

    /**
     * 根据日期模板计算Redis键的过期时间
     *
     * @param tpl 日期模板
     * @return 过期时间（秒）
     */
    private int calculateExpireSeconds(String tpl) {
        // 根据日期模板的精度确定过期时间
        if (tpl.contains("yyyy") || tpl.contains("yy")) {
            if (tpl.contains("MM") || tpl.contains("M")) {
                if (tpl.contains("dd") || tpl.contains("d")) {
                    // 包含年月日，按天过期
                    return 86400; // 24小时
                } else {
                    // 包含年月，按月过期
                    return 86400 * 31; // 31天
                }
            } else {
                // 只包含年，按年过期
                return 86400 * 366; // 366天（考虑闰年）
            }
        } else if (tpl.contains("MM") || tpl.contains("M")) {
            if (tpl.contains("dd") || tpl.contains("d")) {
                // 包含月日，按天过期
                return 86400; // 24小时
            } else {
                // 只包含月，按月过期
                return 86400 * 31; // 31天
            }
        } else if (tpl.contains("dd") || tpl.contains("d")) {
            // 只包含日，按天过期
            return 86400; // 24小时
        } else {
            // 其他情况，默认按天过期
            return 86400; // 24小时
        }
    }
}
