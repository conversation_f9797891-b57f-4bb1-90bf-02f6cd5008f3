package cn.trasen.ams.common.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.constant
 * @className: PermissionConst
 * @author: chenbin
 * @description: TODO
 * @date: 2025/4/25 09:54
 * @version: 1.0
 */

public class PermissionConst {

    public static final String ZCSB_ADMIN_CODE = "ZCSB_ADMIN";

    public static final String ROLE_SKU_TYPE = "AMS_ROLE_SKU_TYPE";

    public static final String BUSINESS_ROLE = "AMS_BUSINESS_ROLE";

    public static final String TYPE_ALL = "ALL";

    public static final String TYPE_SKU_TYPE = "SKU_TYPE";

    public static final String TYPE_DEPT = "DEPT";

    public static final String TYPE_SELF = "SELF";

    // 个人
    public static final String SSO_AUTH_SELF = "SELF";

    // 科室
    public static final String SSO_AUTH_SELF_DEPT = "SELF_DEPT";

    // 科室及下级
    public static final String SSO_AUTH_SELF_SUB_DEPT = "SELF_SUB_DEPT";


    // 一级业务
    public static final String 业务类型_审核出库单 = "审核出库单";

    public static final String 业务类型_审核入库单 = "审核入库单";

    public static final String 业务类型_审核资产转移单 = "审核资产转移单";

    public static final String 业务类型_审核资产处置单 = "审核资产处置单";

    public static final String 业务类型_资产盘点 = "资产盘点";

    public static final String 业务类型_台账管理 = "台账管理";

    public static final String 业务类型_采购申请 = "采购申请";

    public static final String 业务类型_采购订单 = "采购订单";

    public static final String 业务类型_安装验收 = "安装验收";


    // 二级业务类型，防止业务配置太多，很多的二级业务实际上是共用一个一级业务的，一级业务需要配置字典，防止泛滥，和配置复杂性，这里二级业务内部进行映射

    public static final String 二级业务类型_资产台账列表 = "资产台账列表";

    public static final String 二级业务类型_资产处置单创建选择资产 = "资产处置单创建选择资产";

    public static final String 二级业务类型_资产转移单创建选择资产 = "资产转移单创建选择资产";

    public static final String 二级业务类型_资产出库单选择资产 = "资产出库单选择资产";

    public static final String 二级业务类型_资产退回入库单选择资产 = "资产退回入库单选择资产";

    public static final String 二级业务类型_出库单列表 = "出库单列表";

    public static final String 二级业务类型_入库单列表 = "入库单列表";

    public static final String 二级业务类型_资产处置单列表 = "资产处置单列表";

    public static final String 二级业务类型_盘点列表 = "盘点列表";

    public static final String 二级业务类型_资产转移单列表 = "资产转移单列表";

    public static final String 二级业务类型_安装验收采购入库登记列表 = "安装验收采购入库登记列表";

    public static final String 二级业务类型_安装验收其他入库登记列表 = "安装验收其他入库登记列表";

    public static final String 二级业务类型_安装验收已登记 = "安装验收已登记";


}
