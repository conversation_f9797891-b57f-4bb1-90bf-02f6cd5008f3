package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.outin.OutBoundOrderListReq;
import cn.trasen.ams.device.bean.outin.OutboundOrderInsertReq;
import cn.trasen.ams.device.model.InboundOrder;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.OutboundOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutboundOrderService
 * @Description TODO
 * @date 2025年2月11日 下午5:25:38
 */
public interface OutboundOrderService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    Integer save(OutboundOrder record);

    String insert(OutboundOrderInsertReq outboundOrderInsertReq);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    Integer update(OutboundOrder record);

    String edit(OutboundOrderInsertReq outboundOrderInsertReq);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return OutboundOrder
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    OutboundOrder selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<OutboundOrder>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    DataSet<OutboundOrder> getDataSetList(Page page, OutBoundOrderListReq record);

    List<OutboundOrder> getListNoPage(OutBoundOrderListReq record);

    void createByInBoundOrder(InboundOrder inboundOrder);


    void confirm(String id);

    void batchConfirm(List<String> ids);

    void dataFmt(OutboundOrder outboundOrder);


}
