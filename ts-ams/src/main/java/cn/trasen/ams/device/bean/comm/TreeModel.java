package cn.trasen.ams.device.bean.comm;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.category22
 * @className: treeNode
 * @author: chenbin
 * @description: TODO
 * @date: 2025/1/23 09:32
 * @version: 1.0
 */

@Data
public class TreeModel {
    private String id;
    private String code;
    private String name;
    private boolean open;
    private String icon;
    private String aliasname;
    private String pid;
    private String alink;
    private Integer menulevel;
    private String isEnable;
    private String iconSkin;
    private boolean checked;
    private boolean isParent;
    private Map userData;
    private List<TreeModel> children;
    private Integer sort;

    // 首拼
    private String sp;

    // 全拼
    private String qp;

    public int compareTo(TreeModel o) {
        if (this.sort != null && o.sort != null) {
            return this.sort >= o.sort ? 1 : -1;
        } else {
            return 0;
        }
    }
}
