package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.service.SkuService;
import cn.trasen.homs.core.exception.BusinessException;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Brand;
import cn.trasen.ams.common.service.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BrandController
 * @Description 品牌控制器
 * @date 2024年9月3日 下午3:22:26
 */
@RestController
@Api(tags = "BrandController")
public class BrandController {

    private transient static final Logger logger = LoggerFactory.getLogger(BrandController.class);

    @Autowired
    private BrandService brandService;

    @Autowired
    private SkuService skuService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveBrand
     * @Description 新增
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/brand/save")
    public PlatformResult<String> saveBrand(@Valid @RequestBody Brand record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            String id = brandService.save(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateBrand
     * @Description 编辑
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/brand/update")
    public PlatformResult<String> updateBrand(@Valid @RequestBody Brand record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            brandService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Brand>
     * @Title selectBrandById
     * @Description 根据ID查询
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/brand/{id}")
    public PlatformResult<Brand> selectBrandById(@PathVariable String id) {
        try {
            Brand record = brandService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteBrandById
     * @Description 根据ID删除
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/brand/delete/{id}")
    public PlatformResult<String> deleteBrandById(@PathVariable String id) {
        try {
            boolean hasUse = skuService.hasUse("brand", id);

            if (hasUse) {
                throw new BusinessException("品牌已被使用，无法删除.");
            }
            
            brandService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Brand>
     * @Title selectBrandList
     * @Description 查询列表
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/brand/list")
    public DataSet<Brand> selectBrandList(Page page, Brand record) {
        record.setSysType(CommonConst.SYS_TYPE_ZCSB);
        return brandService.getDataSetList(page, record);
    }


    @ApiOperation(value = "列表（不分页）", notes = "列表（不分页）")
    @GetMapping("/api/device/brand/listNoPage")
    public PlatformResult<List<Brand>> selectBrandListNoPage(Page page, Brand record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            List<Brand> list = brandService.getList(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @GetMapping(value = "/api/device/brand/tpl")
    @ApiOperation(value = "品牌导入模板", notes = "品牌导入模板")
    public void tpl(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "品牌导入模板.xlsx";
            String template = "template/brandImportTpl.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/api/device/brand/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {
        List<Brand> imports = (List<Brand>) ImportExcelUtil.getExcelDatas(file, Brand.class);
        try {
            int count = brandService.importByExcel(imports, CommonConst.SYS_TYPE_ZCSB);
            return PlatformResult.success("信息导入成功，新增：" + count + "条");

        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }

    }


    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/brand/export")
    public ResponseEntity<byte[]> export(Brand record) throws IOException {

        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            List<Brand> exportList = brandService.getList(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/brandExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("品牌列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
