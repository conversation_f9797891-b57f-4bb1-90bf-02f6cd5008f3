<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InboundOrderMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.InboundOrder">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId"/>
        <result column="belong_to_orgid" jdbcType="VARCHAR" property="belongToOrgid"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="rela_id" jdbcType="VARCHAR" property="relaId"/>
        <result column="money" jdbcType="VARCHAR" property="money"/>
        <result column="supply_id" jdbcType="VARCHAR" property="supplyId"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="doer_id" jdbcType="VARCHAR" property="doerId"/>
        <result column="do_date" jdbcType="TIMESTAMP" property="doDate"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>

    <sql id="getListSql">
        select t1.*, t2.name as supplyName
        from d_inbound_order t1
        left join c_supplier t2 on t1.supply_id = t2.id
        where t1.is_deleted = 'N'
        <if test="batchNo != null and batchNo != ''">
            and t1.batch_no like concat('%', #{batchNo}, '%')
        </if>
        <if test="doerName != null and doerName != ''">
            and t1.doer_name like concat('%', #{doerName}, '%')
        </if>
        <if test="type != null and type != ''">
            and t1.type = #{type}
        </if>
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="start != null and start != ''">
            and t1.do_date between #{start} and #{end}
        </if>
        <if test="supplyId != null and supplyId != ''">
            and t1.supply_id = #{supplyId}
        </if>
        <if test="pmsql != null and pmsql != ''">
            ${pmsql}
        </if>
        order by t1.status asc,t1.create_date desc
    </sql>
    <select id="getList" parameterType="cn.trasen.ams.device.bean.outin.InBoundOrderListReq"
            resultType="cn.trasen.ams.device.model.InboundOrder">
        <include refid="getListSql"/>
    </select>

    <select id="getListNoPage" parameterType="cn.trasen.ams.device.bean.outin.InBoundOrderListReq"
            resultType="cn.trasen.ams.device.model.InboundOrder">
        <include refid="getListSql"/>
    </select>

</mapper>