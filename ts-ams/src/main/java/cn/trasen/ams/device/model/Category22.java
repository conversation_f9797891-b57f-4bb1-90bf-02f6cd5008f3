package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_category22")
@Setter
@Getter
public class Category22 {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 上级ID
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "上级ID")
    private String parentId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Boolean level;

    /**
     * 分类代码
     */
    @ApiModelProperty(value = "分类代码")
    private String code;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;


    @ApiModelProperty(value = "首拼")
    private String sp;

    @ApiModelProperty(value = "全拼")
    private String qp;

    /**
     * 一般用途
     */
    @Column(name = "use_to")
    @ApiModelProperty(value = "一般用途")
    private String useTo;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sord;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 所有上级ID
     */
    @Column(name = "tree_ids")
    @ApiModelProperty(value = "所有上级ID")
    private String treeIds;

    /**
     * 分类描述
     */
    @ApiModelProperty(value = "分类描述")
    private String elx;

    /**
     * 品名举例
     */
    @Column(name = "sku_ex")
    @ApiModelProperty(value = "品名举例")
    private String skuEx;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}