package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.inventory.InventoryPlanCateResp;
import cn.trasen.ams.device.bean.inventory.InventoryPlanOrgResp;
import cn.trasen.ams.device.model.InventoryPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface InventoryPlanMapper extends Mapper<InventoryPlan> {

    List<InventoryPlanOrgResp> selectOrgIdList(@Param("inventoryPlanId") String inventoryPlanId, @Param("statusExt") String statusExt);

    List<InventoryPlanCateResp> selectCategoryIdList(@Param("inventoryPlanId") String inventoryPlanId, @Param("orgId") String orgId, @Param("statusExt") String statusExt);

    List<InventoryPlanCateResp> selectCategory22IdList(@Param("inventoryPlanId") String inventoryPlanId, @Param("orgId") String orgId, @Param("statusExt") String statusExt);

}