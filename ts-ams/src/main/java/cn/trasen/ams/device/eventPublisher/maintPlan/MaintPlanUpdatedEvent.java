package cn.trasen.ams.device.eventPublisher.maintPlan;

import cn.trasen.ams.device.model.MaintPlan;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.event.maint
 * @className: UpdatedEvent
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 09:46
 * @version: 1.0
 */
@Getter
public class MaintPlanUpdatedEvent extends ApplicationEvent {
    private MaintPlan maintPlan;

    public MaintPlanUpdatedEvent(MaintPlan maintPlan) {
        super(maintPlan);
        this.maintPlan = maintPlan;
    }
}
