package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffDeviceListReq;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffPurchaseOrder;
import cn.trasen.ams.device.bean.signoff.SignoffExt;
import cn.trasen.ams.device.bean.signoff.SignoffInsertReq;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.SignoffDetail;
import cn.trasen.ams.device.service.SignoffDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Signoff;
import cn.trasen.ams.device.service.SignoffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SignoffController
 * @Description TODO
 * @date 2025年7月7日 上午11:36:34
 */
@RestController
@Api(tags = "SignoffController")
public class SignoffController {

    private transient static final Logger logger = LoggerFactory.getLogger(SignoffController.class);

    @Autowired
    private SignoffService signoffService;

    @Autowired
    private SignoffDetailService signoffDetailService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveSignoff
     * @Description 新增
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "安装验收新增", notes = "安装验收新增")
    @PostMapping("/api/device/signoff/save")
    public PlatformResult<String> saveSignoff(@RequestBody SignoffInsertReq record) {
        try {
            signoffService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateSignoff
     * @Description 编辑
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "安装验收编辑", notes = "安装验收编辑")
    @PostMapping("/api/device/signoff/update")
    public PlatformResult<String> updateSignoff(@RequestBody SignoffInsertReq record) {
        try {
            signoffService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Signoff>
     * @Title selectSignoffById
     * @Description 根据ID查询
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "安装验收详情", notes = "安装验收详情")
    @GetMapping("/api/device/signoff/{id}")
    public PlatformResult<SignoffInsertReq> selectSignoffById(@PathVariable String id) {
        try {
            Signoff signoff = signoffService.selectById(id);
            List<SignoffDetail> signoffDetailList = signoffDetailService.selectBySignoffId(id);

            SignoffInsertReq res = new SignoffInsertReq();
            res.setSignoff(signoff);
            res.setSignoffDetailList(signoffDetailList);

            return PlatformResult.success(res);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteSignoffById
     * @Description 根据ID删除
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "安装验收撤销", notes = "安装验收撤销")
    @PostMapping("/api/device/signoff/delete/{id}")
    public PlatformResult<String> deleteSignoffById(@PathVariable String id) {
        try {
            signoffService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Signoff>
     * @Title selectSignoffList
     * @Description 查询列表
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "安装验收已登记列表", notes = "安装验收已登记列表")
    @GetMapping("/api/device/signoffed/list")
    public DataSet<SignoffExt> selectSignoffedList(Page page, SignoffExt record) {
        return signoffService.selectSignoffedList(page, record);
    }


    /**
     * @param page
     * @param record
     * @return DataSet<Signoff>
     * @Title selectSignoffPenddingList
     * @Description 查询待安装验收的订单
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "待安装验收采购订单列表", notes = "待安装验收采购订单列表")
    @GetMapping("/api/device/signoff/pendding/list")
    public DataSet<PenddingSignoffPurchaseOrder> selectSignoffPenddingList(Page page, PurchaseOrder record) {
        return signoffService.getPenddingDataSetList(page, record);
    }

    /**
     * @param purchaseOrderId
     * @return List<DeviceExtResp>
     * @Title getPenddingSignoffDeviceList
     * @Description 获取待验收资产列表
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    @ApiOperation(value = "待安装验收资产列表", notes = "待安装验收资产列表")
    @PostMapping("/api/device/signoff/pendding/device/list/{purchaseOrderId}")
    public PlatformResult<List<DeviceExtResp>> getPenddingSignoffDeviceList(
            @PathVariable String purchaseOrderId,
            @RequestBody PenddingSignoffDeviceListReq req
    ) {
        try {
            req.setPurchaseOrderId(purchaseOrderId);
            return PlatformResult.success(signoffService.getPenddingSignoffDeviceList(req));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "确认安装验收", notes = "确认安装验收")
    @GetMapping("/api/device/signoff/sure/{purchaseOrderId}")
    public PlatformResult sureSignoff(@PathVariable String purchaseOrderId) {
        try {
            signoffService.sure(purchaseOrderId);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
