package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.service.SkuService;
import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BrandController
 * @Description 公共控制器
 * @date 2024年9月3日 下午3:22:26
 */
@RestController
@Api(tags = "CommController")
public class CommController {

    private transient static final Logger logger = LoggerFactory.getLogger(CommController.class);

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private SkuService skuService;

    /**
     * @return DataSet<Brand>
     * @Title selectBrandList
     * @Description 查询列表
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/comm/permission")
    public PlatformResult selectPermission(@RequestParam String business) {
        PermissionResp permissionResp = permissionService.cgetPermission(business);
        return PlatformResult.success(permissionResp);
    }


    /**
     * @return DataSet<Brand>
     * @Title selectBrandList
     * @Description 查询列表
     * @date 2024年9月3日 下午3:22:26
     * <AUTHOR>
     */
    @ApiOperation(value = "获取sku_type列表", notes = "获取sku_type列表")
    @GetMapping("/api/device/comm/skuTypeList")
    public PlatformResult selectSkuTypeList(@RequestParam(required = false) String logic) {
        return PlatformResult.success(skuService.getSkuTypeList(logic));
    }


}
