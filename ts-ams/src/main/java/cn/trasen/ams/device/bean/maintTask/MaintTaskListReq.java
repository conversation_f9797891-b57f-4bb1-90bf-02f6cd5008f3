package cn.trasen.ams.device.bean.maintTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.maintTask
 * @className: MaintTaskListReq
 * @author: chenbin
 * @description: 工单查询结构体
 * @date: 2024/12/14 17:15
 * @version: 1.0
 */

@Data
public class MaintTaskListReq {
    @ApiModelProperty(value = "设备名称")
    private String category22Id;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    @ApiModelProperty(value = "所属科室")
    private String belongToOrgId;

    @ApiModelProperty(value = "计划ID")
    private String maintPlanId;

    @ApiModelProperty(value = "获取异常任务 exp = 1")
    private String exp;

    @ApiModelProperty(value = "处理方案 1 暂不处理 2 转报修 3 转报废 4 暂未处理")
    private String solution;
}
