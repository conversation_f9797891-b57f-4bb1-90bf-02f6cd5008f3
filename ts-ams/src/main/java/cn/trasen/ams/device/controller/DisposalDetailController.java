package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.ams.device.service.DisposalDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName DisposalDetailController
 * @Description TODO
 * @date 2025年4月27日 下午4:49:48
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "DisposalDetailController")
public class DisposalDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(DisposalDetailController.class);

	@Autowired
	private DisposalDetailService disposalDetailService;

	/**
	 * @Title saveDisposalDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年4月27日 下午4:49:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/disposalDetail/save")
	public PlatformResult<String> saveDisposalDetail(@RequestBody DisposalDetail record) {
		try {
			disposalDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateDisposalDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年4月27日 下午4:49:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/disposalDetail/update")
	public PlatformResult<String> updateDisposalDetail(@RequestBody DisposalDetail record) {
		try {
			disposalDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectDisposalDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<DisposalDetail>
	 * @date 2025年4月27日 下午4:49:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/disposalDetail/{id}")
	public PlatformResult<DisposalDetail> selectDisposalDetailById(@PathVariable String id) {
		try {
			DisposalDetail record = disposalDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteDisposalDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年4月27日 下午4:49:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/disposalDetail/delete/{id}")
	public PlatformResult<String> deleteDisposalDetailById(@PathVariable String id) {
		try {
			disposalDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectDisposalDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<DisposalDetail>
	 * @date 2025年4月27日 下午4:49:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/disposalDetail/list")
	public DataSet<DisposalDetail> selectDisposalDetailList(Page page, DisposalDetail record) {
		return disposalDetailService.getDataSetList(page, record);
	}
}
