package cn.trasen.ams.device.service;

import cn.trasen.ams.common.bean.ImportResp;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Sku;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SkuService
 * @Description TODO
 * @date 2024年9月9日 下午3:43:43
 */
public interface SkuService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月9日 下午3:43:43
     * <AUTHOR>
     */
    String save(Sku record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月9日 下午3:43:43
     * <AUTHOR>
     */
    Integer update(Sku record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月9日 下午3:43:43
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Sku
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月9日 下午3:43:43
     * <AUTHOR>
     */
    Sku selectById(String id);

    Sku selectOneById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Sku>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月9日 下午3:43:43
     * <AUTHOR>
     */
    DataSet<Sku> getDataSetList(Page page, Sku record);

    List<Sku> getList(Sku record);

    ImportResp importByExcel(List<Sku> list);

    void autoFillCode();

    Boolean hasUse(String relationType, String relationId);

    List<DictItemResp> getSkuTypeList(String logic);

    List<Sku> searchSkuList(String skuType,String keyword, String selectedId);
}
