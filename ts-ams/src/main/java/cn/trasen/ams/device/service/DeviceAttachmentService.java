package cn.trasen.ams.device.service;

import cn.trasen.ams.device.model.Device;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.DeviceAttachment;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceAttachmentService
 * @Description TODO
 * @date 2024年9月11日 上午10:42:47
 */
public interface DeviceAttachmentService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月11日 上午10:42:47
     * <AUTHOR>
     */
    Integer save(DeviceAttachment record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月11日 上午10:42:47
     * <AUTHOR>
     */
    Integer update(DeviceAttachment record);

    void replaceByDeviceId(List<DeviceAttachment> list, Device device);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月11日 上午10:42:47
     * <AUTHOR>
     */
    Integer deleteById(String id);

    Integer deleteByDeviceId(String deviceId);

    /**
     * @return DeviceAttachment
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月11日 上午10:42:47
     * <AUTHOR>
     */
    DeviceAttachment selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DeviceAttachment>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月11日 上午10:42:47
     * <AUTHOR>
     */
    DataSet<DeviceAttachment> getDataSetList(Page page, DeviceAttachment record);

    List<DeviceAttachment> selectListByDeviceIdOrSkuId(String deviceId, String skuId);

    List<DeviceAttachment> selectListBySkuId(String skuId);
}
