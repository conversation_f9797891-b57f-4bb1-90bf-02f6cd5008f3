<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.PurchaseOrderMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.PurchaseOrder">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="definition_id" jdbcType="VARCHAR" property="definitionId"/>
        <result column="form_id" jdbcType="VARCHAR" property="formId"/>
        <result column="son_table_id" jdbcType="VARCHAR" property="sonTableId"/>
        <result column="main_table_name" jdbcType="VARCHAR" property="mainTableName"/>
        <result column="son_table_name" jdbcType="VARCHAR" property="sonTableName"/>
        <result column="rule" jdbcType="VARCHAR" property="rule"/>
        <result column="flow_no" jdbcType="VARCHAR" property="flowNo"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="purchase_type" jdbcType="VARCHAR" property="purchaseType"/>
        <result column="sku_type" jdbcType="VARCHAR" property="skuType"/>
        <result column="sku_nums" jdbcType="VARCHAR" property="skuNums"/>
        <result column="total_nums" jdbcType="INTEGER" property="totalNums"/>
        <result column="asset_total_value" jdbcType="DECIMAL" property="assetTotalValue"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="supplier_contactor" jdbcType="VARCHAR" property="supplierContactor"/>
        <result column="supplier_mobile" jdbcType="VARCHAR" property="supplierMobile"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="r_org_id" jdbcType="VARCHAR" property="rOrgId"/>
        <result column="r_org_name" jdbcType="VARCHAR" property="rOrgName"/>
        <result column="r_dept_name" jdbcType="VARCHAR" property="rDeptName"/>
        <result column="r_dept_id" jdbcType="VARCHAR" property="rDeptId"/>
        <result column="r_create_user" jdbcType="VARCHAR" property="rCreateUser"/>
        <result column="r_create_user_name" jdbcType="VARCHAR" property="rCreateUserName"/>
        <result column="is_mapper_dict" jdbcType="CHAR" property="isMapperDict"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="sku_name_set" jdbcType="LONGVARCHAR" property="skuNameSet"/>
    </resultMap>
    <select id="getNumsByStatus" resultType="java.lang.Integer">
        select count(*) from d_purchase_order t1
        where t1.`is_deleted` = 'N'
        and t1.`definition_id` = #{definitionId}
        and t1.`status` = #{status}
        <if test="permissionSql != null and permissionSql != ''">
            ${sql}
        </if>
    </select>
    <update id="writeBackContractNo">
        UPDATE d_purchase_order po
            JOIN ts_base_oa.toa_ctt_contract c
        ON po.id = c.sys_id
            SET po.contract_id = c.id
        WHERE c.sys_id IS NOT NULL;
    </update>

    <!-- 批量完成采购订单 -->
    <update id="batchComplete">
        UPDATE d_purchase_order
        SET status = #{status},
        update_date = NOW(),
        update_user = #{updateUser},
        update_user_name = #{updateUserName}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 'N'
        AND status = '1'  <!-- 只能完成进行中的订单 -->
    </update>

    <select id="getNoContract3ML" resultType="java.lang.Integer">
        select count(*)
        from d_purchase_order t1
                 left join ts_base_oa.toa_ctt_contract t2 on t1.id = t2.sys_id
        where t1.`is_deleted` = 'N'
          and t2.no is null
          and t1.`create_date` &lt; DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
          and t1.`definition_id` = #{definitionId}
    </select>

    <select id="getList" parameterType="cn.trasen.ams.device.model.PurchaseOrder"
            resultType="cn.trasen.ams.device.model.PurchaseOrder">
        SELECT t1.*,t2.id as contract_id,t2.no as contract_no,t2.create_date as contract_create_date,t2.update_date as contract_update_date
        FROM d_purchase_order t1
        left join ts_base_oa.toa_ctt_contract t2 on t1.id = t2.sys_id
        left join c_supplier t3 on t3.id = t1.supplier_id
        WHERE t1.is_deleted = 'N'
        <if test="definitionId != null and definitionId != ''">
            AND t1.definition_id = #{definitionId}
        </if>
        <if test="status != null and status != ''">
            AND t1.status = #{status}
        </if>
        <!-- 流水号 -->
        <if test="flowNo != null and flowNo != ''">
            AND t1.flow_no LIKE CONCAT('%', #{flowNo}, '%')
        </if>
        <!-- 订单类型 -->
        <if test="orderType != null and orderType != ''">
            AND t1.order_type = #{orderType}
        </if>
        <!-- 采购项目名称 -->
        <if test="name != null and name != ''">
            AND t1.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <!-- 采购方式 -->
        <if test="purchaseType != null and purchaseType != ''">
            AND t1.purchase_type = #{purchaseType}
        </if>
        <!-- 资产类别 -->
        <if test="skuType != null and skuType != ''">
            AND t1.sku_type = #{skuType}
        </if>
        <!-- 资金来源 -->
        <if test="fundSource != null and fundSource != ''">
            AND t1.fund_source = #{fundSource}
        </if>
        <!-- 供应商ID -->
        <if test="supplierId != null and supplierId != ''">
            AND t1.supplier_id = #{supplierId}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND t3.name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <!-- 供应商联系人 -->
        <if test="supplierContactor != null and supplierContactor != ''">
            AND t1.supplier_contactor LIKE CONCAT('%', #{supplierContactor}, '%')
        </if>
        <!-- 供应商手机 -->
        <if test="supplierMobile != null and supplierMobile != ''">
            AND t1.supplier_mobile LIKE CONCAT('%', #{supplierMobile}, '%')
        </if>
        <!-- 相关机构ID -->
        <if test="rOrgId != null and rOrgId != ''">
            AND t1.r_org_id = #{rOrgId}
        </if>
        <if test="rOrgName != null and rOrgName != ''">
            AND t1.r_org_name LIKE CONCAT('%', #{rOrgName}, '%')
        </if>
        <!-- 相关科室ID -->
        <if test="rDeptId != null and rDeptId != ''">
            AND t1.r_dept_id = #{rDeptId}
        </if>
        <!-- 相关科室名称 -->
        <if test="rDeptName != null and rDeptName != ''">
            AND t1.r_dept_name LIKE CONCAT('%', #{rDeptName}, '%')
        </if>
        <!-- 创建时间范围（逗号分隔） -->
        <if test="createDateQuery != null and createDateQuery != ''">
            AND t1.create_date BETWEEN
            SUBSTRING_INDEX(#{createDateQuery}, ',', 1)
            AND
            SUBSTRING_INDEX(#{createDateQuery}, ',', -1)
        </if>
        <!-- 相关人 -->
        <if test="rCreateUser != null and rCreateUser != ''">
            AND t1.r_create_user = #{rCreateUser}
        </if>
        <if test="rCreateUserName != null and rCreateUserName != ''">
            AND t1.r_create_user_name LIKE CONCAT('%', #{rCreateUserName}, '%')
        </if>
        <!-- 采购明细 -->
        <if test="skuNameSet != null and skuNameSet != ''">
            AND t1.sku_name_set LIKE CONCAT('%', #{skuNameSet}, '%')
        </if>
        <if test="contractNo != null and contractNo != ''">
            AND t2.no LIKE CONCAT('%', #{contractNo}, '%')
        </if>
        ORDER BY t1.create_date DESC
    </select>

    <select id="waitDelivery" resultType="cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryResp">
        select id, flow_no
        from d_purchase_order
        where
        arrived_status != '2'
        and is_deleted = 'N'
        and is_mapper_dict = '1'
        and status != '2'
        <if test="skuType != null">
            and sku_type = #{skuType}
        </if>
        order by create_date desc
    </select>

    <select id="waitDeliveryItem" resultType="cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryItem">
        SELECT t1.id as sku_id,
        COALESCE(t1.name, '') as name,
        COALESCE(t1.unit, '') as unit,
        COALESCE(t1.model, '') as model,
        COALESCE(t2.name, '') as brand_name,
        COALESCE(t3.name, '') as manufacturer_name,
        COALESCE(t4.name, '') as category22_name,
        COALESCE(t5.name, '') as category_name,
        COALESCE(pod.nums, 0) AS total_purchase_nums,
        IFNULL(SUM(iod.num), 0) AS inbound_nums,
        (COALESCE(pod.nums, 0) - IFNULL(SUM(iod.num), 0)) AS wait_inbound_nums,
        COALESCE(pod.price, 0.00) AS price
        FROM d_purchase_order po
        INNER JOIN d_purchase_order_detail_merge pod ON po.id = pod.purchase_order_id AND pod.is_deleted != 'Y'
        LEFT JOIN d_inbound_order io
        ON io.rela_id = po.id  AND (io.is_deleted IS NULL OR io.is_deleted != 'Y')
        LEFT JOIN
        d_inbound_order_detail iod
        ON iod.inbound_order_id = io.id
        AND iod.sku_id = pod.sku_id
        AND (iod.is_deleted IS NULL OR iod.is_deleted != 'Y')
        LEFT JOIN
        d_sku as t1
        ON pod.sku_id = t1.id
        LEFT JOIN
        c_brand as t2
        ON t1.brand_id = t2.id
        LEFT JOIN
        c_manufacturer as t3
        ON t1.manufacturer_id = t3.id
        LEFT JOIN
        d_category22 as t4
        ON t1.category22_id = t4.id
        LEFT JOIN
        c_category as t5
        ON t1.category_id = t5.id
        WHERE
        po.is_deleted != 'Y'
        <if test="id != null">
            AND po.id = #{id}
        </if>
        GROUP BY
        po.id,
        po.flow_no,
        pod.id,
        pod.sku_id
        HAVING
        wait_inbound_nums
        > 0
        ORDER BY
        po.flow_no,
        pod.sku_id;
    </select>

    <update id="updatePurchaseOrderArrivedStatus">
        UPDATE d_purchase_order po
            JOIN (
            SELECT
            pod.purchase_order_id AS order_id,
            SUM(pod.nums) AS total_purchase_quantity,
            IFNULL(SUM(inbound.total_inbound), 0) AS total_inbound_quantity,
            CASE
            WHEN IFNULL(SUM(inbound.total_inbound), 0) = 0 THEN MAX(po.arrived_status)
            WHEN IFNULL(SUM(inbound.total_inbound), 0) &lt; SUM(pod.nums) THEN '1'
            ELSE '2'
            END AS new_status
            FROM
            d_purchase_order_detail_merge pod
            JOIN
            d_purchase_order po ON pod.purchase_order_id = po.id AND po.is_deleted != 'Y'
            LEFT JOIN (
            SELECT
            io.rela_id,
            iod.sku_id,
            SUM(iod.num) AS total_inbound
            FROM
            d_inbound_order io
            JOIN
            d_inbound_order_detail iod ON io.id = iod.inbound_order_id
            WHERE
            (io.is_deleted IS NULL OR io.is_deleted != 'Y')
            AND (iod.is_deleted IS NULL OR iod.is_deleted != 'Y')
            GROUP BY
            io.rela_id, iod.sku_id
            ) inbound ON inbound.rela_id = pod.purchase_order_id AND inbound.sku_id = pod.sku_id
            WHERE
            pod.is_deleted != 'Y'
            AND po.arrived_status != '2'
            GROUP BY
            pod.purchase_order_id
            ) AS status_calc ON po.id = status_calc.order_id
            SET
                po.arrived_status = status_calc.new_status,
                po.update_date = NOW(),
                po.update_user = 'admin'
        WHERE
            (po.arrived_status != status_calc.new_status OR po.arrived_status IS NULL)
          AND status_calc.new_status IS NOT NULL;
        update d_purchase_order set `status` = '2',  `update_date` = NOW()  where arrived_status = 2 and `status` != '2';
    </update>

    <select id="selectById"  resultType="cn.trasen.ams.device.model.PurchaseOrder">
        select t1.*,
               t2.id as contract_id,
               t2.no as contract_no
        from d_purchase_order t1
                 left join ts_base_oa.toa_ctt_contract t2 on t1.id = t2.sys_id
        where t1.`is_deleted` = 'N'
          and t1.`id` = #{id}
    </select>
</mapper>