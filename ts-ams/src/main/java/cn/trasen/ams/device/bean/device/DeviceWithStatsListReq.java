package cn.trasen.ams.device.bean.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.device
 * @className: DeviceListWithStatsReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/26 16:42
 * @version: 1.0
 */

@Data
public class DeviceWithStatsListReq extends DeviceListReq {

    @ApiModelProperty(value = "1 保养超期 2 巡检超期 3 计量超期")
    private String isOverdue;

}
