package cn.trasen.ams.device.bean.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseOrderWaitDeliveryResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/6/24 14:11
 * @version: 1.0
 */
@Data
public class PurchaseOrderWaitDeliveryResp {

    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "流水号")
    private String flowNo;
}
