package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.constant.DeviceAttachmentConst;
import cn.trasen.ams.device.model.Device;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.DeviceAttachmentMapper;
import cn.trasen.ams.device.model.DeviceAttachment;
import cn.trasen.ams.device.service.DeviceAttachmentService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceAttachmentServiceImpl
 * @Description TODO
 * @date 2024年9月11日 上午10:42:47
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeviceAttachmentServiceImpl implements DeviceAttachmentService {

    @Autowired
    private DeviceAttachmentMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DeviceAttachment record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DeviceAttachment record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void replaceByDeviceId(List<DeviceAttachment> list, Device device) {

        String deviceId = device.getId();
        String skuId = device.getSkuId();

        Assert.hasText(deviceId, "设备ID不能为空.");
        Assert.hasText(skuId, "设备字典ID不能为空.");


        // 删除本设备的
        Example exp1 = new Example(DeviceAttachment.class);
        Example.Criteria criteria = exp1.createCriteria();
        criteria.andEqualTo("deviceId", deviceId);
        mapper.deleteByExample(exp1);

        // 删除本款号的
        Example exp2 = new Example(DeviceAttachment.class);
        Example.Criteria criteriaForSku = exp2.createCriteria();
        criteriaForSku.andEqualTo("skuId", device.getSkuId()).andEqualTo("owner", DeviceAttachmentConst.OWNER_SKU);
        mapper.deleteByExample(exp2);


        if (list != null && !list.isEmpty()) {
            list.forEach(attachment -> {
                if (attachment.getDeviceId() != null && !attachment.getDeviceId().equals(deviceId)) {
                    // 这里可能会提交deviceId 不等于自己的，不需要理会
                    return;
                }
                attachment.setDeviceId(deviceId);
                attachment.setSkuId(skuId);
                save(attachment);
            });
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DeviceAttachment record = new DeviceAttachment();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Integer deleteByDeviceId(String deviceId) {
        Assert.hasText(deviceId, "设备ID不能为空.");
        DeviceAttachment record = new DeviceAttachment();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        Example example = new Example(DeviceAttachment.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deviceId", deviceId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        return mapper.updateByExampleSelective(record, example);
    }

    @Override
    public DeviceAttachment selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DeviceAttachment> getDataSetList(Page page, DeviceAttachment record) {
        Example example = new Example(DeviceAttachment.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        example.setOrderByClause("create_date desc");
        List<DeviceAttachment> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<DeviceAttachment> selectListByDeviceIdOrSkuId(String deviceId, String skuId) {
        Example example = new Example(DeviceAttachment.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("deviceId", deviceId).orEqualTo("skuId", skuId).andEqualTo("owner", DeviceAttachmentConst.OWNER_SKU);
        return mapper.selectByExample(example);
    }

    @Override
    public List<DeviceAttachment> selectListBySkuId(String skuId) {
        Example example = new Example(DeviceAttachment.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("skuId", skuId).andEqualTo("owner", DeviceAttachmentConst.OWNER_SKU);
        return mapper.selectByExample(example);
    }


}
