package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffDeviceListReq;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffPurchaseOrder;
import cn.trasen.ams.device.bean.signoff.SignoffExt;
import cn.trasen.ams.device.bean.signoff.SignoffInsertReq;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Signoff;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SignoffService
 * @Description TODO
 * @date 2025年7月7日 上午11:36:34
 */
public interface SignoffService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    Integer save(Signoff record);


    void insert(SignoffInsertReq record);

    void edit(SignoffInsertReq record);


    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    Integer update(Signoff record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Signoff
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    Signoff selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Signoff>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月7日 上午11:36:34
     * <AUTHOR>
     */
    DataSet<Signoff> getDataSetList(Page page, Signoff record);

    DataSet<SignoffExt> selectSignoffedList(Page page, SignoffExt record);


    DataSet<PenddingSignoffPurchaseOrder> getPenddingDataSetList(Page page, PurchaseOrder purchaseOrder);

    List<DeviceExtResp> getPenddingSignoffDeviceList(PenddingSignoffDeviceListReq req);

    void sure(String purchaseOrderId);
}
