package cn.trasen.ams.device.model;

import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_inventory_task")
@Setter
@Getter
public class InventoryTask {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 盘点计划ID
     */
    @NotNull(message = "盘点计划ID不能为空")
    @PkExistValid(table = "d_inventory_plan", message = "盘点计划不存在")
    @Column(name = "inventory_plan_id")
    @ApiModelProperty(value = "盘点计划ID")
    private String inventoryPlanId;

    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /**
     * 责任人ID
     */
    @Column(name = "user_id_set")
    @ApiModelProperty(value = "责任人ID")
    private String userIdSet;

    /**
     * 0未盘点  1 盘到 2 盘亏
     */

    @DictExistValid(code = "AMS_INVENTORY_TASK_RET", message = "盘点结果不合法")
    @ApiModelProperty(value = "盘点结果")
    private String ret;

    @Transient
    @ApiModelProperty(value = "0 1 2 3 4  未盘点  正常使用  闲置  故障  损坏 ")
    private String retShow;

    /**
     * 0 1 2 3 - 未盘点 使用中 闲置 遗失
     */
    @DictExistValid(code = "AMS_INVENTORY_TASK_STATUS", message = "盘点状态不合法")
    @ApiModelProperty(value = "0未盘点  1 盘到 2 盘亏")
    private String status;

    @Transient
    private String statusShow;

    /**
     * 附件
     */
    @Column(name = "file_set")
    @ApiModelProperty(value = "附件")
    private String fileSet;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 处理方案 0 未处理 1 暂不处理 2 转报修 3 转报废
     */
    @ApiModelProperty(value = "处理方案 0 未处理 1 暂不处理 2 转报修 3 转处置（包含报废）4 转找回")
    private String solution;

    @Transient
    @ApiModelProperty(value = "处理方案翻译")
    private String solutionShow;
    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见")
    private String opinion;


    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}