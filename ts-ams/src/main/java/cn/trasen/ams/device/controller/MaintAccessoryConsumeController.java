package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintAccessoryConsume;
import cn.trasen.ams.device.service.MaintAccessoryConsumeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MaintAccessoryConsumeController
 * @Description TODO
 * @date 2024年12月17日 下午4:32:47
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "MaintAccessoryConsumeController")
public class MaintAccessoryConsumeController {

	private transient static final Logger logger = LoggerFactory.getLogger(MaintAccessoryConsumeController.class);

	@Autowired
	private MaintAccessoryConsumeService maintAccessoryConsumeService;

	/**
	 * @Title saveMaintAccessoryConsume
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:32:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/maintAccessoryConsume/save")
	public PlatformResult<String> saveMaintAccessoryConsume(@RequestBody MaintAccessoryConsume record) {
		try {
			maintAccessoryConsumeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMaintAccessoryConsume
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:32:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/maintAccessoryConsume/update")
	public PlatformResult<String> updateMaintAccessoryConsume(@RequestBody MaintAccessoryConsume record) {
		try {
			maintAccessoryConsumeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMaintAccessoryConsumeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MaintAccessoryConsume>
	 * @date 2024年12月17日 下午4:32:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/maintAccessoryConsume/{id}")
	public PlatformResult<MaintAccessoryConsume> selectMaintAccessoryConsumeById(@PathVariable String id) {
		try {
			MaintAccessoryConsume record = maintAccessoryConsumeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMaintAccessoryConsumeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:32:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/maintAccessoryConsume/delete/{id}")
	public PlatformResult<String> deleteMaintAccessoryConsumeById(@PathVariable String id) {
		try {
			maintAccessoryConsumeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMaintAccessoryConsumeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MaintAccessoryConsume>
	 * @date 2024年12月17日 下午4:32:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/maintAccessoryConsume/list")
	public DataSet<MaintAccessoryConsume> selectMaintAccessoryConsumeList(Page page, MaintAccessoryConsume record) {
		return maintAccessoryConsumeService.getDataSetList(page, record);
	}
}
