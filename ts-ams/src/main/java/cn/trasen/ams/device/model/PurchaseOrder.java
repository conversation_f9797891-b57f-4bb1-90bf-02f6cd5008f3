package cn.trasen.ams.device.model;

import cn.trasen.ams.device.constant.PurchaseConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_purchase_order")
@Setter
@Getter
public class PurchaseOrder {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 相关流程定义ID
     */
    @NotNull
    @Column(name = "definition_id")
    @ApiModelProperty(value = "相关流程定义ID")
    private String definitionId;


    @Column(name = "definition_json")
    @ApiModelProperty(value = "流程相关的json")
    private String definitionJson;

    /**
     * 主表模板ID
     */

    @Column(name = "form_id")
    @ApiModelProperty(value = "主表模板ID(后台写入)")
    private String formId;

    /**
     * 子表ID
     */
    @Column(name = "son_table_id")
    @ApiModelProperty(value = "子表ID(后台写入)")
    private String sonTableId;

    /**
     * 主表名称
     */
    @Column(name = "main_table_name")
    @ApiModelProperty(value = "主表名称(后台写入)")
    private String mainTableName;

    /**
     * 字表名称
     */
    @Column(name = "son_table_name")
    @ApiModelProperty(value = "子表名称(后台写入)")
    private String sonTableName;

    /**
     * 字典匹配规则
     */
    @Column(name = "rule")
    @ApiModelProperty(value = "字典匹配规则(后台写入)")
    private String rule;

    /**
     * 流水号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "Q:订单号(后台写入)")
    private String flowNo;

    /**
     * 订单类型
     */
    @Column(name = "order_type")
    @ApiModelProperty(value = "Q:订单类型(后台写入)")
    private String orderType;

    /**
     * 采购项目名称
     */
    @NotNull(message = "采购项目名称不能为空")
    @ApiModelProperty(value = "Q:采购项目名称")
    private String name;

    /**
     * 采购方式
     */
    @Column(name = "purchase_type")
    @ApiModelProperty(value = "Q:采购方式")
    @NotNull(message = "采购方式不能为空")
    @DictExistValid(code = PurchaseConst.PURCHASE_TYPE, message = "采购方式不合法")
    private String purchaseType;


    @Column(name = "fund_source")
    @ApiModelProperty(value = "Q:资金来源")
    @NotNull(message = "资金来源不能为空")
    @DictExistValid(code = PurchaseConst.PURCHASE_FUND_SOURCE, message = "资金来源不合法")
    private String fundSource;

    /**
     * 资产类别
     */
    @Column(name = "sku_type")
    @ApiModelProperty(value = "Q:资产类别(后台写入)")
    private String skuType;

    /**
     * 本次采购总数量
     */

    @Column(name = "sku_nums")
    @ApiModelProperty(value = "本次采购总数量(后台写入)")
    private Integer skuNums;

    /**
     * 总采购数量
     */
    @Column(name = "total_nums")
    @ApiModelProperty(value = "总采购数量(后台写入)")
    private Integer totalNums;

    /**
     * 资产总价值
     */
    @Column(name = "asset_total_value")
    @ApiModelProperty(value = "资产总价值(后台写入)")
    private BigDecimal assetTotalValue;

    /**
     * 供应商ID
     */
    @PkExistValid(table = "c_supplier", message = "供应商ID不正确")
    @NotNull(message = "供应商ID不能为空")
    @Column(name = "supplier_id")
    @ApiModelProperty(value = "Q:供应商ID")
    private String supplierId;

    /**
     * 供应商联系人
     */
    @Column(name = "supplier_contactor")
    @ApiModelProperty(value = "Q:供应商联系人")
    private String supplierContactor;

    /**
     * 供应商手机
     */
    @Column(name = "supplier_mobile")
    @ApiModelProperty(value = "Q:供应商手机")
    private String supplierMobile;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "Q:订单状态（后台写入）")
    private String status;

    // arrived_status
    @ApiModelProperty(value = "到货状态")
    private String arrivedStatus;

    @Transient
    @ApiModelProperty(value = "到货状态显示")
    private String arrivedStatusShow;

    /**
     * 相关机构ID
     */
    @Column(name = "r_org_id")
    @ApiModelProperty(value = "Q:采购单位ID")
    private String rOrgId;

    /**
     * 相关机构名称
     */
    @Column(name = "r_org_name")
    @ApiModelProperty(value = "Q:采购单位")
    private String rOrgName;


    /**
     * 相关科室ID
     */
    @Column(name = "r_dept_id")
    @ApiModelProperty(value = "Q:采购科室ID")
    private String rDeptId;

    /**
     * 相关科室名称
     */
    @Column(name = "r_dept_name")
    @ApiModelProperty(value = "Q:采购科室")
    private String rDeptName;


    /**
     * 相关人
     */
    @Column(name = "r_create_user")
    @ApiModelProperty(value = "Q:采购人ID")
    private String rCreateUser;

    /**
     * 相关人名称
     */
    @Column(name = "r_create_user_name")
    @ApiModelProperty(value = "Q:采购人名称")
    private String rCreateUserName;

    /**
     * 是否映射字典
     */
    @Column(name = "is_mapper_dict")
    @ApiModelProperty(value = "是否映射字典")
    private String isMapperDict;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Transient
    @ApiModelProperty(value = "Q:创建时间查询，起始时间逗号隔开")
    private String createDateQuery;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 相关资产冗余设计
     */
    @Column(name = "sku_name_set")
    @ApiModelProperty(value = "Q:采购明细(后台写入)")
    private String skuNameSet;

    @Transient
    @ApiModelProperty(value = "采购方式显示")
    private String purchaseTypeShow;


    @Transient
    @ApiModelProperty(value = "资金来源显示")
    private String fundSourceShow;

    /**
     * 资产类别显示
     */
    @Transient
    @ApiModelProperty(value = "资产类别显示（后台写入）")
    private String skuTypeShow;

    /**
     * 采购订单状态显示
     */
    @Transient
    @ApiModelProperty(value = "采购订单状态显示（后台写入）")
    private String statusShow;


    @Transient
    @ApiModelProperty(value = "Q:供应商名称")
    private String supplierName;

    // ztbbazl 招投标备案资料
    @ApiModelProperty(value = "招投标备案资料")
    private String ztbbazl;

    // jscs 技术参数
    @ApiModelProperty(value = "技术参数")
    private String jscs;

    @Transient
    @ApiModelProperty("关联合同ID")
    private String contractId;

    @Transient
    @ApiModelProperty("管理合同编号")
    private String contractNo;

    @Transient
    @ApiModelProperty(value = "合同登记时间")
    private Date contractCreateDate;

    @Transient
    @ApiModelProperty(value = "合同修改时间")
    private Date contractUpdateDate;


    @Transient
    @ApiModelProperty(value = "前端忽略，传输无效")
    private String pmsql;
}