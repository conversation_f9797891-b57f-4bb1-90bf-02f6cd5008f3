package cn.trasen.ams.device.bean.device;

import cn.trasen.ams.device.model.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean
 * @className: DeviceListResp
 * @author: chenbin
 * @description: 设备字段扩充
 * @date: 2024/9/11 17:03
 * @version: 1.0
 */
@Data
public class DeviceExtResp extends Device {


    @Excel(name = "所属科室")
    @ApiModelProperty(value = "所属科室名称")
    private String belongToOrgName;

    @Excel(name = "使用科室")
    @ApiModelProperty(value = "使用科室名称")
    private String useOrgName;

    @Excel(name = "规格型号")
    @ApiModelProperty(value = "设备型号")
    private String model;

    @ApiModelProperty(value = "单位")
    private String unit;

    @Excel(name = "医疗器械分类")
    @ApiModelProperty(value = "设备22分类ID")
    private String category22Id;

    @Excel(name = "固定资产分类")
    @ApiModelProperty(value = "设备分类名称")
    private String category22Name;


    @ApiModelProperty(value = "资产分类ID")
    private String categoryId;


    @ApiModelProperty(value = "资产分类描述")
    private String categoryName;


    @Excel(name = "品牌")
    @ApiModelProperty(value = "品牌")
    private String brandName;

    @Excel(name = "厂家")
    @ApiModelProperty(value = "厂家")
    private String manufacturerName;

    @Excel(name = "供应商")
    @ApiModelProperty(value = "供应商")
    private String supplierName;


    @ApiModelProperty(value = "资产类型")
    private String skuType;

    @ApiModelProperty(value = "资产类型翻译")
    private String skuTypeShow;


    @ApiModelProperty(value = "是否需要安装")
    private String needInstall;

    @ApiModelProperty(value = "是否需要安装翻译")
    private String needInstallShow;


    @ApiModelProperty(value = "强检类型翻译")
    private String calibrationTypeShow;


    @ApiModelProperty(value = "强检周期单位翻译")
    private String calibrationCycleUnitShow;


    @ApiModelProperty(value = "维护周期单位翻译")
    private String maintCycleUnitShow;


    @ApiModelProperty(value = "保修期单位翻译")
    private String warrantyPeriodUnitShow;


    @ApiModelProperty(value = "使用年限单位翻译")
    private String lifespanUnitShow;

    @ApiModelProperty(value = "巡检周期单位翻译")
    private String inspectionCycleUnitShow;

    @ApiModelProperty(value = "厂家质保时长单位翻译")
    private String maintPeriodUnitShow;


    @ApiModelProperty(value = "状态翻译")
    private String statusShow;

    @ApiModelProperty(value = "保养合同状态翻译")
    private String maintContractStatusShow;


    @ApiModelProperty(value = "是否医疗资产翻译")
    private String isHosAssetShow;

    @ApiModelProperty(value = "折旧方法翻译")
    private String depreciationMethodShow;


    @ApiModelProperty(value = "是否生命支持")
    private String isLifeSupport;

    @ApiModelProperty(value = "是否生命支持翻译")
    private String isLifeSupportShow;

    @ApiModelProperty(value = "是否特种设备")
    private String isSpecial;

    @ApiModelProperty(value = "是否特种设备翻译")
    private String isSpecialShow;

    @ApiModelProperty(value = "入库单号")
    private String inboundOrderFlowNo;

}
