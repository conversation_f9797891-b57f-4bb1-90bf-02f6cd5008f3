package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.device.bean.outin.InBoundOrderDetailResp;
import cn.trasen.ams.device.bean.outin.InBoundOrderInsertReq;
import cn.trasen.ams.device.bean.outin.InBoundOrderListReq;
import cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp;
import cn.trasen.ams.device.constant.OutInConst;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.InboundOrderDetailService;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.validator.right.RightValid;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InboundOrder;
import cn.trasen.ams.device.service.InboundOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InboundOrderController
 * @Description TODO
 * @date 2025年2月11日 下午5:02:53
 */
@RestController
@Api(tags = "InboundOrderController")
@Validated
public class InboundOrderController {

    private transient static final Logger logger = LoggerFactory.getLogger(InboundOrderController.class);

    @Autowired
    private InboundOrderService inboundOrderService;


    @Autowired
    private InboundOrderDetailService inboundOrderDetailService;
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private OrgService orgService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveInboundOrder
     * @Description 新增
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/inboundOrder/save")
    public PlatformResult<String> saveInboundOrder(@Validated @RequestBody InBoundOrderInsertReq record) {
        try {
            return PlatformResult.success(inboundOrderService.insert(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "退回入库", notes = "退回入库")
    @PostMapping("/api/device/inboundOrder/goback")
    public PlatformResult<String> goback(@Validated @RequestBody InBoundOrderInsertReq record) {
        try {
            // 强制写入退回入库类型
            record.getInboundOrder().setType(OutInConst.INBOUND_ORDER_TYPE_THRU);
            return PlatformResult.success(inboundOrderService.insert(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateInboundOrder
     * @Description 编辑
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/inboundOrder/update")
    public PlatformResult<String> updateInboundOrder(@Validated @RequestBody InBoundOrderInsertReq record) {
        try {
            return PlatformResult.success(inboundOrderService.edit(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @RightValid(action = "审核入库单", message = "您没有审核入库单的权限")
    @PostMapping("/api/device/inboundOrder/confirm/{id}")
    public PlatformResult<String> confirm(@PathVariable @Valid String id) {
        try {
            List<String> ids = java.util.Arrays.asList(id.split(","));
            inboundOrderService.batchConfirm(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<InboundOrder>
     * @Title selectInboundOrderById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/inboundOrder/{id}")
    public PlatformResult<InBoundOrderDetailResp> selectInboundOrderById(@PathVariable String id) {
        try {

            InBoundOrderDetailResp resp = new InBoundOrderDetailResp();
            InboundOrder inboundOrder = inboundOrderService.selectById(id);
            inboundOrderService.dataFmt(inboundOrder);
            // 查询本次入库的设备  TODO 这个地方先简单处理了一下，考虑是不是要把deviceId写到inboundOrderDetial device中去
            List<Device> deviceList = deviceService.getListByInboundOrderId(id);

            Map<String, List<String>> deviceId = new HashMap<>();
            Map<String, List<String>> uniqueNo = new HashMap<>();
            Map<String, List<String>> assetCode = new HashMap<>();


            if (!CollectionUtils.isEmpty(deviceList)) {
                // 通过skuId 进行分组，获取设备ID，唯一码，资产编码
                for (Device device : deviceList) {
                    if (deviceId.containsKey(device.getSkuId())) {
                        deviceId.get(device.getSkuId()).add(device.getId());
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(device.getId());
                        deviceId.put(device.getSkuId(), list);
                    }

                    if (uniqueNo.containsKey(device.getSkuId())) {
                        uniqueNo.get(device.getSkuId()).add(device.getUniqueNo());
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(device.getUniqueNo());
                        uniqueNo.put(device.getSkuId(), list);
                    }

                    if (assetCode.containsKey(device.getSkuId())) {
                        assetCode.get(device.getSkuId()).add(device.getAssetCode());
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(device.getAssetCode());
                        assetCode.put(device.getSkuId(), list);
                    }
                }
            }

            List<InboundOrderDetailExtResp> recordDetail = inboundOrderDetailService.selectByInboundOrderId(id);

            if (!CollectionUtils.isEmpty(recordDetail) && !CollectionUtils.isEmpty(deviceId)) {
                // 写入设备ID，唯一码，资产编码
                for (InboundOrderDetailExtResp detail : recordDetail) {
                    detail.setDeviceId(String.join(",", deviceId.get(detail.getSkuId())));
                    detail.setUniqueNo(String.join(",", uniqueNo.get(detail.getSkuId())));
                    detail.setAssetCode(String.join(",", assetCode.get(detail.getSkuId())));
                    // 处理科室数据
                }
            }

            if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
                for (InboundOrderDetailExtResp detail : recordDetail) {
                    if (StringUtils.isEmpty(detail.getBelongToOrgId())) {
                        detail.setBelongToOrgName("无");
                    } else {
                        detail.setBelongToOrgName(orgService.cgetOrgNameById(detail.getBelongToOrgId()));
                    }
                }
            }

            resp.setInboundOrder(inboundOrder);
            resp.setInboundOrderDetailExtResp(recordDetail);


            return PlatformResult.success(resp);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteInboundOrderById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/inboundOrder/delete/{id}")
    public PlatformResult<String> deleteInboundOrderById(@PathVariable String id) {
        try {
            inboundOrderService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<InboundOrder>
     * @Title selectInboundOrderList
     * @Description 查询列表
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/inboundOrder/list")
    public DataSet<InboundOrder> selectInboundOrderList(@Validated Page page, InBoundOrderListReq record) {
        return inboundOrderService.getDataSetList(page, record);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/inboundOrder/export")
    public ResponseEntity<byte[]> export(InBoundOrderListReq record) throws IOException {

        try {

            List<InboundOrder> exportList = inboundOrderService.getListNoPage(record);
            // dataFmt
            for (InboundOrder inboundOrder : exportList) {
                inboundOrderService.dataFmt(inboundOrder);
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/inLogExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("入库单.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    @PostMapping("/api/device/inboundOrder/getInboundDetailListByRelaId/{id}")
    public PlatformResult getInboundDetailListByRelaId(@PathVariable String id) {
        return PlatformResult.success(inboundOrderService.getInboundDetailListByRelaId(id));
    }


}
