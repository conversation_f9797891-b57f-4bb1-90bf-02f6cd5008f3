<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InspectionPlanMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.InspectionPlan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="device_id_set" jdbcType="VARCHAR" property="deviceIdSet" />
    <result column="engineer_id_set" jdbcType="VARCHAR" property="engineerIdSet" />
    <result column="e_engineer_id_set" jdbcType="VARCHAR" property="eEngineerIdSet" />
    <result column="diy_tpl_id" jdbcType="VARCHAR" property="diyTplId" />
    <result column="diy_tpl_config_json" jdbcType="VARCHAR" property="diyTplConfigJson" />
    <result column="start_at" jdbcType="DATE" property="startAt" />
    <result column="end_at" jdbcType="DATE" property="endAt" />
    <result column="e_start_at" jdbcType="VARCHAR" property="eStartAt" />
    <result column="e_end_at" jdbcType="VARCHAR" property="eEndAt" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="cate" jdbcType="VARCHAR" property="cate" />
    <result column="elx" jdbcType="VARCHAR" property="elx" />
    <result column="archive_status" jdbcType="VARCHAR" property="archiveStatus" />
    <result column="completes" jdbcType="INTEGER" property="completes" />
    <result column="targets" jdbcType="INTEGER" property="targets" />
    <result column="work_hours" jdbcType="DECIMAL" property="workHours" />
    <result column="travel_hours" jdbcType="DECIMAL" property="travelHours" />
    <result column="oks" jdbcType="INTEGER" property="oks" />
    <result column="exps" jdbcType="INTEGER" property="exps" />
    <result column="evaluation_text" jdbcType="VARCHAR" property="evaluationText" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="category22_id_set" jdbcType="LONGVARCHAR" property="category22IdSet" />
    <result column="category22_name_set" jdbcType="LONGVARCHAR" property="category22NameSet" />
    <result column="org_id_set" jdbcType="LONGVARCHAR" property="orgIdSet" />
    <result column="org_name_set" jdbcType="LONGVARCHAR" property="orgNameSet" />
    <result column="engineer_name_set" jdbcType="LONGVARCHAR" property="engineerNameSet" />
  </resultMap>
</mapper>