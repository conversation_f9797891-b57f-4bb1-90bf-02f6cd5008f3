package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.MaintWorkHoursMapper;
import cn.trasen.ams.device.model.MaintWorkHours;
import cn.trasen.ams.device.service.MaintWorkHoursService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintWorkHoursServiceImpl
 * @Description TODO
 * @date 2024年12月17日 下午4:33:52
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MaintWorkHoursServiceImpl implements MaintWorkHoursService {

    @Autowired
    private MaintWorkHoursMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(MaintWorkHours record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(MaintWorkHours record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        MaintWorkHours record = new MaintWorkHours();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    public Integer deleteByTaskId(String taskId) {
        Assert.hasText(taskId, "任务ID不能为空.");
        // 根据maintTaskid 物理删除
        Example example = new Example(MaintWorkHours.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("maintTaskId", taskId);
        return mapper.deleteByExample(example);
    }
//    @Override
//    public Integer deleteByTaskId(String taskId) {
//        Assert.hasText(taskId, "任务ID不能为空.");
//        // 根据maintTaskid删除
//        Example example = new Example(MaintWorkHours.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("maintTaskId", taskId);
//        MaintWorkHours record = new MaintWorkHours();
//        record.setUpdateDate(new Date());
//        record.setIsDeleted("Y");
//        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
//        if (user != null) {
//            record.setUpdateUser(user.getUsercode());
//            record.setUpdateUserName(user.getUsername());
//        }
//        return mapper.updateByExampleSelective(record, example);
//    }

    @Override
    public MaintWorkHours selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<MaintWorkHours> getDataSetList(Page page, MaintWorkHours record) {
        Example example = new Example(MaintWorkHours.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        example.setOrderByClause("create_date desc");
        List<MaintWorkHours> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<MaintWorkHours> getList(MaintWorkHours record) {
        Example example = new Example(MaintWorkHours.class);
        Example.Criteria criteria = example.createCriteria();

        if (!StringUtil.isEmpty(record.getMaintTaskId())) {
            criteria.andEqualTo("maintTaskId", record.getMaintTaskId());
        }

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        // order by
        example.setOrderByClause("create_date desc");

        return mapper.selectByExample(example);
    }
}
