package cn.trasen.ams.device.model;

import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_outbound_order")
@Setter
@Getter
public class OutboundOrder {
    @Id
    private String id;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    @Transient
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 资产类别
     */
    @NotNull(message = "资产类别不能为空")
    @ApiModelProperty(value = "资产类别")
    @DictExistValid(code = "SKU_TYPE", message = "资产类别不合法")
    @Column(name = "sku_type")
    private String skuType;

    /**
     * 单号
     */
    @Column(name = "batch_no")
    @ApiModelProperty(value = "单号")
    private String batchNo;

    /**
     * 出库类型 字典
     */
    @NotNull(message = "出库类型不能为空")
    @ApiModelProperty(value = "出库类型 字典")
    private String type;

    @Transient
    @ApiModelProperty(value = "出库类型展示")
    private String typeShow;

    /**
     * 关联ID
     */
    @Column(name = "rela_id")
    @ApiModelProperty(value = "关联ID")
    private String relaId;
    /**
     * 关联单号
     */
    @Transient
    @ApiModelProperty(value = "关联单号")
    private String relaOrderNo;


    @Transient
    @ApiModelProperty(value = "关联单名称")
    private String relaOrderName;

    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额")
    private String money;

    /**
     * 出库科室
     */
    @NotNull(message = "出库科室不能为空")
    @ApiModelProperty(value = "出库科室")
    @Column(name = "out_orgid")
    private String outOrgid;

    /**
     * 所属科室名称
     */
    @Transient
    @ApiModelProperty(value = "出库科室名称")
    private String outOrgName;

    /**
     * 出库状态 0 待出库 1 已出库
     */
    @ApiModelProperty(value = "出库状态 0 待出库 1 已出库")
    private String status;

    /**
     * 出库状态展示
     */
    @Transient
    @ApiModelProperty(value = "出库状态展示")
    private String statusShow;

    /**
     * 领用人(直接记录名称)
     */
    @ApiModelProperty(value = "领用人(直接记录名称)")
    private String gainer;

    /**
     * 出库人员
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "出库人员")
    private String doerId;

    /**
     * 出库人员名称
     */
    @Column(name = "doer_name")
    @ApiModelProperty(value = "出库人员名称")
    private String doerName;

    /**
     * 出库时间
     */
    @Column(name = "do_date")
    @ApiModelProperty(value = "出库时间")
    private Date doDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 制表单位ID
     */
    @ApiModelProperty(value = "制表单位ID")
    @Column(name = "create_dept_id")
    private String createDeptId;

    /**
     * 制表单位
     */
    @ApiModelProperty(value = "制表单位")
    @Column(name = "create_dept_name")
    private String createDeptName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}