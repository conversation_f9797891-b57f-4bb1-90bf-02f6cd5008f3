<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InspectionTaskMapper">

    <resultMap id="DeviceSlimRespMap" type="cn.trasen.ams.device.bean.device.DeviceSlimResp">
        <result column="device_name" property="name"/>
        <result column="asset_code" property="assetCode"/>
        <result column="device_serial_no" property="serialNo"/>
        <result column="belong_to_org_id" property="belongToOrgId"/>
        <result column="belong_to_org_name" property="belongToOrgName"/>
        <result column="use_org_id" property="useOrgId"/>
        <result column="use_org_name" property="useOrgName"/>
        <result column="loc" property="loc"/>
    </resultMap>

    <resultMap id="InspectionTaskMap" type="cn.trasen.ams.device.model.InspectionTask">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
        <result column="inspection_plan_id" jdbcType="VARCHAR" property="inspectionPlanId"/>
        <result column="inspection_plan_name" jdbcType="VARCHAR" property="inspectionPlanName"/>
        <result column="engineer_id_set" jdbcType="VARCHAR" property="engineerIdSet"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="complete_status" jdbcType="VARCHAR" property="completeStatus"/>
        <result column="complete_at" jdbcType="DATE" property="completeAt"/>
        <result column="solution" jdbcType="VARCHAR" property="solution"/>
        <result column="opinion" jdbcType="VARCHAR" property="opinion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="json_value" jdbcType="LONGVARCHAR" property="jsonValue"/>
        <result column="before_file_set" jdbcType="LONGVARCHAR" property="beforeFileSet"/>
        <result column="after_files_set" jdbcType="LONGVARCHAR" property="afterFilesSet"/>
        <result column="other_file_set" jdbcType="LONGVARCHAR" property="otherFileSet"/>
    </resultMap>

    <resultMap id="InspectionTaskListResp" type="cn.trasen.ams.device.bean.inspection.InspectionTaskListResp">
        <association property="deviceSlimResp" javaType="cn.trasen.ams.device.bean.device.DeviceSlimResp"
                     resultMap="DeviceSlimRespMap"/>
        <association property="inspectionTask" javaType="cn.trasen.ams.device.model.InspectionTask"
                     resultMap="InspectionTaskMap"/>
    </resultMap>
    <select id="getList" resultMap="InspectionTaskListResp"
            parameterType="cn.trasen.ams.device.bean.inspection.InspectionTaskListReq">
        select t1.*,
        t2.unique_no,
        t2.asset_code,
        t2.serial_no as device_serial_no,
        t2.loc,
        t3.name as device_name,
        t2.belong_to_org_id,
        t2.use_org_id,
        t4.name as inspection_plan_name,
        NULL as belong_to_org_name,
        NULL as use_org_name
        from d_inspection_task t1
        left join d_device t2 on t1.device_id = t2.id
        left join d_sku t3 on t2.sku_id = t3.id
        left join d_inspection_plan t4 on t1.inspection_plan_id = t4.id
        where t1.is_deleted = 'N'
        <if test="req.inspectionPlanId != null and req.inspectionPlanId != ''">
            and t1.inspection_plan_id = #{req.inspectionPlanId}
        </if>
        <if test="req.category22Id != null and req.category22Id != ''">
            and t3.category22_id = #{req.category22Id}
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            and t2.device_name like CONCAT('%', #{req.deviceName}, '%')
        </if>
        <if test="req.deviceNo != null and req.deviceNo != ''">
            and t2.device_no = #{req.deviceNo}
        </if>
        <if test="req.belongToOrgId != null and req.belongToOrgId != ''">
            and t2.belong_to_org_id = #{req.belongToOrgId}
        </if>
        <if test="req.exp != null">
            and t1.inspection_status in (2,3,4)
        </if>
        <if test="req.solution != null and req.solution != ''">
            <choose>
                <when test='req.solution == "1"'>
                    and t1.solution = '1'
                </when>
                <when test='req.solution == "2"'>
                    and t1.solution = '2'
                </when>
                <when test='req.solution == "3"'>
                    and t1.solution = '3'
                </when>
                <when test='req.solution == "4"'>
                    and t1.solution is null
                </when>
            </choose>
        </if>
        order by t1.create_date desc, t2.belong_to_org_id
    </select>
    <insert id="batchInsert">
        <![CDATA[
              INSERT INTO `d_inspection_task` (
                 `id`,
                 `serial_no`,
                 `inspection_plan_id`,
                 `engineer_id_set`,
                 `device_id`,
                 `json_value`,
                 `inspection_status`,
                 `note`,
                 `status`,
                 `before_file_set`,
                 `after_files_set`,
                 `other_file_set`,
                 `complete_status`,
                 `complete_at`,
                 `solution`,
                 `opinion`,
                 `create_date`,
                 `create_user`,
                 `create_user_name`,
                 `update_date`,
                 `update_user`,
                 `update_user_name`,
                 `sso_org_code`,
                 `sso_org_name`,
                 `is_deleted`,
                 `dept_id`,
                 `dept_name`
             )
              VALUES
		    ]]>
        <foreach collection="list" item="item" index="index" separator=",">
            <![CDATA[
			                (
                     #{item.id},
                     #{item.serialNo},
                     #{item.inspectionPlanId},
                     #{item.engineerIdSet},
                     #{item.deviceId},
                     #{item.jsonValue},
                     #{item.inspectionStatus},
                     #{item.note},
                     #{item.status},
                     #{item.beforeFileSet},
                     #{item.afterFilesSet},
                     #{item.otherFileSet},
                     #{item.completeStatus},
                     #{item.completeAt},
                     #{item.solution},
                     #{item.opinion},
                     #{item.createDate},
                     #{item.createUser},
                     #{item.createUserName},
                     #{item.updateDate},
                     #{item.updateUser},
                     #{item.updateUserName},
                     #{item.ssoOrgCode},
                     #{item.ssoOrgName},
                     #{item.isDeleted},
                     #{item.deptId},
                     #{item.deptName}
                )
			]]>
        </foreach>
    </insert>

</mapper>