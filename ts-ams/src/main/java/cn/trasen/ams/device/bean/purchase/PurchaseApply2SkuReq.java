package cn.trasen.ams.device.bean.purchase;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseApply2SkuReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/6/19 17:26
 * @version: 1.0
 */
@Data
public class PurchaseApply2SkuReq {

    @ApiModelProperty(value = "行 ID 标识")
    private String id;

    @ApiModelProperty(value = "SKUTYPE")
    private String skuType;

    @ApiModelProperty(value = "查询关键字")
    private String keyword;

    @ApiModelProperty(value = "已选的 SKU ID")
    private String selectedId;
}
