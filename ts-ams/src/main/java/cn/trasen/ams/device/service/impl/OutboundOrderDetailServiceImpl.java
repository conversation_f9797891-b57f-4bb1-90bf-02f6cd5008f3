package cn.trasen.ams.device.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.bean.outin.OutBoundOrderDetailExtResp;
import cn.trasen.ams.device.model.*;
import cn.trasen.ams.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.OutboundOrderDetailMapper;
import cn.trasen.ams.device.service.OutboundOrderDetailService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutboundOrderDetailServiceImpl
 * @Description TODO
 * @date 2025年2月11日 下午5:29:25
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class OutboundOrderDetailServiceImpl implements OutboundOrderDetailService {

    @Autowired
    private OutboundOrderDetailMapper mapper;

    @Autowired
    private DeviceService deviceService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(OutboundOrderDetail record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(OutboundOrderDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        OutboundOrderDetail record = new OutboundOrderDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public OutboundOrderDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<OutboundOrderDetail> getDataSetList(Page page, OutboundOrderDetail record) {
        Example example = new Example(OutboundOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<OutboundOrderDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public List<OutBoundOrderDetailExtResp> selectByOutboundOrderId(String outboundOrderId) {
        return mapper.selectByOutboundOrderId(outboundOrderId);
    }

    @Transactional(readOnly = false)
    @Override
    public void createByInboundOrder(InboundOrder inboundOrder, OutboundOrder outboundOrder) {
        // 根据入库单号查询，档案明细
        List<Device> deviceList = deviceService.getListByInboundOrderId(inboundOrder.getId());
        if (CollectionUtils.isEmpty(deviceList)) {
            throw new RuntimeException("入库单号对应的档案明细为空");
        }


        List<OutboundOrderDetail> outboundOrderDetailList = new ArrayList<>();

        // 根据档案逐一创建出库明细
        for (Device device : deviceList) {
            OutboundOrderDetail outboundOrderDetail = new OutboundOrderDetail();
            outboundOrderDetail.setOutboundOrderId(outboundOrder.getId());
            outboundOrderDetail.setDeviceId(device.getId());
            outboundOrderDetail.setId(IdGeneraterUtils.nextId());
            outboundOrderDetail.setTLoc(inboundOrder.getLoc());
            outboundOrderDetail.setCreateDate(new Date());
            outboundOrderDetail.setUpdateDate(new Date());
            outboundOrderDetail.setIsDeleted("N");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                outboundOrderDetail.setCreateUser(user.getUsercode());
                outboundOrderDetail.setCreateUserName(user.getUsername());
                outboundOrderDetail.setUpdateUser(user.getUsercode());
                outboundOrderDetail.setUpdateUserName(user.getUsername());
                outboundOrderDetail.setSsoOrgCode(user.getCorpcode());
                outboundOrderDetail.setSsoOrgName(user.getOrgName());
                outboundOrderDetail.setDeptId(user.getDeptId());
                outboundOrderDetail.setDeptName(user.getDeptname());
            }
            outboundOrderDetailList.add(outboundOrderDetail);
        }

        int batchSize = 500;
        for (int i = 0; i < outboundOrderDetailList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, outboundOrderDetailList.size());
            List<OutboundOrderDetail> batchList = outboundOrderDetailList.subList(i, end);
            mapper.batchInsert(batchList);
        }
    }

    @Override
    public void deleteByOutboundOrderId(String outboundOrderId) {
        Assert.hasText(outboundOrderId, "出库单ID不能为空.");
        Example example = new Example(OutboundOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outboundOrderId", outboundOrderId);
        OutboundOrderDetail record = new OutboundOrderDetail();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    /**
     * @param :
     * @return List<String>
     * <AUTHOR>
     * @description 获取出库中的设备ID
     * @date 2025/3/3 10:51
     */
    @Override
    public List<String> getOutboundingDeviceIds() {
        // 查询出库中的设备ID
        return mapper.getOutboundingDeviceIds();
    }
}
