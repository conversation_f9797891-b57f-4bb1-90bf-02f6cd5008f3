<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InventoryTaskMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.InventoryTask">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="inventory_plan_id" jdbcType="VARCHAR" property="inventoryPlanId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="user_id_set" jdbcType="VARCHAR" property="userIdSet"/>
        <result column="ret" jdbcType="CHAR" property="ret"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="file_set" jdbcType="VARCHAR" property="fileSet"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO d_inventory_task (
        id,
        inventory_plan_id,
        device_id,
        status,
        ret,
        create_date,
        create_user,
        create_user_name,
        update_date,
        update_user,
        update_user_name,
        sso_org_code,
        sso_org_name,
        is_deleted,
        dept_id,
        dept_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.inventoryPlanId},
            #{item.deviceId},
            #{item.status},
            #{item.ret},
            #{item.createDate},
            #{item.createUser},
            #{item.createUserName},
            #{item.updateDate},
            #{item.updateUser},
            #{item.updateUserName},
            #{item.ssoOrgCode},
            #{item.ssoOrgName},
            #{item.isDeleted},
            #{item.deptId},
            #{item.deptName}
            )
        </foreach>
    </insert>

    <sql id="getList">
        select t1.*,
        t2.code as code,
        t2.name as name,
        t2.sku_type as skuType,
        t2.need_install as needInstall,
        t2.model as model,
        t2.brand_id as brandId,
        t3.name as brandName,
        t2.manufacturer_id as manufacturerId,
        t4.name as manufacturerName,
        t2.category22_id as category22Id,
        t5.name as category22Name,
        t8.unique_no as uniqueNo,
        t8.asset_code as assetCode,
        t7.name as supplierName,
        t7.id as supplierId,
        t8.belong_to_org_id as belongToOrgId,
        t8.use_org_id as useOrgId,
        t8.status as assetStatus,
        t9.name as inventoryPlanName,
        t9.id as inventoryPlanId,
        t8.loc,
        t8.use_date
        from d_inventory_task t1
        left join d_device t8 on t1.device_id = t8.id
        left join d_sku t2 on t8.sku_id = t2.id
        left join c_brand t3 on t2.brand_id = t3.id
        left join c_manufacturer t4 on t2.manufacturer_id = t4.id
        left join d_category22 t5 on t2.category22_id = t5.id
        left join c_supplier t7 on t8.supplier_id = t7.id
        left join d_inventory_plan t9 on t1.inventory_plan_id = t9.id
        where t1.is_deleted = 'N'
        <if test="req.inventoryPlanId != null and req.inventoryPlanId != ''">
            and t1.inventory_plan_id = #{req.inventoryPlanId}
        </if>
        <if test="req.deviceName != null and req.deviceName != ''">
            and (t2.name like concat('%',#{req.deviceName},'%')
            or t8.asset_code like concat('%',#{req.deviceName},'%')
            or t8.unique_no like concat('%',#{req.deviceName},'%'))
        </if>
        <if test="req.uniqueNo != null and req.uniqueNo != ''">
            and t8.unique_no like concat('%',#{req.uniqueNo},'%')
        </if>
        <if test="req.assetCode != null and req.assetCode != ''">
            and t8.asset_code like concat('%',#{req.assetCode},'%')
        </if>
        <if test="req.status != null and req.status != ''">
            and t1.status = #{req.status}
        </if>
        <if test='req.isExp != null and req.isExp == "1"'>
            and (t1.status = '2' or t1.ret IN ('3','4','5'))
        </if>
        <if test="req.orgId != null and req.orgId != ''">
            and t8.belong_to_org_id = #{req.orgId}
        </if>
        <if test="req.solution != null and req.solution != ''">
            and t1.solution = #{req.solution}
        </if>
        <if test="req.taskIdList != null and !req.taskIdList.isEmpty()">
            and t1.id in
            <foreach collection="req.taskIdList" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        <if test='req.statusExt != null'>
            <choose>
                <when test='req.statusExt == "0"'>
                    and t1.status = '0' <!-- 未盘点 -->
                </when>
                <when test='req.statusExt == "1"'>
                    and t1.status IN ('1','2') <!-- 已盘点 -->
                </when>
            </choose>
        </if>
        <if test="req.category22Id != null and req.category22Id != ''">
            and t2.category22_id = #{req.category22Id}
        </if>
        <if test="req.categoryId != null and req.categoryId != ''">
            and t2.category_id = #{req.categoryId}
        </if>
        order by t1.update_date desc
    </sql>

    <select id="getListNoPage" resultType="cn.trasen.ams.device.bean.inventory.InventoryTaskExtResp"
            parameterType="cn.trasen.ams.device.bean.inventory.InventoryTaskListReq">
        <include refid="getList"></include>
    </select>
    <select id="getList" resultType="cn.trasen.ams.device.bean.inventory.InventoryTaskExtResp"
            parameterType="cn.trasen.ams.device.bean.inventory.InventoryTaskListReq">
        <include refid="getList"></include>
    </select>

    <select id="getInventoryTaskStatusNums"
            resultType="cn.trasen.ams.device.bean.inventory.InventoryTaskStatusNumsResp">
        select sum(case when status = '0' then 1 else 0 end)           as wps,
               sum(case when status = '1' then 1 else 0 end)           as pds,
               sum(case when status = '2' then 1 else 0 end)           as pks,
               sum(case when ret = '1' then 1 else 0 end)              as zcs,
               sum(case when ret = '2' then 1 else 0 end)              as xzs,
               sum(case when ret = '3' then 1 else 0 end)              as shs,
               sum(case when ret = '4' then 1 else 0 end)              as gzs,
               sum(case when ret IN ('3', '4', '5') then 1 else 0 end) as ycs, -- 异常数
               sum(case when status IN ('1', '2') then 1 else 0 end)   as yps, -- 已盘点数
               count(1)                                                as alls
        from d_inventory_task
        where inventory_plan_id = #{planId}
          and is_deleted = 'N'
    </select>

    <select id="getInventoryTaskStatusNumsExt" parameterType="cn.trasen.ams.device.bean.inventory.InventoryTaskListReq" resultType="cn.trasen.ams.device.bean.inventory.InventoryTaskStatusNumsResp">
        select sum(case when t1.`status` = '0' then 1 else 0 end) as wps,
        sum(case when t1.`status` = '1' then 1 else 0 end) as pds,
        sum(case when t1.`status` = '2' then 1 else 0 end) as pks,
        sum(case when t1.`ret` = '1' then 1 else 0 end) as zcs,
        sum(case when t1.`ret` = '2' then 1 else 0 end) as xzs,
        sum(case when t1.`ret` = '3' then 1 else 0 end) as shs,
        sum(case when t1.`ret` = '4' then 1 else 0 end) as gzs,
        sum(case when t1.`ret` IN ('3', '4', '5') then 1 else 0 end) as ycs, -- 异常数
        sum(case when t1.`status` IN ('1', '2') then 1 else 0 end) as yps, -- 已盘点数
        count(1) as alls
        from d_inventory_task t1
        left join d_device t2 on t1.device_id = t2.id
        left join d_sku t3 on t2.sku_id = t3.id
        where t1.`inventory_plan_id` = #{req.inventoryPlanId}
        and t1.`is_deleted` = 'N'
        <if test="req.orgId != null and req.orgId != ''">
            and t2.belong_to_org_id = #{req.orgId}
        </if>
        <if test="req.category22Id != null and req.category22Id != ''">
            and t3.category22_id = #{req.category22Id}
        </if>
        <if test="req.categoryId != null and req.categoryId != ''">
            and t3.category_id = #{req.categoryId}
        </if>
    </select>

    <select id="selectInventoryTaskIds4AssetCode" resultType="string">
        SELECT t1.id
        FROM d_inventory_task t1
        LEFT JOIN `d_device` t2 ON t1.device_id = t2.id
        WHERE t1.`inventory_plan_id` = #{inventoryPlanId}
        <if test="assetCode != null and !assetCode.isEmpty()">
            AND t2.asset_code IN
            <foreach collection="assetCode" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="taskStatus != null and !taskStatus.isEmpty()">
            and t1.`status` = #{taskStatus}
        </if>
    </select>
</mapper>