package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.inventory.InventoryPlanCateResp;
import cn.trasen.ams.device.bean.inventory.InventoryPlanOrgResp;
import cn.trasen.ams.device.model.Device;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InventoryPlan;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryPlanService
 * @Description TODO
 * @date 2025年2月20日 上午9:06:13
 */
public interface InventoryPlanService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    Integer save(InventoryPlan record);

    String insert(InventoryPlan record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    Integer update(InventoryPlan record);

    String edit(InventoryPlan record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InventoryPlan
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    InventoryPlan selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InventoryPlan>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    DataSet<InventoryPlan> getDataSetList(Page page, InventoryPlan record);

    void markDoing(String id);

    void sure(String id);

    void forceSure(String id);

    void report(String id);

    void dataFmt(InventoryPlan record);

    List<InventoryPlanCateResp> getInventoryPlanCateList(String id, String orgId, String statusExt);

    List<InventoryPlanOrgResp> getInventoryPlanOrgList(String id, String statusExt);


}
