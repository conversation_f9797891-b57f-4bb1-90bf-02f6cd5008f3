package cn.trasen.ams.device.model;

import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;

@Table(name = "d_signoff_detail")
@Setter
@Getter
public class SignoffDetail {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 主表ID
     */
    @Column(name = "signoff_id")
    @ApiModelProperty(value = "主表ID")
    private String signoffId;

    /**
     * 资产字典ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "资产ID")
    private String deviceId;

    @Transient
    @ApiModelProperty(value = "资产字典名称（编辑和新增请前端大佬带上）")
    private String name;

    @ApiModelProperty(value = "设备编号")
    private String uniqueNo;

    @Transient
    @ApiModelProperty(value = "设备型号")
    private String model;

    @Transient
    @ApiModelProperty(value = "品牌")
    private String brandName;

    @Transient
    @ApiModelProperty(value = "厂家")
    private String manufacturerName;
    /**
     * 资产序列号
     */
    @ApiModelProperty(value = "资产序列号")
    private String sn;

    /**
     * 一体化平台编码
     */
    @ApiModelProperty(value = "一体化平台编码")
    private String ythzcbm;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "birth_date")
    @ApiModelProperty(value = "生产日期")
    private Date birthDate;

    /**
     * 启用日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "use_date")
    @ApiModelProperty(value = "启用日期")
    private Date useDate;


    /**
     * 报废年限/报废年限
     */
    @Excel(name = "报废年限")
    @Column(name = "lifespan_val")
    @ApiModelProperty(value = "报废年限")
    private Integer lifespanVal;

    /**
     * 报废年限/报废年限单位 （1 2 3 -年 月 日）
     */
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "报废年限/报废年限单位不正确")
    @Excel(name = "报废年限单位")
    @Column(name = "lifespan_unit")
    @ApiModelProperty(value = "报废年限单位 （1 2 3 -年 月 日）")
    private String lifespanUnit;


    /**
     * 保养周期值
     */
    @Column(name = "maint_cycle_val")
    @ApiModelProperty(value = "保养周期值")
    private Integer maintCycleVal;

    /**
     * 保养周期单位
     */
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "保养周期单位不正确")
    @Column(name = "maint_cycle_unit")
    @ApiModelProperty(value = "保养周期单位")
    private String maintCycleUnit;

    /**
     * 巡检周期值
     */
    @Column(name = "inspection_cycle_val")
    @ApiModelProperty(value = "巡检周期值")
    private Integer inspectionCycleVal;

    /**
     * 巡检周期单位
     */
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "巡检周期单位不正确")
    @Column(name = "inspection_cycle_unit")
    @ApiModelProperty(value = "巡检周期单位")
    private String inspectionCycleUnit;

    /**
     * 厂家保修时长
     */
    @Column(name = "warranty_period_val")
    @ApiModelProperty(value = "厂家保修时长")
    private Integer warrantyPeriodVal;

    /**
     * 厂家保修时长单位
     */
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "厂家保修时长单位不正确")
    @Column(name = "warranty_period_unit")
    @ApiModelProperty(value = "厂家保修时长单位")
    private String warrantyPeriodUnit;


    /**
     * 强检类型 (1 2 -  强检 非强检)
     */
    @Column(name = "calibration_type")
    @ConstValid(constant = {SkuConst.CALIBRATION_TYPE_FORCE, SkuConst.CALIBRATION_TYPE_NORMAL}, message = "计量类型不正确")
    @ApiModelProperty(value = "强检类型 (1 2 -  强检 非强检)")
    private String calibrationType;

    /**
     * 强检测周期值
     */
    @Column(name = "calibration_cycle_val")
    @ApiModelProperty(value = "计量周期")
    private Integer calibrationCycleVal;

    /**
     * 强检周期的单位（（1 2 3 -年 月 日）
     */
    @Column(name = "calibration_cycle_unit")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "强检周期单位不正确")
    @ApiModelProperty(value = "计量周期单位（（1 2 3 -年 月 日）")
    private String calibrationCycleUnit;


    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}