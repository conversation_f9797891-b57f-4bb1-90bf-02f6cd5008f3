package cn.trasen.ams.device.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;
import org.springframework.util.StringUtils;

@Table(name = "d_inventory_plan")
@Setter
@Getter
public class InventoryPlan {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 盘点计划编号
     */
    @Column(name = "serial_no")
    private String serialNo;

    /**
     * 盘点计划名称
     */
    @ApiModelProperty(value = "盘点计划名称")
    private String name;


    /**
     * 0 医疗设备 1 一般资产
     */

    @NotNull(message = "盘点类别不能为空")
    @ApiModelProperty(value = "0 医疗设备 1 一般资产")
    @Column(name = "sku_type")
    private String skuType;

    @Transient
    @ApiModelProperty(value = "盘点类别翻译")
    private String skuTypeShow;

    /**
     * 计划执行人
     */
    @Column(name = "engineer_id_set")
    @ApiModelProperty(value = "计划执行人")
    private String engineerIdSet;

    /**
     * 计划执行人名称
     */
    @ApiModelProperty(value = "计划执行人名称")
    @Column(name = "engineer_name_set")
    private String engineerNameSet;

    @Transient
    @ApiModelProperty(value = "查询用")
    private List<String> engineerIdList;


    /**
     * 盘点开始日期
     */
    //格式化日期 yyyy-MM-dd
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "start_at")
    @ApiModelProperty(value = "盘点开始日期")
    private Date startAt;

    /**
     * 盘点结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_at")
    @ApiModelProperty(value = "盘点结束日期")
    private Date endAt;

    /**
     * 是否必须上传图片 0 1 - 不需要 需要
     */
    @Column(name = "is_pic_must")
    @ApiModelProperty(value = "是否必须上传图片 0 1 - 不需要 需要")
    private Boolean isPicMust;

    /**
     * 是否默认盘到 0 1 否 1 是
     */
    @Column(name = "is_def_pd")
    private String isDefPd;

    /**
     * 状态 0 未开始 1 进行中 2 已结束
     */
    @ApiModelProperty(value = "状态 0 未开始 1 进行中 2 已结束")
    private String status;

    @Transient
    @ApiModelProperty(value = "盘点状态扩展 1 表示待盘点 2 表示已盘点")
    private String statusExt;

    /**
     * 完成人员ID
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "完成人员ID")
    private String doerId;


    /**
     * 完成人员名称
     */
    @ApiModelProperty(value = "完成人员名称")
    @Column(name = "doer_name")
    private String doerName;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "done_date")
    private Date doneDate;

    @Transient
    @ApiModelProperty(value = "状态翻译")
    private String statusShow;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 22分类ID集合
     */
    @Column(name = "cate22_id_set")
    @ApiModelProperty(value = "22分类ID集合")
    private String cate22IdSet;


    /**
     * 22分类名称集合
     */
    @Column(name = "cate22_name_set")
    @ApiModelProperty(value = "22分类名称集合")
    private String cate22NameSet;

    @Transient
    @ApiModelProperty(value = "查询用")
    private List<String> cate22IdList;


    @Column(name = "cate_id_set")
    @ApiModelProperty(value = "22分类ID集合")
    private String cateIdSet;


    @ApiModelProperty(value = "分类名称集合")
    @Column(name = "cate_name_set")
    private String cateNameSet;

    @Transient
    @ApiModelProperty(value = "查询用")
    private List<String> cateIdList;


    /**
     * 科室ID集合
     */
    @Column(name = "org_id_set")
    @ApiModelProperty(value = "科室ID集合")
    private String orgIdSet;


    /**
     * 科室名称集合
     */

    @ApiModelProperty(value = "科室名称集合")
    private String orgNameSet;

    @Transient
    @ApiModelProperty(value = "查询用")
    private List<String> orgIdList;

    /**
     * 设备数量
     */
    @Column(name = "devices")
    @ApiModelProperty(value = "资产数量")
    private Integer devices;


    @ApiModelProperty(value = "未盘点数量")
    private Integer wps;

    @Column(name = "pds")
    @ApiModelProperty(value = "盘到数量")
    private int pds;

    @ApiModelProperty(value = "盘亏数量")
    private Integer pks;

    @ApiModelProperty(value = "正常数量")
    private Integer zcs;

    @ApiModelProperty(value = "损坏数量")
    private Integer shs;

    @ApiModelProperty(value = "故障数量")
    private Integer gzs;

    @ApiModelProperty(value = "异常数量")
    private Integer ycs;

    @ApiModelProperty(value = "已盘点数")
    private Integer yps;

    @ApiModelProperty(value = "闲置数量")
    private Integer xzs;

    @Transient
    @ApiModelProperty(value = "正常总数")
    private Integer zczs;


    @Transient
    @ApiModelProperty(value = "盘点科室数量")
    private Integer orgs;


    public void idStr2List() {
        if (!StringUtils.isEmpty(engineerIdSet)) {
            engineerIdList = Arrays.asList(engineerIdSet.split(","));
        }
        if (!StringUtils.isEmpty(cate22IdSet)) {
            cate22IdList = Arrays.asList(cate22IdSet.split(","));
        }
        if (!StringUtils.isEmpty(orgIdSet)) {
            orgIdList = Arrays.asList(orgIdSet.split(","));
        }
        if (!StringUtils.isEmpty(cateIdSet)) {
            cateIdList = Arrays.asList(cateIdSet.split(","));
        }
    }

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}