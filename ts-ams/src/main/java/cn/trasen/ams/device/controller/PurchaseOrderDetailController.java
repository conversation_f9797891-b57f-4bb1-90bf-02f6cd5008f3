package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.model.PurchaseOrderDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.service.PurchaseOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PurchaseOrderDetailController
 * @Description TODO
 * @date 2025年6月16日 下午1:49:57
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PurchaseOrderDetailController")
public class PurchaseOrderDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(PurchaseOrderDetailController.class);

	@Autowired
	private PurchaseOrderDetailService purchaseOrderDetailService;

	/**
	 * @Title savePurchaseOrderDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年6月16日 下午1:49:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/purchaseOrderDetail/save")
	public PlatformResult<String> savePurchaseOrderDetail(@RequestBody PurchaseOrderDetail record) {
		try {
			purchaseOrderDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePurchaseOrderDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年6月16日 下午1:49:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/purchaseOrderDetail/update")
	public PlatformResult<String> updatePurchaseOrderDetail(@RequestBody PurchaseOrderDetail record) {
		try {
			purchaseOrderDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPurchaseOrderDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PurchaseOrderDetail>
	 * @date 2025年6月16日 下午1:49:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/purchaseOrderDetail/{id}")
	public PlatformResult<PurchaseOrderDetail> selectPurchaseOrderDetailById(@PathVariable String id) {
		try {
			PurchaseOrderDetail record = purchaseOrderDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePurchaseOrderDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年6月16日 下午1:49:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/purchaseOrderDetail/delete/{id}")
	public PlatformResult<String> deletePurchaseOrderDetailById(@PathVariable String id) {
		try {
			purchaseOrderDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPurchaseOrderDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PurchaseOrderDetail>
	 * @date 2025年6月16日 下午1:49:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/purchaseOrderDetail/list")
	public DataSet<PurchaseOrderDetail> selectPurchaseOrderDetailList(Page page, PurchaseOrderDetail record) {
		return purchaseOrderDetailService.getDataSetList(page, record);
	}
}
