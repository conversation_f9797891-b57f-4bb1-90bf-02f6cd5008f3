package cn.trasen.ams.device.bean.comm;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.comm
 * @className: CommApproveReq
 * @author: chenbin
 * @description: 通用审批请求类
 * @date: 2025/4/22 11:36
 * @version: 1.0
 */
@Data
public class CommApproveReq {

    @NotNull(message = "ID不能为空")
    private String id;

    @DictExistValid(code = CommonConst.CHECK, message = "通用审批状态不合法")
    @NotNull(message = "审批状态不能为空")
    private String status;


    private String note;

    private String fileSet;
}
