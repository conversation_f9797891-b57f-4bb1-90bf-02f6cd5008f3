package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: SkuConst
 * @author: chenbin
 * @description: SKU 常量类
 * @date: 2024/9/9 17:25
 * @version: 1.0
 */

public class SkuConst {

    public static final String YMD = "AMS_YMD";

    // 年
    public static final String CYCLE_VAL_YEAR = "1";

    public static final String CYCLE_VAL_YEAR_SHOW = "年";

    // 月
    public static final String CYCLE_VAL_MONTH = "2";

    public static final String CYCLE_VAL_MONTH_SHOW = "月";


    // 日
    public static final String CYCLE_VAL_DAY = "3";

    public static final String CYCLE_VAL_DAY_SHOW = "日";


    public static final String CALIBRATION_TYPE = "AMS_CALIBRATION_TYPE";
    // 强检
    public static final String CALIBRATION_TYPE_FORCE = "1";

    public static final String CALIBRATION_TYPE_FORCE_SHOW = "强检";


    // 普通
    public static final String CALIBRATION_TYPE_NORMAL = "2";
    public static final String CALIBRATION_TYPE_NORMAL_SHOW = "非强检";

    // 是生命支持设备
    public static final String LIFE_SUPPORT_YES = "1";
    public static final String LIFE_SUPPORT_YES_SHOW = "是";

    // 不是生命支持设备
    public static final String LIFE_SUPPORT_NO = "0";
    public static final String LIFE_SUPPORT_NO_SHOW = "否";

    // 是特种设备
    public static final String IS_SPECIAL_YES = "1";
    public static final String IS_SPECIAL_YES_SHOW = "是";

    // 不是特种设备
    public static final String IS_SPECIAL_NO = "0";
    public static final String IS_SPECIAL_NO_SHOW = "否";


    public static final String SKU_TYPE = "AMS_SKU_TYPE";

    public static final String SKU_TYPE_YLSB = "0";

    public static final String SKU_TYPE_PTZC = "1";


}
