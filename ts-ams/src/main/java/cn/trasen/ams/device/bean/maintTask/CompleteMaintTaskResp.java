package cn.trasen.ams.device.bean.maintTask;

import cn.trasen.ams.device.model.MaintAccessoryConsume;
import cn.trasen.ams.device.model.MaintCost;
import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.ams.device.model.MaintWorkHours;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.maintTask
 * @className: CompleteMaintTaskReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/17 16:28
 * @version: 1.0
 */
@Data

public class CompleteMaintTaskResp extends MaintTaskDetailResp {

}
