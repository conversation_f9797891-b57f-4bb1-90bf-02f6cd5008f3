package cn.trasen.ams.device.bean.disposal;

import cn.trasen.ams.device.model.Disposal;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.ams.device.model.Transfer;
import cn.trasen.ams.device.model.TransferDetail;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.transfer
 * @className: TransferInsertReq
 * @author: chenbin
 * @description: 资产处置新增编辑参数
 * @date: 2025/4/18 09:29
 * @version: 1.0
 */

@Data
@Validated
public class DisposalInsertReq {

    @NotNull(message = "资产处置对象必传")
    @Valid // 触发嵌套验证
    Disposal disposal;

    @NotNull(message = "资产处置明细数组必传")
    @Valid // 触发嵌套验证
    List<DisposalDetail> disposalDetailList;
}
