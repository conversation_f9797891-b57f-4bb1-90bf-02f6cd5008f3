package cn.trasen.ams.device.bean.outin;

import cn.trasen.ams.device.model.InboundOrder;
import cn.trasen.ams.device.model.InboundOrderDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: InBoundOrderInsertReq
 * @author: chenbin
 * @description: 入库单创建和修改的结构体
 * @date: 2025/2/11 17:47
 * @version: 1.0
 */

@Data
@Validated
public class InBoundOrderInsertReq {

    @ApiModelProperty(value = "入库单结构体")
    @NotNull(message = "入库单对象必传")
    @Valid // 触发嵌套验证
    InboundOrder inboundOrder;

    @ApiModelProperty(value = "入库单明细结构体")
    @NotNull(message = "入库单明细数组必传")
    @Valid // 触发嵌套验证
    List<InboundOrderDetail> inboundOrderDetailList;

}
