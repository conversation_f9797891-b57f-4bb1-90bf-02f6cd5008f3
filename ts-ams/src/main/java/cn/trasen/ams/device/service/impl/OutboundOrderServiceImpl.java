package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.outin.OutBoundOrderListReq;
import cn.trasen.ams.device.bean.outin.OutboundOrderInsertReq;
import cn.trasen.ams.device.constant.OperationConst;
import cn.trasen.ams.device.constant.OutInConst;
import cn.trasen.ams.device.model.*;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.OutboundOrderDetailService;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.*;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.feign.message.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.OutboundOrderMapper;
import cn.trasen.ams.device.service.OutboundOrderService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutboundOrderServiceImpl
 * @Description TODO
 * @date 2025年2月11日 下午5:25:38
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class OutboundOrderServiceImpl implements OutboundOrderService {

    @Autowired
    private OutboundOrderMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private OutboundOrderDetailService outboundOrderDetailService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DictService dictService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private PermissionService permissionService;


    @Autowired
    private EmployeeService employeeService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(OutboundOrder record) {
        if (StringUtils.isEmpty(record.getId())) {
            record.setId(IdGeneraterUtils.nextId());
        }
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setCreateDeptId(user.getDeptId());
            record.setCreateDeptName(user.getDeptname());
            // 统一规划
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());

        }
        return mapper.insertSelective(record);
    }

    // 检测是否重复出库
    private boolean checkOutRepeat(List<OutboundOrderDetail> outboundOrderDetailList) {
        List<String> deviceIds = outboundOrderDetailList.stream().map(OutboundOrderDetail::getDeviceId).collect(Collectors.toList());
        List<String> outboudingDeviceIds = outboundOrderDetailService.getOutboundingDeviceIds();
        for (String deviceId : deviceIds) {
            if (outboudingDeviceIds.contains(deviceId)) {
                return true;
            }
        }
        return false;
    }

    private BigDecimal totalByOutboundOrderDetailList(List<OutboundOrderDetail> outboundOrderDetailList) {
        List<String> deviceIds = outboundOrderDetailList.stream().map(OutboundOrderDetail::getDeviceId).collect(Collectors.toList());
        // 通过device_id查询设备信息，计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;

        List<DeviceExtResp> deviceList = deviceService.getListByIds(String.join(",", deviceIds));
        for (DeviceExtResp device : deviceList) {
            if (device.getOriginalVal() != null) {
                totalAmount = totalAmount.add(device.getOriginalVal());
            }
        }
        return totalAmount;

    }

    @Transactional(readOnly = false)
    @Override
    public String insert(OutboundOrderInsertReq outboundOrderInsertReq) {

        OutboundOrder outboundOrder = outboundOrderInsertReq.getOutboundOrder();
        List<OutboundOrderDetail> outboundOrderDetailList = outboundOrderInsertReq.getOutboundOrderDetailList();

        if (outboundOrder == null) {
            throw new RuntimeException("出库单信息不能为空");
        }

        if (CollectionUtils.isEmpty(outboundOrderDetailList)) {
            throw new RuntimeException("出库单明细不能为空");
        }

        if (checkOutRepeat(outboundOrderDetailList)) {
            throw new RuntimeException("存在重复出库的设备，无法提交");
        }

        outboundOrder.setBatchNo(genBatchNo());

        // 通过明细数组，收集所有的device_id

        outboundOrder.setMoney(totalByOutboundOrderDetailList(outboundOrderDetailList).toString());

        // 取默认仓库
        Warehouse defWarehouse = warehouseService.getZcsbDefWarehouse();
        outboundOrder.setWarehouseId(defWarehouse.getId());

        // 保存出库单
        save(outboundOrder);

        // 保存出库单明细
        for (OutboundOrderDetail detail : outboundOrderDetailList) {
            detail.setOutboundOrderId(outboundOrder.getId());
            outboundOrderDetailService.save(detail);
        }
        // 标记资产为出库中
        deviceService.goouting(outboundOrder);
        // 通知审核人
        noticeCheck(outboundOrderInsertReq);
        return outboundOrder.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(OutboundOrder record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public String edit(OutboundOrderInsertReq outboundOrderInsertReq) {

        OutboundOrder outboundOrder = outboundOrderInsertReq.getOutboundOrder();
        List<OutboundOrderDetail> outboundOrderDetailList = outboundOrderInsertReq.getOutboundOrderDetailList();

        if (CommonConst.YES.equals(outboundOrder.getStatus())) {
            throw new RuntimeException("已出库的单据不允许修改");
        }

        if (outboundOrder == null) {
            throw new RuntimeException("入库单信息不能为空");
        }


        if (StringUtils.isEmpty(outboundOrder.getId())) {
            throw new RuntimeException("入库单ID不能为空");
        }


        if (CollectionUtils.isEmpty(outboundOrderDetailList)) {
            throw new RuntimeException("入库单明细信息不能为空");
        }

        // 把之前的资产状态改回来
        deviceService.gooutCancel(outboundOrder);

        // 检测是否有重复出库的设备
        if (checkOutRepeat(outboundOrderDetailList)) {
            throw new RuntimeException("存在重复出库的设备，无法提交");
        }


        // 对之前的出库明细进行删除
        outboundOrderDetailService.deleteByOutboundOrderId(outboundOrder.getId());


        // 对当前的入库单进行修改
        for (OutboundOrderDetail outboundOrderDetail : outboundOrderDetailList) {
            outboundOrderDetail.setOutboundOrderId(outboundOrder.getId());
            outboundOrderDetailService.save(outboundOrderDetail);
        }
        outboundOrder.setMoney(totalByOutboundOrderDetailList(outboundOrderDetailList).toString());
        update(outboundOrder);
        // 标记资产为出库中
        deviceService.goouting(outboundOrder);

        return outboundOrder.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        // 判断状态是否为已入库 否则不允许删除
        OutboundOrder row = mapper.selectByPrimaryKey(id);

        if (CommonConst.YES.equals(row.getStatus())) {
            throw new RuntimeException("已出库的单据不允许删除");
        }
        // 退回资产状态
        deviceService.gooutCancel(row);

        // 删除出库单
        OutboundOrder record = new OutboundOrder();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public OutboundOrder selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }


    private List<OutboundOrder> getList(Page page, OutBoundOrderListReq record, boolean isPaged) {
        Example example = new Example(OutboundOrder.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(record.getBatchNo())) {
            criteria.andLike("batchNo", "%" + record.getBatchNo() + "%");
        }
        if (!StringUtils.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }
        if (!StringUtils.isEmpty(record.getType())) {
            criteria.andEqualTo("type", record.getType());
        }
        if (!StringUtils.isEmpty(record.getDoerName())) {
            criteria.andLike("doerName", "%" + record.getDoerName() + "%");
        }

        if (!StringUtils.isEmpty(record.getOutOrgid())) {
            criteria.andEqualTo("outOrgid", record.getOutOrgid());
        }

        if (StringUtils.isEmpty(record.getEnd()) && !StringUtils.isEmpty(record.getStart())) {
            LocalDateTime startDateTime = LocalDateTime.parse(record.getStart(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endDateTime = startDateTime.withHour(23).withMinute(59).withSecond(59);
            record.setEnd(endDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (!StringUtils.isEmpty(record.getStart())) {
            criteria.andBetween("doDate", record.getStart(), record.getEnd());
        }
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        // 数据权限处理
        String business = PermissionConst.二级业务类型_出库单列表;
        permissionService.appendPermissionCondition(business, criteria);

        // 按照创建时间倒序 和 状态排序
        example.orderBy("status").asc().orderBy("createDate").desc();

        if (isPaged) {
            return mapper.selectByExampleAndRowBounds(example, page);
        } else {
            return mapper.selectByExample(example);
        }
    }

    @Override
    public DataSet<OutboundOrder> getDataSetList(Page page, OutBoundOrderListReq record) {
        List<OutboundOrder> records = getList(page, record, true);

        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(this::dataFmt);
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<OutboundOrder> getListNoPage(OutBoundOrderListReq record) {
        List<OutboundOrder> records = getList(null, record, false);

        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(this::dataFmt);
        }

        return records;
    }

    public void dataFmt(OutboundOrder record) {
        if (record == null) {
            return;
        }
        // 类型
        record.setTypeShow(dictService.cgetNameByValue(OutInConst.OUTBOUND_ORDER_TYPE, record.getType()));
        // 状态
        record.setStatusShow(dictService.cgetNameByValue(OutInConst.OUTBOUND_ORDER_STATUS, record.getStatus()));
        // 所属科室
        Map<String, String> orgMap = orgService.cgetOrgMap();
        record.setOutOrgName(orgMap.get(record.getOutOrgid()));

        if (record.getWarehouseId() != null) {
            Warehouse warehouse = warehouseService.selectById(record.getWarehouseId());
            if (warehouse != null) {
                record.setWarehouseName(warehouse.getName());
            }
        }
    }


    @Transactional(readOnly = false)

    @Override
    public void createByInBoundOrder(InboundOrder inboundOrder) {
        // 根据入库单创建出库单
        if (!CommonConst.YES.equals(inboundOrder.getStatus())) {
            throw new RuntimeException("入库单状态未确认，不能创建出库单");
        }

        OutboundOrder outboundOrder = new OutboundOrder();

        String id = IdGeneraterUtils.nextId();

        outboundOrder.setId(id);
        outboundOrder.setWarehouseId(inboundOrder.getWarehouseId());
        outboundOrder.setBatchNo(genBatchNo());
        outboundOrder.setType(OutInConst.OUTBOUND_ORDER_TYPE_ZDCK);
        outboundOrder.setRelaId(inboundOrder.getId());
        outboundOrder.setMoney(inboundOrder.getMoney());
        outboundOrder.setStatus(CommonConst.YES);
        outboundOrder.setOutOrgid(inboundOrder.getBelongToOrgid());
        // 零用人员待处理
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            outboundOrder.setDoerId(user.getUsercode());
            outboundOrder.setDoerName(user.getUsername());
        }
        outboundOrder.setDoDate(new Date());

        save(outboundOrder);
        // 创建出库单明细
        outboundOrderDetailService.createByInboundOrder(inboundOrder, outboundOrder);
    }

    @Transactional(readOnly = false)
    @Override
    public void confirm(String id) {
        // 根据出库单ID确认出库
        OutboundOrder outboundOrder = selectById(id);
        if (outboundOrder == null) {
            throw new RuntimeException("出库单不存在");
        }

        if (CommonConst.YES.equals(outboundOrder.getStatus())) {
            throw new RuntimeException("已出库的单据不允许重复出库");
        }

        // 修改出库单状态
        outboundOrder.setStatus(CommonConst.YES);
        outboundOrder.setDoDate(new Date());

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            outboundOrder.setDoerId(user.getUsercode());
            outboundOrder.setDoerName(user.getUsername());
        }
        update(outboundOrder);

        switch (outboundOrder.getType()) {
            case OutInConst.OUTBOUND_ORDER_TYPE_LYCK:
                // 领用出库
                break;
            case OutInConst.OUTBOUND_ORDER_TYPE_ZDCK:
                // 自动出库
                break;
            case OutInConst.OUTBOUND_ORDER_TYPE_QTCK:
                // 其他出库
                break;
            default:
                throw new RuntimeException("出库单类型错误");
        }

        // 标记资产为已出库
        deviceService.goout(outboundOrder);
        // 通知申请人
        noticeChecked(outboundOrder);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> ids) {
        //分别调用comfirm
        for (String id : ids) {
            confirm(id);
        }
    }

    private String genBatchNo() {
        return serialNoGenService.genByDate("CK");
    }


    private void noticeChecked(OutboundOrder record) {
        String userCode = record.getCreateUser();
        String doerId = record.getDoerId();
        String doerName = record.getDoerName();

        if (StringUtils.isEmpty(userCode) || StringUtils.isEmpty(doerId)) {
            return;
        }

        String content = "您申请的出库单：" + record.getBatchNo() + "已审核通过";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(userCode)  //接收人
                .sender(doerId) //发送人
                .senderName(doerName) //发送人name
                .subject("资产设备-出库申请审核通过").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/inventory-management/outbound-record").source("出库管理").build();
        NoticeService.sendAsynNotice(notice);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 入库审核提醒
     * @date 2025/5/22 15:42
     */
    private void noticeCheck(OutboundOrderInsertReq record) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String receiver = employeeService.cgetThpsUserSetByOperationId(OperationConst.出库审批);

        if (StringUtils.isEmpty(receiver)) {
            return;
        }

        OutboundOrder outboundOrder = record.getOutboundOrder();
        List<OutboundOrderDetail> inboundOrderDetailList = record.getOutboundOrderDetailList();

        String content = user.getDeptname() + ":" + user.getUsername() + "发起了一个出库申请，单号：" + outboundOrder.getBatchNo() + "，涉及" + inboundOrderDetailList.size() + "个产品，涉及金额" + outboundOrder.getMoney() + "元，请及时审核";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(receiver)  //接收人
                .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                .subject("资产设备-出库申请").url("#").wxSendType("1").businessId(outboundOrder.getId()).toUrl("/ts-web-equipment/inventory-management/outbound-record").source("出库管理").build();

        NoticeService.sendAsynNotice(notice);
    }
}
