package cn.trasen.ams.device.bean.inventory;

import cn.trasen.ams.device.model.InventoryTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inventory
 * @className: InventoryTaskListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/2/23 09:36
 * @version: 1.0
 */

@Data
public class InventoryTaskListReq {

    @ApiModelProperty(value = "任务ID")
    private String inventoryPlanId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "设备编号")
    private String uniqueNo;

    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "盘点状态")
    private String status;

    @ApiModelProperty(value = "盘点结果")
    private String solution;

    @ApiModelProperty(value = "是否异常 0否 1是")
    private String isExp;

    @ApiModelProperty(value = "按照科室查询")
    private String orgId;

    @ApiModelProperty(value = "医疗器械分类查询")
    private String category22Id;

    @ApiModelProperty(value = "固定资产分类查询")
    private String categoryId;

    @ApiModelProperty(value = "分类查询")
    private String cateId;

    private List<String> taskIdList;

    @ApiModelProperty(value = "盘点状态扩展 1 表示待盘点 2 表示已盘点")
    private String statusExt;

}
