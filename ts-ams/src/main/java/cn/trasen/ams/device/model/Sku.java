package cn.trasen.ams.device.model;

import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import io.swagger.annotations.*;

import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.validation.annotation.Validated;

@Table(name = "d_sku")
@Setter
@Getter
@Validated

public class Sku {


    @Transient
    @ApiModelProperty(value = "逻辑代码 inbound")
    private String logic;

    @Transient
    @ApiModelProperty(value = "选中的ID(查询用),多个逗号隔开")
    private String selectedId;

    @Transient
    @ApiModelProperty(value = "不需要传")
    private List<String> selectedIds;


    @Transient
    @ApiModelProperty(value = "忽略的ID(查询用),多个逗号隔开")
    private String ignoreId;

    @Transient
    @ApiModelProperty(value = "不需要传")
    private List<String> ignoreIds;


    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    @Column(name = "code")
    @ApiModelProperty(value = "设备字典编码")
    private String code;

    /**
     * 设备名称
     */
    @Excel(name = "资产名称")
    @NotNull(message = "资产名称不能为空")
    @Size(max = 50, message = "资产名称长度不能超过50")
    @ApiModelProperty(value = "资产名称")
    private String name;

    // 首拼
    @ApiModelProperty(value = "首拼")
    private String sp;

    // 全拼
    @ApiModelProperty(value = "全拼")
    private String qp;


    /**
     * 设备类型ID
     */


    @Column(name = "sku_type")
    @ApiModelProperty(value = "资产类型")
    private String skuType;


    @Transient
    @Excel(name = "资产类别")
    @ApiModelProperty(value = "资产类型展示")
    private String skuTypeShow;


    /**
     * 是否需要安装
     */

    @Column(name = "need_install")
    @Excel(name = "是否需要安装")
    @ApiModelProperty(value = "是否需要安装")
    private String needInstall;


    @Transient
    @ApiModelProperty(value = "是否需要安装翻译")
    private String needInstallShow;


    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String model;


    @Excel(name = "单位")
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 品牌ID
     */
    @PkExistValid(table = "c_brand", message = "品牌ID不正确")
    @Column(name = "brand_id")
    @ApiModelProperty(value = "品牌ID")
    private String brandId;


    @Excel(name = "品牌")
    @Transient
    @ApiModelProperty(value = "品牌")
    private String brandName;

    /**
     * 厂家ID
     */
    @PkExistValid(table = "c_manufacturer", message = "厂家ID不正确")
    @Column(name = "manufacturer_id")
    @ApiModelProperty(value = "厂家ID")
    private String manufacturerId;


    @Excel(name = "厂家")
    @Transient
    @ApiModelProperty(value = "厂家")
    private String manufacturerName;


    /**
     * 22分类ID
     */
    @PkExistValid(table = "d_category22", message = "医疗设备分类ID不正确")
    @Column(name = "category22_id")
    @ApiModelProperty(value = "22分类ID")
    private String category22Id;


    @Excel(name = "医疗器械分类")
    @Transient
    @ApiModelProperty(value = "设备医疗器械分类")
    private String category22Name;


    @PkExistValid(table = "c_category", message = "设备医疗器械分类ID不正确")
    @Column(name = "category_id")
    @ApiModelProperty(value = "设备医疗器械分类分类ID")
    private String categoryId;


    @Excel(name = "固定资产分类")
    @Transient
    @ApiModelProperty(value = "固定资产分类描述")
    private String categoryName;


    /**
     * 报废年限/报废年限
     */
    @Excel(name = "报废年限")
    @Column(name = "lifespan_val")
    @ApiModelProperty(value = "报废年限")
    private Integer lifespanVal;

    /**
     * 报废年限/报废年限单位 （1 2 3 -年 月 日）
     */
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "报废年限/报废年限单位不正确")
    @Excel(name = "报废年限单位")
    @Column(name = "lifespan_unit")
    @ApiModelProperty(value = "报废年限单位 （1 2 3 -年 月 日）")
    private String lifespanUnit;

    /**
     * 强检类型 (1 2 -  强检 非强检)
     */
    @Excel(name = "计量类型")
    @Column(name = "calibration_type")
    @ConstValid(constant = {SkuConst.CALIBRATION_TYPE_FORCE, SkuConst.CALIBRATION_TYPE_NORMAL}, message = "计量类型不正确")
    @ApiModelProperty(value = "强检类型 (1 2 -  强检 非强检)")
    private String calibrationType;

    /**
     * 强检测周期值
     */
    @Excel(name = "计量周期")
    @Column(name = "calibration_cycle_val")
    @ApiModelProperty(value = "计量周期")
    private Integer calibrationCycleVal;

    /**
     * 强检周期的单位（（1 2 3 -年 月 日）
     */
    @Excel(name = "计量周期单位")
    @Column(name = "calibration_cycle_unit")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "强检周期单位不正确")
    @ApiModelProperty(value = "计量周期单位（（1 2 3 -年 月 日）")
    private String calibrationCycleUnit;

    /**
     * 保养周期值
     */
    @Excel(name = "保养周期")
    @Column(name = "maint_cycle_val")
    @ApiModelProperty(value = "保养周期值")
    private Integer maintCycleVal;

    /**
     * 保养周期单位 （1 2 3 -年 月 日）
     */
    @Excel(name = "保养周期单位")
    @Column(name = "maint_cycle_unit")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "保养周期单位不正确")
    @ApiModelProperty(value = "保养周期单位 （1 2 3 -年 月 日）")
    private String maintCycleUnit;

    /**
     * 是否生命支持设备 （0 1 -否 是）
     */
    @Excel(name = "是否生命支持设备")
    @Column(name = "is_life_support")
    @ConstValid(constant = {SkuConst.LIFE_SUPPORT_NO, SkuConst.LIFE_SUPPORT_YES}, message = "是否生命支持设备值不正确")
    @ApiModelProperty(value = "是否生命支持设备 （0 1 -否 是）")
    private String isLifeSupport;

    /**
     * 是否特种设备（0 1 - 否 是）
     */
    @Excel(name = "是否特种设备")
    @Column(name = "is_special")
    @ConstValid(constant = {SkuConst.IS_SPECIAL_NO, SkuConst.IS_SPECIAL_YES}, message = "是否特种设备值不正确")
    @ApiModelProperty(value = "是否特种设备（0 1 - 否 是）")
    private String isSpecial;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;


    @Transient
    @ApiModelProperty(value = "md5指纹，导入用")
    private String md5;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}