package cn.trasen.ams.device.service.impl;

import java.util.*;

import cn.trasen.ams.device.bean.device.DeviceSlimResp;
import cn.trasen.ams.device.bean.inspection.*;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.InspectionPlanConst;
import cn.trasen.ams.device.constant.InspectionTaskConst;
import cn.trasen.ams.device.constant.MaintPlanConst;
import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.ams.device.service.InspectionPlanService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.InspectionTaskMapper;
import cn.trasen.ams.device.model.InspectionTask;
import cn.trasen.ams.device.service.InspectionTaskService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InspectionTaskServiceImpl
 * @Description TODO
 * @date 2024年12月20日 下午1:50:02
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InspectionTaskServiceImpl implements InspectionTaskService {

    @Autowired
    private InspectionTaskMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private InspectionPlanService inspectionPlanService;

    @Autowired
    private DictService dictService;

    @Autowired
    private OrgService orgService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InspectionTask record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InspectionTask record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InspectionTask record = new InspectionTask();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InspectionTask selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<InspectionTaskListResp> getDataSetList(Page page, InspectionTaskListReq record) {
        List<InspectionTaskListResp> records = mapper.getList(page, record);
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(item -> {
                dataFmt4DeviceSlimResp(item.getDeviceSlimResp());
                dataFmt(item.getInspectionTask());
            });
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    public void dataFmt4DeviceSlimResp(DeviceSlimResp deviceSlimResp) {
        if (deviceSlimResp == null) {
            return;
        }
        Map<String, String> orgMap = orgService.cgetOrgMap();
        deviceSlimResp.setBelongToOrgName(orgMap.get(deviceSlimResp.getBelongToOrgId()));
        deviceSlimResp.setUseOrgName(orgMap.get(deviceSlimResp.getUseOrgId()));
    }

    @Transactional(readOnly = false)
    @Override
    public void createTaskByInspectionPlan(InspectionPlan inspectionPlan) {

        int count = getTasksByInspectionPlanId(inspectionPlan.getId());
        if (count > 0) {
            return;
        }

        List<InspectionTask> inspectionTaskList = new ArrayList<>();
        List<String> deviceIdList = Arrays.asList(inspectionPlan.getDeviceIdSet().split(","));

        if (deviceIdList == null || deviceIdList.size() == 0) {
            return;
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        // loop deviceIdList
        for (String deviceId : deviceIdList) {
            InspectionTask record = new InspectionTask();
            record.setId(IdGeneraterUtils.nextId());
            record.setCreateDate(new Date());
            record.setUpdateDate(new Date());
            record.setIsDeleted("N");
            if (user != null) {
                record.setCreateUser(user.getUsercode());
                record.setCreateUserName(user.getUsername());
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
                record.setSsoOrgCode(user.getCorpcode());
                record.setSsoOrgName(user.getOrgName());
                record.setDeptId(user.getDeptId());
                record.setDeptName(user.getDeptname());
            }

            try {
                record.setSerialNo(serialNoGenService.genByDate("MT"));
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException("生成流水号失败");
            }

            record.setInspectionPlanId(inspectionPlan.getId());
            record.setDeviceId(deviceId);
            record.setStatus(CommonConst.YES);
            record.setCompleteStatus(CommonConst.NO);

            inspectionTaskList.add(record);
        }

        if (inspectionTaskList.size() > 0) {
            mapper.batchInsert(inspectionTaskList);
        }

    }

    @Transactional(readOnly = false)
    @Override
    public void updateTaskByInspectionPlan(InspectionPlan inspectionPlan) {
        // 删除旧记录
        deleteByInspectionPlan(inspectionPlan);
        // 重新建立记录
        createTaskByInspectionPlan(inspectionPlan);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByInspectionPlan(InspectionPlan inspectionPlan) {
        // 多做一层容错
        if (!inspectionPlan.getStatus().equals(MaintPlanConst.STATUS_WAIT)) {
            throw new BusinessException("保险计划状态只有未开始的状态，才能删除");
        }
        Example example = new Example(InspectionTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inspectionPlanId", inspectionPlan.getId());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        mapper.deleteByExample(example);
    }

    @Override
    public int getTasksByInspectionPlanId(String inspectionPlanId) {
        Example example = new Example(InspectionTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inspectionPlanId", inspectionPlanId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        int count = mapper.selectCountByExample(example);
        return count;
    }

    @Override
    public List<InspectionTask> getTaskByPlanId(String inspectionPlanId) {
        Example example = new Example(InspectionTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inspectionPlanId", inspectionPlanId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<InspectionTask> records = mapper.selectByExample(example);
        return records;
    }

    @Override
    public InspectionTaskDetailResp getTaskDetail(String inspectionPlanId) {
        InspectionTaskDetailResp resp = new InspectionTaskDetailResp();
        InspectionPlan inspectionPlan = inspectionPlanService.selectById(inspectionPlanId);
        resp.setInspectionPlan(inspectionPlan);

        List<InspectionTask> inspectionTaskList = getTaskByPlanId(inspectionPlanId);
        if (inspectionTaskList != null && inspectionTaskList.size() > 0) {
            inspectionTaskList.forEach(this::dataFmt);
        }

        resp.setInspectionTaskList(inspectionTaskList);
        return resp;
    }

    @Transactional(readOnly = false)
    @Override
    public void completeInspectionTaskBatch(CompleteInspectionTaskReq record) {
        List<InspectionTask> inspectionTaskList = record.getInspectionTaskList();
        if (CollectionUtils.isEmpty(inspectionTaskList)) {
            throw new BusinessException("巡检任务为空");
        }

        int completes = 0;
        int oks = 0;
        int exps = 0;

        String status = InspectionPlanConst.STATUS_DOING;

        for (InspectionTask inspectionTask : inspectionTaskList) {

            if (inspectionTask.getJsonValue() != null) {
                inspectionTask.setCompleteStatus(CommonConst.YES);
                inspectionTask.setCompleteAt(new Date());
                completes++;

                // 正常情况
                if (InspectionTaskConst.MAINT_TASK_STATUS_NORMAL.equals(inspectionTask.getInspectionStatus())) {
                    oks++;
                } else {
                    exps++;
                }
            }
            update(inspectionTask);

        }
        // 当完成数量等于任务数量时，更新巡检计划状态为已完成
        if (completes == inspectionTaskList.size()) {
            status = InspectionPlanConst.STATUS_DONE;
        }

        InspectionPlan inspectionPlan = record.getInspectionPlan();

        inspectionPlan.setStatus(status);
        inspectionPlan.setCompletes(completes);
        inspectionPlan.setOks(oks);
        inspectionPlan.setExps(exps);

        inspectionPlanService.update(inspectionPlan);

    }

    @Transactional(readOnly = false)
    @Override
    public void solveExpMaintTask(SolveExpInspectionTaskReq record) {
        if (record.getId() == null) {
            throw new BusinessException("巡检工单ID不能为空");
        }

        InspectionTask inspectionTask = new InspectionTask();
        inspectionTask.setId(record.getId());
        inspectionTask.setSolution(record.getSolution());
        inspectionTask.setOpinion(record.getOpinion());

        update(inspectionTask);
    }

    private void dataFmt(InspectionTask record) {
        record.setStatusShow(dictService.cgetNameByValue(InspectionTaskConst.INSPECTION_TASK_STATUS, record.getStatus()));
        record.setCompleteStatusShow(dictService.cgetNameByValue(InspectionTaskConst.INSPECTION_TASK_COMPLETE_STATUS, record.getCompleteStatus()));
        record.setInspectionStatusShow(dictService.cgetNameByValue(InspectionTaskConst.INSPECTION_TASK_INSPECTION_STATUS, record.getInspectionStatus()));
        record.setSolutionShow(dictService.cgetNameByValue(InspectionTaskConst.INSPECTION_TASK_SOLUTION, record.getSolution()));
    }
}
