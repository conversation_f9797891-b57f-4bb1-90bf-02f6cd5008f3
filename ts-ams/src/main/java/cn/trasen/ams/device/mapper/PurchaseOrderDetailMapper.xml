<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.PurchaseOrderDetailMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.PurchaseOrderDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="agree_nums" jdbcType="VARCHAR" property="agreeNums"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="sku_desc" jdbcType="VARCHAR" property="skuDesc"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="nums" jdbcType="INTEGER" property="nums"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="instance_field_kv_json" jdbcType="LONGVARCHAR" property="instanceFieldKvJson"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO d_purchase_order_detail (
        id,
        purchase_order_id,
        base_instance_id,
        instance_id,
        instance_field_kv_json,
        agree_nums,
        sku_id,
        sku_desc,
        sku_obj,
        price,
        nums,
        total,
        create_date,
        create_user,
        create_user_name,
        dept_id,
        dept_name,
        update_date,
        update_user,
        update_user_name,
        sso_org_code,
        sso_org_name,
        is_deleted
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.purchaseOrderId},
            #{item.baseInstanceId},
            #{item.instanceId},
            #{item.instanceFieldKvJson},
            #{item.agreeNums},
            #{item.skuId},
            #{item.skuDesc},
            #{item.skuObj},
            #{item.price},
            #{item.nums},
            #{item.total},
            #{item.createDate},
            #{item.createUser},
            #{item.createUserName},
            #{item.deptId},
            #{item.deptName},
            #{item.updateDate},
            #{item.updateUser},
            #{item.updateUserName},
            #{item.ssoOrgCode},
            #{item.ssoOrgName},
            #{item.isDeleted}
            )
        </foreach>
    </insert>
</mapper>