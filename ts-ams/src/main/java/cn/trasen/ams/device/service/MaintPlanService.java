package cn.trasen.ams.device.service;

import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintPlan;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintPlanService
 * @Description TODO
 * @date 2024年12月12日 上午10:27:03
 */
public interface MaintPlanService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    Integer save(MaintPlan record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    Integer update(MaintPlan record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    Integer deleteById(String id);


    /**
     * @param id:
     * @return Integer
     * <AUTHOR>
     * @description 归档保养计划
     * @date 2024/12/14 11:34
     */
    Integer archiveById(String id);

    /**
     * @return MaintPlan
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    MaintPlan selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MaintPlan>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    DataSet<MaintPlan> getDataSetList(Page page, MaintPlan record);

    void syncProcess(MaintTask maintTask);
}
