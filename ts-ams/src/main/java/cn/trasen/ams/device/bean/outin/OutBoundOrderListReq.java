package cn.trasen.ams.device.bean.outin;

import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: InBoundOrderListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/2/14 09:28
 * @version: 1.0
 */

@Data

public class OutBoundOrderListReq {


    @ApiModelProperty(value = "出库开始时间")
    private String start;

    @ApiModelProperty(value = "出库结束时间")
    private String end;

    @ApiModelProperty(value = "出库单号")
    private String batchNo;

    @ApiModelProperty(value = "出库类型")
    @DictExistValid(code = "AMS_OUTBOUND_ORDER_TYPE", message = "入库类型不合法")
    private String type;

    @ApiModelProperty(value = "出库状态")
    @DictExistValid(code = "AMS_YES_OR_NO", message = "入库状态不合法")
    private String status;

    @ApiModelProperty(value = "出库人员名称")
    private String doerName;

    @ApiModelProperty(value = "出库科室")
    private String outOrgid;

}
