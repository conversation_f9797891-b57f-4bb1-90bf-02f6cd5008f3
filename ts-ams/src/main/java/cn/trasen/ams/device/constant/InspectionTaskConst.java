package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: InspectionTaskConst
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/23 16:55
 * @version: 1.0
 */

public class InspectionTaskConst {
    public static final String INSPECTION_TASK_INSPECTION_STATUS = "AMS_INSPECTION_TASK_INSPECTION_STATUS";
    // AMS_INSPECTION_TASK_STATUS
    public static final String INSPECTION_TASK_STATUS = "AMS_INSPECTION_TASK_STATUS";
    // AMS_INSPECTION_TASK_COMPLETE_STATUS
    public static final String INSPECTION_TASK_COMPLETE_STATUS = "AMS_INSPECTION_TASK_COMPLETE_STATUS";
    //AMS_INSPECTION_TASK_SOLUTION
    public static final String INSPECTION_TASK_SOLUTION = "AMS_INSPECTION_TASK_SOLUTION";


    // 正常使用
    public static final String MAINT_TASK_STATUS_NORMAL = "1";
    //LACK 可以使用，部分功能失效
    public static final String MAINT_TASK_STATUS_LACK = "2";
    // FAULT 无法使用，故障维修
    public static final String MAINT_TASK_STATUS_FAULT = "3";
    // DEATH 无法维修，待报废
    public static final String MAINT_TASK_STATUS_DEATH = "4";
}
