package cn.trasen.ams.device.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.DiyTplConst;
import cn.trasen.ams.device.constant.InspectionPlanConst;
import cn.trasen.ams.device.model.DiyTpl;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.DiyTplService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.InspectionPlanMapper;
import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.ams.device.service.InspectionPlanService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InspectionPlanServiceImpl
 * @Description TODO
 * @date 2024年12月20日 下午1:47:26
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InspectionPlanServiceImpl implements InspectionPlanService {

    @Autowired
    private InspectionPlanMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DiyTplService diyTplService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InspectionPlan record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        try {
            record.setSerialNo(serialNoGenService.genByDate("IP"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("生成计划编号失败.");
        }

        autoFillColumn(record);

        return mapper.insertSelective(record);
    }

    private void autoFillColumn(InspectionPlan record) {
        // 检查关联的设备
        if (record.getDeviceIdSet() == null || record.getDeviceIdSet().isEmpty()) {
            throw new BusinessException("关联的设备不能为空.");
        }
        // 填充分类和科室的ID和名称
        // deviceIdSet 字符串类型转换成 List<String>
        List<DeviceExtResp> deviceExtRespList = deviceService.getListByIds(record.getDeviceIdSet());

        List<String> orgIdList = new ArrayList<>();
        List<String> orgNameList = new ArrayList<>();

        List<String> category22IdList = new ArrayList<>();
        List<String> category22NameList = new ArrayList<>();

        deviceExtRespList.forEach(deviceExtResp -> {
            // 翻译ID和key
            deviceService.dataFmt(deviceExtResp);

            orgIdList.add(deviceExtResp.getBelongToOrgId());
            orgNameList.add(deviceExtResp.getBelongToOrgName());

            category22IdList.add(deviceExtResp.getCategory22Id());
            category22NameList.add(deviceExtResp.getCategory22Name());

        });

        record.setTargets(deviceExtRespList.size());
        record.setOrgIdSet(String.join(",", orgIdList));
        record.setOrgNameSet(String.join(",", orgNameList));
        record.setCategory22IdSet(String.join(",", category22IdList));
        record.setCategory22NameSet(String.join(",", category22NameList));


        // 填充计划执行人
        if (record.getEngineerIdSet() != null) {
            List<String> engineerNameList = new ArrayList<>();
            List<String> engineerIdList = Arrays.asList(record.getEngineerIdSet().split(","));
            PlatformResult<List<EmployeeResp>> pr = hrmsEmployeeFeignService.getEmployeeDetailByIds(engineerIdList);
            List<EmployeeResp> employeeRespList = pr.getObject();
            if (employeeRespList != null) {
                employeeRespList.forEach(employeeResp -> {
                    engineerNameList.add(employeeResp.getEmployeeName());
                });
            }
            record.setEngineerNameSet(String.join(",", engineerNameList));
        }


        // 填充实际计划执行人
        if (record.getEEngineerIdSet() != null) {
            List<String> eEngineerNameList = new ArrayList<>();
            List<String> eEngineerIdList = Arrays.asList(record.getEEngineerIdSet().split(","));
            PlatformResult<List<EmployeeResp>> pr = hrmsEmployeeFeignService.getEmployeeDetailByIds(eEngineerIdList);
            List<EmployeeResp> employeeRespList = pr.getObject();
            if (employeeRespList != null) {
                employeeRespList.forEach(employeeResp -> {
                    eEngineerNameList.add(employeeResp.getEmployeeName());
                });
            }
            record.setEEngineerNameSet(String.join(",", eEngineerNameList));
        }


        // 填充模板的配置规则，防止后续模板变动造成数据不一致
        if (!StringUtil.isEmpty(record.getDiyTplId())) {
            DiyTpl diyTpl = diyTplService.selectById(record.getDiyTplId());
            if (diyTpl != null) {
                record.setDiyTplConfigJson(diyTpl.getConfigJson());
            }
        }

    }

    @Transactional(readOnly = false)
    @Override
    public Integer archiveById(String id) {

        Assert.hasText(id, "ID不能为空.");
        InspectionPlan record = new InspectionPlan();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setArchiveStatus(CommonConst.YES);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        return mapper.updateByPrimaryKeySelective(record);

    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InspectionPlan record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        autoFillColumn(record);
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InspectionPlan record = new InspectionPlan();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InspectionPlan selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InspectionPlan ret = mapper.selectByPrimaryKey(id);
        dataFmt(ret);
        return ret;
    }

    @Override
    public DataSet<InspectionPlan> getDataSetList(Page page, InspectionPlan record) {
        Example example = new Example(InspectionPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if (record.getName() != null) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        if (record.getStatus() != null) {
            criteria.andEqualTo("status", record.getStatus());
        }

        // 排序
        example.setOrderByClause("create_date desc");
        List<InspectionPlan> records = mapper.selectByExampleAndRowBounds(example, page);

        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    private void dataFmt(InspectionPlan record) {
        record.setStatusShow(dictService.cgetNameByValue(InspectionPlanConst.INSPECTION_PLAN_STATUS, record.getStatus()));
        record.setCateShow(dictService.cgetNameByValue(DiyTplConst.DIY_TPL_INSPECTION_CATE, record.getCate()));
        record.setArchiveStatusShow(dictService.cgetNameByValue(InspectionPlanConst.INSPECTION_PLAN_ARCHIVE_STATUS, record.getArchiveStatus()));
    }
}
