package cn.trasen.ams.device.bean.inventory;

import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inventory
 * @className: InventoryTaskDoitScanReq
 * @author: chenbin
 * @description: 扫码盘点
 * @date: 2025/4/14 15:25
 * @version: 1.0
 */
@Data
@Validated
public class InventoryTaskDoitScanReq {
    @ApiModelProperty(value = "计划ID")
    private String inventoryPlanId;

    @NotBlank(message = "盘点结果不能为空")
    @DictExistValid(code = "AMS_INVENTORY_TASK_RET", message = "盘点结果不合法")
    @ApiModelProperty(value = "盘点结果")
    private String ret;

    /**
     * 0 1 2 3 - 未盘点 使用中 闲置 遗失
     */
    @NotBlank(message = "盘点状态不能为空")
    @DictExistValid(code = "AMS_INVENTORY_TASK_STATUS", message = "盘点状态不合法")
    @ApiModelProperty(value = "0未盘点  1 盘到 2 盘亏")
    private String status;


    @NotBlank(message = "资产编码不能为空")
    @ApiModelProperty(value = "资产编码")
    private String assetCode;
}
