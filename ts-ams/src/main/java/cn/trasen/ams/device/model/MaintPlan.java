package cn.trasen.ams.device.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_maint_plan")
@Setter
@Getter
public class MaintPlan {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    @Column(name = "serial_no")
    @ApiModelProperty(value = "计划编号")
    private String serialNo;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 关联设备集合
     */
    @Column(name = "device_id_set")
    @ApiModelProperty(value = "关联设备集合")
    private String deviceIdSet;


    @Column(name = "category22_id_set")
    @ApiModelProperty(value = "关联ID集合")
    private String category22IdSet;


    @Column(name = "org_id_set")
    @ApiModelProperty(value = "关联科室集合")
    private String orgIdSet;


    @Column(name = "category22_name_set")
    @ApiModelProperty(value = "关联分类集合")
    private String category22NameSet;


    @Column(name = "org_name_set")
    @ApiModelProperty(value = "关联科室集合")
    private String orgNameSet;

    @Column(name = "engineer_name_set")
    @ApiModelProperty(value = "关联分类集合")
    private String engineerNameSet;


    @Column(name = "e_engineer_name_set")
    @ApiModelProperty(value = "关联科室集合")
    @JsonProperty("eEngineerNameSet")
    private String eEngineerNameSet;


    /**
     * 计划执行人
     */
    @Column(name = "engineer_id_set")
    @ApiModelProperty(value = "计划执行人")
    private String engineerIdSet;

    /**
     * 实际计划执行人
     */
    @Column(name = "e_engineer_id_set")
    @ApiModelProperty(value = "实际计划执行人")
    @JsonProperty("eEngineerIdSet")
    private String eEngineerIdSet;

    /**
     * 本次计划执行的开始时间;本次计划执行的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "start_at")
    @ApiModelProperty(value = "本次计划执行的开始时间;本次计划执行的开始时间")
    private Date startAt;

    /**
     * 本次计划执行的结束时间;本次计划执行的结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_at")
    @ApiModelProperty(value = "本次计划执行的结束时间;本次计划执行的结束时间")
    private Date endAt;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "e_start_at")
    @ApiModelProperty(value = "实际开始时间")
    @JsonProperty("eStartAt")
    private Date eStartAt;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "e_end_at")
    @ApiModelProperty(value = "实际结束时间")
    @JsonProperty("eEndAt")
    private Date eEndAt;

    /**
     * 保养级别 0 日常保养 1 一级保养 2 二级保养 3 三级保养
     */
    @ApiModelProperty(value = "保养级别 0 日常保养 1 一级保养 2 二级保养 3 三级保养")
    private String level;

    @Transient
    @ApiModelProperty(value = "保养级别翻译")
    private String levelShow;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String elx;

    /**
     * 执行状态 0 未开始 1 进行中 2 已完成 3 超期未完成
     */
    @ApiModelProperty(value = "执行状态 0 未开始 1 进行中 2 已完成 3 超期未完成")
    private String status;


    @Transient
    @ApiModelProperty(value = "执行状态 中文翻译")
    private String statusShow;

    /**
     * 归档状态 0 未归档 1 已归档
     */
    @Column(name = "archive_status")
    @ApiModelProperty(value = "归档状态 0 未归档 1 已归档")
    private String archiveStatus;

    @Transient
    @ApiModelProperty(value = "归档状态 0 未归档 1 已归档")
    private String archiveStatusShow;

    /**
     * 完成数量;完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Integer completes;

    /**
     * 目标数量;targets
     */
    @ApiModelProperty(value = "目标数量;targets")
    private Integer targets;

    /**
     * 保养总耗时
     */
    @Column(name = "work_hours")
    @ApiModelProperty(value = "保养总耗时")
    private BigDecimal workHours;

    /**
     * 旅行总耗时
     */
    @Column(name = "travel_hours")
    @ApiModelProperty(value = "旅行总耗时")
    private BigDecimal travelHours;

    /**
     * 总费用
     */
    @ApiModelProperty(value = "总费用")
    private BigDecimal costs;

    /**
     * 健康数量
     */
    @ApiModelProperty(value = "健康数量")
    private Integer oks;

    /**
     * 异常数量
     */
    @ApiModelProperty(value = "异常数量")
    private Integer exps;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}