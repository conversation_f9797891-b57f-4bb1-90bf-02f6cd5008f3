package cn.trasen.ams.device.bean.inspection;

import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.ams.device.model.InspectionTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inspection
 * @className: InspectionTaskDetail
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/23 10:11
 * @version: 1.0
 */

@Data
public class InspectionTaskDetailResp {

    @ApiModelProperty("巡检计划信息")
    private InspectionPlan inspectionPlan;

    @ApiModelProperty("巡检任务信息")
    private List<InspectionTask> inspectionTaskList;
}
