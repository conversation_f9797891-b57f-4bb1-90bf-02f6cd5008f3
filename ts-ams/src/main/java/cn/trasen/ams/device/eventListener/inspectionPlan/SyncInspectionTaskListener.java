package cn.trasen.ams.device.eventListener.inspectionPlan;

import cn.trasen.ams.device.eventPublisher.inspection.InspectionPlanCreatedEvent;
import cn.trasen.ams.device.eventPublisher.inspection.InspectionPlanDeletedEvent;
import cn.trasen.ams.device.eventPublisher.inspection.InspectionPlanUpdatedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanCreatedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanDeletedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanUpdatedEvent;
import cn.trasen.ams.device.service.InspectionTaskService;
import cn.trasen.ams.device.service.MaintTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.eventListener.inspectionPlan
 * @className: SyncInspectionTaskListener
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/20 16:12
 * @version: 1.0
 */

@Component
public class SyncInspectionTaskListener {
    @Autowired
    private InspectionTaskService inspectionTaskService;

    @EventListener(classes = {InspectionPlanCreatedEvent.class})
    public void onInspectionPlanCreated(InspectionPlanCreatedEvent event) {
        System.out.println("onInspectionPlanCreated.onEventPublished");
        inspectionTaskService.createTaskByInspectionPlan(event.getInspectionPlan());
    }

    @EventListener(classes = {InspectionPlanUpdatedEvent.class})
    public void onInspectionPlanUpdated(InspectionPlanUpdatedEvent event) {
        System.out.println("onInspectionPlanUpdated.onEventPublished");
        inspectionTaskService.updateTaskByInspectionPlan(event.getInspectionPlan());
    }

    @EventListener(classes = {InspectionPlanDeletedEvent.class})
    public void onInspectionPlanDeleted(InspectionPlanDeletedEvent event) {
        System.out.println("onInspectionPlanDeleted.onEventPublished");
        inspectionTaskService.deleteByInspectionPlan(event.getInspectionPlan());
    }
}
