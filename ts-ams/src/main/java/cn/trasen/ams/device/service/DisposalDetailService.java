package cn.trasen.ams.device.service;

import cn.trasen.ams.device.model.TransferDetail;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.DisposalDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DisposalDetailService
 * @Description TODO
 * @date 2025年4月27日 下午4:49:48
 */
public interface DisposalDetailService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年4月27日 下午4:49:48
     * <AUTHOR>
     */
    Integer save(DisposalDetail record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年4月27日 下午4:49:48
     * <AUTHOR>
     */
    Integer update(DisposalDetail record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年4月27日 下午4:49:48
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DisposalDetail
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年4月27日 下午4:49:48
     * <AUTHOR>
     */
    DisposalDetail selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DisposalDetail>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年4月27日 下午4:49:48
     * <AUTHOR>
     */
    DataSet<DisposalDetail> getDataSetList(Page page, DisposalDetail record);

    void deleteByDisposalId(String disposalId);

    List<DisposalDetail> selectByDisposalId(String disposalId);
}
