package cn.trasen.ams.device.bean.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.device
 * @className: DeviceListForSelect
 * @author: chenbin
 * @description: 专门给前端设备选择器使用的设备列表
 * @date: 2024/12/13 08:45
 * @version: 1.0
 */

@Data
public class DeviceList4SelectReq extends DeviceListReq {

    @ApiModelProperty(value = "选择设备逻辑 ｜" +
            " maint 保养计划选设备," +
            "inspection 巡检计划选设备," +
            "metrology 计量计划选设备" +
            "transfer 资产转移选设备," +
            "disposal 资产处置选设备,"
    )
    private String logic;

    private String logicSql;
}
