package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_life_event_log")
@Setter
@Getter
public class LifeEventLog {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")

    private String deviceId;


    /**
     * 1 转科 2 验收 3 报废 4 维修 5 保养 6 计量 7 巡检 8 转借 9 购入
     */
    @ApiModelProperty(value = "1 转科 2 验收 3 报废 4 维修 5 保养 6 计量 7 巡检 8 转借 9 购入")
    private String type;


    @Transient
    private String typeShow;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String elx;

    /**
     * 具体关联事件的真实ID
     */
    @Column(name = "relation_id")
    @ApiModelProperty(value = "具体关联事件的真实ID")
    private String relationId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}