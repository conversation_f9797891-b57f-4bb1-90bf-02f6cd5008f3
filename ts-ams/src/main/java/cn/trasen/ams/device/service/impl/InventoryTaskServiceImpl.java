package cn.trasen.ams.device.service.impl;

import java.util.*;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.bean.inventory.*;
import cn.trasen.ams.device.constant.*;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.model.InventoryPlan;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.InventoryPlanService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.InventoryTaskMapper;
import cn.trasen.ams.device.model.InventoryTask;
import cn.trasen.ams.device.service.InventoryTaskService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryTaskServiceImpl
 * @Description TODO
 * @date 2025年2月20日 下午5:33:21
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InventoryTaskServiceImpl implements InventoryTaskService {

    @Autowired
    private InventoryTaskMapper mapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DictService dictService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private InventoryPlanService inventoryPlanService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InventoryTask record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InventoryTask record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void doit(InventoryTask record) {

        InventoryPlan inventoryPlan = inventoryPlanService.selectById(record.getInventoryPlanId());

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已完成，无法盘点");
        }
        // 标记开始
        inventoryPlanService.markDoing(record.getInventoryPlanId());
        // 更新当前数据
        update(record);
        // 更新统计数量
        inventoryPlanService.report(record.getInventoryPlanId());

    }

    @Transactional(readOnly = false)
    @Override
    public InventoryTaskExtResp doit4Scan(InventoryTaskDoitScanReq record) {

        InventoryPlan inventoryPlan = inventoryPlanService.selectById(record.getInventoryPlanId());

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已完成，无法盘点");
        }

        List<String> assetCodeList = new ArrayList<>();
        assetCodeList.add(record.getAssetCode());
        // 检查是否能匹配到
        List<String> taskIdList = mapper.selectInventoryTaskIds4AssetCode(record.getInventoryPlanId(), assetCodeList, "");


        if (taskIdList == null || taskIdList.isEmpty()) {
            throw new RuntimeException("当前资产不在本次盘点任务列表中，无需盘点");
        }

        // 标记开始
        inventoryPlanService.markDoing(record.getInventoryPlanId());
        // 执行盘点
        InventoryTask inventoryTask = new InventoryTask();
        inventoryTask.setId(taskIdList.get(0));
        inventoryTask.setInventoryPlanId(record.getInventoryPlanId());
        inventoryTask.setStatus(record.getStatus());
        inventoryTask.setRet(record.getRet());
        update(inventoryTask);

        // 更新统计数量
        inventoryPlanService.report(record.getInventoryPlanId());

        InventoryTaskListReq req = new InventoryTaskListReq();
        req.setTaskIdList(taskIdList);
        List<InventoryTaskExtResp> list = mapper.getListNoPage(req);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        InventoryTaskExtResp resp = list.get(0);
        // fmt
        dataFmt(resp);
        return resp;
    }

    @Transactional(readOnly = false)
    @Override
    public void forceDoit(String planId) {
        // 校验参数
        Assert.hasText(planId, "盘点计划ID不能为空");

        // 构建查询条件
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N") // 未删除
                .andEqualTo("inventoryPlanId", planId)         // 关联盘点计划ID
                .andEqualTo("status", InventoryConst.TASK_STATUS_READY); // 未盘点状态

        // 构建更新内容
        InventoryTask updateTask = new InventoryTask();
        updateTask.setStatus(InventoryConst.TASK_STATUS_PK); // 设置为盘亏状态
        updateTask.setRet(InventoryConst.TASK_RET_LOST);     // 设置盘亏结果
        updateTask.setUpdateDate(new Date());               // 更新时间
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            updateTask.setUpdateUser(user.getUsercode());   // 更新人
            updateTask.setUpdateUserName(user.getUsername());
        }
        // 执行批量更新
        mapper.updateByExampleSelective(updateTask, example);
    }


    @Transactional(readOnly = false)
    @Override
    public List<InventoryTaskExtResp> doit4Rfid(InventoryTaskDoitBatchReq InventoryTaskDoitBatchReq) {

        List<String> assetCodeList = InventoryTaskDoitBatchReq.getAssetCodeList();

        if (CollectionUtils.isEmpty(assetCodeList)) {
            throw new RuntimeException("资产编码不能为空");
        }

        InventoryPlan inventoryPlan = inventoryPlanService.selectById(InventoryTaskDoitBatchReq.getInventoryPlanId());

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已完成，无法盘点");
        }

        // 通过asset_code 查询到需要盘点的任务ID
        List<String> taskIdList = mapper.selectInventoryTaskIds4AssetCode(InventoryTaskDoitBatchReq.getInventoryPlanId(), assetCodeList, InventoryConst.TASK_STATUS_READY);
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Collections.emptyList();
        }

        // 标记开始
        inventoryPlanService.markDoing(InventoryTaskDoitBatchReq.getInventoryPlanId());


        // 对于于批量盘点的情况，直接更新
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        // asset_code IN
        criteria.andIn("id", taskIdList);

        InventoryTask update = new InventoryTask();
        update.setUpdateDate(new Date());
        update.setStatus(InventoryTaskDoitBatchReq.getStatus());
        update.setRet(InventoryTaskDoitBatchReq.getRet());

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            update.setCreateUser(user.getUsercode());
            update.setCreateUserName(user.getUsername());
            update.setUpdateUser(user.getUsercode());
            update.setUpdateUserName(user.getUsername());
            update.setSsoOrgCode(user.getCorpcode());
            update.setSsoOrgName(user.getOrgName());
            update.setDeptId(user.getDeptId());
            update.setDeptName(user.getDeptname());
        }
        // 执行更新
        mapper.updateByExampleSelective(update, example);

        // 更新统计数量
        inventoryPlanService.report(InventoryTaskDoitBatchReq.getInventoryPlanId());

        InventoryTaskListReq req = new InventoryTaskListReq();
        req.setTaskIdList(taskIdList);
        List<InventoryTaskExtResp> list = mapper.getListNoPage(req);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        list.forEach(this::dataFmt);
        return list;
    }


    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InventoryTask record = new InventoryTask();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InventoryTask selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<InventoryTask> getDataSetList(Page page, InventoryTask record) {
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<InventoryTask> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param inventoryPlan:
     * @return int
     * <AUTHOR>
     * @description 创建盘点计划的任务清单
     * @date 2025/2/21 08:28
     */
    @Override
    public int insertByPlan(InventoryPlan inventoryPlan) {

        List<Device> deviceList = deviceService.getListByInventoryPlan(inventoryPlan);

        if (CollectionUtils.isEmpty(deviceList)) {
            return 0;
        }

        List<InventoryTask> inventoryTaskList = new ArrayList<>();
        for (Device device : deviceList) {
            InventoryTask inventoryTask = new InventoryTask();
            inventoryTask.setInventoryPlanId(inventoryPlan.getId());
            inventoryTask.setDeviceId(device.getId());

            inventoryTask.setId(IdGeneraterUtils.nextId());
            inventoryTask.setCreateDate(new Date());
            inventoryTask.setUpdateDate(new Date());
            inventoryTask.setIsDeleted("N");

            // 如果默认盘到的情况下
            if (CommonConst.YES.equals(inventoryPlan.getIsDefPd())) {
                inventoryTask.setStatus(InventoryConst.TASK_STATUS_PD);
                inventoryTask.setRet(InventoryConst.TASK_RET_NORMAL);
            } else {
                // 否则默认是未盘
                inventoryTask.setStatus(InventoryConst.TASK_STATUS_READY);
            }

            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                inventoryTask.setCreateUser(user.getUsercode());
                inventoryTask.setCreateUserName(user.getUsername());
                inventoryTask.setUpdateUser(user.getUsercode());
                inventoryTask.setUpdateUserName(user.getUsername());
                inventoryTask.setSsoOrgCode(user.getCorpcode());
                inventoryTask.setSsoOrgName(user.getOrgName());
                inventoryTask.setDeptId(user.getDeptId());
                inventoryTask.setDeptName(user.getDeptname());
            }
            inventoryTaskList.add(inventoryTask);
        }

        if (!CollectionUtils.isEmpty(inventoryTaskList)) {
            int batchSize = 500;
            for (int i = 0; i < inventoryTaskList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, inventoryTaskList.size());
                List<InventoryTask> batchList = inventoryTaskList.subList(i, end);
                mapper.batchInsert(batchList);
            }
        }

        return deviceList.size();

    }

    /**
     * @param inventoryPlan:
     * @return void
     * <AUTHOR>
     * @description 删除盘点任务
     * @date 2025/2/21 08:27
     */
    @Transactional(readOnly = false)
    @Override
    public void deleteByPlan(InventoryPlan inventoryPlan) {
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryPlanId", inventoryPlan.getId());
        InventoryTask record = new InventoryTask();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    /**
     * @param inventoryPlan:
     * @return int
     * <AUTHOR>
     * @description 查询盘点了多少
     * @date 2025/2/20 17:56
     */
    @Transactional(readOnly = false)
    @Override
    public int donesByPlan(InventoryPlan inventoryPlan) {
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryPlanId", inventoryPlan.getId());
        criteria.andEqualTo("isDeleted", "N");
        // 状态 IN 查询
        List<String> statusList = new ArrayList<>();
        statusList.add(InventoryConst.TASK_STATUS_PD);
        statusList.add(InventoryConst.TASK_STATUS_PK);
        criteria.andIn("status", statusList);
        // 并且create_date 不等于 update_date
        criteria.andCondition("create_date <> update_date");

        return mapper.selectCountByExample(example);
    }

    /**
     * @param planId:
     * @return List<InventoryTask>
     * <AUTHOR>
     * @description 获取异常记录
     * @date 2025/2/23 17:24
     */

    @Override
    public List<InventoryTask> getExpListByPlanId(String planId) {
        Example example = new Example(InventoryTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryPlanId", planId);
        criteria.andEqualTo("isDeleted", "N");
        // 放开限制处理所有的结果
        //  ret in ('2','3','4','5')
//        List<String> retList = new ArrayList<>();
//        retList.add(InventoryConst.TASK_RET_IDLE);
//        retList.add(InventoryConst.TASK_RET_FAULT);
//        retList.add(InventoryConst.TASK_RET_DAMAGE);
//        retList.add(InventoryConst.TASK_RET_LOST);
//        criteria.andIn("ret", retList);

        List<InventoryTask> records = mapper.selectByExample(example);
        return records;
    }

    @Override
    public DataSet<InventoryTaskExtResp> getDataSet(Page page, InventoryTaskListReq req) {

        if (!StringUtils.isEmpty(req.getCateId())) {
            String planId = req.getInventoryPlanId();
            InventoryPlan inventoryPlan = inventoryPlanService.selectById(planId);
            if (inventoryPlan == null) {
                throw new RuntimeException("盘点计划不存在");
            }

            if (SkuConst.SKU_TYPE_PTZC.equals(inventoryPlan.getSkuType())) {
                req.setCategoryId(req.getCateId());
            } else {
                req.setCategory22Id(req.getCateId());
            }
        }

        List<InventoryTaskExtResp> records = mapper.getList(page, req);
        // fmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public InventoryTaskStatusNumsResp getInventoryTaskStatusNums(String planId) {
        InventoryTaskStatusNumsResp resp = mapper.getInventoryTaskStatusNums(planId);
        if (resp == null) {
            resp = new InventoryTaskStatusNumsResp();
        }
        return resp;
    }

    @Override
    public InventoryTaskStatusNumsResp getInventoryTaskStatusNumsExt(InventoryTaskListReq req) {
        if (!StringUtils.isEmpty(req.getCateId())) {
            String planId = req.getInventoryPlanId();
            InventoryPlan inventoryPlan = inventoryPlanService.selectById(planId);
            if (inventoryPlan == null) {
                throw new RuntimeException("盘点计划不存在");
            }

            if (SkuConst.SKU_TYPE_PTZC.equals(inventoryPlan.getSkuType())) {
                req.setCategoryId(req.getCateId());
            } else {
                req.setCategory22Id(req.getCateId());
            }
        }
        InventoryTaskStatusNumsResp resp = mapper.getInventoryTaskStatusNumsExt(req);
        if (resp == null) {
            resp = new InventoryTaskStatusNumsResp();
        }
        return resp;
    }


    void dataFmt(InventoryTaskExtResp record) {
        if (record == null) {
            return;
        }
        // 类型
        record.setRetShow(dictService.cgetNameByValue(InventoryConst.INVENTORY_TASK_RET, record.getRet()));
        // 状态
        record.setStatusShow(dictService.cgetNameByValue(InventoryConst.INVENTORY_TASK_STATUS, record.getStatus()));
        Map<String, String> orgMap = orgService.cgetOrgMap();

        record.setBelongToOrgName(orgMap.get(record.getBelongToOrgId()));
        record.setUseOrgName(orgMap.get(record.getUseOrgId()));

        record.setAssetStatusShow(dictService.cgetNameByValue(DeviceConst.DEVICE_STATUS, record.getAssetStatus()));


    }

}
