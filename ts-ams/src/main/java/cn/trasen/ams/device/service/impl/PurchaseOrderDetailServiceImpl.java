package cn.trasen.ams.device.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.PurchaseOrderDetailMapper;
import cn.trasen.ams.device.service.PurchaseOrderDetailService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderDetailServiceImpl
 * @Description TODO
 * @date 2025年6月16日 下午1:49:57
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseOrderDetailServiceImpl implements PurchaseOrderDetailService {

    @Autowired
    private PurchaseOrderDetailMapper mapper;



    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseOrderDetail record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseOrderDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseOrderDetail record = new PurchaseOrderDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PurchaseOrderDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<PurchaseOrderDetail> getDataSetList(Page page, PurchaseOrderDetail record) {
        Example example = new Example(PurchaseOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<PurchaseOrderDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional
    @Override
    public void batchInsert(List<PurchaseOrderDetail> list) {
        mapper.batchInsert(list);
    }

    @Override
    public void deleteByPurchaseOrderId(String purchaseOrderId) {
        Example example = new Example(PurchaseOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("purchaseOrderId", purchaseOrderId);
        PurchaseOrderDetail record = new PurchaseOrderDetail();
        record.setIsDeleted("Y");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<PurchaseOrderDetail> selectByPurchaseOrderId(String purchaseOrderId) {

        Example example = new Example(PurchaseOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("purchaseOrderId", purchaseOrderId);
        List<PurchaseOrderDetail> records = mapper.selectByExample(example);

        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return records;
    }


}
