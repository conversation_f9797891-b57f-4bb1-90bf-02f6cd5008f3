package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintCost;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintCostService
 * @Description TODO
 * @date 2024年12月17日 下午4:33:22
 */
public interface MaintCostService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月17日 下午4:33:22
     * <AUTHOR>
     */
    Integer save(MaintCost record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月17日 下午4:33:22
     * <AUTHOR>
     */
    Integer update(MaintCost record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月17日 下午4:33:22
     * <AUTHOR>
     */
    Integer deleteById(String id);

    Integer deleteByTaskId(String taskId);

    /**
     * @return MaintCost
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月17日 下午4:33:22
     * <AUTHOR>
     */
    MaintCost selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MaintCost>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月17日 下午4:33:22
     * <AUTHOR>
     */
    DataSet<MaintCost> getDataSetList(Page page, MaintCost record);

    List<MaintCost> getList(MaintCost record);

}
