package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffDeviceListReq;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffPurchaseOrder;
import cn.trasen.ams.device.bean.signoff.SignoffExt;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.Signoff;
import cn.trasen.ams.device.model.SignoffDetail;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SignoffMapper extends Mapper<Signoff> {
    List<PenddingSignoffPurchaseOrder> getPenddingList(Page page, PurchaseOrder purchaseOrder);

    List<DeviceExtResp> getPenddingSignoffDeviceList(PenddingSignoffDeviceListReq req);

    List<SignoffExt> selectSignoffedList(Page page, SignoffExt record);

}