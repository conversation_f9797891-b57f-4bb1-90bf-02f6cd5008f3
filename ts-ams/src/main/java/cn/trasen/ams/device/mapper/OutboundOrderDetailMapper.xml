<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.OutboundOrderDetailMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.OutboundOrderDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="outbound_order_id" jdbcType="VARCHAR" property="outboundOrderId"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO `d_outbound_order_detail`
        (`id`, `outbound_order_id`, `device_id`,`t_loc`, `create_date`, `create_user`, `create_user_name`,
        `update_date`, `update_user`, `update_user_name`, `sso_org_code`, `sso_org_name`, `is_deleted`,
        `dept_id`,
        `dept_name`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.outboundOrderId},
            #{item.deviceId},
            #{item.tLoc},
            #{item.createDate},
            #{item.createUser},
            #{item.createUserName},
            #{item.updateDate},
            #{item.updateUser},
            #{item.updateUserName},
            #{item.ssoOrgCode},
            #{item.ssoOrgName},
            #{item.isDeleted},
            #{item.deptId},
            #{item.deptName}
            )
        </foreach>
    </insert>
    <select id="getOutboundingDeviceIds" resultType="java.lang.String">
        select DISTINCT t1.device_id
        from d_outbound_order_detail t1
                 left join `d_outbound_order` t2 on t1.`outbound_order_id` = t2.id
                 left join `d_device` t3 on t1.`device_id` = t3.id
        where t2.`status` = '0'
          and t3.`warehouse_status` = '4'
          and t2.`is_deleted` = 'N'
          and t1.`is_deleted` = 'N'
          and device_id is not null
    </select>

    <select id="selectByOutboundOrderId" resultType="cn.trasen.ams.device.bean.outin.OutBoundOrderDetailExtResp">
        select
            t1.*,
            t2.code            as code,
            t2.name            as name,
            t2.sku_type        as skuType,
            t2.need_install    as needInstall,
            t2.model           as model,
            t2.brand_id        as brandId,
            t3.name            as brandName,
            t2.manufacturer_id as manufacturerId,
            t4.name            as manufacturerName,
            t2.category22_id   as category22Id,
            t5.name            as category22Name,
            t6.unique_no       as uniqueNo,
            t6.asset_code      as assetCode,
            t7.name            as supplierName,
            t7.id              as supplierId,
            t2.unit            as unit,
            t6.original_val    as price
        from d_outbound_order_detail t1
                 left join d_device t6 on t1.device_id = t6.id
                 left join d_sku t2 on t6.sku_id = t2.id
                 left join c_brand t3 on t2.brand_id = t3.id
                 left join c_manufacturer t4 on t2.manufacturer_id = t4.id
                 left join d_category22 t5 on t2.category22_id = t5.id
                 left join c_supplier t7 on t6.supplier_id = t7.id
        where t1.outbound_order_id = #{outboundOrderId}
          and t1.is_deleted = 'N'
    </select>
</mapper>