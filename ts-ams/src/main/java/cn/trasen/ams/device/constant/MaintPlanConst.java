package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: MaintPlanConst
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 11:39
 * @version: 1.0
 */

public class MaintPlanConst {

    // 计划状态字典
    public static final String MAINT_PLAN_STATUS = "AMS_MAINT_PLAN_STATUS";

    // 归档状态字典
    public static final String MAINT_PLAN_ARCHIVE_STATUS = "AMS_MAINT_PLAN_ARCHIVE_STATUS";

    // 保养计划级别字典
    public static final String MAINT_PLAN_LEVEL = "AMS_MAINT_PLAN_LEVEL";

    //执行状态 0 未开始 1 进行中 2 已完成 3 超期未完成
    public static final String STATUS_WAIT = "0";

    public static final String STATUS_DOING = "1";

    public static final String STATUS_DONE = "2";

    public static final String STATUS_OVERDUE = "3";


}
