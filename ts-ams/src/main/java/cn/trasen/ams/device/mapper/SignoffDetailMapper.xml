<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.SignoffDetailMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.SignoffDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="signoff_id" jdbcType="VARCHAR" property="signoffId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="ythzcbm" jdbcType="VARCHAR" property="ythzcbm"/>
        <result column="birth_date" jdbcType="DATE" property="birthDate"/>
        <result column="use_date" jdbcType="DATE" property="useDate"/>
        <result column="maint_cycle_val" jdbcType="INTEGER" property="maintCycleVal"/>
        <result column="maint_cycle_unit" jdbcType="VARCHAR" property="maintCycleUnit"/>
        <result column="inspection_cycle_val" jdbcType="INTEGER" property="inspectionCycleVal"/>
        <result column="inspection_cycle_unit" jdbcType="VARCHAR" property="inspectionCycleUnit"/>
        <result column="warranty_period_val" jdbcType="INTEGER" property="warrantyPeriodVal"/>
        <result column="warranty_period_unit" jdbcType="VARCHAR" property="warrantyPeriodUnit"/>
        <result column="calibration_type" jdbcType="CHAR" property="calibrationType"/>
        <result column="calibration_cycle_val" jdbcType="INTEGER" property="calibrationCycleVal"/>
        <result column="calibration_cycle_unit" jdbcType="CHAR" property="calibrationCycleUnit"/>
        <result column="is_life_support" jdbcType="CHAR" property="isLifeSupport"/>
        <result column="is_special" jdbcType="CHAR" property="isSpecial"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <select id="selectBySignoffId" resultType="cn.trasen.ams.device.model.SignoffDetail">
        SELECT sd.id,
               sd.signoff_id,
               sd.device_id,
               sd.sn,
               sd.ythzcbm,
               sd.birth_date,
               sd.use_date,
               COALESCE(sd.lifespan_val, dd.lifespan_val, t2.lifespan_val) AS lifespan_val,
               COALESCE(sd.lifespan_unit, dd.lifespan_unit, t2.lifespan_unit) AS lifespan_unit,
               COALESCE(sd.maint_cycle_val, dd.maint_cycle_val, t2.maint_cycle_val) AS maint_cycle_val,
               COALESCE(sd.maint_cycle_unit, dd.maint_cycle_unit, t2.maint_cycle_unit) AS maint_cycle_unit,
               COALESCE(sd.inspection_cycle_val, dd.calibration_cycle_val, t2.calibration_cycle_val) AS inspection_cycle_val,
               COALESCE(sd.inspection_cycle_unit, dd.calibration_cycle_unit, t2.calibration_cycle_unit) AS inspection_cycle_unit,
               COALESCE(sd.warranty_period_val, dd.warranty_period_val) AS warranty_period_val,
               COALESCE(sd.warranty_period_unit, dd.warranty_period_unit) AS warranty_period_unit,
               COALESCE(sd.calibration_type, dd.calibration_type, t2.calibration_type) AS calibration_type,
               COALESCE(sd.calibration_cycle_val, dd.calibration_cycle_val, t2.calibration_cycle_val) AS calibration_cycle_val,
               COALESCE(sd.calibration_cycle_unit, dd.calibration_cycle_unit, t2.calibration_cycle_unit) AS calibration_cycle_unit,
               dd.unique_no,
               sd.create_date,
               sd.create_user,
               sd.create_user_name,
               sd.dept_id,
               sd.dept_name,
               sd.update_date,
               sd.update_user,
               sd.update_user_name,
               sd.sso_org_code,
               sd.sso_org_name,
               sd.is_deleted,
               t2.name,
               t2.model,
               t3.name as brand_name,
               t4.name as manufacturer_name,
               t2.is_life_support,
               t2.is_special
        FROM d_signoff t1
                 left join d_signoff_detail sd on t1.id = sd.signoff_id
                 left join d_device dd on sd.device_id = dd.id
                 LEFT JOIN d_sku t2 ON dd.sku_id = t2.id
                 LEFT JOIN c_brand t3 ON t2.brand_id = t3.id
                 LEFT JOIN c_manufacturer t4 ON t2.manufacturer_id = t4.id
        where t1.id = #{signoffId}
          and t1.is_deleted = 'N'
          and sd.is_deleted = 'N'
          and dd.is_deleted = 'N'
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO d_signoff_detail (
        id,
        signoff_id,
        device_id,
        sn,
        ythzcbm,
        birth_date,
        use_date,
        lifespan_val,
        lifespan_unit,
        maint_cycle_val,
        maint_cycle_unit,
        inspection_cycle_val,
        inspection_cycle_unit,
        warranty_period_val,
        warranty_period_unit,
        calibration_type,
        calibration_cycle_val,
        calibration_cycle_unit,
        create_date,
        create_user,
        create_user_name,
        dept_id,
        dept_name,
        update_date,
        update_user,
        update_user_name,
        sso_org_code,
        sso_org_name,
        is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.signoffId},
            #{item.deviceId},
            #{item.sn},
            #{item.ythzcbm},
            #{item.birthDate},
            #{item.useDate},
            #{item.lifespanVal},
            #{item.lifespanUnit},
            #{item.maintCycleVal},
            #{item.maintCycleUnit},
            #{item.inspectionCycleVal},
            #{item.inspectionCycleUnit},
            #{item.warrantyPeriodVal},
            #{item.warrantyPeriodUnit},
            #{item.calibrationType},
            #{item.calibrationCycleVal},
            #{item.calibrationCycleUnit},
            #{item.createDate},
            #{item.createUser},
            #{item.createUserName},
            #{item.deptId},
            #{item.deptName},
            #{item.updateDate},
            #{item.updateUser},
            #{item.updateUserName},
            #{item.ssoOrgCode},
            #{item.ssoOrgName},
            #{item.isDeleted}
            )
        </foreach>
    </insert>
</mapper>