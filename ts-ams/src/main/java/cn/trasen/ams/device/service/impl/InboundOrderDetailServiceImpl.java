package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.InboundOrderDetailMapper;
import cn.trasen.ams.device.model.InboundOrderDetail;
import cn.trasen.ams.device.service.InboundOrderDetailService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InboundOrderDetailServiceImpl
 * @Description TODO
 * @date 2025年2月11日 下午5:29:40
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InboundOrderDetailServiceImpl implements InboundOrderDetailService {

    @Autowired
    private InboundOrderDetailMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InboundOrderDetail record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InboundOrderDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InboundOrderDetail record = new InboundOrderDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InboundOrderDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<InboundOrderDetail> getDataSetList(Page page, InboundOrderDetail record) {
        Example example = new Example(InboundOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<InboundOrderDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void deleteByInboundOrderId(String inboundOrderId) {
        Assert.hasText(inboundOrderId, "入库单ID不能为空.");
        Example example = new Example(InboundOrderDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inboundOrderId", inboundOrderId);
        InboundOrderDetail record = new InboundOrderDetail();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<InboundOrderDetailExtResp> selectByInboundOrderId(String inboundOrderId) {

        return mapper.selectByInboundOrderId(inboundOrderId);
    }

    /**
     * @param :
     * @return List<String>
     * <AUTHOR>
     * @description 获取回退中的设备ID
     * @date 2025/3/3 11:30
     */
    @Override
    public List<String> getGoBackingDeviceIds() {
        return mapper.getGoBackingDeviceIds();
    }
}
