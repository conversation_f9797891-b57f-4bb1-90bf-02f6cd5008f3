<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.SignoffMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.Signoff">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="flow_no" jdbcType="VARCHAR" property="flowNo"/>
        <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId"/>
        <result column="sku_name_set" jdbcType="VARCHAR" property="skuNameSet"/>
        <result column="sure_date" jdbcType="DATE" property="sureDate"/>
        <result column="sure_user" jdbcType="VARCHAR" property="sureUser"/>
        <result column="sure_user_name" jdbcType="VARCHAR" property="sureUserName"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="file_set" jdbcType="VARCHAR" property="fileSet"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <select id="getPenddingList" parameterType="cn.trasen.ams.device.model.PurchaseOrder"
            resultType="cn.trasen.ams.device.bean.signoff.PenddingSignoffPurchaseOrder">
        SELECT
        <!-- 总采购数量 -->
        SUM(pod.nums) AS total_purchase_nums,
        <!-- 已入库总数量 -->
        COALESCE(inbound_stats.total_inbound_nums, 0) AS completed_inbound_nums,
        <!-- 已完成登记数量 -->
        COALESCE(device_stats.completed_inbound_nums, 0) AS completed_signoff_nums,
        <!-- 到货状态-->
        CASE
        WHEN COALESCE(inbound_stats.total_inbound_nums, 0) = 0 THEN '0'
        WHEN COALESCE(inbound_stats.total_inbound_nums, 0) &lt; pod.nums THEN '1'
        ELSE '2'
        END AS inbound_status,
        s.name AS supplier_name,
        po.*,
        ctt.id AS contract_id,
        ctt.flow_no AS contract_no
        FROM d_purchase_order po
        LEFT JOIN ts_base_oa.toa_ctt_contract ctt on po.id = ctt.sys_id
        LEFT JOIN c_supplier s ON po.supplier_id = s.id
        INNER JOIN d_purchase_order_detail_merge pod
        ON po.id = pod.purchase_order_id AND pod.is_deleted != 'Y' AND po.`is_mapper_dict` = '1'

        <!-- 统计入库数量 -->
        LEFT JOIN (
        SELECT
        io.rela_id AS purchase_order_id,
        SUM(iod.num) AS total_inbound_nums
        FROM d_inbound_order io
        LEFT JOIN d_inbound_order_detail iod ON io.id = iod.inbound_order_id
        WHERE (io.is_deleted IS NULL OR io.is_deleted != 'Y')
        AND (iod.is_deleted IS NULL OR iod.is_deleted != 'Y')
        AND io.status = '1'
        GROUP BY io.rela_id
        ) inbound_stats
        ON inbound_stats.purchase_order_id = po.id

        <!-- 统计已完成登记数量（设备表） -->
        LEFT JOIN (
        SELECT
        io.rela_id AS purchase_order_id,
        COUNT(*) AS completed_inbound_nums
        FROM d_inbound_order io
        LEFT JOIN d_device d ON d.inbound_order_id = io.id
        LEFT JOIN d_signoff_detail sd ON d.id = sd.device_id
        WHERE (d.acceptance_date is not null OR (sd.id IS NOT NULL and sd.`is_deleted` = 'N'))
        AND io.rela_id is not null
        AND (io.is_deleted IS NULL OR io.is_deleted != 'Y')
        GROUP BY io.rela_id
        ) device_stats
        ON device_stats.purchase_order_id = po.id
        WHERE po.is_deleted = 'N'
        <!-- 创建时间范围（逗号分隔） -->
        <if test="createDateQuery != null and createDateQuery != ''">
            AND po.create_date BETWEEN
            SUBSTRING_INDEX(#{createDateQuery}, ',', 1)
            AND
            SUBSTRING_INDEX(#{createDateQuery}, ',', -1)
        </if>
        <if test="name != null and name != ''">
            AND po.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="pmsql != null and pmsql != ''">
            ${pmsql}
        </if>
        GROUP BY po.id
        HAVING completed_signoff_nums &lt; total_purchase_nums and completed_inbound_nums > 0
        ORDER BY po.create_date DESC, po.flow_no, pod.sku_id
    </select>
    <select id="getPenddingSignoffDeviceList"
            parameterType="cn.trasen.ams.device.bean.signoff.PenddingSignoffDeviceListReq"
            resultType="cn.trasen.ams.device.bean.device.DeviceExtResp">
        SELECT t1.*
        , t2.name
        , t3.name AS brand_name
        , t4.name AS manufacturer_name
        , t5.id AS category22_id
        , t5.name AS category22_name
        , t6.name AS supplier_name
        , t2.is_life_support
        , t2.is_special
        , t2.model AS model
        , t9.id AS category_id
        , t9.name AS category_name
        , t7.batch_no as inbound_order_flow_no
        FROM d_device t1
        LEFT JOIN d_sku t2 ON t1.sku_id = t2.id
        LEFT JOIN c_brand t3 ON t2.brand_id = t3.id
        LEFT JOIN c_manufacturer t4 ON t2.manufacturer_id = t4.id
        LEFT JOIN d_category22 t5 ON t2.category22_id = t5.id
        LEFT JOIN c_supplier t6 ON t1.supplier_id = t6.id
        LEFT JOIN d_inbound_order t7 ON t1.`inbound_order_id` = t7.id
        LEFT JOIN d_purchase_order t8 ON t7.`rela_id` = t8.id
        LEFT JOIN c_category t9 ON t2.category_id = t9.id
        WHERE t8.id = #{purchaseOrderId}
        AND t1.is_deleted = 'N'
        AND t1.acceptance_date is null
        AND NOT EXISTS (
        SELECT 1 FROM d_signoff_detail sd
        WHERE sd.device_id = t1.id AND sd.is_deleted = 'N'
        )
        <if test="deviceName != null and deviceName != ''">
            AND t2.name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="ignoreIdList != null and ignoreIdList.size() > 0">
            AND t1.id NOT IN
            <foreach item="item" index="index" collection="ignoreIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t1.create_date desc
    </select>
    <select id="selectSignoffedList" resultType="cn.trasen.ams.device.bean.signoff.SignoffExt"
            parameterType="cn.trasen.ams.device.bean.signoff.SignoffExt">
        SELECT t1.*,
        t2.`name` AS name,
        t3.`name` AS `supplier_name`,
        t2.`supplier_contactor` AS supplier_contactor,
        t2.`supplier_mobile` AS supplier_mobile
        FROM d_signoff t1
        LEFT JOIN d_purchase_order t2 ON t1.`purchase_order_id` = t2.`id`
        LEFT JOIN c_supplier t3 ON t2.`supplier_id` = t3.`id`
        where t1.is_deleted = 'N'
        <if test="sureDateQuery">
            AND t1.sure_date BETWEEN
            SUBSTRING_INDEX(#{sureDateQuery}, ',', 1)
            AND
            SUBSTRING_INDEX(#{sureDateQuery}, ',', -1)
        </if>
        <if test="name != null and name != ''">
            AND t2.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="skuNameSet != null and skuNameSet != ''">
            AND t1.sku_name_set LIKE CONCAT('%', #{skuNameSet}, '%')
        </if>
        <if test="status != null and status != ''">
            AND t1.status = #{status}
        </if>
        <if test="pmsql != null and pmsql != ''">
            ${pmsql}
        </if>
        order by t1.sure_date desc
    </select>
</mapper>