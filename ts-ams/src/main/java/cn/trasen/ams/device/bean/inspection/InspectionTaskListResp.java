package cn.trasen.ams.device.bean.inspection;

import cn.trasen.ams.device.bean.device.DeviceSlimResp;
import cn.trasen.ams.device.model.InspectionTask;
import cn.trasen.ams.device.model.MaintTask;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.maintTask
 * @className: MaintTaskListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 17:15
 * @version: 1.0
 */

@Data
public class InspectionTaskListResp {
    private DeviceSlimResp deviceSlimResp;
    private InspectionTask inspectionTask;
}
