package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.service.ScriptService;
import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.controller
 * @className: TestController
 * @author: chenbin
 * @description: 测试
 * @date: 2024/12/12 15:22
 * @version: 1.0
 */

@RestController
@Api(tags = "TestController")
public class TestController {

    @Autowired
    DictService dictService;

    @Autowired
    RedisService redisService;

    @Autowired
    PermissionService permissionService;

    @Autowired
    ScriptService scriptService;

    @ApiOperation(value = "test", notes = "test")
    @GetMapping("/api/device/permission")
    public PlatformResult test() {
        try {
            PermissionResp p = permissionService.getPermission(PermissionConst.业务类型_审核资产转移单);
            return PlatformResult.success(p);
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "clearCache", notes = "clearCache")
    @GetMapping("/api/device/clearCache/{p}")
    public PlatformResult test(@PathVariable String p) {
        try {
            if (!"bingo1000w".equals(p)) {
                return PlatformResult.success("姿势不对");
            }
            redisService.clearAll();
            return PlatformResult.success("缓存清除成功");
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "修复资产编码", notes = "修复资产编码")
    @GetMapping("/api/device/fixAssetCode")
    public PlatformResult fixAssetCode() {
        try {
            scriptService.fixAssetCode();
            return PlatformResult.success("修复完成");
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


}
