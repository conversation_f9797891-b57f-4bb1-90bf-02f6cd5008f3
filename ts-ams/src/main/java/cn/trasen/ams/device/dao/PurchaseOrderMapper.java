package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryDetailResp;
import cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryItem;
import cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryResp;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface PurchaseOrderMapper extends Mapper<PurchaseOrder> {
    // TODO : 禁止把permissionSql 参数暴露给客户端
    Integer getNumsByStatus(@Param("definitionId") String definitionId, @Param("status") String status, @Param("permissionSql") String permissionSql);

    List<PurchaseOrder> getList(Page page, PurchaseOrder record);

    /**
     * 批量完成采购订单
     *
     * @param ids            采购订单ID列表
     * @param status         目标状态
     * @param updateUser     更新用户
     * @param updateUserName 更新用户名
     * @return 更新的记录数
     */
    Integer batchComplete(@Param("ids") List<String> ids,
                          @Param("status") String status,
                          @Param("updateUser") String updateUser,
                          @Param("updateUserName") String updateUserName);

    Integer getNoContract3ML(@Param("definitionId") String definitionId);

    List<PurchaseOrderWaitDeliveryResp> waitDelivery(@Param("skuType") String skuType);

    List<PurchaseOrderWaitDeliveryItem> waitDeliveryItem(@Param("id") String id);

    void updatePurchaseOrderArrivedStatus();

    PurchaseOrder selectById(@Param("id") String id);

}