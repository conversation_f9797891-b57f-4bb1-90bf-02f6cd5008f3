<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.TransferMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.Transfer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="sku_type" jdbcType="VARCHAR" property="skuType" />
    <result column="sku_name_set" jdbcType="VARCHAR" property="skuNameSet" />
    <result column="nums" jdbcType="INTEGER" property="nums" />
    <result column="asset_total_value" jdbcType="DECIMAL" property="assetTotalValue" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="do_date" jdbcType="TIMESTAMP" property="doDate" />
    <result column="do_note" jdbcType="VARCHAR" property="doNote" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="do_file_set" jdbcType="LONGVARCHAR" property="doFileSet" />
  </resultMap>
</mapper>