package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.inspection.*;
import cn.trasen.ams.device.bean.maintTask.MaintTaskListResp;
import cn.trasen.ams.device.bean.maintTask.SolveExpMaintTaskReq;
import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InspectionTask;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InspectionTaskService
 * @Description TODO
 * @date 2024年12月20日 下午1:50:02
 */
public interface InspectionTaskService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    Integer save(InspectionTask record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    Integer update(InspectionTask record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InspectionTask
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    InspectionTask selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InspectionTask>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    DataSet<InspectionTaskListResp> getDataSetList(Page page, InspectionTaskListReq record);

    void createTaskByInspectionPlan(InspectionPlan inspectionPlan);

    void updateTaskByInspectionPlan(InspectionPlan inspectionPlan);

    void deleteByInspectionPlan(InspectionPlan inspectionPlan);

    int getTasksByInspectionPlanId(String inspectionPlanId);

    List<InspectionTask> getTaskByPlanId(String inspectionPlanId);

    InspectionTaskDetailResp getTaskDetail(String inspectionPlanId);

    void completeInspectionTaskBatch(CompleteInspectionTaskReq record);

    void solveExpMaintTask(SolveExpInspectionTaskReq record);

}
