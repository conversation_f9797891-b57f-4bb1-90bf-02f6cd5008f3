package cn.trasen.ams.device.controller;

import cn.trasen.ams.common.constant.ConfigConst;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Config;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.ams.common.service.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ConfigController
 * @Description TODO
 * @date 2025年1月8日 下午2:19:37
 */
@RestController
@Api(tags = "ConfigController")
public class ConfigDController {

    private transient static final Logger logger = LoggerFactory.getLogger(ConfigDController.class);

    @Autowired
    private ConfigService configService;

    @ApiOperation(value = "资产标签打印模板选择", notes = "资产标签打印模板选择")
    @PostMapping("/api/device/config/assetTagTpl")

    public PlatformResult<String> saveAssetTagTpl(@RequestParam @ApiParam(value = "模板ID", required = true) String tplId) {
        try {
            Config req = new Config();
            req.setSysType(CommonConst.SYS_TYPE_ZCSB);
            req.setItemCode(ConfigConst.ASSET_TAG_TPL);
            req.setItemValue(tplId);
            req.setItemName("资产标签模板配置");
            req.setRemark("资产标签模板配置");
            configService.setConfig(req);
            return PlatformResult.success("保存成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取资产标签打印模板选择值", notes = "获取资产标签打印模板选择值")
    @GetMapping("/api/device/config/detail/assetTagTpl")

    public PlatformResult<String> getAssetTagTpl() {
        try {
            Config req = new Config();
            req.setSysType(CommonConst.SYS_TYPE_ZCSB);
            req.setItemCode(ConfigConst.ASSET_TAG_TPL);
            String ret = configService.getConfig(req);
            return PlatformResult.success(ret);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


}
