package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.lang.reflect.Method;
import java.util.stream.Collectors;

import cn.hutool.json.JSONUtil;
import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.service.SupplierService;
import cn.trasen.ams.device.bean.outin.InBoundOrderDetailResp;
import cn.trasen.ams.device.bean.purchase.*;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.PurchaseConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.PurchaseOrderDetail;
import cn.trasen.ams.device.model.PurchaseOrderDetailMerge;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.device.service.*;
import cn.trasen.ams.common.bean.instance.InstanceListReq;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfo;
import cn.trasen.ams.common.service.AMSInstanceService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.core.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.dao.PurchaseOrderMapper;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderServiceImpl
 * @Description TODO
 * @date 2025年6月10日 上午11:09:09
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseOrderServiceImpl implements PurchaseOrderService {

    @Autowired
    private PurchaseOrderMapper mapper;

    @Autowired
    private AMSInstanceService amsInstanceService;

    @Autowired
    private DictService dictService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @Autowired
    private PurchaseOrderDetailMergeService purchaseOrderDetailMergeService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private InboundOrderService inboundOrderService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseOrder record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseOrder record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseOrder record = new PurchaseOrder();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PurchaseOrder selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseOrder r = mapper.selectByPrimaryKey(id);
        dataFmt(r);
        return r;
    }

    @Transactional(readOnly = false)
    @Override
    public DataSet<PurchaseOrder> getDataSetList(Page page, PurchaseOrder record) {

        updatePurchaseOrderArrivedStatus();
        List<PurchaseOrder> records = mapper.getList(page, record);
        // 格式化数据

        if (!CollectionUtils.isEmpty(records)) {
            for (PurchaseOrder rec : records) {
                dataFmt(rec);
            }
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    private void dataFmt(PurchaseOrder record) {

        record.setStatusShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_ORDER_STATUS, record.getStatus()));
        record.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, record.getOrderType()));

        // 供应商
        if (!StringUtil.isEmpty(record.getSupplierId())) {
            try {
                String supplierName = (String) redisService.fetch("purchaseOrder:supplierName:" + record.getId(), () -> {

                    Supplier supplier = supplierService.selectById(record.getSupplierId());
                    if (supplier == null) {
                        return null;
                    }
                    return supplier.getName();
                }, 300);
                record.setSupplierName(supplierName);

            } catch (Exception e) {
                // 供应商获取失败
                record.setSupplierName("");
                throw new RuntimeException(e);
            }
        } else {
            record.setSupplierName("");
        }

        record.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, record.getSkuType()));
        record.setPurchaseTypeShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_TYPE, record.getPurchaseType()));
        record.setFundSourceShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_FUND_SOURCE, record.getFundSource()));
        record.setArrivedStatusShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_ORDER_ARRIVED_STATUS, record.getArrivedStatus()));
    }

    @Override
    public Map<String, Integer> getNumsWithStatus(String definitionId, List<String> withStatus) {

        Map<String, Integer> res = new HashMap<>();

        for (String status : withStatus) {
            Integer nums = mapper.getNumsByStatus(definitionId, status, "");
            res.put(status, nums);
        }

        return res;
    }

    private String genFlowNo() {
        return serialNoGenService.genByDate("CGDD");
    }


    public PurchaseOrderInstanceConfig getInstanceConfig(String definitionId) {

        PurchaseOrderInstanceConfig config = new PurchaseOrderInstanceConfig();

        Map<String, String> configMap = dictService.cgetCV(PurchaseConst.PURCHASE_ORDER_INSTANCE_CONFIG);

        String purchaseOrderInstanceConfig = configMap.get(definitionId);

        if (StringUtil.isEmpty(purchaseOrderInstanceConfig)) {
            throw new RuntimeException("当前审批流程未配置采购订单创建匹配规则，请联系管理员配置！");
        }

        config.setRule(purchaseOrderInstanceConfig);

        // 获取流程定义信息
        AMSInstanceDefinitionInfo definitionInfo = amsInstanceService.getInstanceDefinitionById(definitionId);
        if (definitionInfo == null) {
            throw new RuntimeException("采购订单流程定义不存在，请联系管理员！");
        }
        config.setDefinitionId(definitionId);
        config.setDefinitionName(definitionInfo.getWorkflowName());

        // 获取主表相关
        String mainTableName = amsInstanceService.getMainFormTableName(definitionInfo.getFormId());

        String sonTableId = amsInstanceService.getSonFromTableId(definitionInfo.getFormId());
        String sonTableName = amsInstanceService.getSonFormTableName(definitionInfo.getFormId());

        config.setFormId(definitionInfo.getFormId());
        config.setTableId(sonTableId);
        config.setMainTableName(mainTableName);
        config.setSonTableName(sonTableName);


        // 先根据｜拆分 得到长度为2的
        String[] configParts = purchaseOrderInstanceConfig.split("\\|");
        if (configParts.length != 2) {
            throw new RuntimeException("采购订单配置格式错误，请联系管理员！");
        }
        // 下表0的是一些关键配置，下标为1的是字段配置
        String keyConfig = configParts[0];
        // keyConfig 包括skuType、isMapperDict、numsField
        String[] keyConfigParts = keyConfig.split(",");
        if (keyConfigParts.length < 4) {
            throw new RuntimeException("采购订单流程约定字段配置错误，请联系管理员！");
        }
        config.setSkuType(keyConfigParts[0]);
        config.setIsMapperDict(keyConfigParts[1]);
        config.setNumsField(keyConfigParts[2]);
        config.setNameField(keyConfigParts[3]);

        // 字段配置未做限制 可以检测字段是否配置正确
        String fieldConfig = configParts[1];
        String[] fieldConfigParts = fieldConfig.split(",");
        if (fieldConfigParts.length < 1) {
            throw new RuntimeException("采购订单流程展示字段配置错误，请联系管理员！");
        }
        // 转list
        List<String> showFields = Arrays.asList(fieldConfigParts);
        config.setShowfield(showFields);

        return config;
    }

    @Override
    public Integer getPurchaseRows(String definitionId, String status, Integer month) {

        InstanceListReq req = new InstanceListReq();
        req.setStatus(status);
        req.setDefinitionId(definitionId);
        req.setJoin(" LEFT JOIN (\n" + "    SELECT sum(COALESCE(nums, 0)) as purchase_nums, instance_id \n" + "    FROM d_purchase_order_detail \n" + "    WHERE is_deleted = 'N'\n" + "    GROUP BY instance_id  \n" + ") as dod \n" + "    ON dod.instance_id = zdyt.ID ");
        PurchaseOrderInstanceConfig config = getInstanceConfig(definitionId);
        String defWhere = " and (dod.purchase_nums < zdyt.`" + config.getNumsField() + "` or dod.instance_id is null )";
        // 增加 超过3个月未转合同的订单
        if (month != null && month > 0) {
            defWhere += " and wii.WF_FINISHED_DATE < DATE_SUB(CURDATE(), INTERVAL " + month + " MONTH)";
        }

        req.setDefWhere(defWhere);
        req.setDefField(",CAST(zdyt.`" + config.getNumsField() + "` - COALESCE(dod.purchase_nums, 0) AS SIGNED) AS wait_purchase_nums" + ",CAST(COALESCE(dod.purchase_nums, 0) AS SIGNED) AS purchase_nums");

        // 设置page 防止为空报错
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(10);

        DataSet<Map<String, Object>> ret = amsInstanceService.getInstanceSonFromList(page, req);
        return ret.getTotalCount();
    }

    @Override
    public Integer getNoContract3ML(String definitionId) {
        // 获取超过三个月没有登记合同的订单数量
        return mapper.getNoContract3ML(definitionId);
    }

    @Override
    public List<PurchaseOrderWaitDeliveryResp> waitDelivery(String skuType) {
        return mapper.waitDelivery(skuType);
    }

    @Override
    public PurchaseOrderWaitDeliveryDetailResp waitDeliveryDetail(String id) {
        PurchaseOrder order = mapper.selectById(id);

        List<PurchaseOrderWaitDeliveryItem> deliveryItemList = mapper.waitDeliveryItem(id);
        PurchaseOrderWaitDeliveryDetailResp r = new PurchaseOrderWaitDeliveryDetailResp();
        r.setPurchaseOrder(order);
        r.setPurchaseOrderWaitDeliveryItemList(deliveryItemList);
        return r;
    }


    @Transactional(readOnly = false)
    @Override
    public void updatePurchaseOrderArrivedStatus() {
        mapper.updatePurchaseOrderArrivedStatus();
    }


    /**
     * 准备采购订单数据，包括初始化基本信息、处理明细、计算汇总数据等
     *
     * @param record 采购订单插入请求对象，包含采购订单主表和明细信息
     * @throws RuntimeException 当采购订单或明细为空时抛出异常
     */
    private void dataPrepare(PurchaseOrderInsertReq record) {

        PurchaseOrder purchaseOrder = record.getPurchaseOrder();
        List<PurchaseOrderDetail> purchaseOrderDetailList = record.getPurchaseOrderDetailList();

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        // 处理采购订单基本信息
        processPurchaseOrder(purchaseOrder, user);
        // 处理采购订单明细并计算汇总数据
        PurchaseOrderSummary summary = processPurchaseOrderDetails(purchaseOrderDetailList, purchaseOrder, user);
        // 更新采购订单汇总信息
        updatePurchaseOrderSummary(purchaseOrder, summary);
        // 处理合并明细
        record.setPurchaseOrderDetailMergeList(mergeDetailsBySkuId(purchaseOrderDetailList));
    }


    /**
     * 初始化采购订单的基本信息，包括ID、表单信息、流程编号等
     *
     * @param purchaseOrder 采购订单对象
     * @param user          当前用户信息
     */
    private void processPurchaseOrder(PurchaseOrder purchaseOrder, ThpsUser user) {
        String definitionId = purchaseOrder.getDefinitionId();
        PurchaseOrderInstanceConfig config = getInstanceConfig(definitionId);

        if (StringUtil.isEmpty(purchaseOrder.getId())) {
            purchaseOrder.setId(IdGeneraterUtils.nextId());
        }

        purchaseOrder.setFormId(config.getFormId());
        purchaseOrder.setSonTableId(config.getTableId());
        purchaseOrder.setMainTableName(config.getMainTableName());
        purchaseOrder.setSonTableName(config.getSonTableName());
        purchaseOrder.setRule(config.getRule());
        purchaseOrder.setOrderType(config.getDefinitionName());
        purchaseOrder.setSkuType(config.getSkuType());
        purchaseOrder.setFlowNo(genFlowNo());
        purchaseOrder.setIsMapperDict(config.getIsMapperDict());

        // 设置订单状态
        purchaseOrder.setStatus(PurchaseConst.PURCHASE_PAGE_STATUS_ING);
        purchaseOrder.setArrivedStatus(PurchaseConst.PURCHASE_ORDER_ARRIVED_STATUS_NO);

        // 处理用户相关信息
        handleUserInfo(purchaseOrder, user);
    }

    /**
     * 采购订单汇总数据类，用于存储计算过程中的中间数据
     */
    private static class PurchaseOrderSummary {
        BigDecimal totalPurchaseValue = BigDecimal.ZERO;
        int totalPurchaseNum = 0;
        Map<String, Integer> skuNameCountMap = new HashMap<>();
    }

    /**
     * 处理采购订单明细并计算汇总数据
     *
     * @param details       采购订单明细列表
     * @param purchaseOrder 采购订单对象
     * @param user          当前用户信息
     * @return 采购订单汇总数据对象
     */
    private PurchaseOrderSummary processPurchaseOrderDetails(List<PurchaseOrderDetail> details, PurchaseOrder purchaseOrder, ThpsUser user) {
        PurchaseOrderSummary summary = new PurchaseOrderSummary();
        PurchaseOrderInstanceConfig config = getInstanceConfig(purchaseOrder.getDefinitionId());

        details.forEach(detail -> {
            processDetail(detail, purchaseOrder, user, config, summary);
        });

        return summary;
    }

    /**
     * 处理单个采购订单明细
     *
     * @param detail        采购订单明细
     * @param purchaseOrder 采购订单对象
     * @param user          当前用户信息
     * @param config        采购订单实例配置
     * @param summary       采购订单汇总数据对象
     */
    private void processDetail(PurchaseOrderDetail detail, PurchaseOrder purchaseOrder, ThpsUser user, PurchaseOrderInstanceConfig config, PurchaseOrderSummary summary) {
        Map<String, String> kvMap = JSON.parseObject(detail.getInstanceFieldKvJson(), Map.class);
        String skuName = kvMap.getOrDefault(config.getNameField(), "");
        Integer agreeNums = Integer.valueOf(kvMap.getOrDefault(config.getNumsField(), "0"));
        // 计算总价值
        BigDecimal totalValue = detail.getPrice().multiply(BigDecimal.valueOf(detail.getNums()));
        summary.totalPurchaseValue = summary.totalPurchaseValue.add(totalValue);

        // 更新汇总数据
        summary.totalPurchaseNum += detail.getNums();
        summary.skuNameCountMap.merge(skuName, detail.getNums(), Integer::sum);

        // 设置明细基本信息
        setDetailBasicInfo(detail, purchaseOrder, user);

        // 处理SKU ID
        if (CommonConst.NO.equals(purchaseOrder.getIsMapperDict())) {
            detail.setSkuId(MD5.string2MD5(detail.getId()));
        }

        // 计算总价
        detail.setTotal(totalValue);
        detail.setAgreeNums(agreeNums);

    }

    /**
     * 设置采购订单明细的基本信息
     *
     * @param detail        采购订单明细
     * @param purchaseOrder 采购订单对象
     * @param user          当前用户信息
     */
    private void setDetailBasicInfo(PurchaseOrderDetail detail, PurchaseOrder purchaseOrder, ThpsUser user) {
        detail.setId(IdGeneraterUtils.nextId());
        detail.setPurchaseOrderId(purchaseOrder.getId());
//        detail.setAgreeNums(detail.getAgreeNums());
        detail.setCreateDate(new Date());
        detail.setUpdateDate(new Date());
        detail.setCreateUser(user.getUsercode());
        detail.setCreateUserName(user.getUsername());
        detail.setUpdateUser(user.getUsercode());
        detail.setUpdateUserName(user.getUsername());
        detail.setSsoOrgCode(user.getCorpcode());
        detail.setSsoOrgName(user.getOrgName());
        detail.setDeptId(user.getDeptId());
        detail.setDeptName(user.getDeptname());
        detail.setIsDeleted("N");
    }

    /**
     * 更新采购订单的汇总信息
     *
     * @param purchaseOrder 采购订单对象
     * @param summary       采购订单汇总数据对象
     */
    private void updatePurchaseOrderSummary(PurchaseOrder purchaseOrder, PurchaseOrderSummary summary) {
        String skuNameSet = summary.skuNameCountMap.entrySet().stream().map(entry -> entry.getKey() + "*" + entry.getValue()).collect(Collectors.joining(","));

        purchaseOrder.setSkuNums(summary.skuNameCountMap.size());
        purchaseOrder.setTotalNums(summary.totalPurchaseNum);
        purchaseOrder.setAssetTotalValue(summary.totalPurchaseValue);
        purchaseOrder.setSkuNameSet(skuNameSet);
    }

    /**
     * 处理采购订单的用户相关信息，包括创建人、部门、组织等信息
     * 使用反射机制动态设置用户相关字段，避免硬编码
     *
     * @param purchaseOrder 采购订单对象
     * @param user          当前用户信息
     */
    private void handleUserInfo(PurchaseOrder purchaseOrder, ThpsUser user) {
        if (user == null) return;

        Map<String, String> userInfoMap = new HashMap<>();
        userInfoMap.put("RCreateUser", user.getUsercode());
        userInfoMap.put("RCreateUserName", user.getUsername());
        userInfoMap.put("ROrgId", user.getCorpcode());
        userInfoMap.put("ROrgName", user.getOrgName());
        userInfoMap.put("RDeptId", user.getDeptId());
        userInfoMap.put("RDeptName", user.getDeptname());

        userInfoMap.forEach((field, value) -> {
            try {
                String getter = "get" + field;
                String setter = "set" + field;
                Method getMethod = PurchaseOrder.class.getMethod(getter);
                Method setMethod = PurchaseOrder.class.getMethod(setter, String.class);

                if (StringUtil.isEmpty((String) getMethod.invoke(purchaseOrder))) {
                    setMethod.invoke(purchaseOrder, value);
                }
            } catch (Exception e) {
                // 忽略反射异常
            }
        });
    }

    /**
     * 按SKU ID合并采购订单明细
     *
     * @param details 原始采购订单明细列表
     * @return 合并后的采购订单明细列表
     */
    private List<PurchaseOrderDetailMerge> mergeDetailsBySkuId(List<PurchaseOrderDetail> details) {
        return details.stream().collect(Collectors.groupingBy(PurchaseOrderDetail::getSkuId)).values().stream().map(this::mergeDetailGroup).collect(Collectors.toList());
    }

    /**
     * 合并一组相同SKU ID的采购订单明细
     *
     * @param group 相同SKU ID的采购订单明细组
     * @return 合并后的采购订单明细
     */
    private PurchaseOrderDetailMerge mergeDetailGroup(List<PurchaseOrderDetail> group) {
        if (group.isEmpty()) return null;

        PurchaseOrderDetail first = group.get(0);
        PurchaseOrderDetailMerge merged = new PurchaseOrderDetailMerge();

        // 检查同一SKU ID下的价格是否一致
        BigDecimal firstPrice = first.getPrice();
        boolean hasInconsistentPrice = group.stream().anyMatch(detail -> detail.getPrice() == null || detail.getPrice().compareTo(firstPrice) != 0);
        if (hasInconsistentPrice) {
            throw new RuntimeException("同一商品价格必须一致");
        }

        // 复制基础字段
        BeanUtils.copyProperties(first, merged);
        merged.setIsDeleted("N");

        // 合并数量
        int totalNums = group.stream().mapToInt(d -> d.getNums() != null ? d.getNums() : 0).sum();
        int totalAgreeNums = group.stream().mapToInt(d -> d.getAgreeNums() != null ? d.getAgreeNums() : 0).sum();
        BigDecimal totalPrice = first.getPrice().multiply(new BigDecimal(totalNums));

        merged.setTotal(totalPrice);
        merged.setNums(totalNums);
        merged.setAgreeNums(totalAgreeNums);

        // 处理instance_field_kv_json
        handleInstanceFieldKvJson(merged, first);

        return merged;
    }

    /**
     * 处理采购订单明细的实例字段JSON数据
     * 如果sku_desc存在且包含分隔符，则按分隔符分割并转换为JSON格式
     *
     * @param merged 合并后的采购订单明细
     * @param first  原始采购订单明细
     */
    private void handleInstanceFieldKvJson(PurchaseOrderDetailMerge merged, PurchaseOrderDetail first) {
        String skuDesc = first.getSkuDesc();
        String json = first.getInstanceFieldKvJson();
        if (StringUtil.isEmpty(json)) {
            String[] descArr = skuDesc.split("#");
            Map<String, String> kvMap = new LinkedHashMap<>();
            // 这里通过约定 根据下标位置来
            // 0: 名称, 1: 规格, 2: 品牌 3: 厂家
            kvMap.put("skuName", descArr.length > 0 ? descArr[0] : "");
            kvMap.put("skuSpec", descArr.length > 1 ? descArr[1] : "");
            kvMap.put("skuBrand", descArr.length > 2 ? descArr[2] : "");
            kvMap.put("skuManufacturer", descArr.length > 3 ? descArr[3] : "");
            merged.setInstanceFieldKvJson(JSONUtil.toJsonStr(kvMap));
        } else {
            merged.setInstanceFieldKvJson(json);
        }
    }

    /**
     * 将采购订单明细转换为合并明细对象
     *
     * @param detail 原始采购订单明细
     * @return 合并后的采购订单明细
     */
    private PurchaseOrderDetailMerge convertToMergeDetail(PurchaseOrderDetail detail) {
        PurchaseOrderDetailMerge mergeDetail = new PurchaseOrderDetailMerge();
        BeanUtils.copyProperties(detail, mergeDetail);
        return mergeDetail;
    }


    @Transactional(readOnly = false)
    @Override
    public void insert(PurchaseOrderInsertReq record) {
        // 处理数据
        dataPrepare(record);

        PurchaseOrder purchaseOrder = record.getPurchaseOrder();
        // 保存采购订单
        save(purchaseOrder);

        // 保存采购订单明细
        List<PurchaseOrderDetail> purchaseOrderDetailList = record.getPurchaseOrderDetailList();
        purchaseOrderDetailService.batchInsert(purchaseOrderDetailList);

        // 保存采购订单合并明细
        List<PurchaseOrderDetailMerge> purchaseOrderDetailMergeList = record.getPurchaseOrderDetailMergeList();
        purchaseOrderDetailMergeService.batchInsert(purchaseOrderDetailMergeList);
    }

    @Transactional(readOnly = false)
    @Override
    public void edit(PurchaseOrderInsertReq record) {

        PurchaseOrder purchaseOrder = record.getPurchaseOrder();

        if (!canModify(purchaseOrder)) {
            throw new RuntimeException("该采购订单已经处理了到货登记，无法进行修改");
        }

        // 处理数据
        dataPrepare(record);

        // 更新采购订单
        update(purchaseOrder);

        // 更新采购订单明细
        List<PurchaseOrderDetail> purchaseOrderDetailList = record.getPurchaseOrderDetailList();
        purchaseOrderDetailService.deleteByPurchaseOrderId(purchaseOrder.getId());
        purchaseOrderDetailService.batchInsert(purchaseOrderDetailList);

        // 更新采购订单合并明细
        List<PurchaseOrderDetailMerge> purchaseOrderDetailMergeList = record.getPurchaseOrderDetailMergeList();
        purchaseOrderDetailMergeService.deleteByPurchaseOrderId(purchaseOrder.getId());
        purchaseOrderDetailMergeService.batchInsert(purchaseOrderDetailMergeList);

    }

    @Transactional(readOnly = false)
    @Override
    public void cancel(String orderId) {

        Assert.hasText(orderId, "订单ID不能为空");
        PurchaseOrder purchaseOrder = selectById(orderId);

        if (!canModify(purchaseOrder)) {
            throw new RuntimeException("该采购订单已经处理了到货登记，无法进行撤销");
        }


        if (!PurchaseConst.PURCHASE_PAGE_STATUS_APPLY.contains(purchaseOrder.getStatus())) {
            throw new RuntimeException("只有进行中的采购订单允许撤销");
        }

        // TODO 后续如果创建了收货单等关联数据，也不允许撤销

        deleteById(orderId);
        // 删除采购订单明细
        purchaseOrderDetailService.deleteByPurchaseOrderId(orderId);
        // 删除采购订单合并明细
        purchaseOrderDetailMergeService.deleteByPurchaseOrderId(orderId);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchCancel(List<String> ids) {
        ids.forEach(this::cancel);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer batchComplete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new RuntimeException("采购订单ID列表不能为空");
        }

        // 获取当前用户信息
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String updateUser = user != null ? user.getUsercode() : "";
        String updateUserName = user != null ? user.getUsername() : "";

        // 调用Mapper进行批量更新
        Integer updatedCount = mapper.batchComplete(ids, PurchaseConst.PURCHASE_PAGE_STATUS_FINISH, updateUser, updateUserName);

        if (updatedCount == null || updatedCount == 0) {
            throw new RuntimeException("没有找到符合条件的采购订单或更新失败");
        }
        return updatedCount;
    }


    /**
     * @param order:
     * @return boolean
     * <AUTHOR>
     * @description 是否可以修改
     * @date 2025/7/2 15:36
     */
    private boolean canModify(PurchaseOrder order) {
        // 如果到货登记了则不给修改，和撤销了
        List<InBoundOrderDetailResp> list = inboundOrderService.getInboundDetailListByRelaId(order.getId());

        if (list == null || list.size() == 0) {
            return true;
        }

        return false;
    }
}
