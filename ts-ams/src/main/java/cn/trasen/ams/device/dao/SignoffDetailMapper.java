package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.model.SignoffDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SignoffDetailMapper extends Mapper<SignoffDetail> {
    List<SignoffDetail> selectBySignoffId(@Param("signoffId") String signoffId);

    void batchInsert(List<SignoffDetail> signoffDetails);
}