package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.inventory.*;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.model.InventoryPlan;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InventoryTask;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryTaskService
 * @Description TODO
 * @date 2025年2月20日 下午5:33:21
 */
public interface InventoryTaskService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    Integer save(InventoryTask record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    Integer update(InventoryTask record);

    void doit(InventoryTask record);

    InventoryTaskExtResp doit4Scan(InventoryTaskDoitScanReq inventoryTaskDoitScanReq);

    void forceDoit(String planId);

    List<InventoryTaskExtResp> doit4Rfid(InventoryTaskDoitBatchReq InventoryTaskDoitBatchReq);


    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InventoryTask
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    InventoryTask selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InventoryTask>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    DataSet<InventoryTask> getDataSetList(Page page, InventoryTask record);


    int insertByPlan(InventoryPlan inventoryPlan);

    void deleteByPlan(InventoryPlan inventoryPlan);

    int donesByPlan(InventoryPlan inventoryPlan);

    List<InventoryTask> getExpListByPlanId(String planId);

    DataSet<InventoryTaskExtResp> getDataSet(Page page, InventoryTaskListReq req);

    InventoryTaskStatusNumsResp getInventoryTaskStatusNums(String planId);

    InventoryTaskStatusNumsResp getInventoryTaskStatusNumsExt(InventoryTaskListReq record);


}
