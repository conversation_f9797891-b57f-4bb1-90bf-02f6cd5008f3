package cn.trasen.ams.device.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "d_maint_work_hours")
@Setter
@Getter
public class MaintWorkHours {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 保养计划执行工单ID
     */
    @Column(name = "maint_task_id")
    @ApiModelProperty(value = "保养计划执行工单ID")
    private String maintTaskId;

    /**
     * 工程师ID
     */
    @Column(name = "engineer_id")
    @ApiModelProperty(value = "工程师ID")
    private String engineerId;

    /**
     * 保养日期
     */
    @Column(name = "operation_date")
    @ApiModelProperty(value = "保养日期")
    private Date operationDate;

    /**
     * 保养工时
     */
    @Column(name = "operation_hours")
    @ApiModelProperty(value = "保养工时")
    private BigDecimal operationHours;

    /**
     * 旅行工时
     */
    @Column(name = "travel_hours")
    @ApiModelProperty(value = "旅行工时")
    private BigDecimal travelHours;

    /**
     * 费用
     */
    @ApiModelProperty(value = "费用")
    private BigDecimal costs;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}