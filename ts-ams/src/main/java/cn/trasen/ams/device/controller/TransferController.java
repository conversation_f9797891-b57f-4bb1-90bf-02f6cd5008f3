package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.device.bean.comm.CommApproveReq;
import cn.trasen.ams.device.bean.transfer.TransferDetailResp;
import cn.trasen.ams.device.bean.transfer.TransferInsertReq;
import cn.trasen.ams.device.model.TransferDetail;
import cn.trasen.ams.device.service.TransferDetailService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Transfer;
import cn.trasen.ams.device.service.TransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName TransferController
 * @Description TODO
 * @date 2025年4月18日 上午9:16:35
 */
@RestController
@Api(tags = "TransferController")
@Validated
public class TransferController {

    private transient static final Logger logger = LoggerFactory.getLogger(TransferController.class);

    @Autowired
    private TransferService transferService;

    @Autowired
    private TransferDetailService transferDetailService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveTransfer
     * @Description 新增
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/transfer/save")
    public PlatformResult<String> saveTransfer(@Validated @RequestBody TransferInsertReq record) {
        try {
            transferService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateTransfer
     * @Description 编辑
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/transfer/update")
    public PlatformResult<String> updateTransfer(@Validated @RequestBody TransferInsertReq record) {
        try {
            transferService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Transfer>
     * @Title selectTransferById
     * @Description 根据ID查询
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/transfer/{id}")
    public PlatformResult<TransferDetailResp> selectTransferById(@PathVariable String id) {
        try {
            Transfer record = transferService.selectById(id);
            transferService.dataFmt(record);
            List<TransferDetail> transferDetailList = transferDetailService.selectByTransferId(id);

            TransferDetailResp transferDetailResp = new TransferDetailResp();

            transferDetailResp.setTransfer(record);
            transferDetailResp.setTransferDetailList(transferDetailList);

            return PlatformResult.success(transferDetailResp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "批量详情", notes = "批量详情")
    @PostMapping("/api/device/transfer/batchDetail")
    public PlatformResult selectTransferListByIdList(@RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return PlatformResult.failure("ID列表不能为空");
        }

        List<TransferDetailResp> list = new ArrayList<>();
        for (String id : idList) {
            Transfer record = transferService.selectById(id);
            List<TransferDetail> transferDetailList = transferDetailService.selectByTransferId(id);
            if (record == null) {
                continue;
            }
            transferService.dataFmt(record);
            TransferDetailResp transferDetailResp = new TransferDetailResp();
            transferDetailResp.setTransfer(record);
            transferDetailResp.setTransferDetailList(transferDetailList);
            list.add(transferDetailResp);
        }
        return PlatformResult.success(list);
    }

    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteTransferById
     * @Description 根据ID删除
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/transfer/delete/{id}")
    public PlatformResult<String> deleteTransferById(@PathVariable String id) {
        try {
            transferService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Transfer>
     * @Title selectTransferList
     * @Description 查询列表
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/transfer/list")
    public DataSet<Transfer> selectTransferList(Page page, Transfer record) {
        return transferService.getDataSetList(page, record);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/api/device/transfer/export")
    public ResponseEntity<byte[]> export(Transfer record) throws IOException {

        try {

            List<Transfer> exportList = transferService.getList(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/transferExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("资产转移列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /*
     * @param record:
     * @return PlatformResult<String>
     * <AUTHOR>
     * @description 资产转移审核
     * @date 2025/4/22 14:11
     */
//    @RightValid(action = "审核资产转移单", message = "您没有审批资产转移单的权限")
    @PostMapping("/api/device/transfer/approve")
    public PlatformResult<String> approve(@RequestBody CommApproveReq record) {
        try {
            transferService.approve(record);
            return PlatformResult.success();
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }
}
