package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintAccessoryConsume;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintAccessoryConsumeService
 * @Description TODO
 * @date 2024年12月17日 下午4:32:47
 */
public interface MaintAccessoryConsumeService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    Integer save(MaintAccessoryConsume record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    Integer update(MaintAccessoryConsume record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @param taskId
     * @return Integer
     * @Title deleteByTaskId
     * @Description 根据任务ID删除
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    Integer deleteByTaskId(String taskId);

    /**
     * @return MaintAccessoryConsume
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    MaintAccessoryConsume selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MaintAccessoryConsume>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月17日 下午4:32:47
     * <AUTHOR>
     */
    DataSet<MaintAccessoryConsume> getDataSetList(Page page, MaintAccessoryConsume record);

    List<MaintAccessoryConsume> getList(MaintAccessoryConsume record);
}
