package cn.trasen.ams.device.controller;

import cn.trasen.ams.common.bean.instance.InstanceListReq;
import cn.trasen.ams.common.bean.instance.PageHeadData;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfo;
import cn.trasen.ams.common.service.AMSInstanceService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.controller
 * @className: PurchaseController
 * @author: chenbin
 * @description: 采购控制器
 * @date: 2025/5/27 11:39
 * @version: 1.0
 */

@RestController
@Api(tags = "PurchaseController")
public class PurchaseController {


    @Autowired
    private AMSInstanceService amsInstanceService;


    @ApiOperation(value = "获取采购类型列表", notes = "获取采购类型列表")
    @PostMapping("/api/device/purchase/purchaseTypeList")
    public PlatformResult getPurchaseTypeList() {
        // 获取采购类型列表
        try {
            return PlatformResult.success(amsInstanceService.getPurchaseTypeList());
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "获取表单的字段配置", notes = "获取表单的字段配置")
    @PostMapping("/api/device/purchase/getDefinitionFieldConfig/{formId}")
    public PlatformResult filedConfig(@PathVariable("formId") String formId) {
        try {
            return PlatformResult.success(amsInstanceService.getInstanceDefinitionFieldSetListByTemplateId(formId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "获流程列表，且不同状态下面的流程实例数量", notes = "获流程列表，且不同状态下面的流程实例数量")
    @PostMapping("/api/device/purchase/pageHeadData")

    public PlatformResult getPageHeadData(@RequestBody List<String> withStatus) {
        try {
            List<AMSInstanceDefinitionInfo> definitionList = amsInstanceService.getPurchaseTypeList();

            Map<String, Map<String, Integer>> numsDefinitionMap = new HashMap<>();
            Map<String, Integer> numsStatusMap = new HashMap<>();
            for (AMSInstanceDefinitionInfo definition : definitionList) {
                Map<String, Integer> numsStatusMapItem = amsInstanceService.getInstancesWithStatus(definition.getWfDefinitionId(), withStatus);
                numsDefinitionMap.put(definition.getWfDefinitionId(), numsStatusMapItem);
                // 累加每个状态的数量
                for (Map.Entry<String, Integer> entry : numsStatusMapItem.entrySet()) {
                    String k = entry.getKey();
                    Integer v = entry.getValue();
                    if (numsStatusMap.containsKey(k)) {
                        numsStatusMap.put(k, numsStatusMap.get(k) + v);
                    } else {
                        numsStatusMap.put(k, v);
                    }
                }
            }

            PageHeadData resp = new PageHeadData();

            resp.setDefinitionList(definitionList);
            resp.setNumsStatusMap(numsStatusMap);
            resp.setNumsDefinitionMap(numsDefinitionMap);

            return PlatformResult.success(resp);
        } catch (RuntimeException e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/api/device/purchase/list/{definitionId}/{status}")
    public DataSet selectInstanceList(Page page, @RequestParam Map<String, String> query, @RequestBody Map<String, String> params, @PathVariable("definitionId") String definitionId, @PathVariable("status") String status) {
        InstanceListReq req = new InstanceListReq();
        req.setStatus(status);
        req.setDefinitionId(definitionId);
        req.setQuery(query);
        req.setParams(params);
        return amsInstanceService.getInstanceList(page, req);
    }



}
