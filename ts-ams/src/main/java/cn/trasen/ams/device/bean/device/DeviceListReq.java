package cn.trasen.ams.device.bean.device;

import cn.trasen.ams.device.model.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean
 * @className: DeviceListRe
 * @author: chenbin
 * @description: 设备列表请求
 * @date: 2024/9/11 17:23
 * @version: 1.0
 */

@Data
public class DeviceListReq extends Device {


    @Transient
    @ApiModelProperty(value = "动作 outbound 出库 ｜ gobacking 退回入库")
    private String act;

    @Transient
    @ApiModelProperty(value = "不查询的ID，多个逗号隔开")
    private String ignoreId;


    @ApiModelProperty(value = "资产目录类型 0 医疗设备 1 普通资产")
    private String skuType;

    @ApiModelProperty(value = "在库状态 0 在库 1 在役 2 减少（报废）")
    private String warehouseStatus;

    @Transient
    @ApiModelProperty(value = "不需要传")
    private List<String> ignoreIds;

    @Column(name = "brand_id")
    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    /**
     * 厂家ID
     */

    @Column(name = "manufacturer_id")
    @ApiModelProperty(value = "厂家ID")
    private String manufacturerId;


    /**
     * 22分类ID
     */
    @Column(name = "category22_id")
    @ApiModelProperty(value = "22分类ID")
    private String category22Id;

    /**
     * 资产分类ID
     */
    @ApiModelProperty(value = "资产分类ID")
    private String categoryId;


    @ApiModelProperty(value = "验收日期起")
    private Date acceptanceDateStart;


    @ApiModelProperty(value = "验收日期止")
    private Date acceptanceDateEnd;

    @ApiModelProperty(value = "设备规格")
    private String model;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "厂家名称")
    private String manufacturerName;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "获取期初数据 0 否 1 是")
    private String getBase;

    @ApiModelProperty(value = "前端忽略，传输无效")
    private String pmsql;
}
