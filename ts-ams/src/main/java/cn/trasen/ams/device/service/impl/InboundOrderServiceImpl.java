package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.SupplierService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.device.bean.outin.InBoundOrderDetailResp;
import cn.trasen.ams.device.bean.outin.InBoundOrderInsertReq;
import cn.trasen.ams.device.bean.outin.InBoundOrderListReq;
import cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.OperationConst;
import cn.trasen.ams.device.constant.OutInConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.model.InboundOrderDetail;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.device.service.*;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.*;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.feign.message.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.dao.InboundOrderMapper;
import cn.trasen.ams.device.model.InboundOrder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InboundOrderServiceImpl
 * @Description TODO
 * @date 2025年2月11日 下午5:02:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InboundOrderServiceImpl implements InboundOrderService {

    @Autowired
    private InboundOrderMapper mapper;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private InboundOrderDetailService inboundOrderDetailService;

    @Autowired
    private DictService dictService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private OutboundOrderService outboundOrderService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private EmployeeService employeeService;


    @Transactional(readOnly = false)
    @Override
    public Integer save(@Validated InboundOrder record) {
        if (StringUtils.isEmpty(record.getId())) {
            record.setId(IdGeneraterUtils.nextId());
        }
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setCreateDeptId(user.getDeptId());
            record.setCreateDeptName(user.getDeptname());
            // 统一规划
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        // 自动写入单号
        autoFillColumn(record);

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public String insert(InBoundOrderInsertReq record) {
        InboundOrder inboundOrder = record.getInboundOrder();
        List<InboundOrderDetail> inboundOrderDetailList = record.getInboundOrderDetailList();
        if (inboundOrder == null) {
            throw new RuntimeException("入库单信息不能为空");
        }

        if (inboundOrderDetailList == null || inboundOrderDetailList.size() == 0) {
            throw new RuntimeException("入库单明细信息不能为空");
        }
        String id = IdGeneraterUtils.nextId();
        inboundOrder.setId(id);
        // 设置状态为初始状态
        inboundOrder.setStatus(CommonConst.NO);
        // 取默认仓库
        Warehouse defWarehouse = warehouseService.getZcsbDefWarehouse();
        inboundOrder.setWarehouseId(defWarehouse.getId());

        List<String> goBackingDeviceIds = Collections.emptyList();

        // 获取正在退回入库中的设备
        if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
            goBackingDeviceIds = inboundOrderDetailService.getGoBackingDeviceIds();
        }


        // 计算总金额
        BigDecimal totalAmount = new BigDecimal(0);
        for (InboundOrderDetail inboundOrderDetail : inboundOrderDetailList) {
            inboundOrderDetail.setInboundOrderId(id);
            inboundOrderDetail.setLoc(inboundOrder.getLoc());
            BigDecimal price = inboundOrderDetail.getPrice();
            int num = inboundOrderDetail.getNum();
            BigDecimal amount = price.multiply(new BigDecimal(num));
            // 对 totalAmount 进行累加
            totalAmount = totalAmount.add(amount);
            if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
                // 所有正在退回入库中的设备不允许重复发起退回入库
                if (!CollectionUtils.isEmpty(goBackingDeviceIds)) {
                    if (goBackingDeviceIds.contains(inboundOrderDetail.getDeviceId())) {
                        throw new RuntimeException("存在正在退回入库中的设备，不允许重复发起退回入库");
                    }
                }
            }
            // 插入副表数据
            inboundOrderDetailService.save(inboundOrderDetail);
        }

        inboundOrder.setMoney(totalAmount.toString());

        // 插入主表数据
        save(inboundOrder);
        // 标记设备为退回入库中
        if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
            deviceService.goBacking(inboundOrder);
        }

        // 入库审核提醒
        noticeCheck(record);

        return id;
    }


    private String genBatchNo() {
        return serialNoGenService.genByDate("RK");
    }

    private void autoFillColumn(InboundOrder inboundOrder) {
        inboundOrder.setBatchNo(genBatchNo());
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InboundOrder record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public String edit(InBoundOrderInsertReq record) {

        InboundOrder inboundOrder = record.getInboundOrder();
        List<InboundOrderDetail> inboundOrderDetailList = record.getInboundOrderDetailList();

        if (CommonConst.YES.equals(inboundOrder.getStatus())) {
            throw new RuntimeException("已入库的单据不允许修改");
        }

        if (inboundOrder == null) {
            throw new RuntimeException("入库单信息不能为空");
        }


        if (StringUtils.isEmpty(inboundOrder.getId())) {
            throw new RuntimeException("入库单ID不能为空");
        }


        if (inboundOrderDetailList == null || inboundOrderDetailList.size() == 0) {
            throw new RuntimeException("入库单明细信息不能为空");
        }


        // 如果是退回入库则需要将设备状态回退
        if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
            deviceService.goBackCancel(inboundOrder);
        }

        // 对之前的入库明细进行删除
        inboundOrderDetailService.deleteByInboundOrderId(inboundOrder.getId());


        // 对当前的入库单进行修改
        BigDecimal totalAmount = new BigDecimal(0);
        for (InboundOrderDetail inboundOrderDetail : inboundOrderDetailList) {
            inboundOrderDetail.setInboundOrderId(inboundOrder.getId());
            BigDecimal price = inboundOrderDetail.getPrice();
            int num = inboundOrderDetail.getNum();
            BigDecimal amount = price.multiply(new BigDecimal(num));
            // 对 totalAmount 进行累加
            totalAmount = totalAmount.add(amount);
            // 插入副表数据
            inboundOrderDetailService.save(inboundOrderDetail);
        }
        inboundOrder.setMoney(totalAmount.toString());
        update(inboundOrder);

        // 如果是退回入库则需要把新绑定的设备改成退回入库中
        if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(inboundOrder.getType())) {
            deviceService.goBacking(inboundOrder);
        }

        return inboundOrder.getId();
    }


    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        // 判断状态是否为已入库 否则不允许删除
        InboundOrder row = mapper.selectByPrimaryKey(id);

        if (CommonConst.YES.equals(row.getStatus())) {
            throw new RuntimeException("已入库的单据不允许删除");
        }

        // 判断是否退回入库 则资产回退状态
        if (OutInConst.INBOUND_ORDER_TYPE_THRU.equals(row.getType())) {
            deviceService.goBackCancel(row);
        }


        InboundOrder record = new InboundOrder();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InboundOrder selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }


    private void setEndDateIfNeeded(InBoundOrderListReq record) {
        if (StringUtils.isEmpty(record.getEnd()) && !StringUtils.isEmpty(record.getStart())) {
            // Parse start date and set end time to 23:59:59
            LocalDateTime startDateTime = LocalDateTime.parse(record.getStart(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endDateTime = startDateTime.withHour(23).withMinute(59).withSecond(59);
            record.setEnd(endDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
    }


    @Override
    public DataSet<InboundOrder> getDataSetList(Page page, InBoundOrderListReq record) {
        setEndDateIfNeeded(record);
        List<InboundOrder> records = getList(page, record);

        // Format conversion
        for (InboundOrder row : records) {
            dataFmt(row);
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<InboundOrder> getListNoPage(InBoundOrderListReq record) {
        setEndDateIfNeeded(record);
        return mapper.getListNoPage(record);
    }


    public void dataFmt(InboundOrder record) {
        if (record == null) {
            return;
        }

        if (StringUtils.isEmpty(record.getSupplyName()) && !StringUtils.isEmpty(record.getSupplyId())) {
            Supplier supplier = supplierService.selectById(record.getSupplyId());
            if (supplier != null) {
                record.setSupplyName(supplier.getName());
            }
        }
        // 类型
        record.setTypeShow(dictService.cgetNameByValue(OutInConst.INBOUND_ORDER_TYPE, record.getType()));
        // 状态
        record.setStatusShow(dictService.cgetNameByValue(OutInConst.INBOUND_ORDER_STATUS, record.getStatus()));
        // skuType
        record.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, record.getSkuType()));
        // 所属科室
        Map<String, String> orgMap = orgService.cgetOrgMap();
        record.setBelongToOrgName(orgMap.get(record.getBelongToOrgid()));
        // 仓库
        if (record.getWarehouseId() != null) {
            Warehouse warehouse = warehouseService.selectById(record.getWarehouseId());
            if (warehouse != null) {
                record.setWarehouseName(warehouse.getName());
            }
        }
    }


    @Override
    public List<InboundOrder> getList(Page page, InBoundOrderListReq record) {
        // 权限控制
        // TODO 这里必须先置空，防止前端注入
        // TODO 这里必须先置空，防止前端注入
        // TODO 这里必须先置空，防止前端注入
        record.setPmsql(null);
        String business = PermissionConst.二级业务类型_入库单列表;

        StringBuffer sql = new StringBuffer();
        permissionService.appendPermissionSql(business, sql);

        if (sql.length() > 0) {
            record.setPmsql(sql.toString());
        }

        return mapper.getList(page, record);
    }


    @Transactional(readOnly = false)
    @Override
    public void confirm(String id) {

        InboundOrder record = selectById(id);
        if (record == null) {
            throw new RuntimeException("入库单不存在");
        }

        if (CommonConst.YES.equals(record.getStatus())) {
            throw new RuntimeException("已入库的单据不允许重复入库");
        }

        record.setStatus(CommonConst.YES);
        record.setDoDate(new Date());

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setDoerId(user.getUsercode());
            record.setDoerName(user.getUsername());
        }
        update(record);

        switch (record.getType()) {
            case OutInConst.INBOUND_ORDER_TYPE_THRU:
                // 更新档案信息
                deviceService.goBack(record);
                break;
            case OutInConst.INBOUND_ORDER_TYPE_QTRU:
            case OutInConst.INBOUND_ORDER_TYPE_CGRK:
            default:
                // 暂时也按照其他入库处理
                try {
                    deviceService.createByInBoundOrder(record);
                } catch (Exception e) {
                    throw new RuntimeException("设备建档失败" + e.getMessage());
                }
                // 如果选择了科室则创建出库单
                if (!StringUtils.isEmpty(record.getBelongToOrgid())) {
                    outboundOrderService.createByInBoundOrder(record);
                }
                break;
        }
        // 通知创建人已经审核
        noticeChecked(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> ids) {
        // 根据ids 查询出所有的入库单
        for (String id : ids) {
            confirm(id);
        }
    }

    @Override
    public List<InBoundOrderDetailResp> getInboundDetailListByRelaId(String relaId) {
        // 根据relaId查处所有相关的订单
        Example example = new Example(InboundOrder.class);
        example.createCriteria()
                .andEqualTo("relaId", relaId)
                .andEqualTo("isDeleted", "N");

        // order by
        example.setOrderByClause("create_date");

        List<InboundOrder> inboundOrders = mapper.selectByExample(example);

        if (CollectionUtils.isEmpty(inboundOrders)) {
            return Collections.emptyList();
        }

        // 根据订单查询订单的明细并组装list
        List<InBoundOrderDetailResp> result = new ArrayList<>();
        for (InboundOrder inboundOrder : inboundOrders) {
            InBoundOrderDetailResp resp = new InBoundOrderDetailResp();
            dataFmt(inboundOrder);
            resp.setInboundOrder(inboundOrder);

            // 查询入库单明细
            List<InboundOrderDetailExtResp> detailList = inboundOrderDetailService.selectByInboundOrderId(inboundOrder.getId());
            resp.setInboundOrderDetailExtResp(detailList);

            result.add(resp);
        }

        return result;
    }


    private void noticeChecked(InboundOrder record) {
        String userCode = record.getCreateUser();
        String doerId = record.getDoerId();
        String doerName = record.getDoerName();

        if (StringUtils.isEmpty(userCode) || StringUtils.isEmpty(doerId)) {
            return;
        }

        String content = "您申请的入库单：" + record.getBatchNo() + "已审核通过";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(userCode)  //接收人
                .sender(doerId) //发送人
                .senderName(doerName) //发送人name
                .subject("资产设备-入库申请审核通过").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/inventory-management/storage-record").source("入库管理").build();
        NoticeService.sendAsynNotice(notice);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 入库审核提醒
     * @date 2025/5/22 15:42
     */
    private void noticeCheck(InBoundOrderInsertReq record) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String receiver = employeeService.cgetThpsUserSetByOperationId(OperationConst.入库审批);

        if (StringUtils.isEmpty(receiver)) {
            return;
        }

        InboundOrder inboundOrder = record.getInboundOrder();
        List<InboundOrderDetail> inboundOrderDetailList = record.getInboundOrderDetailList();

        String content = user.getDeptname() + ":" + user.getUsername() + "发起了一个入库申请，单号：" + inboundOrder.getBatchNo() + "，涉及" + inboundOrderDetailList.size() + "个产品，涉及金额" + inboundOrder.getMoney() + "元，请及时审核";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(receiver)  //接收人
                .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                .subject("资产设备-入库申请").url("#").wxSendType("1").businessId(record.getInboundOrder().getId()).toUrl("/ts-web-equipment/inventory-management/storage-record").source("入库管理").build();

        NoticeService.sendAsynNotice(notice);
    }
}
