package cn.trasen.ams.device.bean.inspection;

import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.ams.device.model.InspectionTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inspection
 * @className: CompleteInspectionTaskReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/23 17:16
 * @version: 1.0
 */

@Data
public class CompleteInspectionTaskReq {
    @ApiModelProperty("巡检计划信息")
    private InspectionPlan inspectionPlan;

    @ApiModelProperty("巡检任务信息")
    private List<InspectionTask> inspectionTaskList;
}
