package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_outbound_order_detail")
@Setter
@Getter
public class OutboundOrderDetail {
    @Id
    private String id;
    @Column(name = "outbound_order_id")
    private String outboundOrderId;

    /**
     * 资产ID
     */
    @NotNull(message = "资产ID不能为空")
    @Column(name = "device_id")
    @ApiModelProperty(value = "资产ID")
    private String deviceId;


    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_no")
    private String contractNo;


    @ApiModelProperty(value = "出库位置")
    @Column(name = "t_loc")
    private String tLoc;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}