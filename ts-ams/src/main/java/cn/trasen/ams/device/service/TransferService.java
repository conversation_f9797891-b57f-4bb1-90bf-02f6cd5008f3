package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.comm.CommApproveReq;
import cn.trasen.ams.device.bean.transfer.TransferInsertReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Transfer;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName TransferService
 * @Description TODO
 * @date 2025年4月18日 上午9:16:35
 */
public interface TransferService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    Integer save(Transfer record);

    void insert(TransferInsertReq transferInsertReq);

    void edit(TransferInsertReq transferInsertReq);

    void approve(CommApproveReq commApproveReq);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    Integer update(Transfer record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Transfer
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    Transfer selectById(String id);

    void dataFmt(Transfer transfer);


    /**
     * @param page
     * @param record
     * @return DataSet<Transfer>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年4月18日 上午9:16:35
     * <AUTHOR>
     */
    DataSet<Transfer> getDataSetList(Page page, Transfer record);

    List<Transfer> getList(Transfer record);
}
