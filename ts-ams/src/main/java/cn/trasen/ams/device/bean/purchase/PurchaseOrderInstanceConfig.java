package cn.trasen.ams.device.bean.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseOrderInstanceConfig
 * @author: chenbin
 * @description: 采购订单对应的流程匹配配置
 * @date: 2025/6/17 14:34
 * @version: 1.0
 */
@Data
public class PurchaseOrderInstanceConfig {

    @ApiModelProperty(value = "流程定义ID")
    private String definitionId;

    @ApiModelProperty(value = "流程定义名称")
    private String definitionName;

    @ApiModelProperty(value = "流程定义主表定义ID")
    // dp_form_template
    private String formId;
    // dp_table
    @ApiModelProperty(value = "流程定义子表ID")
    private String tableId;

    @ApiModelProperty(value = "主表名称")
    private String mainTableName;

    @ApiModelProperty(value = "子表名称")
    private String sonTableName;

    @ApiModelProperty(value = "字典匹配规则")
    private String rule;

    @ApiModelProperty(value = "关联什么sku类型")
    private String skuType;

    @ApiModelProperty(value = "是否匹配字典")
    private String isMapperDict;

    @ApiModelProperty(value = "数量字段是哪个")
    private String numsField;

    @ApiModelProperty(value = "名称字段是哪个")
    private String nameField;

    @ApiModelProperty(value = "所有展示的字段")
    private List<String> showfield;


}
