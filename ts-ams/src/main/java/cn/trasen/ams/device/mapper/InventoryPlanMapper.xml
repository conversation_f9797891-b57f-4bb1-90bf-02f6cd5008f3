<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InventoryPlanMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.InventoryPlan">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="engineer_id_set" jdbcType="VARCHAR" property="engineerIdSet"/>
        <result column="start_at" jdbcType="TIMESTAMP" property="startAt"/>
        <result column="end_at" jdbcType="TIMESTAMP" property="endAt"/>
        <result column="is_pic_must" jdbcType="BIT" property="isPicMust"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="cate22_id_set" jdbcType="LONGVARCHAR" property="cate22IdSet"/>
        <result column="org_id_set" jdbcType="LONGVARCHAR" property="orgIdSet"/>
    </resultMap>

    <select id="selectCategoryIdList" parameterType="java.lang.String" resultType="cn.trasen.ams.device.bean.inventory.InventoryPlanCateResp">
        select t3.category_id as id,
        t4.name,
        <choose>
            <when test='statusExt == "2"'>
                count(case when t1.status IN('1','2') then 1 else null end) as nums
            </when>
            <otherwise>
                count(case when t1.status = '0' then 1 else null end) as nums
            </otherwise>
        </choose>
        from d_inventory_task t1
        left join d_device t2 on t1.`device_id` = t2.`id`
        left join d_sku t3 on t2.`sku_id` = t3.`id`
        left join c_category t4 on t3.`category_id` = t4.`id`
        where inventory_plan_id = #{inventoryPlanId}
        and t3.category_id is not null
        and t1.`is_deleted` = 'N'
        <if test="orgId != null and orgId != ''">
            and t2.belong_to_org_id = #{orgId}
        </if>
        group by t3.category_id;
    </select>

    <select id="selectCategory22IdList" parameterType="java.lang.String" resultType="cn.trasen.ams.device.bean.inventory.InventoryPlanCateResp">
        select t3.category22_id as id,
        t4.name,
        <choose>
            <when test='statusExt == "2"'>
                count(case when t1.status IN('1','2') then 1 else null end) as nums
            </when>
            <otherwise>
                count(case when t1.status = '0' then 1 else null end) as nums
            </otherwise>
        </choose>
        from d_inventory_task t1
        left join d_device t2 on t1.`device_id` = t2.`id`
        left join d_sku t3 on t2.`sku_id` = t3.`id`
        left join d_category22 t4 on t3.`category22_id` = t4.`id`
        where inventory_plan_id = #{inventoryPlanId}
        and t3.category22_id is not null
        and t1.`is_deleted` = 'N'
        <if test="orgId != null and orgId != ''">
            and t2.belong_to_org_id = #{orgId}
        </if>
        group by t3.category22_id;
    </select>

    <select id="selectOrgIdList" parameterType="java.lang.String" resultType="cn.trasen.ams.device.bean.inventory.InventoryPlanOrgResp">
        select t2.belong_to_org_id as id,
        <choose>
            <when test='statusExt == "2"'>
                count(case when t1.status IN('1','2') then 1 else null end) as nums
            </when>
            <otherwise>
                count(case when t1.status = '0' then 1 else null end) as nums
            </otherwise>
        </choose>
        from d_inventory_task t1
        left join d_device t2 on t1.`device_id` = t2.`id`
        left join d_sku t3 on t2.`sku_id` = t3.`id`
        where inventory_plan_id = #{inventoryPlanId}
        and t2.belong_to_org_id is not null
        and t1.`is_deleted` = 'N'
        group by t2.belong_to_org_id;
    </select>


</mapper>