package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.DiyTpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DiyTplService
 * @Description TODO
 * @date 2024年12月19日 下午5:36:26
 */
public interface DiyTplService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    Integer save(DiyTpl record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    Integer update(DiyTpl record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DiyTpl
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    DiyTpl selectById(String id);

    DiyTpl copyById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DiyTpl>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    DataSet<DiyTpl> getDataSetList(Page page, DiyTpl record);

    List<DiyTpl> getListByDeviceId(String deviceId);
}
