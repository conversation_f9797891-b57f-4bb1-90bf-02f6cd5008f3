package cn.trasen.ams.device.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_inspection_plan")
@Setter
@Getter
public class InspectionPlan {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流水号
     */
    @Column(name = "serial_no")
    @ApiModelProperty(value = "流水号")
    private String serialNo;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 关联设备集合
     */
    @Column(name = "device_id_set")
    @ApiModelProperty(value = "关联设备集合")
    private String deviceIdSet;

    /**
     * 指定执行人
     */
    @Column(name = "engineer_id_set")
    @ApiModelProperty(value = "指定执行人")
    private String engineerIdSet;

    /**
     * 实际执行人
     */
    @Column(name = "e_engineer_id_set")
    @ApiModelProperty(value = "实际执行人")
    @JsonProperty("eEngineerIdSet")
    private String eEngineerIdSet;

    /**
     * 巡检模板ID
     */
    @Column(name = "diy_tpl_id")
    @ApiModelProperty(value = "巡检模板ID")
    private String diyTplId;

    /**
     * 模板的json配置快照
     */
    @Column(name = "diy_tpl_config_json")
    @ApiModelProperty(value = "模板的json配置快照")
    private String diyTplConfigJson;

    /**
     * 本次计划执行的开始时间;本次计划执行的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "start_at")
    @ApiModelProperty(value = "本次计划执行的开始时间;本次计划执行的开始时间")
    private Date startAt;

    /**
     * 本次计划执行的结束时间;本次计划执行的结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_at")
    @ApiModelProperty(value = "本次计划执行的结束时间;本次计划执行的结束时间")
    private Date endAt;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "e_start_at")
    @ApiModelProperty(value = "实际开始时间")
    @JsonProperty("eStartAt")
    private Date eStartAt;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "e_end_at")
    @ApiModelProperty(value = "实际结束时间")
    @JsonProperty("eEndAt")
    private Date eEndAt;

    /**
     * 执行状态 0 未开始 1 进行中 2 已完成 3 超期未完成;执行状态 0 未开始 1 进行中 2 已完成
     */
    @ApiModelProperty(value = "执行状态 0 未开始 1 进行中 2 已完成 3 超期未完成;")
    private String status;

    @Transient
    @ApiModelProperty(value = "执行状态名称")
    private String statusShow;

    /**
     * 巡检类型
     */
    @ApiModelProperty(value = "巡检类型")
    private String cate;

    @Transient
    @ApiModelProperty(value = "巡检类型名称")
    private String cateShow;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String elx;

    /**
     * 归档状态
     */
    @Column(name = "archive_status")
    @ApiModelProperty(value = "归档状态")
    private String archiveStatus;

    @Transient
    private String archiveStatusShow;

    /**
     * 完成数量;完成数量
     */
    @ApiModelProperty(value = "完成数量;完成数量")
    private Integer completes;

    /**
     * 目标数量;targets
     */
    @ApiModelProperty(value = "目标数量;targets")
    private Integer targets;

    /**
     * 巡检耗费工时
     */
    @Column(name = "work_hours")
    @ApiModelProperty(value = "巡检耗费工时")
    private BigDecimal workHours;

    /**
     * 旅行工时
     */
    @Column(name = "travel_hours")
    @ApiModelProperty(value = "旅行工时")
    private BigDecimal travelHours;

    /**
     * 正常数量
     */
    @ApiModelProperty(value = "正常数量")
    private Integer oks;

    /**
     * 异常数量
     */
    @ApiModelProperty(value = "异常数量")
    private Integer exps;

    /**
     * 评价描述
     */
    @Column(name = "evaluation_text")
    @ApiModelProperty(value = "评价描述")
    private String evaluationText;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    @Column(name = "category22_id_set")
    private String category22IdSet;

    @Column(name = "category22_name_set")
    private String category22NameSet;

    @Column(name = "org_id_set")
    private String orgIdSet;

    @Column(name = "org_name_set")
    private String orgNameSet;

    @Column(name = "engineer_name_set")
    private String engineerNameSet;

    @JsonProperty("eEngineerNameSet")
    @Column(name = "e_engineer_name_set")
    private String eEngineerNameSet;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}