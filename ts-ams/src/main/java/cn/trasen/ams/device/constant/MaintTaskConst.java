package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: MaintTaskConst
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/17 14:45
 * @version: 1.0
 */

public class MaintTaskConst {
    public static final String MAINT_TAKS_STATUS = "AMS_MAINT_TAKS_STATUS";
    public static final String MAINT_TASK_MAINT_STATUS = "AMS_MAINT_TASK_MAINT_STATUS";
    public static final String MAINT_TASK_COMPLETE_STATUS = "AMS_MAINT_TASK_COMPLETE_STATUS";
    public static final String MAINT_TASK_SOLUTION = "AMS_MAINT_TASK_SOLUTION";

    // 保存资料
    public static final String ACT_SAVE = "1";
    // 提交并保存
    public static final String ACT_SUBMIT = "2";


    // 正常使用
    public static final String MAINT_TASK_STATUS_NORMAL = "1";
    //LACK 可以使用，部分功能失效
    public static final String MAINT_TASK_STATUS_LACK = "2";
    // FAULT 无法使用，故障维修
    public static final String MAINT_TASK_STATUS_FAULT = "3";
    // DEATH 无法维修，待报废
    public static final String MAINT_TASK_STATUS_DEATH = "4";
}
