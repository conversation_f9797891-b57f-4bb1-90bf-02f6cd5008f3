package cn.trasen.ams.device.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.common.service.EmployeeService;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.homs.bean.base.EmployeeResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.TransferDetailMapper;
import cn.trasen.ams.device.model.TransferDetail;
import cn.trasen.ams.device.service.TransferDetailService;
import org.thymeleaf.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName TransferDetailServiceImpl
 * @Description TODO
 * @date 2025年4月18日 上午9:26:50
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class TransferDetailServiceImpl implements TransferDetailService {

    @Autowired
    private TransferDetailMapper mapper;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private OrgService orgService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(TransferDetail record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(TransferDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        TransferDetail record = new TransferDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public TransferDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<TransferDetail> getDataSetList(Page page, TransferDetail record) {
        Example example = new Example(TransferDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<TransferDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void deleteByTransferId(String transferId) {
        // 根据 transferId 进行逻辑删除
        TransferDetail record = new TransferDetail();
        record.setTransferId(transferId);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        Example example = new Example(TransferDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("transferId", transferId);
        mapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<TransferDetail> selectByTransferId(String transferId) {

        // 根据TransferId 查询
        Example example = new Example(TransferDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("transferId", transferId);
        List<TransferDetail> records = mapper.selectByExample(example);
        if (records == null || records.isEmpty()) {
            return Collections.emptyList();
        }
        // 数据格式化
        records.forEach(this::dataFmt);
        // 抽取所有的deviceId
        List<String> deviceIds = records.stream()
                .map(TransferDetail::getDeviceId)
                .collect(Collectors.toList());
        String deviceIdsStr = String.join(",", deviceIds);

        // 查询设备信息
        if (!deviceIds.isEmpty()) {
            Map<String, DeviceExtResp> deviceMap = deviceService.getListByIds(deviceIdsStr).stream()
                    .collect(Collectors.toMap(DeviceExtResp::getId, device -> device));

            // 装载设备属性到 records 中
            records.forEach(record -> {
                DeviceExtResp device = deviceMap.get(record.getDeviceId());
                if (device != null) {
                    record.setName(device.getName());
                    record.setModel(device.getModel());
                    record.setAssetCode(device.getAssetCode());
                    record.setBelongToOrgId(device.getBelongToOrgId());
                    record.setBelongToOrgName(device.getBelongToOrgName());
                    record.setLoc(device.getLoc());
                    record.setOriginalValue(device.getOriginalVal());
                    record.setGzwbh(device.getGzwbh());
                }
            });
        }

        return records;
    }

    private void dataFmt(TransferDetail record) {
        EmployeeResp jsr = employeeService.cgetEmployeeByCode(record.getJsrId());

        if (jsr != null) {
            record.setJsrName(jsr.getEmployeeName());
        }

        EmployeeResp yjr = employeeService.cgetEmployeeByCode(record.getYjrId());
        if (yjr != null) {
            record.setYjrName(yjr.getEmployeeName());
        }

        if (!StringUtils.isEmpty(record.getCurOrgId())) {
            record.setCurOrgName(orgService.cgetOrgNameById(record.getCurOrgId()));
        }

        if (!StringUtils.isEmpty(record.getNextOrgId())) {
            record.setNextOrgName(orgService.cgetOrgNameById(record.getNextOrgId()));
        }

    }

}
