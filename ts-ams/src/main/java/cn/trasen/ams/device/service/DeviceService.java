package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.device.*;
import cn.trasen.ams.device.model.*;
import cn.trasen.ams.common.bean.ImportResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceService
 * @Description TODO
 * @date 2024年9月11日 上午9:14:02
 */
public interface DeviceService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    String insert(DeviceInsertReq record) throws Exception;

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */

    Integer edit(DeviceInsertReq record);


    /**
     * @param record
     * @return String
     * @Title save
     * @Description 新增
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    String save(Device record);


    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    Integer update(Device record);

    void createByInBoundOrder(InboundOrder record) throws Exception;


    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Device
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    Device selectById(String id);

    /**
     * @param id:
     * @return DeviceDetailResp
     * <AUTHOR>
     * @description 获取设备详情
     * @date 2024/12/13 15:12
     */

    DeviceDetailResp selectDetailById(String id) throws Exception;

    /**
     * @param page
     * @param record
     * @return DataSet<Device>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    DataSet<DeviceExtResp> getDataSetList(Page page, DeviceListReq record);

    /**
     * @param page:
     * @param record:
     * @return DataSet<DeviceExtResp>
     * <AUTHOR>
     * @description 获取设备列表（选设备组件）
     * @date 2024/12/13 15:13
     */
    DataSet<DeviceExtResp> getDataSetList4Select(Page page, DeviceList4SelectReq record);


    /**
     * @param :
     * @return String
     * <AUTHOR>
     * @description 生成设备编码
     * @date 2024/12/13 15:13
     */
    String genCode() throws Exception;


    /**
     * @param record:
     * @return List<DeviceExtResp>
     * <AUTHOR>
     * @description 获取设备列表无分页
     * @date 2024/12/13 15:13
     */
    List<DeviceExtResp> getList(DeviceListReq record);


    /**
     * @param ids:
     * @return List<DeviceExtResp>
     * @Title getListByIds
     * @Description 根据ID集合获取设备列表
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    List<DeviceExtResp> getListByIds(String ids);

    /**
     * @param list:
     * @return List<DeviceExtResp>
     * @Title getDeviceListByGroupId
     * @Description 根据设备组ID获取设备列表
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    ImportResp importByExcel(List<DeviceImport> list);


    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 自动填充设备编码
     * @date 2024/12/13 15:14
     */

    void autoFillCode();

    /**
     * @param deviceExtResp:
     * @return void
     * <AUTHOR>
     * @description 格式化
     * @date 2025/4/18 14:46
     */
    void dataFmt(DeviceExtResp deviceExtResp);


    /**
     * @param relationType:
     * @param relationId:
     * @return Boolean
     * <AUTHOR>
     * @description 判断与资产相关的实体是否被用到
     * @date 2025/4/18 14:46
     */
    Boolean hasUse(String relationType, String relationId);


    /**
     * @param inboundOrderId:
     * @return List<Device>
     * <AUTHOR>
     * @description 根据入库单ID获取设备列表
     * @date 2025/4/18 14:47
     */
    List<Device> getListByInboundOrderId(String inboundOrderId);

    /**
     * @param :
     * @return String
     * <AUTHOR>
     * @description 生成资产编码
     * @date 2025/4/18 14:47
     */
    String genAssetNo();

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 退回仓库
     * @date 2025/4/18 14:48
     */
    void goBack(InboundOrder record);

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 出库
     * @date 2025/4/18 14:48
     */
    void goout(OutboundOrder record);

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 取消出库
     * @date 2025/4/18 14:48
     */
    void gooutCancel(OutboundOrder record);

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 退回入库中
     * @date 2025/4/18 14:48
     */
    void goBacking(InboundOrder record);

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 取消退回入库
     * @date 2025/4/18 14:49
     */
    void goBackCancel(InboundOrder record);

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 出库中
     * @date 2025/4/18 14:49
     */
    void goouting(OutboundOrder record);

    void goTransfer(Transfer transfer);

    void goTransfering(Transfer transfer);

    void goTransferCancel(Transfer transfer);


    void goDisposal(Disposal disposal);

    void goDisposaling(Disposal disposal);

    void goDisposalCancel(Disposal disposal);


    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 资产盘亏标记
     * @date 2025/4/18 14:49
     */
    void inventoryException(InventoryPlan record);

    /**
     * @param record:
     * @return int
     * <AUTHOR>
     * @description 计算盘点任务下有多少个资产 TODO 这个当初为什么没有写在inventoryTaskService ？？
     * @date 2025/4/18 14:49
     */
    int count4InventoryPlan(InventoryPlan record);

    /**
     * @param record:
     * @return List<Device>
     * <AUTHOR>
     * @description 根据盘点任务获取资产列表
     * @date 2025/4/18 14:55
     */
    List<Device> getListByInventoryPlan(InventoryPlan record);

    /**
     * @param cateId:
     * @return Integer
     * <AUTHOR>
     * @description 根据分类ID获取资产数量 暂时字段未作用
     * @date 2025/4/18 14:55
     */
    Integer countByCateId(String cateId);

    void printTag(String type, List<String> id);

}
