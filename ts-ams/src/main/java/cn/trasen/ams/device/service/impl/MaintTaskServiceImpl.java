package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.util.*;

import cn.trasen.ams.device.bean.device.DeviceSlimResp;
import cn.trasen.ams.device.bean.maintTask.*;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.MaintCostConst;
import cn.trasen.ams.device.constant.MaintPlanConst;
import cn.trasen.ams.device.constant.MaintTaskConst;
import cn.trasen.ams.device.model.*;
import cn.trasen.ams.device.service.*;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.MaintTaskMapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintTaskServiceImpl
 * @Description TODO
 * @date 2024年12月14日 下午2:07:59
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MaintTaskServiceImpl implements MaintTaskService {

    @Autowired
    private MaintTaskMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private DictService dictService;

    @Autowired
    private MaintCostService maintCostService;

    @Autowired
    private MaintAccessoryConsumeService maintAccessoryConsumeService;

    @Autowired
    private MaintWorkHoursService maintWorkHoursService;


    @Autowired
    private DeviceService deviceService;


    @Transactional(readOnly = false)
    @Override
    public Integer save(MaintTask record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(MaintTask record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        MaintTask record = new MaintTask();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MaintTask selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public MaintTaskDetailResp getDetailById(String id) {

        MaintTaskDetailResp record = new MaintTaskDetailResp();
        MaintTask maintTask = selectById(id);
        if (maintTask == null) {
            throw new BusinessException("保养工单不存在");
        }

        dataFmt4MaintTask(maintTask);
        record.setMaintTask(maintTask);

        // maintCost
        MaintCost maintCost = new MaintCost();
        maintCost.setMaintTaskId(id);
        List<MaintCost> maintCostList = maintCostService.getList(maintCost);
        record.setMaintCostList(maintCostList);

        // workHours
        MaintWorkHours maintWorkHours = new MaintWorkHours();
        maintWorkHours.setMaintTaskId(id);
        List<MaintWorkHours> maintWorkHoursList = maintWorkHoursService.getList(maintWorkHours);
        record.setMaintWorkHoursList(maintWorkHoursList);

        // maintAccessoryConsume
        MaintAccessoryConsume maintAccessoryConsume = new MaintAccessoryConsume();
        maintAccessoryConsume.setMaintTaskId(id);
        List<MaintAccessoryConsume> maintAccessoryConsumeList = maintAccessoryConsumeService.getList(maintAccessoryConsume);
        record.setMaintAccessoryConsumeList(maintAccessoryConsumeList);

        return record;
    }

    @Override
    public DataSet<MaintTaskListResp> getDataSetList(Page page, MaintTaskListReq record) {

        List<MaintTaskListResp> records = mapper.getList(page, record);
        if (records != null && records.size() > 0) {
            records.forEach(item -> {
                dataFmt4DeviceSlimResp(item.getDeviceSlimResp());
                dataFmt4MaintTask(item.getMaintTask());
            });
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void createTaskByMaintPlan(MaintPlan maintPlan) {

        int count = getTasksByMaintPlanId(maintPlan.getId());
        if (count > 0) {
            return;
        }

        List<MaintTask> maintTaskList = new ArrayList<>();
        List<String> deviceIdList = Arrays.asList(maintPlan.getDeviceIdSet().split(","));

        if (deviceIdList == null || deviceIdList.size() == 0) {
            return;
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        // loop deviceIdList
        for (String deviceId : deviceIdList) {
            MaintTask record = new MaintTask();
            record.setId(IdGeneraterUtils.nextId());
            record.setCreateDate(new Date());
            record.setUpdateDate(new Date());
            record.setIsDeleted("N");
            if (user != null) {
                record.setCreateUser(user.getUsercode());
                record.setCreateUserName(user.getUsername());
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
                record.setSsoOrgCode(user.getCorpcode());
                record.setSsoOrgName(user.getOrgName());
                record.setDeptId(user.getDeptId());
                record.setDeptName(user.getDeptname());
            }

            try {
                record.setSerialNo(serialNoGenService.genByDate("MT"));
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException("生成流水号失败");
            }

            record.setMaintPlanId(maintPlan.getId());
            record.setDeviceId(deviceId);
            record.setStatus(CommonConst.YES);
            record.setCompleteStatus(CommonConst.NO);

            record.setWorkHours(new BigDecimal(0.00));
            record.setTravelHours(new BigDecimal(0.00));
            record.setCosts(new BigDecimal(0.00));
            maintTaskList.add(record);
        }

        if (maintTaskList.size() > 0) {
            mapper.batchInsert(maintTaskList);
        }

    }

    @Transactional(readOnly = false)
    @Override
    public void updateTaskByMaintPlan(MaintPlan maintPlan) {
        // 删除旧记录
        deleteByMaintPlan(maintPlan);
        // 重新建立记录
        createTaskByMaintPlan(maintPlan);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByMaintPlan(MaintPlan maintPlan) {
        // 多做一层容错
        if (!maintPlan.getStatus().equals(MaintPlanConst.STATUS_WAIT)) {
            throw new BusinessException("保险计划状态只有未开始的状态，才能删除");
        }
        Example example = new Example(MaintTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("maintPlanId", maintPlan.getId());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        mapper.deleteByExample(example);
    }

    @Override
    public int getTasksByMaintPlanId(String maintPlanId) {
        Example example = new Example(MaintTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("maintPlanId", maintPlanId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        int count = mapper.selectCountByExample(example);
        return count;
    }

    @Transactional(readOnly = false)
    @Override
    public void scrap(String id, String act) {
        MaintTask record = new MaintTask();
        record.setId(id);
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        if (CommonConst.NO.equals(act)) {
            record.setStatus(CommonConst.NO);
        } else {
            record.setStatus(CommonConst.YES);
        }
        mapper.updateByPrimaryKeySelective(record);
    }


    @Transactional(readOnly = false)
    @Override
    public void completeMaintTask(CompleteMaintTaskReq record) {

        MaintTask maintTask = record.getMaintTask();
        if (maintTask == null) {
            throw new BusinessException("保养工单不能为空");
        }

        Device device = deviceService.selectById(maintTask.getDeviceId());

        List<String> engineerIdList = new ArrayList<>();

        BigDecimal workHours = new BigDecimal(0.00);
        BigDecimal travelHours = new BigDecimal(0.00);
        BigDecimal costs = new BigDecimal(0.00);
        List<MaintCost> maintCostList = new ArrayList<>();

        // 清理旧数据
        maintWorkHoursService.deleteByTaskId(maintTask.getId());
        maintAccessoryConsumeService.deleteByTaskId(maintTask.getId());
        maintCostService.deleteByTaskId(maintTask.getId());
        List<MaintWorkHours> maintWorkHoursList = record.getMaintWorkHoursList();


        // 保存工时
        if (maintWorkHoursList != null && maintWorkHoursList.size() > 0) {

            for (MaintWorkHours item : maintWorkHoursList) {

                item.setMaintTaskId(maintTask.getId());
                // 生成工时记录
                maintWorkHoursService.save(item);

                // 合计工时和其他数据
                workHours = workHours.add(item.getOperationHours());
                travelHours = travelHours.add(item.getTravelHours());
                costs = costs.add(item.getCosts());

                // 生成费用记录
                MaintCost maintCost = new MaintCost();

                maintCost.setCosts(item.getCosts());
                maintCost.setMaintTaskId(maintTask.getId());
                maintCost.setType(MaintCostConst.TYPE_WORK_HOUR);
                // 谁在什么时候对什么设备做了多长时间保养，花费多少
                maintCost.setItem(String.format("保养工单[%s]，工号[%s]于%s对编号[%s]的设备进行保养，耗时%s小时，耗费%s元",
                        maintTask.getSerialNo(),
                        item.getEngineerId(),
                        CommonUtil.dateFormate(item.getCreateDate(), "yyyy-MM-dd HH:ii:ss"),
                        device.getUniqueNo(),
                        item.getOperationHours(),
                        item.getCosts()));
                maintCost.setCosts(item.getCosts());
                maintCostList.add(maintCost);

                // 维护实际保养人员
                engineerIdList.add(item.getEngineerId());
            }

        }

        List<MaintAccessoryConsume> maintAccessoryConsumeList = record.getMaintAccessoryConsumeList();

        // 保存配件
        if (maintAccessoryConsumeList != null && maintAccessoryConsumeList.size() > 0) {
            for (MaintAccessoryConsume item : maintAccessoryConsumeList) {

                item.setMaintTaskId(maintTask.getId());
                maintAccessoryConsumeService.save(item);

                // 合计工时和其他数据
                costs = costs.add(item.getCosts());
                // 生成费用记录
                MaintCost maintCost = new MaintCost();

                maintCost.setMaintTaskId(maintTask.getId());
                maintCost.setType(MaintCostConst.TYPE_ACCESSORY);
                // 什么东西，在哪次保养中，使用配件，多少个，花费多少
                maintCost.setItem(String.format("保养工单[%s]，编号[%s]的设备，消耗配件[%s](%s)%d个，花费%s元",
                        maintTask.getSerialNo(),
                        device.getUniqueNo(),
                        item.getName(),
                        item.getSpec(),
                        item.getNums(),
                        item.getCosts()));
                maintCost.setCosts(item.getCosts());
                maintCostList.add(maintCost);

            }
        }

        List<MaintCost> maintCostOtherList = record.getMaintCostList();
        // 保存费用
        if (maintCostOtherList != null && maintCostOtherList.size() > 0) {
            for (MaintCost item : maintCostOtherList) {
                item.setMaintTaskId(maintTask.getId());
                item.setType(MaintCostConst.TYPE_OTHER);
                maintCostService.save(item);
                // 合计工时和其他数据
                costs = costs.add(item.getCosts());
            }
        }

        // 保存配件和工时费用
        if (maintCostList.size() > 0) {
            for (MaintCost item : maintCostList) {
                maintCostService.save(item);
            }
        }

        // 保存工单信息
        maintTask.setWorkHours(workHours);
        maintTask.setTravelHours(travelHours);
        maintTask.setCosts(costs);
        maintTask.setEngineerIdSet(String.join(",", engineerIdList));

        if (MaintTaskConst.ACT_SUBMIT.equals(record.getAct())) {
            maintTask.setCompleteStatus(CommonConst.YES);
            maintTask.setCompleteAt(new Date());
        }
        update(maintTask);
    }

    @Transactional(readOnly = false)
    @Override
    public void solveExpMaintTask(SolveExpMaintTaskReq record) {

        if (record.getId() == null) {
            throw new BusinessException("保养工单ID不能为空");
        }

        MaintTask maintTask = new MaintTask();
        maintTask.setId(record.getId());
        maintTask.setSolution(record.getSolution());
        maintTask.setOpinion(record.getOpinion());

        update(maintTask);
    }

    @Override
    public List<MaintTask> getMaintTaskListByMaintPlanId(String maintPlanId) {
        Example example = new Example(MaintTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("maintPlanId", maintPlanId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        // order by
        example.setOrderByClause("create_date asc");
        List<MaintTask> maintTaskList = mapper.selectByExample(example);
        return maintTaskList;
    }

    public void dataFmt4DeviceSlimResp(DeviceSlimResp deviceSlimResp) {
        if (deviceSlimResp == null) {
            return;
        }
        Map<String, String> orgMap = orgService.cgetOrgMap();
        deviceSlimResp.setBelongToOrgName(orgMap.get(deviceSlimResp.getBelongToOrgId()));
        deviceSlimResp.setUseOrgName(orgMap.get(deviceSlimResp.getUseOrgId()));
    }


    public void dataFmt4MaintTask(MaintTask maintTask) {
        if (maintTask == null) {
            return;
        }

        maintTask.setMaintStatusShow(dictService.cgetNameByValue(MaintTaskConst.MAINT_TASK_MAINT_STATUS, maintTask.getMaintStatus()));
        maintTask.setCompleteStatusShow(dictService.cgetNameByValue(MaintTaskConst.MAINT_TASK_COMPLETE_STATUS, maintTask.getCompleteStatus()));
        maintTask.setStatusShow(dictService.cgetNameByValue(MaintTaskConst.MAINT_TAKS_STATUS, maintTask.getStatus()));
        maintTask.setSolutionShow(dictService.cgetNameByValue(MaintTaskConst.MAINT_TASK_SOLUTION, maintTask.getSolution()));

    }

}
