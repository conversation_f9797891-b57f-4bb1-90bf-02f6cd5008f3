package cn.trasen.ams.device.bean.maintTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.maintTask
 * @className: SolveExpMaintTaskReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/19 17:16
 * @version: 1.0
 */

@Data
public class SolveExpMaintTaskReq {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "处理方案")
    private String solution;

    @ApiModelProperty(value = "处理意见")
    private String opinion;

}
