package cn.trasen.ams.device.bean.signoff;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.signoff
 * @className: PenddingSignoffDeviceListReq
 * @author: chenbin
 * @description: 待签收设备列表请求参数
 * @date: 2025/7/9 17:28
 * @version: 1.0
 */

@Data
public class PenddingSignoffDeviceListReq {

    @ApiModelProperty(value = "采购订单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "资产名称")
    private String deviceName;

    @ApiModelProperty(value = "忽略哪些ID，逗号分隔")
    private List<String> ignoreIdList;
}
