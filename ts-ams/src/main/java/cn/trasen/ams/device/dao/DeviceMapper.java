package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.device.*;
import cn.trasen.ams.device.bean.outin.InboundOrderGobackUpdate;
import cn.trasen.ams.device.bean.outin.OutboundOrderGooutUpdate;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.model.InventoryPlan;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

public interface DeviceMapper extends Mapper<Device> {
    List<DeviceExtResp> selectDeviceList(Page page, DeviceListReq deviceListReq);


    List<DeviceExtResp> selectDeviceList4Select(Page page, DeviceList4SelectReq deviceListReq);

    List<DeviceExtResp> selectDeviceListByIds(@Param("ids") List<String> ids);

    List<DeviceExtResp> selectDeviceListNoPage(DeviceListReq deviceListReq);

    DeviceExtResp selectDeviceOne(@Param("deviceId") String deviceId);

    Set<String> selectAllAssetCode();

    Set<String> selectAllYthzcbm();

    int batchInsert(@Param("list") List<DeviceImport> list);

    List<DeviceWithStatsListResp> selectDeviceListWithStats(Page page, DeviceWithStatsListReq deviceListWithStatsReq);

    void goback(@Param("update") InboundOrderGobackUpdate update, @Param("list") List<String> list);

    void goout(@Param("update") OutboundOrderGooutUpdate update, @Param("list") List<String> list);

    int count4InventoryPlan(InventoryPlan inventoryPlan);

    List<Device> getListByInventoryPlan(InventoryPlan inventoryPlan);

    void printTag(@Param("idList") List<String> idList, @Param("type") String type, @Param("log") String log);

}