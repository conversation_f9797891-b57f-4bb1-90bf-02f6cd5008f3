package cn.trasen.ams.device.service.impl;

import cn.trasen.ams.device.dao.DeviceMapper;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.service.ScriptService;
import cn.trasen.homs.core.contants.Contants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.awt.*;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.service.impl
 * @className: ScriptServiceImpl
 * @author: chenbin
 * @description: 用来写处理脚本的类
 * @date: 2025/5/12 17:08
 * @version: 1.0
 */

@Service
public class ScriptServiceImpl implements ScriptService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public void fixAssetCode() {
        // 查询所有的资产
        // 对资产编码长度为7位数的资产进行处理
        // 通过对字符串进行拆分，比如2500001拆分为25和0001，然后对第二位后面的字符串在前面补0，达到6位数，然后再拼接，一共8位数
        Example example = new Example(Device.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Device> devices = deviceMapper.selectByExample(example);

        // 对资产编码长度为7位数的资产进行处理
        devices.stream()
                .filter(device -> device.getAssetCode() != null && device.getAssetCode().length() <= 7)
                .forEach(device -> {
                    String assetCode = device.getAssetCode();
                    // 拆分字符串
                    String prefix = assetCode.substring(0, 2);
                    String suffix = assetCode.substring(2);
                    // 补齐后缀到6位
                    String newSuffix = String.format("%06d", Integer.parseInt(suffix));
                    // 拼接新的资产编码
                    String newAssetCode = prefix + newSuffix;
                    device.setAssetCode(newAssetCode);

                    // 更新数据库
                    deviceMapper.updateByPrimaryKeySelective(device);
                });
    }
}
