package cn.trasen.ams.device.bean.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseOrderExportRow
 * @author: chenbin
 * @description: TODO
 * @date: 2025/7/5 15:04
 * @version: 1.0
 */
@Data
public class PurchaseOrderExportRow {


    private Integer no;

    @Transient
    private String itemValue1;

    @Transient
    private String itemValue2;

    @Transient
    private String itemValue3;

    @Transient
    private String itemValue4;

    /**
     * 采购单价
     */
    @ApiModelProperty(value = "采购单价")
    private BigDecimal price;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private Integer nums;

    /**
     * 采购总价
     */
    @ApiModelProperty(value = "采购单价")
    private BigDecimal total;

}
