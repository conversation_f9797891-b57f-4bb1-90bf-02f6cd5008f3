package cn.trasen.ams.device.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.DeviceConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.validation.annotation.Validated;

@Table(name = "d_device")
@Setter
@Getter
@Validated
public class Device {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    @ApiModelProperty(value = "入库单号")
    @Column(name = "inbound_order_id")
    private String inboundOrderId;


    @Transient
    @Column(name = "name")
    @ApiModelProperty(value = "资产名称")
    private String name;


    @Column(name = "supplier_id")
    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    /**
     * 设备款号ID
     */
    @NotNull(message = "必须选择设备款号")
    @Column(name = "sku_id")
    @ApiModelProperty(value = "设备款号ID")
    private String skuId;


    //warehouse_id
    /**
     * 仓库ID
     */

    @Column(name = "warehouse_id")
    @ApiModelProperty(value = "仓库ID")
    private String warehouseId;


    //warehouse_status
    /**
     * 仓库状态  0 在库 1 在役  2 减少 （报废）
     */
    @Column(name = "warehouse_status")
    @ApiModelProperty(value = "仓库状态  0 在库 1 在役  2 减少 （报废）")
    private String warehouseStatus;


    @Transient
    @ApiModelProperty(value = "在库状态展示")
    private String warehouseStatusShow;


    /**
     * 所属科室ID
     */
    @NotNull(message = "必须选择所属科室")
    @Column(name = "belong_to_org_id")
    @ApiModelProperty(value = "所属科室ID")
    private String belongToOrgId;

    /**
     * 使用科室ID
     */

    @Column(name = "use_org_id")
    @ApiModelProperty(value = "使用科室ID")
    private String useOrgId;

    /**
     * 设备唯一编号
     */

    @Column(name = "unique_no")
    @ApiModelProperty(value = "设备唯一编号")
    private String uniqueNo;


    @Excel(name = "一体化平台资产编码")
    @ApiModelProperty(value = "一体化平台资产编码")
    @Column(name = "ythzcbm")
    private String ythzcbm;


    /**
     * 国资委编号
     */
    @Excel(name = "国资委编码")
    @ApiModelProperty(value = "国资委编号")
    private String gzwbh;

    /**
     * 资产编码
     */
    @Excel(name = "资产编码")
    @Column(name = "asset_code")
    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    /**
     * 设备序列号
     */
    @Excel(name = "资产SN码")
    @Column(name = "serial_no")
    @ApiModelProperty(value = "资产SN码")
    private String serialNo;

    /**
     * 强检类型 (1 2 -  强检 非强检)
     */
    @Excel(name = "强检类型")
    @ConstValid(constant = {SkuConst.CALIBRATION_TYPE_FORCE, SkuConst.CALIBRATION_TYPE_NORMAL}, message = "强检类型值错误")
    @Column(name = "calibration_type")
    @ApiModelProperty(value = "强检类型 (1 2 -  强检 非强检)")
    private String calibrationType;

    /**
     * 计量周期
     */
    @Excel(name = "计量周期")
    @Column(name = "calibration_cycle_val")
    @ApiModelProperty(value = "计量周期")
    private Integer calibrationCycleVal;

    /**
     * 强检周期的单位（1 2 3 -年 月 日）
     */
    @Excel(name = "计量周期单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "强检周期的单位不正确")
    @Column(name = "calibration_cycle_unit")
    @ApiModelProperty(value = "计量周期的单位（1 2 3 -年 月 日）")
    private String calibrationCycleUnit;

    /**
     * 保养周期值
     */
    @Excel(name = "保养周期")
    @Column(name = "maint_cycle_val")
    @ApiModelProperty(value = "保养周期值")
    private Integer maintCycleVal;

    /**
     * 保养周期单位 (1 2 3 -年 月 日）
     */
    @Excel(name = "保养周期单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "保养周期的单位不正确")
    @Column(name = "maint_cycle_unit")
    @ApiModelProperty(value = "保养周期单位 (1 2 3 -年 月 日）")
    private String maintCycleUnit;

    /**
     * 厂家质保时长
     */
    @Excel(name = "厂家质保时长")
    @Column(name = "warranty_period_val")
    @ApiModelProperty(value = "厂家质保时长")
    private Integer warrantyPeriodVal;

    /**
     * 厂家质保时长单位(1 2 3 -年 月 日）
     */
    @Excel(name = "厂家质保时长单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "厂家质保时的长单位不正确")
    @Column(name = "warranty_period_unit")
    @ApiModelProperty(value = "厂家质保时长单位(1 2 3 -年 月 日）")
    private String warrantyPeriodUnit;

    /**
     * 报废年限值
     */
    @Excel(name = "报废年限")
    @Column(name = "lifespan_val")
    @ApiModelProperty(value = "报废年限值")
    private Integer lifespanVal;

    /**
     * 报废年限单位（1 2 3 -年 月 日）
     */
    @Excel(name = "报废年限单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "报废年限的单位不正确")
    @Column(name = "lifespan_unit")
    @ApiModelProperty(value = "报废年限单位（1 2 3 -年 月 日）")
    private String lifespanUnit;

    /**
     * 巡检周期值
     */
    @Excel(name = "巡检周期")
    @Column(name = "inspection_cycle_val")
    @ApiModelProperty(value = "巡检周期值")
    private Integer inspectionCycleVal;

    /**
     * 巡检周期单位
     */
    @Excel(name = "巡检周期单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "巡检周期的单位不正确")
    @Column(name = "inspection_cycle_unit")
    @ApiModelProperty(value = "巡检周期单位")
    private String inspectionCycleUnit;

    /**
     * 厂家维保时长值
     */
    @Excel(name = "厂家维保时长")
    @Column(name = "maint_period_val")
    @ApiModelProperty(value = "厂家维保时长值")
    private Integer maintPeriodVal;

    /**
     * 厂家维保时长单位
     */
    @Excel(name = "厂家维保时长单位")
    @ConstValid(constant = {SkuConst.CYCLE_VAL_YEAR, SkuConst.CYCLE_VAL_MONTH, SkuConst.CYCLE_VAL_DAY}, message = "厂家质保时的长单位不正确")
    @Column(name = "maint_period_unit")
    @ApiModelProperty(value = "厂家维保时长单位")
    private String maintPeriodUnit;

    /**
     * 生产日期
     */
    @Excel(name = "生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "manufacture_date")
    @ApiModelProperty(value = "生产日期")
    private Date manufactureDate;


    /**
     * 启用日期
     */
    @Excel(name = "启用日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "use_date")
    @ApiModelProperty(value = "启用日期")
    private Date useDate;

    /**
     * 验收日期
     */
    @Excel(name = "验收时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "acceptance_date")
    @ApiModelProperty(value = "验收日期")
    private Date acceptanceDate;

    /**
     * 处置日期
     */
    @Excel(name = "处置日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 资产状态 （0 未安装,1 使用中,2 故障中,3 闲置,4 已报废,5 遗失）
     */
    @Excel(name = "资产状态")
    @ConstValid(constant = {DeviceConst.STATUS_UN_INS, DeviceConst.STATUS_USE, DeviceConst.STATUS_FAULT, DeviceConst.STATUS_IDLE, DeviceConst.STATUS_SCRAP, DeviceConst.STATUS_LOSS}, message = "设备状态值不正确")
    @ApiModelProperty(value = "资产状态 （0 未安装,1 使用中,2 故障中,3 闲置,4 已报废,5 遗失）")
    private String status;

    //maint_status
    /**
     * 保养状态 （0 未保养,1 已保养）
     */
    @ConstValid(constant = {CommonConst.NO, CommonConst.YES}, message = "保养合同状态值不正确")
    @Column(name = "maint_contract_status")
    @ApiModelProperty(value = "保养合同状态 （1 在保,0 脱保）")
    private String maintContractStatus;


    /**
     * 是否是医院资产 ( 0  1 - 否 是)
     */

    @Excel(name = "医院资产")
    @ConstValid(constant = {CommonConst.YES, CommonConst.NO}, message = "是否是医院资产值不正确")
    @Column(name = "is_hos_asset")
    @ApiModelProperty(value = "是否是医院资产 ( 0  1 - 否 是)")
    private String isHosAsset;

    /**
     * 放置位置
     */
    @Excel(name = "放置位置")
    @ApiModelProperty(value = "放置位置")
    private String loc;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 折旧方法 （0 1 2 - 直线法  递减余额法  年数总和法）
     */
    @Excel(name = "折旧方法")
//    @ConstValid(constant = {DeviceConst.STRAIGHT_LINE, DeviceConst.DECLINING_BALANCE, DeviceConst.SUM_OF_YEARS_DIGITS}, message = "折旧方法值不正确")
    @Column(name = "depreciation_method")
    @ApiModelProperty(value = "折旧方法 （0 1 2 - 直线法  递减余额法  年数总和法）")
    private String depreciationMethod;

    /**
     * 设备原值
     */
    @Excel(name = "设备原值")
    @Column(name = "original_val")
    @ApiModelProperty(value = "设备原值")
    private BigDecimal originalVal;

    /**
     * 设备残值
     */
    @Excel(name = "设备残值")
    @Column(name = "salvage_val")
    @ApiModelProperty(value = "设备残值")
    private BigDecimal salvageVal;

    /**
     * 账面净值
     */
    @Excel(name = "账面净值")
    @Column(name = "book_val")
    @ApiModelProperty(value = "账面净值")
    private BigDecimal bookVal;

    //prev_date_repair prev_date_maint prev_date_calibrate prev_date_inspect
    /**
     * 上次维修日期
     */
    @Column(name = "prev_date_repair")
    @ApiModelProperty(value = "上次维修日期")
    private Date prevDateRepair;

    /**
     * 上次保养日期
     */
    @Column(name = "prev_date_maint")
    @ApiModelProperty(value = "上次保养日期")
    private Date prevDateMaint;

    /**
     * 上次校准日期
     */

    @Column(name = "prev_date_calibrate")
    @ApiModelProperty(value = "上次校准日期")
    private Date prevDateCalibrate;

    /**
     * 上次巡检日期
     */
    @Column(name = "prev_date_inspect")
    @ApiModelProperty(value = "上次巡检日期")
    private Date prevDateInspect;


    /**
     * 设备标签
     */
    @Excel(name = "资产标签")
    @Column(name = "tags")
    @ApiModelProperty(value = "资产标签")
    private String tags;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    //normal_printed
    @Column(name = "normal_printed")
    @ApiModelProperty(value = "普通打印标识")
    private String normalPrinted;

    @Transient
    @ApiModelProperty(value = "普通打印标识展示")
    private String normalPrintedShow;


    // normal_printed_log
    @Column(name = "normal_printed_log")
    @ApiModelProperty(value = "普通打印日志")
    private String normalPrintedLog;

    //rfid_printed
    @Column(name = "rfid_printed")
    @ApiModelProperty(value = "RFID打印标识")
    private String rfidPrinted;

    @Transient
    @ApiModelProperty(value = "RFID打印标识展示")
    private String rfidPrintedShow;

    //rfid_printed_log
    @Column(name = "rfid_printed_log")
    @ApiModelProperty(value = "RFID打印日志")
    private String rfidPrintedLog;


}