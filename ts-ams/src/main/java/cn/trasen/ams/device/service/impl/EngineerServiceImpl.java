package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.constant.EngineerConst;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.common.service.SupplierService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.EngineerMapper;
import cn.trasen.ams.device.model.Engineer;
import cn.trasen.ams.device.service.EngineerService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName EngineerServiceImpl
 * @Description TODO
 * @date 2024年12月25日 下午4:45:40
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class EngineerServiceImpl implements EngineerService {

    @Autowired
    private EngineerMapper mapper;


    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private DictService dictService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SupplierService supplierService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Engineer record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        autoFillColumn(record);

        if (exsit(record.getUserId())) {
            throw new BusinessException(String.format("工号[%s]人员已存在，请勿重复添加", record.getUserId()));
        }

        return mapper.insertSelective(record);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 自动填充列
     * @date 2024/12/25 17:07
     */
    private void autoFillColumn(Engineer record) {
        if (!StringUtil.isEmpty(record.getUserId())) {

            PlatformResult<EmployeeResp> ret = hrmsEmployeeFeignService.getEmployeeDetailByCode(record.getUserId());
            EmployeeResp reto = ret.getObject();

            if (reto == null) {
                throw new BusinessException("员工信息不存在");
            }
            record.setName(reto.getEmployeeName());
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Engineer record) {

        record.setUpdateDate(new Date());

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        autoFillColumn(record);

        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Engineer record = new Engineer();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Engineer selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }


    private Example createExample(Engineer record) {
        Example example = new Example(Engineer.class);
        Example.Criteria criteria = example.createCriteria();

        if (!StringUtil.isEmpty(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        if (!StringUtil.isEmpty(record.getItemSet())) {
            criteria.andLike("itemSet", "%" + record.getItemSet() + "%");
        }

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        example.setOrderByClause("create_date desc");
        return example;
    }

    @Override
    public DataSet<Engineer> getDataSetList(Page page, Engineer record) {
        Example example = createExample(record);
        List<Engineer> records = mapper.selectByExampleAndRowBounds(example, page);
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<Engineer> getList(Engineer record) {
        Example example = createExample(record);
        List<Engineer> records = mapper.selectByExample(example);
        records.forEach(this::dataFmt);
        return records;
    }

    private boolean exsit(String userId) {
        Engineer record = new Engineer();
        record.setUserId(userId);
        record.setIsDeleted("N");
        return mapper.selectCount(record) > 0;
    }


    private void dataFmt(Engineer engineer) {
        if (!StringUtil.isEmpty(engineer.getItemSet())) {
            engineer.setItemSetShow(dictService.cgetKS(EngineerConst.AMS_ENGINEER_ITEM).get(engineer.getItemSet()));
        }
        try {
            if (!StringUtil.isEmpty(engineer.getSupplierId())) {
                Supplier supplier = (Supplier) redisService.fetch("supplier" + engineer.getSupplierId(), () -> {
                    return supplierService.selectById(engineer.getSupplierId());
                }, 300);
                engineer.setSupplierName(supplier.getName());
            }

        } catch (Exception e) {
            throw new BusinessException("员工信息不存在");
        }


    }
}
