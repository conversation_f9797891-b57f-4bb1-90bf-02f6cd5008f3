package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.util.*;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.MaintPlanConst;
import cn.trasen.ams.device.constant.MaintTaskConst;
import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.MaintTaskService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.MaintPlanMapper;
import cn.trasen.ams.device.model.MaintPlan;
import cn.trasen.ams.device.service.MaintPlanService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintPlanServiceImpl
 * @Description TODO
 * @date 2024年12月12日 上午10:27:03
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MaintPlanServiceImpl implements MaintPlanService {

    @Autowired
    private MaintPlanMapper mapper;


    @Autowired
    private DeviceService deviceService;

    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private DictService dictService;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private MaintTaskService maintTaskService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(MaintPlan record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        try {
            record.setSerialNo(serialNoGenService.genByDate("MP"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("生成计划编号失败.");
        }

        autoFillColumn(record);

        return mapper.insertSelective(record);
    }


    private void autoFillColumn(MaintPlan record) {
        // 检查关联的设备
        if (record.getDeviceIdSet() == null || record.getDeviceIdSet().isEmpty()) {
            throw new BusinessException("关联的设备不能为空.");
        }
        // 填充分类和科室的ID和名称
        // deviceIdSet 字符串类型转换成 List<String>
        List<DeviceExtResp> deviceExtRespList = deviceService.getListByIds(record.getDeviceIdSet());

        List<String> orgIdList = new ArrayList<>();
        List<String> orgNameList = new ArrayList<>();

        List<String> category22IdList = new ArrayList<>();
        List<String> category22NameList = new ArrayList<>();

        deviceExtRespList.forEach(deviceExtResp -> {
            // 翻译ID和key
            deviceService.dataFmt(deviceExtResp);

            orgIdList.add(deviceExtResp.getBelongToOrgId());
            orgNameList.add(deviceExtResp.getBelongToOrgName());

            category22IdList.add(deviceExtResp.getCategory22Id());
            category22NameList.add(deviceExtResp.getCategory22Name());

        });

        record.setTargets(deviceExtRespList.size());
        record.setOrgIdSet(String.join(",", orgIdList));
        record.setOrgNameSet(String.join(",", orgNameList));
        record.setCategory22IdSet(String.join(",", category22IdList));
        record.setCategory22NameSet(String.join(",", category22NameList));


        // 填充计划执行人
        if (record.getEngineerIdSet() != null) {
            List<String> engineerNameList = new ArrayList<>();
            List<String> engineerIdList = Arrays.asList(record.getEngineerIdSet().split(","));
            PlatformResult<List<EmployeeResp>> pr = hrmsEmployeeFeignService.getEmployeeDetailByIds(engineerIdList);
            List<EmployeeResp> employeeRespList = pr.getObject();
            if (employeeRespList != null) {
                employeeRespList.forEach(employeeResp -> {
                    engineerNameList.add(employeeResp.getEmployeeName());
                });
            }
            record.setEngineerNameSet(String.join(",", engineerNameList));
        }


        // 填充实际计划执行人
        if (record.getEEngineerIdSet() != null) {
            List<String> eEngineerNameList = new ArrayList<>();
            List<String> eEngineerIdList = Arrays.asList(record.getEEngineerIdSet().split(","));
            PlatformResult<List<EmployeeResp>> pr = hrmsEmployeeFeignService.getEmployeeDetailByIds(eEngineerIdList);
            List<EmployeeResp> employeeRespList = pr.getObject();
            if (employeeRespList != null) {
                employeeRespList.forEach(employeeResp -> {
                    eEngineerNameList.add(employeeResp.getEmployeeName());
                });
            }
            record.setEEngineerNameSet(String.join(",", eEngineerNameList));
        }

    }


    @Transactional(readOnly = false)
    @Override
    public Integer update(MaintPlan record) {

        MaintPlan maintPlan = selectById(record.getId());

        if (CommonConst.YES.equals(maintPlan.getArchiveStatus())) {
            throw new BusinessException("已归档的计划不能删除.");
        }

        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        autoFillColumn(record);
        return mapper.updateByPrimaryKeySelective(record);

    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        MaintPlan maintPlan = selectById(id);

        if (CommonConst.YES.equals(maintPlan.getArchiveStatus())) {
            throw new BusinessException("已归档的计划不能删除.");
        }

        Assert.hasText(id, "ID不能为空.");
        MaintPlan record = new MaintPlan();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        return mapper.updateByPrimaryKeySelective(record);

    }

    @Transactional(readOnly = false)
    @Override
    public Integer archiveById(String id) {

        Assert.hasText(id, "ID不能为空.");
        MaintPlan record = new MaintPlan();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setArchiveStatus(CommonConst.YES);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        int rt = mapper.updateByPrimaryKeySelective(record);

        return rt;
    }

    @Override
    public MaintPlan selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<MaintPlan> getDataSetList(Page page, MaintPlan record) {
        Example example = new Example(MaintPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getName() != null) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        if (record.getStatus() != null) {
            criteria.andEqualTo("status", record.getStatus());
        }

        // 排序
        example.setOrderByClause("create_date desc");

        List<MaintPlan> records = mapper.selectByExampleAndRowBounds(example, page);

        records.forEach(this::dataFmt);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void syncProcess(MaintTask maintTask) {

        // 保养任务完成后，更新计划的完成状态
        MaintPlan plan = selectById(maintTask.getMaintPlanId());
        if (plan == null) {
            return;
        }

        List<MaintTask> maintTaskList = maintTaskService.getMaintTaskListByMaintPlanId(maintTask.getMaintPlanId());
        if (maintTaskList == null || maintTaskList.isEmpty()) {
            return;
        }
        List<String> eEngineerIdList = new ArrayList<>();
        int completes = 0;
        BigDecimal workHours = new BigDecimal(0);
        BigDecimal travelHours = new BigDecimal(0);
        BigDecimal costs = new BigDecimal(0);
        int oks = 0;
        int exps = 0;
        int index = 0;
        int scraps = 0;

        for (MaintTask task : maintTaskList) {
            // 任务未完成的情况下忽略掉
            if (CommonConst.NO.equals(task.getCompleteStatus())) {
                index++;
                continue;
            }
            //处理实际计划执行人
            String eEngineerIdSet = task.getEngineerIdSet();
            if (eEngineerIdSet != null) {
                eEngineerIdList.addAll(Arrays.asList(eEngineerIdSet.split(",")));
            }

            // 记录已完成的数量
            if (CommonConst.YES.equals(task.getCompleteStatus())) {
                completes++;
            }

            // 记录作废的数量
            if (CommonConst.NO.equals(task.getStatus())) {
                scraps++;
            }

            //处理实际开始时间和结束时间 list是按照时间顺序来的，所以第一个是开始时间，最后一个是结束时间
            if (index == 0) {
                plan.setEStartAt(task.getCreateDate());
            }

            // 必须满足 完成 + 作废的数量等于总数量
            if (index == maintTaskList.size() - 1 && completes + scraps == maintTaskList.size()) {
                plan.setEEndAt(task.getUpdateDate());
            }
            if (task.getWorkHours() != null) {
                workHours = workHours.add(task.getWorkHours());
            }

            if (task.getTravelHours() != null) {
                travelHours = travelHours.add(task.getTravelHours());
            }

            if (task.getCosts() != null) {
                costs = costs.add(task.getCosts());
            }

            if (task.getMaintStatus() != null) {
                if (MaintTaskConst.MAINT_TASK_STATUS_NORMAL.equals(task.getMaintStatus())) {
                    oks++;
                } else {
                    exps++;
                }
            }
            costs = costs.add(task.getCosts());

            index++;
        }

        // 对实际计划执行人去重
        List<String> eEngineerIdListDistinct = new ArrayList<>(new LinkedHashSet<>(eEngineerIdList));
        plan.setEEngineerIdSet(String.join(",", eEngineerIdListDistinct));
        // 执行状态 0 未开始 1 进行中 2 已完成
        if (completes == 0) {
            plan.setStatus(MaintPlanConst.STATUS_WAIT);
        } else if (completes + scraps == maintTaskList.size()) {
            plan.setStatus(MaintPlanConst.STATUS_DONE);
        } else {
            plan.setStatus(MaintPlanConst.STATUS_DOING);
        }

        plan.setCompletes(completes);
        plan.setWorkHours(workHours);
        plan.setTravelHours(travelHours);
        plan.setCosts(costs);
        plan.setOks(oks);
        plan.setExps(exps);

        // 更新计划
        update(plan);

    }

    private void dataFmt(MaintPlan plan) {
        plan.setStatusShow(dictService.cgetNameByValue(MaintPlanConst.MAINT_PLAN_STATUS, plan.getStatus()));
        plan.setArchiveStatusShow(dictService.cgetNameByValue(MaintPlanConst.MAINT_PLAN_ARCHIVE_STATUS, plan.getArchiveStatus()));
        plan.setLevelShow(dictService.cgetNameByValue(MaintPlanConst.MAINT_PLAN_LEVEL, plan.getLevel()));

    }
}
