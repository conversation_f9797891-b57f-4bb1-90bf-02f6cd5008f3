package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.inventory.InventoryTaskExtResp;
import cn.trasen.ams.device.bean.inventory.InventoryTaskListReq;
import cn.trasen.ams.device.bean.inventory.InventoryTaskStatusNumsResp;
import cn.trasen.ams.device.model.InventoryTask;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface InventoryTaskMapper extends Mapper<InventoryTask> {

    void batchInsert(@Param("list") List<InventoryTask> inventoryTaskList);

    List<InventoryTaskExtResp> getList(Page page, @Param("req") InventoryTaskListReq req);

    List<InventoryTaskExtResp> getListNoPage(@Param("req") InventoryTaskListReq req);

    InventoryTaskStatusNumsResp getInventoryTaskStatusNums(@Param("planId") String planId);

    InventoryTaskStatusNumsResp getInventoryTaskStatusNumsExt(@Param("req") InventoryTaskListReq record);

    List<String> selectInventoryTaskIds4AssetCode(
            @Param("inventoryPlanId") String inventoryPlanId,
            @Param("assetCode") List<String> assetCode,
            @Param("taskStatus") String taskStatus
    );
}