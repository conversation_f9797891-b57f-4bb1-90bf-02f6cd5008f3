package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.outin.OutBoundOrderDetailExtResp;
import cn.trasen.ams.device.model.InboundOrder;
import cn.trasen.ams.device.model.InboundOrderDetail;
import cn.trasen.ams.device.model.OutboundOrder;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.OutboundOrderDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutboundOrderDetailService
 * @Description TODO
 * @date 2025年2月11日 下午5:29:25
 */
public interface OutboundOrderDetailService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月11日 下午5:29:25
     * <AUTHOR>
     */
    Integer save(OutboundOrderDetail record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月11日 下午5:29:25
     * <AUTHOR>
     */
    Integer update(OutboundOrderDetail record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:29:25
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return OutboundOrderDetail
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:29:25
     * <AUTHOR>
     */
    OutboundOrderDetail selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<OutboundOrderDetail>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月11日 下午5:29:25
     * <AUTHOR>
     */
    DataSet<OutboundOrderDetail> getDataSetList(Page page, OutboundOrderDetail record);

    List<OutBoundOrderDetailExtResp> selectByOutboundOrderId(String outboundOrderId);

    void createByInboundOrder(InboundOrder inboundOrder, OutboundOrder outboundOrder);

    void deleteByOutboundOrderId(String outboundOrderId);

    List<String> getOutboundingDeviceIds();
}
