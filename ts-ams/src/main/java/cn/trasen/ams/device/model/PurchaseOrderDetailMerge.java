package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_purchase_order_detail_merge")
@Setter
@Getter
public class PurchaseOrderDetailMerge {


    @Transient
    @ApiModelProperty(value = "字典信息")
    Sku sku;

    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 主表ID
     */
    @Column(name = "purchase_order_id")
    @ApiModelProperty(value = "主表ID")
    private String purchaseOrderId;


    @Column(name = "agree_nums")
    @ApiModelProperty(value = "采购申请的最终批复数量")
    private Integer agreeNums;

    /**
     * 对应的资产ID|如果没有dict 就直接name字段的md5
     */
    @Column(name = "sku_id")
    @ApiModelProperty(value = "对应的资产ID|如果没有dict 就直接name字段的md5")
    private String skuId;

    /**
     * 对应的资产描述
     */
    @Column(name = "sku_desc")
    @ApiModelProperty(value = "对应的资产描述")
    private String skuDesc;


    /**
     * 采购单价
     */
    @ApiModelProperty(value = "采购单价")
    private BigDecimal price;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private Integer nums;


    /**
     * 采购总价
     */
    @ApiModelProperty(value = "采购单价")
    private BigDecimal total;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 流程子表单字段kv json
     */
    @Column(name = "instance_field_kv_json")
    @ApiModelProperty(value = "流程子表单字段kv json ")
    private String instanceFieldKvJson;



}