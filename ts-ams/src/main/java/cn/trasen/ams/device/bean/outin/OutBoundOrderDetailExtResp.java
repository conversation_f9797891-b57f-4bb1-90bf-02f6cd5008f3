package cn.trasen.ams.device.bean.outin;

import cn.trasen.ams.device.model.OutboundOrderDetail;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: InBoundOrderInsertReq
 * @author: chenbin
 * @description: 出库单详情
 * @date: 2025/2/11 17:47
 * @version: 1.0
 */

@Data
public class OutBoundOrderDetailExtResp extends OutboundOrderDetail {

    @Column(name = "code")
    @ApiModelProperty(value = "设备字典编码")
    private String code;

    /**
     * 设备名称
     */

    @ApiModelProperty(value = "设备名称")
    private String name;

    /**
     * 设备类型ID
     */

    @Excel(name = "设备类型")
    @Column(name = "sku_type")
    @ApiModelProperty(value = "设备类型")
    private String skuType;


    /**
     * 是否需要安装
     */

    @Column(name = "need_install")
    @Excel(name = "是否需要安装")
    @ApiModelProperty(value = "是否需要安装")
    private String needInstall;


    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String model;

    /**
     * 品牌ID
     */
    @Column(name = "brand_id")
    @ApiModelProperty(value = "品牌ID")
    private String brandId;


    @Excel(name = "品牌")

    @ApiModelProperty(value = "品牌")
    private String brandName;

    /**
     * 厂家ID
     */
    @Column(name = "manufacturer_id")
    @ApiModelProperty(value = "厂家ID")
    private String manufacturerId;


    @Excel(name = "厂家")

    @ApiModelProperty(value = "厂家")
    private String manufacturerName;


    /**
     * 供应商ID
     */

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;


    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 22分类ID
     */
    @PkExistValid(table = "d_category22", message = "分类ID不正确")
    @Column(name = "category22_id")
    @ApiModelProperty(value = "22分类ID")
    private String category22Id;


    @Excel(name = "分类")

    @ApiModelProperty(value = "分类")
    private String category22Name;


    @ApiModelProperty(value = "资产编号")
    private String uniqueNo;


    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单价")
    private String price;
}
