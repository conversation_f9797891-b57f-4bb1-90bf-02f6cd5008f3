package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.purchase.PurchaseOrderInsertReq;
import cn.trasen.ams.device.bean.purchase.PurchaseOrderInstanceConfig;
import cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryDetailResp;
import cn.trasen.ams.device.bean.purchase.PurchaseOrderWaitDeliveryResp;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.form.model.DpTableField;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderService
 * @Description TODO
 * @date 2025年6月10日 上午11:09:09
 */
public interface PurchaseOrderService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年6月10日 上午11:09:09
     * <AUTHOR>
     */
    Integer save(PurchaseOrder record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年6月10日 上午11:09:09
     * <AUTHOR>
     */
    Integer update(PurchaseOrder record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年6月10日 上午11:09:09
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return PurchaseOrder
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年6月10日 上午11:09:09
     * <AUTHOR>
     */
    PurchaseOrder selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseOrder>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年6月10日 上午11:09:09
     * <AUTHOR>
     */
    DataSet<PurchaseOrder> getDataSetList(Page page, PurchaseOrder record);

    Map<String, Integer> getNumsWithStatus(String definitionId, List<String> status);


    void insert(PurchaseOrderInsertReq record);

    void edit(PurchaseOrderInsertReq record);

    void cancel(String orderId);

    void batchCancel(List<String> ids);

    /**
     * 批量完成采购订单
     *
     * @param ids 采购订单ID列表
     * @return 完成的订单数量
     */
    Integer batchComplete(List<String> ids);

    PurchaseOrderInstanceConfig getInstanceConfig(String definitionId);

    Integer getPurchaseRows(String definitionId, String status, Integer month);

    Integer getNoContract3ML(String definitionId);

    List<PurchaseOrderWaitDeliveryResp> waitDelivery(String skuType);

    PurchaseOrderWaitDeliveryDetailResp waitDeliveryDetail(String id);

    void updatePurchaseOrderArrivedStatus();
}
