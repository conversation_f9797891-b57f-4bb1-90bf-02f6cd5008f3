package cn.trasen.ams.device.bean.signoff;

import cn.trasen.ams.device.model.Signoff;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.signoff
 * @className: SignoffListResp
 * @author: chenbin
 * @description: 用于返回验收单列表的响应对象
 * @date: 2025/7/10 17:35
 * @version: 1.0
 */
@Data
public class SignoffExt extends Signoff {
    /**
     * 采购项目名称
     */
    @ApiModelProperty(value = "Q:采购项目名称")
    private String name;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "Q:供应商名称")
    private String supplierName;

    /**
     * 供应商联系人
     */

    @ApiModelProperty(value = "Q:供应商联系人")
    private String supplierContactor;

    /**
     * 供应商手机
     */
    @ApiModelProperty(value = "Q:供应商手机")
    private String supplierMobile;


    @ApiModelProperty(value = "后端预留，前端传入无效")
    private String pmsql;

}
