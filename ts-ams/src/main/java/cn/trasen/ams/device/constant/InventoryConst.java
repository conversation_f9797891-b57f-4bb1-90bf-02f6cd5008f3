package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: InventoryConst
 * @author: chenbin
 * @description: TODO
 * @date: 2025/2/21 08:54
 * @version: 1.0
 */

public class InventoryConst {

    // 未开始
    public static final String PLAN_STATUS_READY = "0";

    // 进行中
    public static final String PLAN_STATUS_ING = "1";

    // 已完成
    public static final String PLAN_STATUS_DONE = "2";

    // 0未盘点
    public static final String TASK_STATUS_READY = "0";

    //1 盘到
    public static final String TASK_STATUS_PD = "1";

    //2 盘亏
    public static final String TASK_STATUS_PK = "2";

    // 0 1 2 3 4 5  未盘点  正常使用  闲置  故障  损坏  遗失
    public static final String TASK_RET_READY = "0";

    public static final String TASK_RET_NORMAL = "1";

    public static final String TASK_RET_IDLE = "2";

    public static final String TASK_RET_FAULT = "3";

    public static final String TASK_RET_DAMAGE = "4";

    public static final String TASK_RET_LOST = "5";


    //AMS_INVENTORY_TASK_RET
    public static final String INVENTORY_TASK_RET = "AMS_INVENTORY_TASK_RET";

    //AMS_INVENTORY_TASK_STATUS
    public static final String INVENTORY_TASK_STATUS = "AMS_INVENTORY_TASK_STATUS";

    //AMS_INVENTORY_PLAN_STATUS
    public static final String INVENTORY_PLAN_STATUS = "AMS_INVENTORY_PLAN_STATUS";



}
