package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanCreatedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanUpdatedEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintPlan;
import cn.trasen.ams.device.service.MaintPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintPlanController
 * @Description TODO
 * @date 2024年12月12日 上午10:27:03
 */
@RestController
@Api(tags = "MaintPlanController")
public class MaintPlanController {

    private transient static final Logger logger = LoggerFactory.getLogger(MaintPlanController.class);

    @Autowired
    private MaintPlanService maintPlanService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveMaintPlan
     * @Description 新增
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/maintPlan/save")
    public PlatformResult<String> saveMaintPlan(@RequestBody MaintPlan record) {
        try {
            maintPlanService.save(record);
            // 发布新增事件
            applicationEventPublisher.publishEvent(new MaintPlanCreatedEvent(record));
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateMaintPlan
     * @Description 编辑
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/maintPlan/update")
    public PlatformResult<String> updateMaintPlan(@RequestBody MaintPlan record) {
        try {
            maintPlanService.update(record);
            // 发布编辑事件
            applicationEventPublisher.publishEvent(new MaintPlanUpdatedEvent(maintPlanService.selectById(record.getId())));
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<MaintPlan>
     * @Title selectMaintPlanById
     * @Description 根据ID查询
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/maintPlan/{id}")
    public PlatformResult<MaintPlan> selectMaintPlanById(@PathVariable String id) {
        try {
            MaintPlan record = maintPlanService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteMaintPlanById
     * @Description 根据ID删除
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/maintPlan/delete/{id}")
    public PlatformResult<String> deleteMaintPlanById(@PathVariable String id) {
        try {
            maintPlanService.deleteById(id);
            // 发布删除事件
            applicationEventPublisher.publishEvent(new MaintPlanUpdatedEvent(maintPlanService.selectById(id)));
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<MaintPlan>
     * @Title selectMaintPlanList
     * @Description 查询列表
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/maintPlan/list")
    public DataSet<MaintPlan> selectMaintPlanList(Page page, MaintPlan record) {
        return maintPlanService.getDataSetList(page, record);
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteMaintPlanById
     * @Description 根据ID删除
     * @date 2024年12月12日 上午10:27:03
     * <AUTHOR>
     */
    @ApiOperation(value = "归档", notes = "归档")
    @PostMapping("/api/device/maintPlan/archive/{id}")
    public PlatformResult<String> archiveMaintPlanById(@PathVariable String id) {
        try {
            maintPlanService.archiveById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
