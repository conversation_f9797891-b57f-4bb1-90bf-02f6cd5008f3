package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.service.DeviceService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.ams.common.service.SupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SupplierController
 * @Description 供应商控制器
 * @date 2024年9月3日 下午5:35:59
 */
@RestController
@Api(tags = "SupplierController")
public class SupplierController {

    private transient static final Logger logger = LoggerFactory.getLogger(SupplierController.class);

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private DeviceService deviceService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveSupplier
     * @Description 新增
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/supplier/save")
    public PlatformResult<String> saveSupplier(@Valid @RequestBody Supplier record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            supplierService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateSupplier
     * @Description 编辑
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/supplier/update")
    public PlatformResult<String> updateSupplier(@Valid @RequestBody Supplier record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            supplierService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Supplier>
     * @Title selectSupplierById
     * @Description 根据ID查询
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/supplier/{id}")
    public PlatformResult<Supplier> selectSupplierById(@PathVariable String id) {
        try {
            Supplier record = supplierService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteSupplierById
     * @Description 根据ID删除
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/supplier/delete/{id}")
    public PlatformResult<String> deleteSupplierById(@PathVariable String id) {
        try {
            if (deviceService.hasUse("supplier", id)) {
                throw new RuntimeException("该供应商已被使用，无法删除.");
            }
            supplierService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Supplier>
     * @Title selectSupplierList
     * @Description 查询列表
     * @date 2024年9月3日 下午5:35:59
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/supplier/list")
    public DataSet<Supplier> selectSupplierList(Page page, Supplier record) {
        record.setSysType(CommonConst.SYS_TYPE_ZCSB);
        return supplierService.getDataSetList(page, record);
    }

    @ApiOperation(value = "列表（不分页）", notes = "列表（不分页）")
    @GetMapping("/api/device/supplier/listNoPage")
    public PlatformResult<List<Supplier>> selectSupplierListNoPage(Page page, Supplier record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            List<Supplier> list = supplierService.getList(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @GetMapping(value = "/api/device/supplier/tpl")
    @ApiOperation(value = "导入模板", notes = "导入模板")
    public void tpl(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "供应商导入模板.xlsx";
            String template = "template/supplierImportTpl.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/api/device/supplier/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {
        List<Supplier> imports = (List<Supplier>) ImportExcelUtil.getExcelDatas(file, Supplier.class);
        try {
            int count = supplierService.importByExcel(imports, CommonConst.SYS_TYPE_ZCSB);
            return PlatformResult.success("信息导入成功，新增：" + count + "条");

        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }

    }


    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/supplier/export")
    public ResponseEntity<byte[]> export(Supplier record) throws IOException {

        try {

            List<Supplier> exportList = supplierService.getList(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/supplierExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("供应商列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
