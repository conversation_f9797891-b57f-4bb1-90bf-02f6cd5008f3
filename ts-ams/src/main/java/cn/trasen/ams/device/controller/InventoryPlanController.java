package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.homs.bean.hrms.HrmsOrganization;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InventoryPlan;
import cn.trasen.ams.device.service.InventoryPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryPlanController
 * @Description TODO
 * @date 2025年2月20日 上午9:06:13
 */
@RestController
@Api(tags = "InventoryPlanController")
public class InventoryPlanController {

    private transient static final Logger logger = LoggerFactory.getLogger(InventoryPlanController.class);

    @Autowired
    private InventoryPlanService inventoryPlanService;

    @Autowired
    private DeviceService deviceService;


    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveInventoryPlan
     * @Description 新增
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/inventoryPlan/save")
    public PlatformResult<String> saveInventoryPlan(@Validated @RequestBody InventoryPlan record) {
        try {
            inventoryPlanService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateInventoryPlan
     * @Description 编辑
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/inventoryPlan/update")
    public PlatformResult<String> updateInventoryPlan(@RequestBody InventoryPlan record) {
        try {
            inventoryPlanService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title updateInventoryPlan
     * @Description 盘点确认
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "盘点确认", notes = "盘点确认")
    @PostMapping("/api/device/inventoryPlan/sure/{id}")
    public PlatformResult<String> sure(@PathVariable String id) {
        try {
            inventoryPlanService.forceSure(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<InventoryPlan>
     * @Title selectInventoryPlanById
     * @Description 根据ID查询
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/inventoryPlan/{id}")
    public PlatformResult<InventoryPlan> selectInventoryPlanById(@PathVariable String id) {
        try {
            InventoryPlan record = inventoryPlanService.selectById(id);
            inventoryPlanService.dataFmt(record);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param record
     * @return PlatformResult<Integer>
     * @Title saveInventoryPlan
     * @Description 查询当前有多少资产
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "任务数量", notes = "任务数量")
    @PostMapping("/api/device/inventoryPlan/tasks")
    public PlatformResult<Integer> getCount(InventoryPlan record) {
        try {
            record.idStr2List();
            int res = deviceService.count4InventoryPlan(record);
            return PlatformResult.success(res);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteInventoryPlanById
     * @Description 根据ID删除
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/inventoryPlan/delete/{id}")
    public PlatformResult<String> deleteInventoryPlanById(@PathVariable String id) {
        try {
            inventoryPlanService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<InventoryPlan>
     * @Title selectInventoryPlanList
     * @Description 查询列表
     * @date 2025年2月20日 上午9:06:13
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/inventoryPlan/list")
    public DataSet<InventoryPlan> selectInventoryPlanList(Page page, InventoryPlan record) {
        return inventoryPlanService.getDataSetList(page, record);
    }

    @ApiOperation(value = "盘点组织分类列表", notes = "盘点组织分类列表")
    @GetMapping("/api/device/inventoryPlan/cateList/{id}")
    public PlatformResult selectOrg(@PathVariable String id, @RequestParam(required = false) String orgId, @RequestParam(required = false) String statusExt) {
        try {
            return PlatformResult.success(inventoryPlanService.getInventoryPlanCateList(id, orgId, statusExt));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "盘点组织科室列表", notes = "盘点组织科室列表")
    @GetMapping("/api/device/inventoryPlan/orgList/{id}")
    public PlatformResult selectCategory(@PathVariable String id, @RequestParam(required = false) String statusExt) {
        try {
            return PlatformResult.success(inventoryPlanService.getInventoryPlanOrgList(id, statusExt));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


}
