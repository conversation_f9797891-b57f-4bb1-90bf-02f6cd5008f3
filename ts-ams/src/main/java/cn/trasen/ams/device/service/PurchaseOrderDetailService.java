package cn.trasen.ams.device.service;

import cn.trasen.ams.device.model.PurchaseOrderDetail;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderDetailService
 * @Description TODO
 * @date 2025年6月16日 下午1:49:57
 */
public interface PurchaseOrderDetailService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年6月16日 下午1:49:57
     * <AUTHOR>
     */
    Integer save(PurchaseOrderDetail record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年6月16日 下午1:49:57
     * <AUTHOR>
     */
    Integer update(PurchaseOrderDetail record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年6月16日 下午1:49:57
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return PurchaseOrderDetail
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年6月16日 下午1:49:57
     * <AUTHOR>
     */
    PurchaseOrderDetail selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseOrderDetail>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年6月16日 下午1:49:57
     * <AUTHOR>
     */
    DataSet<PurchaseOrderDetail> getDataSetList(Page page, PurchaseOrderDetail record);

    void batchInsert(List<PurchaseOrderDetail> list);

    void deleteByPurchaseOrderId(String purchaseOrderId);

    List<PurchaseOrderDetail> selectByPurchaseOrderId(String purchaseOrderId);

}
