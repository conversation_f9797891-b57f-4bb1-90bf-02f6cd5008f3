package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.bean.inspection.*;
import cn.trasen.ams.device.bean.maintTask.CompleteMaintTaskReq;
import cn.trasen.ams.device.bean.maintTask.MaintTaskListReq;
import cn.trasen.ams.device.bean.maintTask.MaintTaskListResp;
import cn.trasen.ams.device.bean.maintTask.SolveExpMaintTaskReq;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.eventPublisher.maintTask.MaintTaskCompletedEvent;
import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.ams.device.model.InspectionTask;
import cn.trasen.ams.device.service.InspectionTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InspectionTaskController
 * @Description TODO
 * @date 2024年12月20日 下午1:50:02
 */
@RestController
@Api(tags = "InspectionTaskController")
public class InspectionTaskController {

    private transient static final Logger logger = LoggerFactory.getLogger(InspectionTaskController.class);

    @Autowired
    private InspectionTaskService inspectionTaskService;

    /**
     * @param id
     * @return PlatformResult<InspectionTask>
     * @Title selectInspectionTaskById
     * @Description 根据ID查询
     * @date 2024年12月20日 下午1:50:02
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/inspectionTask/{id}")
    public PlatformResult<InspectionTaskDetailResp> selectInspectionTaskById(@PathVariable String id) {
        try {
            return PlatformResult.success(inspectionTaskService.getTaskDetail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "批量完成巡检任务", notes = "批量完成巡检任务")
    @PostMapping("/api/device/inspectionTask/completeBatch")
    public PlatformResult<String> completeInspectionTask(@RequestBody CompleteInspectionTaskReq record) {
        try {
            inspectionTaskService.completeInspectionTaskBatch(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "异常列表", notes = "异常列表")
    @GetMapping("/api/device/inspectionTask/explist")
    public DataSet<InspectionTaskListResp> selectMaintTaskExpList(Page page, InspectionTaskListReq record) {
        record.setExp(CommonConst.YES);
        return inspectionTaskService.getDataSetList(page, record);
    }
    @ApiOperation(value = "处理保养工单异常", notes = "处理保养工单异常")
    @PostMapping("/api/device/inspectionTask/solveExp")

    public PlatformResult<String> solveExpMaintTask(@RequestBody SolveExpInspectionTaskReq record) {
        try {
            inspectionTaskService.solveExpMaintTask(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
