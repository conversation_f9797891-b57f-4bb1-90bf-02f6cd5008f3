package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.TransferDetail;

import java.util.List;

/**
 * @ClassName TransferDetailService
 * @Description TODO
 * @date 2025年4月18日 上午9:26:50
 * <AUTHOR>
 * @version 1.0
 */
public interface TransferDetailService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	Integer save(TransferDetail record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	Integer update(TransferDetail record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return TransferDetail
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	TransferDetail selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<TransferDetail>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	DataSet<TransferDetail> getDataSetList(Page page, TransferDetail record);


	void deleteByTransferId(String transferId);

	List<TransferDetail> selectByTransferId(String transferId);
}
