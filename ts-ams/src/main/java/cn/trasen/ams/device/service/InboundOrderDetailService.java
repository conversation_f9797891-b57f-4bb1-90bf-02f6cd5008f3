package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InboundOrderDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InboundOrderDetailService
 * @Description TODO
 * @date 2025年2月11日 下午5:29:40
 */
public interface InboundOrderDetailService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月11日 下午5:29:40
     * <AUTHOR>
     */
    Integer save(InboundOrderDetail record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月11日 下午5:29:40
     * <AUTHOR>
     */
    Integer update(InboundOrderDetail record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:29:40
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InboundOrderDetail
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:29:40
     * <AUTHOR>
     */
    InboundOrderDetail selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InboundOrderDetail>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月11日 下午5:29:40
     * <AUTHOR>
     */
    DataSet<InboundOrderDetail> getDataSetList(Page page, InboundOrderDetail record);

    void deleteByInboundOrderId(String inboundOrderId);

    List<InboundOrderDetailExtResp> selectByInboundOrderId(String inboundOrderId);

    List<String> getGoBackingDeviceIds();
}
