package cn.trasen.ams.device.eventListener.maintTask;

import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanCreatedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanDeletedEvent;
import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanUpdatedEvent;
import cn.trasen.ams.device.service.MaintTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.eventListener.maintTask
 * @className: MaintPlanModifyListener
 * @author: chenbin
 * @description: 当保养计划被创建、更新、删除时，同步更新保养任务
 * @date: 2024/12/19 09:11
 * @version: 1.0
 */

@Component
public class SyncMaintTaskListener {

    @Autowired
    private MaintTaskService maintTaskService;

    @EventListener(classes = {MaintPlanCreatedEvent.class})
    public void onMaintPlanCreated(MaintPlanCreatedEvent event) {
        System.out.println("onMaintPlanCreated.onEventPublished");
        maintTaskService.createTaskByMaintPlan(event.getMaintPlan());
    }


    @EventListener(classes = {MaintPlanUpdatedEvent.class})
    public void onMaintPlanUpdated(MaintPlanUpdatedEvent event) {
        System.out.println("onMaintPlanUpdated.onEventPublished");
        maintTaskService.updateTaskByMaintPlan(event.getMaintPlan());
    }

    @EventListener(classes = {MaintPlanDeletedEvent.class})
    public void onMaintPlanDeleted(MaintPlanDeletedEvent event) {
        System.out.println("onMaintPlanDeleted.onEventPublished");
        maintTaskService.deleteByMaintPlan(event.getMaintPlan());
    }
}
