<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.Category22Mapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.Category22">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="level" jdbcType="BIT" property="level" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="use_to" jdbcType="VARCHAR" property="useTo" />
    <result column="sord" jdbcType="INTEGER" property="sord" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="tree_ids" jdbcType="LONGVARCHAR" property="treeIds" />
    <result column="elx" jdbcType="LONGVARCHAR" property="elx" />
    <result column="sku_ex" jdbcType="LONGVARCHAR" property="skuEx" />
  </resultMap>
</mapper>