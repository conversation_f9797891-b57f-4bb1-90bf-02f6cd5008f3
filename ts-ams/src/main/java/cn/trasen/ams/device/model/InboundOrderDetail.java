package cn.trasen.ams.device.model;

import cn.trasen.ams.common.validator.pk.PkExistValid;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_inbound_order_detail")
@Setter
@Getter
public class InboundOrderDetail {
    @Id
    private String id;

    @Column(name = "inbound_order_id")
    private String inboundOrderId;

    /**
     * 资产ID（当退回入库时）
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "资产ID（当退回入库时）")
    private String deviceId;

    /**
     * 资产ID
     */
    @PkExistValid(table = "d_sku", message = "资产ID不合法")
    @Column(name = "sku_id")
    @ApiModelProperty(value = "资产ID")
    private String skuId;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量最小为1")
    @ApiModelProperty(value = "数量")
    private Integer num;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @ApiModelProperty(value = "金额")
    private BigDecimal price;

    /**
     * 存放位置
     */

    @ApiModelProperty(value = "存放位置")
    private String loc;


    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}