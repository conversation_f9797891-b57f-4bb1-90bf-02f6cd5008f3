package cn.trasen.ams.device.bean.outin;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.model.OutboundOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: InBoundOrderInsertReq
 * @author: chenbin
 * @description: 出库单详情
 * @date: 2025/2/11 17:47
 * @version: 1.0
 */

@Data
public class OutBoundOrderDetailResp {

    @ApiModelProperty(value = "出库单结构体")
    OutboundOrder outboundOrder;

    @ApiModelProperty(value = "出库单明细结构体")
    List<OutBoundOrderDetailExtResp> outboundOrderDetailExtResp;
}
