package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.bean.comm.CommApproveReq;
import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.disposal.DisposalInsertReq;
import cn.trasen.ams.device.constant.DeviceConst;
import cn.trasen.ams.device.constant.OperationConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.DisposalDetailService;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.EmployeeService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.message.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.DisposalMapper;
import cn.trasen.ams.device.model.Disposal;
import cn.trasen.ams.device.service.DisposalService;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DisposalServiceImpl
 * @Description TODO
 * @date 2025年4月27日 下午4:48:41
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DisposalServiceImpl implements DisposalService {

    @Autowired
    private DisposalMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DisposalDetailService disposalDetailService;

    @Autowired
    private DictService dictService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private PermissionService permissionService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Disposal record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Disposal record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        Disposal row = selectById(id);
        if (row == null) {
            throw new RuntimeException("资产处置单不存在");
        }
        if (!CommonConst.CHECK_WAIT.equals(row.getStatus())) {
            throw new RuntimeException("资产处置单已经审核，无法删除");
        }
        // 取消资产转移单
        deviceService.goDisposalCancel(row);

        Disposal record = new Disposal();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Disposal selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    private Example.Criteria buildCriteria(Example.Criteria criteria, Disposal record) {
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (!StringUtil.isEmpty(record.getSkuNameSet())) {
            criteria.andLike("skuNameSet", "%" + record.getSkuNameSet() + "%");
        }

        if (!StringUtil.isEmpty(record.getCreateUserName())) {
            criteria.andLike("createUserName", "%" + record.getCreateUserName() + "%");
        }

        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        permissionService.appendPermissionCondition(PermissionConst.二级业务类型_资产处置单列表, criteria);


        return criteria;
    }

    @Override
    public List<Disposal> getList(Disposal disposal) {
        Example example = new Example(Disposal.class);
        Example.Criteria criteria = example.createCriteria();
        buildCriteria(criteria, disposal);
        example.setOrderByClause("create_date desc");
        List<Disposal> records = mapper.selectByExample(example);
        records.forEach(this::dataFmt);
        return records;
    }

    @Override
    public DataSet<Disposal> getDataSetList(Page page, Disposal record) {
        Example example = new Example(Disposal.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        buildCriteria(criteria, record);
        // order by
        example.setOrderByClause("create_date desc");
        List<Disposal> records = mapper.selectByExampleAndRowBounds(example, page);
        // 格式化数据
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    private String genFlowNo() {
        return serialNoGenService.genByDate("CZ");
    }

    private void prepare(DisposalInsertReq disposalInsertReq) {


        String transferIdInit = IdGeneraterUtils.nextId();

        Disposal disposal = disposalInsertReq.getDisposal();

        // 添加的时候要设置ID
        if (disposal.getId() == null) {
            disposal.setId(transferIdInit);
        }

        // 获取资产转移明细列表
        List<DisposalDetail> disposalDetailList = disposalInsertReq.getDisposalDetailList();

        // 处理冗余数据
        // 提取所有资产ID
        List<String> deviceIdList = disposalDetailList.stream().map(DisposalDetail::getDeviceId).collect(Collectors.toList());

        // 根据资产ID集合获取设备列表
        List<DeviceExtResp> deviceList = deviceService.getListByIds(String.join(",", deviceIdList));

        // 计算资产转移数量和总价值
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 使用 Map 统计名称出现次数
        Map<String, Integer> nameCountMap = new HashMap<>();
        Map<String, DeviceExtResp> deviceMap = new HashMap<>();


        for (DeviceExtResp deviceExtResp : deviceList) {
            // 检查资产状态
            if (!DeviceConst.WAREHOUSE_STATUS_ZY.equals(deviceExtResp.getWarehouseStatus())) {
                throw new RuntimeException("存在资产状态不是在役，无法自此发起处置申请");
            }

            // 计算资产转移总价值
            totalAmount = totalAmount.add(deviceExtResp.getOriginalVal());
            // 统计名称出现次数
            nameCountMap.put(deviceExtResp.getName(), nameCountMap.getOrDefault(deviceExtResp.getName(), 0) + 1);
            deviceMap.put(deviceExtResp.getId(), deviceExtResp);
        }

        // 格式化名称统计结果为 A*2,B*1,C*3 的形式
        String skuNameSet = nameCountMap.entrySet().stream().map(entry -> entry.getKey() + "*" + entry.getValue()).collect(Collectors.joining(","));

        Assert.notNull(disposal, "资产处置对象不能为空");
        Assert.notEmpty(disposalDetailList, "资产处置明细不能为空");

        // 新增的时候默认就是待审核
        disposal.setStatus(CommonConst.CHECK_WAIT);
        // 设置资产转移单号
        disposal.setFlowNo(genFlowNo());
        disposal.setSkuNameSet(skuNameSet);
        disposal.setNums(disposalDetailList.size());
        disposal.setAssetTotalValue(totalAmount);

    }

    @Transactional(readOnly = false)
    @Override
    public void insert(DisposalInsertReq disposalInsertReq) {
        // 准备和验证数据
        prepare(disposalInsertReq);
        Disposal disposal = disposalInsertReq.getDisposal();
        // 获取资产转移明细列表
        List<DisposalDetail> disposalDetailList = disposalInsertReq.getDisposalDetailList();

        save(disposal);
        // 批量插入资产转移明细
        for (DisposalDetail disposalDetail : disposalDetailList) {
            // 标记转移状态
            // 添加数据行
            disposalDetail.setDisposalId(disposal.getId());
            disposalDetailService.save(disposalDetail);
        }
        // 更新资产状态
        deviceService.goDisposaling(disposal);
        // 资产转移审核通知
        noticeCheck(disposal);
    }

    @Transactional(readOnly = false)
    @Override
    public void edit(DisposalInsertReq disposalInsertReq) {

        Disposal disposal = disposalInsertReq.getDisposal();
        Disposal origin = selectById(disposal.getId());

        if (CommonConst.CHECK_PASS.equals(origin.getStatus())) {
            throw new RuntimeException("审核通过了不允许修改");
        }

        disposal.setStatus(CommonConst.CHECK_WAIT);

        // 取消上一批资产的状态
        deviceService.goDisposalCancel(disposalInsertReq.getDisposal());
        // 准备和验证数据
        prepare(disposalInsertReq);
        //删除掉旧的详情数据
        disposalDetailService.deleteByDisposalId(disposalInsertReq.getDisposal().getId());

        update(disposal);
        // 获取资产转移明细列表
        List<DisposalDetail> disposalDetailList = disposalInsertReq.getDisposalDetailList();
        for (DisposalDetail disposalDetail : disposalDetailList) {
            // 标记转移状态
            // 添加数据行
            disposalDetail.setDisposalId(disposal.getId());
            disposalDetailService.save(disposalDetail);
        }
        // 对新数据进行标记
        deviceService.goDisposaling(disposal);

        // 如果是审核不通过重新提交
        if (origin.getStatus().equals(CommonConst.CHECK_FAIL)) {
            noticeCheck(disposal);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void approve(CommApproveReq commApproveReq) {
        Disposal row = selectById(commApproveReq.getId());

        if (row == null) {
            throw new RuntimeException("资产处置单不存在");
        }
        String checkRule = CommonConst.CHECK_PASS + "," + CommonConst.CHECK_FAIL;

        if (!checkRule.contains(commApproveReq.getStatus())) {
            throw new RuntimeException("审核状态不合法");
        }

        if (!CommonConst.CHECK_WAIT.equals(row.getStatus())) {
            throw new RuntimeException("资产处置单已经审核，无法再次审核");
        }

        String disposalId = commApproveReq.getId();
        // 变更资产转移单的信息
        Disposal disposal = new Disposal();
        disposal.setId(disposalId);
        disposal.setStatus(commApproveReq.getStatus());
        disposal.setDoFileSet(commApproveReq.getFileSet());
        disposal.setDoNote(commApproveReq.getNote());
        disposal.setDoDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            disposal.setDoerId(user.getUsercode());
            disposal.setDoerName(user.getUsername());
            // 发通知用
            row.setDoerId(user.getUsercode());
            row.setDoerName(user.getUsername());
        }
        update(disposal);
        if (CommonConst.CHECK_PASS.equals(commApproveReq.getStatus())) {
            // 更新资产所属ID及状态
            deviceService.goDisposal(disposal);
        } else {
            deviceService.goDisposalCancel(disposal);
        }


        // 通知审核
        noticeChecked(row);
    }

    @Override
    public void dataFmt(Disposal disposal) {
        // 状态翻译
        disposal.setStatusShow(dictService.cgetNameByValue(CommonConst.CHECK, disposal.getStatus()));
        // skuType翻译
        disposal.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, disposal.getSkuType()));
        // 申请人科室
        EmployeeResp employee = employeeService.cgetEmployeeByCode(disposal.getCreateUser());
        disposal.setCreateUserOrgName(employee.getOrgName());
    }


    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 通知审核结果
     * @date 2025/5/23 10:10
     */
    private void noticeChecked(Disposal record) {
        String userCode = record.getCreateUser();
        String doerId = record.getDoerId();
        String doerName = record.getDoerName();

        if (StringUtils.isEmpty(userCode) || StringUtils.isEmpty(doerId)) {
            return;
        }

        String content = "您发起的处置计划：" + record.getFlowNo() + "已经审核完成，请查看审核结果。";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(userCode)  //接收人
                .sender(doerId) //发送人
                .senderName(doerName) //发送人name
                .subject("资产设备-资产处置审核").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/equipment-asset-management/asset-disposal").source("资产转移管理").build();
        NoticeService.sendAsynNotice(notice);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 通知审核
     * @date 2025/5/22 15:42
     */
    private void noticeCheck(Disposal record) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String receiver = employeeService.cgetThpsUserSetByOperationId(OperationConst.转移审批);

        if (StringUtils.isEmpty(receiver)) {
            return;
        }

        String content = user.getDeptname() + ":" + user.getUsername() + "发起了一个处置申请，单号：" + record.getFlowNo() + "，涉及资产:" + record.getSkuNameSet() + ",涉及金额：" + record.getAssetTotalValue() + "，请及时审核。";
        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(receiver)  //接收人
                .sender(record.getCreateUser()) //发送人
                .senderName(record.getCreateUserName()) //发送人name
                .subject("资产设备-资产处置申请").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/equipment-asset-management/asset-disposal").source("资产转移管理").build();

        NoticeService.sendAsynNotice(notice);
    }


}
