package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.maintTask.*;
import cn.trasen.ams.device.model.MaintPlan;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintTask;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintTaskService
 * @Description TODO
 * @date 2024年12月14日 下午2:07:59
 */
public interface MaintTaskService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    Integer save(MaintTask record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    Integer update(MaintTask record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return MaintTask
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    MaintTask selectById(String id);

    MaintTaskDetailResp getDetailById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MaintTask>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    DataSet<MaintTaskListResp> getDataSetList(Page page, MaintTaskListReq record);


    /**
     * @param maintPlan:
     * @return void
     * <AUTHOR>
     * @description 根据计划创建任务
     * @date 2024/12/14 14:11
     */
    void createTaskByMaintPlan(MaintPlan maintPlan);

    /**
     * @param maintPlan:
     * @return void
     * @Title updateTaskByMaintPlan
     * @Description 根据计划更新任务
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    void updateTaskByMaintPlan(MaintPlan maintPlan);

    /**
     * @param maintPlan:
     * @return void
     * @Title deleteByMaintPlanId
     * @Description 根据计划ID删除任务
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    void deleteByMaintPlan(MaintPlan maintPlan);

    int getTasksByMaintPlanId(String maintPlanId);

    void scrap(String id, String act);

    void completeMaintTask(CompleteMaintTaskReq record);

    void solveExpMaintTask(SolveExpMaintTaskReq record);

    List<MaintTask> getMaintTaskListByMaintPlanId(String maintPlanId);
}
