package cn.trasen.ams.device.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.bean.device.DeviceDetailResp;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.DiyTplConst;
import cn.trasen.ams.device.model.Category22;
import cn.trasen.ams.device.service.Category22Service;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.DiyTplMapper;
import cn.trasen.ams.device.model.DiyTpl;
import cn.trasen.ams.device.service.DiyTplService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DiyTplServiceImpl
 * @Description TODO
 * @date 2024年12月19日 下午5:36:26
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DiyTplServiceImpl implements DiyTplService {

    @Autowired
    private DiyTplMapper mapper;

    @Autowired
    private Category22Service category22Service;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DeviceService deviceService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DiyTpl record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        record.setStatus(CommonConst.YES);
        autoFillColumn(record);

        return mapper.insertSelective(record);
    }

    private void autoFillColumn(DiyTpl record) {


        if (StringUtil.isEmpty(record.getModelId())) {
            return;
        }

        List<String> modelIdList = Arrays.asList(record.getModelId().split(","));
        List<String> modelNameList = new ArrayList<>();

        if (DiyTplConst.MODEL_TYPE_MAINT.equals(record.getModelType())) {
            // 保养
            for (String modelId : modelIdList) {
                try {
                    Category22 category22 = (Category22) redisService.fetch(String.format("%s:%s", "category22", modelId), () -> {
                        return category22Service.selectById(modelId);
                    }, 300);
                    modelNameList.add(category22.getName());
                } catch (Exception e) {
                    throw new BusinessException("不存在的分类");
                }
            }


        } else {
            // 巡检
            for (String modelId : modelIdList) {
                String modelName = dictService.cgetNameByValue(DiyTplConst.DIY_TPL_INSPECTION_CATE, modelId);
                if (StringUtil.isEmpty(modelName)) {
                    continue;
                }
                modelNameList.add(modelName);
            }
        }
        record.setModelName(String.join(",", modelNameList));
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DiyTpl record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        autoFillColumn(record);
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DiyTpl record = new DiyTpl();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DiyTpl selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Transactional(readOnly = false)
    @Override
    public DiyTpl copyById(String id) {
        DiyTpl record = selectById(id);
        DiyTpl copyRecord = new DiyTpl();
        BeanUtils.copyProperties(record, copyRecord);
        copyRecord.setName(record.getName() + "(复制)");
        save(copyRecord);
        return copyRecord;
    }

    @Override
    public DataSet<DiyTpl> getDataSetList(Page page, DiyTpl record) {
        Example example = new Example(DiyTpl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (!StringUtil.isEmpty(record.getModelType())) {
            criteria.andEqualTo("modelType", record.getModelType());
        }

        if (!StringUtil.isEmpty(record.getModelName())) {
            criteria.andLike("modelName", "%" + record.getModelName() + "%");
        }

        if (!StringUtil.isEmpty(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        List<DiyTpl> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public List<DiyTpl> getListByDeviceId(String deviceId) {

        try {
            DeviceDetailResp deviceDetail = deviceService.selectDetailById(deviceId);
            if (deviceDetail == null) {
                return new ArrayList<>();
            }
            String category22id = deviceDetail.getSku().getCategory22Id();

            Example example = new Example(DiyTpl.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

            criteria.andEqualTo("modelType", DiyTplConst.MODEL_TYPE_MAINT);
            criteria.andLike("modelId", "%" + category22id + "%");
            return mapper.selectByExample(example);

        } catch (Exception e) {
            return new ArrayList<>();
        }


    }
}
