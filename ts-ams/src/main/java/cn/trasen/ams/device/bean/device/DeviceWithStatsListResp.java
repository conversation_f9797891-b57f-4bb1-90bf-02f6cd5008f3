package cn.trasen.ams.device.bean.device;

import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.model.DeviceStats;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.device
 * @className: DeviceListResp4Maint
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/26 16:33
 * @version: 1.0
 */
@Data
public class DeviceWithStatsListResp {
    Device device;
    DeviceStats deviceStats;
}
