package cn.trasen.ams.device.eventListener.maintPlan;

import cn.trasen.ams.device.eventPublisher.maintPlan.MaintPlanCreatedEvent;
import cn.trasen.ams.device.eventPublisher.maintTask.MaintTaskCompletedEvent;
import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.ams.device.service.MaintPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.eventListener.maintPlan
 * @className: MaintPlanProcessListener
 * @author: chenbin
 * @description: 保养计划进程监听器
 * @date: 2024/12/19 08:44
 * @version: 1.0
 */
@Component
public class MaintPlanProcessListener {

    @Autowired
    private MaintPlanService maintPlanService;

    @EventListener(classes = {MaintTaskCompletedEvent.class})
    public void onMaintTaskCompleted(MaintTaskCompletedEvent event) {
        System.out.println("onMaintTaskCompleted.onEventPublished");
        maintPlanService.syncProcess(event.getMaintTask());
    }

}
