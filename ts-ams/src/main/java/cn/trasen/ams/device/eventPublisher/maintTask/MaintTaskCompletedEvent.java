package cn.trasen.ams.device.eventPublisher.maintTask;

import cn.trasen.ams.device.model.MaintTask;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.eventPublisher.maintTask
 * @className: MaintTaskUpdatedEvent
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/18 18:01
 * @version: 1.0
 */

@Getter
public class MaintTaskCompletedEvent extends ApplicationEvent {

    private MaintTask maintTask;

    public MaintTaskCompletedEvent(MaintTask maintTask) {
        super(maintTask);
        this.maintTask = maintTask;
    }
}
