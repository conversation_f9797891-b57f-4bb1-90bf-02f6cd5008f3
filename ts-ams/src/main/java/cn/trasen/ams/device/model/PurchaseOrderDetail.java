package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_purchase_order_detail")
@Setter
@Getter
public class PurchaseOrderDetail {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    /**
     * 主表ID
     */
    @Column(name = "purchase_order_id")
    @ApiModelProperty(value = "主表ID")
    private String purchaseOrderId;


    //main_instance_id
    @NotNull(message = "流程基础表ID不能为空")
    @Column(name = "base_instance_id")
    @ApiModelProperty(value = "流程基础表ID")
    private String baseInstanceId;


    /**
     * 流程ID
     */
    @NotNull(message = "流程子表ID不能为空")
    @Column(name = "instance_id")
    @ApiModelProperty(value = "流程子表ID")
    private String instanceId;


    // instance_field_kv_json
    @NotNull(message = "流程子表单字段kv json不能为空")
    @Column(name = "instance_field_kv_json")
    @ApiModelProperty(value = "流程子表单字段kv json ")
    private String instanceFieldKvJson;

    /**
     * 采购申请的最终批复数量
     */
    @Column(name = "agree_nums")
    @ApiModelProperty(value = "采购申请的最终批复数量")
    private Integer agreeNums;

    /**
     * 对应的资产ID
     */
    @Column(name = "sku_id")
    @ApiModelProperty(value = "对应的资产ID")
    private String skuId;

    /**
     * 对应的资产描述
     */
    @Column(name = "sku_desc")
    @ApiModelProperty(value = "对应的资产描述")
    private String skuDesc;

    /**
     * 前端选中对象冗余
     */
    @Column(name = "sku_obj")
    @ApiModelProperty(value = "前端选中对象冗余")
    private String skuObj;

    /**
     * 采购单价
     */
    @NotNull(message = "采购单价不能为空")
    @ApiModelProperty(value = "采购单价")
    private BigDecimal price;

    /**
     * 采购数量
     */
    @NotNull(message = "采购数量不能为空")
    @ApiModelProperty(value = "采购数量")
    private Integer nums;


    /**
     * 采购总价
     */
    @ApiModelProperty(value = "采购总价")
    private BigDecimal total;


    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;


    @Transient
    @ApiModelProperty(value = "未采购数量")
    private Integer wait_purchase_nums;

}