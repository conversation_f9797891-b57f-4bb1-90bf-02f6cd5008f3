package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_diy_tpl")
@Setter
@Getter
public class DiyTpl {
    /**
     * 主键
     */

    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 模板标题
     */
    @ApiModelProperty(value = "模板标题")
    private String name;

    /**
     * 类型 1 保养模板 2 巡检模板
     */
    @Column(name = "model_type")
    @ApiModelProperty(value = "类型 1 保养模板 2 巡检模板")
    private String modelType;

    /**
     * 保养关联分类ID，可以多个逗号隔开，巡检模板 取字典值 OMS-D-CALIBRATION-CATELIST
     */
    @Column(name = "model_id")
    @ApiModelProperty(value = "保养关联分类ID，可以多个逗号隔开，巡检模板 取字典值 OMS-D-CALIBRATION-CATELIST")
    private String modelId;

    /**
     * 分类名称
     */
    @Column(name = "model_name")
    @ApiModelProperty(value = "分类名称")
    private String modelName;

    /**
     * 模板状态
     */
    @ApiModelProperty(value = "模板状态")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 模板配置json串
     */
    @Column(name = "config_json")
    @ApiModelProperty(value = "模板配置json串")
    private String configJson;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}