package cn.trasen.ams.device.service.impl;

import java.util.*;

import cn.trasen.ams.common.model.Category;
import cn.trasen.ams.common.service.CategoryService;
import cn.trasen.ams.device.bean.inventory.InventoryPlanCateResp;
import cn.trasen.ams.device.bean.inventory.InventoryPlanOrgResp;
import cn.trasen.ams.device.bean.inventory.InventoryTaskStatusNumsResp;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.InventoryConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.model.*;
import cn.trasen.ams.device.service.*;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.*;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.message.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.InventoryPlanMapper;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryPlanServiceImpl
 * @Description TODO
 * @date 2025年2月20日 上午9:06:13
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InventoryPlanServiceImpl implements InventoryPlanService {

    @Autowired
    private InventoryPlanMapper mapper;

    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private Category22Service category22Service;

    @Autowired
    private OrgService orgService;

    @Autowired
    private InventoryTaskService inventoryTaskService;

    @Autowired
    private DictService dictService;
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private EmployeeService employeeService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InventoryPlan record) {
        if (StringUtils.isEmpty(record.getId())) {
            record.setId(IdGeneraterUtils.nextId());
        }
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");

        // 填充流水号
        try {
            record.setSerialNo(genSerialNo());
        } catch (Exception e) {
            throw new RuntimeException("生成流水号失败");
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public String insert(InventoryPlan record) {
        String id = IdGeneraterUtils.nextId();
        // 设置ID
        record.setId(id);
        // 初始状态
        record.setStatus(InventoryConst.PLAN_STATUS_READY);
        autoFillColumn(record);

        // 创建明细数据
        // 填充查询需要的list
        record.idStr2List();
        int inserts = inventoryTaskService.insertByPlan(record);

        if (inserts == 0) {
            throw new RuntimeException("当前分类或者科室下面没有资产,创建盘点任务失败");
        }

        // 回填明细数据
        record.setDevices(inserts);
        save(record);

        //通知
        noticeTask(record);

        return id;
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InventoryPlan record) {

        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public String edit(InventoryPlan record) {
        //如果计划已经执行，不允许修改
        int dones = inventoryTaskService.donesByPlan(record);

        if (dones > 0) {
            throw new RuntimeException("计划已经执行，不允许修改");
        }
        // 填充必要字段
        autoFillColumn(record);

        // 删除明细数据
        inventoryTaskService.deleteByPlan(record);
        // 创建明细数据
        // 填充查询需要的list
        record.idStr2List();
        int inserts = inventoryTaskService.insertByPlan(record);

        // 更新主数据
        record.setDevices(inserts);
        update(record);

        return record.getId();
    }


    public String genSerialNo() throws Exception {

        return serialNoGenService.genByDate("PDJH");
    }

    private void autoFillColumn(InventoryPlan record) {

        // 填充计划执行人
        if (record.getEngineerIdSet() != null) {
            List<String> engineerNameList = new ArrayList<>();
            List<String> engineerIdList = Arrays.asList(record.getEngineerIdSet().split(","));
            PlatformResult<List<EmployeeResp>> pr = hrmsEmployeeFeignService.getEmployeeDetailByCodes(engineerIdList);
            List<EmployeeResp> employeeRespList = pr.getObject();
            if (employeeRespList != null) {
                employeeRespList.forEach(employeeResp -> {
                    engineerNameList.add(employeeResp.getEmployeeName());
                });
            }
            record.setEngineerNameSet(String.join(",", engineerNameList));
        }

        // 填充22分类
        if (!StringUtils.isEmpty(record.getCate22IdSet())) {
            // id String 转 List<String> 根据,分隔
            List<String> cate22IdList = Arrays.asList(record.getCate22IdSet().split(","));
            List<Category22> category22List = category22Service.getListByIds(cate22IdList);
            List<String> cate22NameList = new ArrayList<>();

            if (category22List != null) {
                category22List.forEach(category22 -> {
                    cate22NameList.add(category22.getName());
                });
            }
            record.setCate22NameSet(String.join(",", cate22NameList));

        } else {
            record.setCate22NameSet("全部分类");
        }

        // 填充资产分类
        if (!StringUtils.isEmpty(record.getCateIdSet())) {
            // id String 转 List<String> 根据,分隔
            List<String> cateIdList = Arrays.asList(record.getCateIdSet().split(","));
            List<Category> categoryList = categoryService.getListByIds(cateIdList);
            List<String> cateNameList = new ArrayList<>();

            if (categoryList != null) {
                categoryList.forEach(category -> {
                    cateNameList.add(category.getName());
                });
            }
            record.setCateNameSet(String.join(",", cateNameList));
        } else {
            record.setCateNameSet("全部分类");
        }

        // 填充计划执行部门
        if (!StringUtils.isEmpty(record.getOrgIdSet())) {
            // 根据,拆分orgIdSet，查询orgName
            List<String> orgIdList = Arrays.asList(record.getOrgIdSet().split(","));
            List<String> orgNameList = new ArrayList<>();


            List<HrmsOrganizationResp> orgList = orgService.cgetOrgList();
            //找寻出orgIdSet所有包含的orgId
//            List<String> orgIdListFinal = new ArrayList<>();
//            for (String orgId : orgIdList) {
//                for (HrmsOrganizationResp org : orgList) {
//                    if (org.getTreeIds().contains(orgId)) {
//                        // 如果orgIdListFinal中不包含orgId，则添加
//                        if (!orgIdListFinal.contains(org.getOrganizationId())) {
//                            orgIdListFinal.add(org.getOrganizationId());
//                        }
//                    }
//                }
//            }

            Map<String, String> orgMap = orgService.cgetOrgMap();
            orgIdList.forEach(orgId -> {
                orgNameList.add(orgMap.get(orgId));
            });
            record.setOrgNameSet(String.join(",", orgNameList));

        } else {
            record.setOrgNameSet("全部科室");
        }

    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        InventoryPlan inventoryPlan = selectById(id);

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已经完成,无法删除");
        }

        InventoryPlan record = new InventoryPlan();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InventoryPlan selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<InventoryPlan> getDataSetList(Page page, InventoryPlan record) {
        Example example = new Example(InventoryPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        // 数据权限改造
        String business = PermissionConst.二级业务类型_盘点列表;
        permissionService.appendPermissionCondition(business, criteria);

        if (!StringUtils.isEmpty(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        if (!StringUtils.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        if (!StringUtils.isEmpty(record.getSkuType())) {
            criteria.andEqualTo("skuType", record.getSkuType());
        }

        if (record.getStatusExt() != null) {
            if (CommonConst.NO.equals(record.getStatusExt())) {
                criteria.andIn("status", Arrays.asList(InventoryConst.PLAN_STATUS_READY, InventoryConst.PLAN_STATUS_ING)); // 未盘点
            } else if (CommonConst.YES.equals(record.getStatusExt())) {
                criteria.andEqualTo("status", InventoryConst.PLAN_STATUS_DONE); // 已盘点
            }
        }

        // order by
        example.orderBy("createDate").desc();
        List<InventoryPlan> records = mapper.selectByExampleAndRowBounds(example, page);
        // dataFmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    public void dataFmt(InventoryPlan record) {
        // 填充状态
        record.setStatusShow(dictService.cgetNameByValue(InventoryConst.INVENTORY_PLAN_STATUS, record.getStatus()));
        record.setZczs(record.getZcs() + record.getXzs());
        // 盘点类别翻译
        record.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, record.getSkuType()));
        // 填充orgs
        if (StringUtils.isEmpty(record.getOrgIdSet())) {
            record.setOrgs(orgService.getOrgMap().size());
        } else {
            record.idStr2List();
            record.setOrgs(record.getOrgIdList().size());
        }
    }

    @Override
    public List<InventoryPlanCateResp> getInventoryPlanCateList(String id, String orgId, String statusExt) {
        InventoryPlan inventoryPlan = selectById(id);
        if (inventoryPlan == null) {
            throw new RuntimeException("盘点计划不存在");
        }
        List<InventoryPlanCateResp> list = new ArrayList<>();

        if (SkuConst.SKU_TYPE_PTZC.equals(inventoryPlan.getSkuType())) {
            list = mapper.selectCategoryIdList(id, orgId, statusExt);
        } else {
            list = mapper.selectCategory22IdList(id, orgId, statusExt);
        }

        return list;
    }

    @Override
    public List<InventoryPlanOrgResp> getInventoryPlanOrgList(String id, String statusExt) {
        List<InventoryPlanOrgResp> list = mapper.selectOrgIdList(id, statusExt);

        if (list == null) {
            return Collections.emptyList();
        }

        // loop
        for (InventoryPlanOrgResp inventoryPlanOrgResp : list) {
            // 组织名称
            inventoryPlanOrgResp.setName(orgService.cgetOrgNameById(inventoryPlanOrgResp.getId()));
        }

        return list;
    }

    /*
     * @param id:
     * @return void
     * <AUTHOR>
     * @description 标记计划进行中
     * @date 2025/2/23 17:00
     */
    @Override
    public void markDoing(String id) {
        InventoryPlan record = new InventoryPlan();
        record.setId(id);
        record.setStatus(InventoryConst.PLAN_STATUS_ING);
        update(record);
    }

    /*
     * @param null:
     * @return null
     * <AUTHOR>
     * @description 确认盘点计划完成
     * @date 2025/2/23 17:00
     */

    @Transactional(readOnly = false)
    @Override
    public void sure(String id) {
        InventoryPlan inventoryPlan = selectById(id);

        if (inventoryPlan == null) {
            throw new RuntimeException("盘点计划不存在");
        }

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已经完成,无需重复提交");
        }

        //检查任务是否已经全部完成
        int dones = inventoryTaskService.donesByPlan(inventoryPlan);
        if (dones == inventoryPlan.getDevices()) {
            InventoryPlan record = new InventoryPlan();
            record.setId(id);
            record.setStatus(InventoryConst.PLAN_STATUS_DONE);
            record.setDoneDate(new Date());
            // 零用人员待处理
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                record.setDoerId(user.getUsercode());
                record.setDoerName(user.getUsername());
                inventoryPlan.setDoerId(user.getUsercode());
                inventoryPlan.setDoerName(user.getUsername());
            }
            // 更新状态
            update(record);
            // 更新统计情况
            report(id);
            // 对异常的资产进行状态变更
            deviceService.inventoryException(inventoryPlan);
            // 通知
            noticeTaskOk(inventoryPlan);
        } else {
            // 计算位盘点的数量
            int left = inventoryPlan.getDevices() - dones;
            throw new RuntimeException("当前盘点计划还有" + left + "个任务未完成,请先完成任务");
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void forceSure(String id) {
        InventoryPlan inventoryPlan = selectById(id);

        if (inventoryPlan == null) {
            throw new RuntimeException("盘点计划不存在");
        }

        if (InventoryConst.PLAN_STATUS_DONE.equals(inventoryPlan.getStatus())) {
            throw new RuntimeException("盘点计划已经完成,无需重复提交");
        }

        inventoryTaskService.forceDoit(id);
        InventoryPlan record = new InventoryPlan();
        record.setId(id);
        record.setStatus(InventoryConst.PLAN_STATUS_DONE);
        record.setDoneDate(new Date());
        // 零用人员待处理
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setDoerId(user.getUsercode());
            record.setDoerName(user.getUsername());
            inventoryPlan.setDoerId(user.getUsercode());
            inventoryPlan.setDoerName(user.getUsername());
        }
        // 更新状态
        update(record);
        // 更新统计情况
        report(id);
        // 对异常的资产进行状态变更
        deviceService.inventoryException(inventoryPlan);
        // 通知
        noticeTaskOk(inventoryPlan);

    }


    /**
     * @param id:
     * @return void
     * <AUTHOR>
     * @description 更新统计信息
     * @date 2025/2/26 09:15
     */
    @Transactional(readOnly = false)
    @Override
    public void report(String id) {
        // 统计盘点结果
        InventoryTaskStatusNumsResp nums = inventoryTaskService.getInventoryTaskStatusNums(id);
        InventoryPlan record = new InventoryPlan();
        record.setId(id);
        record.setWps(nums.getWps());
        record.setPds(nums.getPds());
        record.setPks(nums.getPks());
        record.setZcs(nums.getZcs());
        record.setShs(nums.getShs());
        record.setGzs(nums.getGzs());
        // xzs yps ycs
        record.setXzs(nums.getXzs());
        record.setYps(nums.getYps());
        record.setYcs(nums.getYcs());
        update(record);
    }


    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 通知盘点任务完成
     * @date 2025/5/23 10:10
     */
    private void noticeTaskOk(InventoryPlan record) {
        String userCode = record.getCreateUser();
        String doerId = record.getDoerId();
        String doerName = record.getDoerName();

        if (StringUtils.isEmpty(userCode) || StringUtils.isEmpty(doerId)) {
            return;
        }

        String content = "您发起的盘点计划：" + record.getSerialNo() + "已经执行完成，请查看盘点结果。";

        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(userCode)  //接收人
                .sender(doerId) //发送人
                .senderName(doerName) //发送人name
                .subject("资产设备-盘点计划完成").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/equipment-asset-management/equipment-inventory").source("盘点管理").build();
        NoticeService.sendAsynNotice(notice);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 通知下达了盘点任务
     * @date 2025/5/22 15:42
     */
    private void noticeTask(InventoryPlan record) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String receiver = record.getEngineerIdSet();

        if (StringUtils.isEmpty(receiver)) {
            return;
        }

        String content = user.getDeptname() + ":" + user.getUsername() + "发起了一个盘点计划，单号：" + record.getSerialNo() + "，请及时跟进处理。";
        NoticeReq notice = NoticeReq.builder().content(content).noticeType("3").receiver(receiver)  //接收人
                .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                .subject("资产设备-盘点任务下达").url("#").wxSendType("1").businessId(record.getId()).toUrl("/ts-web-equipment/equipment-asset-management/equipment-inventory").source("盘点管理").build();

        NoticeService.sendAsynNotice(notice);
    }
}
