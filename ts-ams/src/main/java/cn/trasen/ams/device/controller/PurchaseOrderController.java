package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.device.bean.purchase.*;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.PurchaseConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.PurchaseOrderDetail;
import cn.trasen.ams.device.model.PurchaseOrderDetailMerge;
import cn.trasen.ams.device.model.Sku;
import cn.trasen.ams.device.service.PurchaseOrderDetailMergeService;
import cn.trasen.ams.device.service.PurchaseOrderDetailService;
import cn.trasen.ams.device.service.SkuService;
import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.bean.instance.InstanceListReq;
import cn.trasen.ams.common.bean.instance.PageHeadData;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.model.AMSInstanceDefinitionInfo;
import cn.trasen.ams.common.service.AMSInstanceService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.util.CommonUtil;
import com.alibaba.fastjson.JSON;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.service.PurchaseOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderController
 * @Description TODO
 * @date 2025年6月10日 上午11:09:09
 */
@RestController
@Api(tags = "PurchaseOrderController")
@Validated
public class PurchaseOrderController {

    private transient static final Logger logger = LoggerFactory.getLogger(PurchaseOrderController.class);

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    private AMSInstanceService amsInstanceService;

    @Autowired
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @Autowired
    private PurchaseOrderDetailMergeService purchaseOrderDetailMergeService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private PermissionService permissionService;

    @ApiOperation(value = "获取表单的字段配置", notes = "获取表单的字段配置")
    @PostMapping("/api/device/purchaseOrder/getSonFormFieldConfig/{formId}")
    public PlatformResult filedConfig(@PathVariable("formId") String formId) {
        try {
            return PlatformResult.success(amsInstanceService.getSonFormFieldFromFormId(formId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "获取采购订单页头数据", notes = "获流程列表，获取采购订单页头数据")
    @PostMapping("/api/device/purchaseOrder/pageHeadData")

    public PlatformResult getPageHeadData() {


        try {
            // 刷新状态
            purchaseOrderService.updatePurchaseOrderArrivedStatus();

            List<String> withStatusIns = new ArrayList<>();
            List<String> withStatusOrd = new ArrayList<>();

            withStatusIns.add(PurchaseConst.PURCHASE_PAGE_STATUS_APPLY);
            withStatusOrd.add(PurchaseConst.PURCHASE_PAGE_STATUS_ING);
            withStatusOrd.add(PurchaseConst.PURCHASE_PAGE_STATUS_FINISH);

            List<AMSInstanceDefinitionInfo> definitionList = amsInstanceService.getPurchaseTypeList();


            // 判断当前用户有处理哪些流程的权限
            PermissionResp permissionResp = permissionService.cgetPermission(PermissionConst.业务类型_采购订单);
            switch (permissionResp.getType()) {
                case PermissionConst.TYPE_ALL:
                    // 管理员为所欲为
                    break;
                case PermissionConst.TYPE_SKU_TYPE:
                    // 查询采购字典配置，对definitionId进行过滤
                    // 循环definitionList 对definitionId进行过滤
                    List<String> skuList = permissionResp.getSkuTypeList();
                    Iterator<AMSInstanceDefinitionInfo> iterator = definitionList.iterator();
                    while (iterator.hasNext()) {
                        AMSInstanceDefinitionInfo definition = iterator.next();
                        PurchaseOrderInstanceConfig config = purchaseOrderService.getInstanceConfig(definition.getWfDefinitionId());
                        if (!skuList.contains(config.getSkuType())) {
                            iterator.remove();
                        }
                    }
                    break;
                default:
                    throw new RuntimeException("当前登录用户权限不足，无法使用采购订单");
            }


            Map<String, Map<String, Integer>> numsDefinitionMap = new HashMap<>();
            Map<String, Integer> numsStatusMap = new HashMap<>();

            // 查询数量


            for (AMSInstanceDefinitionInfo definition : definitionList) {

                //Map<String, Integer> numsStatusMapItem = amsInstanceService.getSonFormsWithStatus(definition.getWfDefinitionId(), withStatusIns);
                Map<String, Integer> numsStatusMapItem = new HashMap<>();

                int num = purchaseOrderService.getPurchaseRows(definition.getWfDefinitionId(), PurchaseConst.PURCHASE_PAGE_STATUS_APPLY, 0);
                numsStatusMapItem.put(PurchaseConst.PURCHASE_PAGE_STATUS_APPLY, num);

                numsDefinitionMap.put(definition.getWfDefinitionId(), numsStatusMapItem);
                // 累加每个状态的数量
                for (Map.Entry<String, Integer> entry : numsStatusMapItem.entrySet()) {
                    String k = entry.getKey();
                    Integer v = entry.getValue();
                    if (numsStatusMap.containsKey(k)) {
                        numsStatusMap.put(k, numsStatusMap.get(k) + v);
                    } else {
                        numsStatusMap.put(k, v);
                    }
                }
            }

            for (AMSInstanceDefinitionInfo definition : definitionList) {

                Map<String, Integer> numsStatusMapItem = purchaseOrderService.getNumsWithStatus(definition.getWfDefinitionId(), withStatusOrd);

                // 合并numsDefinitionMap中的状态数量，而不是直接覆盖
                Map<String, Integer> existingStatusMap = numsDefinitionMap.get(definition.getWfDefinitionId());
                if (existingStatusMap == null) {
                    existingStatusMap = new HashMap<>();
                }
                for (Map.Entry<String, Integer> entry : numsStatusMapItem.entrySet()) {
                    String k = entry.getKey();
                    Integer v = entry.getValue();
                    // 合并到definition维度
                    existingStatusMap.put(k, existingStatusMap.getOrDefault(k, 0) + v);
                    // 合并到全局状态维度
                    numsStatusMap.put(k, numsStatusMap.getOrDefault(k, 0) + v);
                }
                numsDefinitionMap.put(definition.getWfDefinitionId(), existingStatusMap);
            }

            PageHeadData resp = new PageHeadData();

            resp.setDefinitionList(definitionList);
            resp.setNumsStatusMap(numsStatusMap);
            resp.setNumsDefinitionMap(numsDefinitionMap);

            // 超过3个月未采购的申请
            // 要根据每一个definition 查对应已完结的数量


            Map<String, Integer> extMap = new HashMap<>();
            Integer C3GYWCGSL = 0;
            for (AMSInstanceDefinitionInfo definition : definitionList) {
                String definitionId = definition.getWfDefinitionId();
                String status = PurchaseConst.PURCHASE_PAGE_STATUS_APPLY;
                C3GYWCGSL += purchaseOrderService.getPurchaseRows(definitionId, status, 30);
            }
            extMap.put("C3GYWCGSL", C3GYWCGSL);

            // 超过3个月未转合同的订单
            Integer C3GYWDJHTDD = 0;
            for (AMSInstanceDefinitionInfo definition : definitionList) {
                String definitionId = definition.getWfDefinitionId();
                C3GYWCGSL += purchaseOrderService.getNoContract3ML(definitionId);
            }
            extMap.put("C3GYWDJHTDD", C3GYWCGSL);

            resp.setExtMap(extMap);

            return PlatformResult.success(resp);
        } catch (RuntimeException e) {
            return PlatformResult.failure(e.getMessage());
        }

    }

    @ApiOperation(value = "采购申请列表", notes = "采购申请列表")
    @PostMapping("/api/device/purchaseApply/list/{definitionId}/{status}")
    public DataSet<Map<String, Object>> selectInstanceList(Page page, @RequestParam Map<String, String> query, @RequestBody PurchaseApplyListReq body, @PathVariable("definitionId") String definitionId, @PathVariable("status") String status) {

        try {
            InstanceListReq req = new InstanceListReq();
            req.setStatus(status);
            req.setDefinitionId(definitionId);
            req.setQuery(query);
            req.setParams(body.getParams());
            req.setJoin(" LEFT JOIN (\n" + "    SELECT sum(COALESCE(nums, 0)) as purchase_nums, instance_id \n" + "    FROM d_purchase_order_detail \n" + "    WHERE is_deleted = 'N'\n" + "    GROUP BY instance_id  \n" + ") as dod \n" + "    ON dod.instance_id = zdyt.ID ");
            PurchaseOrderInstanceConfig config = purchaseOrderService.getInstanceConfig(definitionId);

            String defWhere = " and (dod.purchase_nums < zdyt.`" + config.getNumsField() + "` or dod.instance_id is null ) ";
            if (!CollectionUtils.isEmpty(body.getIgnoreIds())) {
                // 如果有忽略的申请单ID，则添加到查询条件中
                StringBuilder ignoreIds = new StringBuilder();
                for (String id : body.getIgnoreIds()) {
                    if (ignoreIds.length() > 0) {
                        ignoreIds.append(",");
                    }
                    ignoreIds.append("'").append(id).append("'");
                }
                defWhere += " AND zdyt.ID NOT IN (" + ignoreIds + ")";
            }
            // selectedIds 不为空，则添加到查询条件中
            if (!CollectionUtils.isEmpty(body.getSelectedIds())) {
                StringBuilder selectedIds = new StringBuilder();
                for (String id : body.getSelectedIds()) {
                    if (selectedIds.length() > 0) {
                        selectedIds.append(",");
                    }
                    selectedIds.append("'").append(id).append("'");
                }
                defWhere += " AND zdyt.ID IN (" + selectedIds + ")";
            }

            req.setDefWhere(defWhere);
            req.setDefField(",CAST(zdyt.`" + config.getNumsField() + "` - COALESCE(dod.purchase_nums, 0) AS SIGNED) AS wait_purchase_nums" + ",CAST(COALESCE(dod.purchase_nums, 0) AS SIGNED) AS purchase_nums");
            DataSet<Map<String, Object>> ret = amsInstanceService.getInstanceSonFromList(page, req);
            // 如果需要匹配，则进行匹配逻辑
            if (CommonConst.YES.equals(body.getWithMatch())) {
                List<String> fieldList = config.getShowfield();
                List<Map<String, Object>> list = ret.getRows();
                // 循环list 并进行匹配
                StringBuilder matchStr = new StringBuilder();
                for (Map<String, Object> row : list) {
                    // 取前两个字段
                    matchStr.setLength(0); // 清空之前的内容
                    for (int i = 0; i < Math.min(2, fieldList.size()); i++) {
                        String field = fieldList.get(i);
                        Object value = row.get(field);
                        if (value != null) {
                            matchStr.append(value.toString()).append(" ");
                        }
                    }
                    // 进行匹配

                    List<Sku> skuList = skuService.searchSkuList(config.getSkuType(), matchStr.toString(), null);

                    List<PurchaseOrderMatchItem> matchItems = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(skuList)) {
                        List<String> targetFields = new ArrayList<>();
                        for (Sku sku : skuList) {
                            PurchaseOrderMatchItem matchItem = new PurchaseOrderMatchItem();
                            targetFields.add(sku.getName());
                            targetFields.add(sku.getModel());
                            targetFields.add(sku.getBrandName());
                            targetFields.add(sku.getManufacturerName());
                            matchItem.setSkuId(sku.getId());
                            // # 分割 targetFields 转字符串 去掉空值
                            targetFields.removeIf(String::isEmpty);
                            matchItem.setSkuDesc(String.join("#", targetFields));
                            matchItems.add(matchItem);
                        }
                    }
                    row.put("match", matchItems);

                }
            }
            return ret;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new DataSet();
    }


    @ApiOperation(value = "采购订单列表", notes = "采购订单列表")
    @PostMapping("/api/device/purchaseOrder/list/{definitionId}/{status}")
    public DataSet selectPurchaseOrderList(Page page, @RequestBody PurchaseOrder record, @PathVariable("definitionId") String definitionId, @PathVariable("status") String status) {

        record.setDefinitionId(definitionId);
        record.setStatus(status);

        return purchaseOrderService.getDataSetList(page, record);
    }


    @ApiOperation(value = "创建采购订单", notes = "创建采购订单")
    @PostMapping("/api/device/purchaseOrder/save")
    // 创建采购订单
    public PlatformResult save(@Validated @RequestBody PurchaseOrderInsertReq record) {
        try {
            purchaseOrderService.insert(record);
            return PlatformResult.success("创建采购订单成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "编辑采购订单", notes = "编辑采购订单")
    @PostMapping("/api/device/purchaseOrder/update")
    // 编辑采购订单
    public PlatformResult update(@Validated @RequestBody PurchaseOrderInsertReq record) {
        try {

            purchaseOrderService.edit(record);
            return PlatformResult.success("编辑采购订单成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    // 采购订单撤销

    @ApiOperation(value = "撤销采购订单", notes = "撤销采购订单")
    @PostMapping("/api/device/purchaseOrder/cancel")
    public PlatformResult cancel(@RequestBody List<String> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return PlatformResult.failure("采购订单ID列表不能为空");
            }
            purchaseOrderService.batchCancel(ids);
            return PlatformResult.success("采购订单撤销成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取采购订单信息", notes = "获取采购订单信息")
    @GetMapping("/api/device/purchaseOrder/detail/{id}")
    public PlatformResult detail(@PathVariable String id) {
        try {
            PurchaseOrder purchaseOrder = purchaseOrderService.selectById(id);
            List<PurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailService.selectByPurchaseOrderId(id);
            List<PurchaseOrderDetailMerge> purchaseOrderDetailMerges = purchaseOrderDetailMergeService.selectByPurchaseOrderId(id);

            PurchaseOrderInsertReq resp = new PurchaseOrderInsertReq();
            resp.setPurchaseOrder(purchaseOrder);
            resp.setPurchaseOrderDetailList(purchaseOrderDetails);
            // 这里先简单处理一下 把原始的采购申请查出来，如果优值则去值，如果没有则为0
            // 通过循环 purchaseOrderDetails instance_id

            List<String> instanceIdList = new ArrayList<>();
            for (PurchaseOrderDetail detail : purchaseOrderDetails) {
                String instanceId = detail.getInstanceId();
                if (instanceId == null || instanceIdList.contains(instanceId)) {
                    continue;
                }
                instanceIdList.add(instanceId);
            }

            if (!instanceIdList.isEmpty()) {
                Page page = new Page();
                page.setPageNo(1);
                page.setPageSize(10000000); // 尽可能大 ， TODO 这种属于危险，但是不可能发生的事情，不要害怕

                Map<String, String> query = new HashMap<>();
                PurchaseApplyListReq purchaseApplyListReq = new PurchaseApplyListReq();
                purchaseApplyListReq.setSelectedIds(instanceIdList);
                DataSet dataSet = selectInstanceList(page, query, purchaseApplyListReq, purchaseOrder.getDefinitionId(), PurchaseConst.PURCHASE_PAGE_STATUS_APPLY);
                // 组成一个ID 和 wait_purchase_nums 的map
                Map<String, Integer> instanceIdMap = new HashMap<>();

                if (dataSet != null && dataSet.getRows() != null) {
                    for (Object obj : dataSet.getRows()) {
                        if (obj instanceof Map) {
                            Map row = (Map) obj;
                            String instanceId = String.valueOf(row.get("ID"));
                            Object waitNumObj = row.get("wait_purchase_nums");
                            Integer waitNum = waitNumObj == null ? 0 : Integer.parseInt(waitNumObj.toString());
                            instanceIdMap.put(instanceId, waitNum);
                        }
                    }
                }

                // 更新 purchaseOrderDetails
                for (PurchaseOrderDetail detail : purchaseOrderDetails) {
                    String instanceId = detail.getInstanceId();
                    if (instanceIdMap.containsKey(instanceId)) {
                        detail.setWait_purchase_nums(instanceIdMap.get(instanceId));
                    } else {
                        detail.setWait_purchase_nums(0);
                    }
                }
            }


            resp.setPurchaseOrderDetailMergeList(purchaseOrderDetailMerges);

            return PlatformResult.success(resp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量获取采购订单信息", notes = "根据ID数组批量获取采购订单信息，返回Map结构")
    @PostMapping("/api/device/purchaseOrder/batchDetail")
    public PlatformResult batchDetail(@RequestBody List<String> ids) {
        try {
            Map<String, PurchaseOrderInsertReq> resultMap = new HashMap<>();

            for (String id : ids) {
                PurchaseOrder purchaseOrder = purchaseOrderService.selectById(id);
                List<PurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailService.selectByPurchaseOrderId(id);
                List<PurchaseOrderDetailMerge> purchaseOrderDetailMerges = purchaseOrderDetailMergeService.selectByPurchaseOrderId(id);

                PurchaseOrderInsertReq resp = new PurchaseOrderInsertReq();
                resp.setPurchaseOrder(purchaseOrder);
                resp.setPurchaseOrderDetailList(purchaseOrderDetails);
                resp.setPurchaseOrderDetailMergeList(purchaseOrderDetailMerges);

                resultMap.put(id, resp);
            }

            return PlatformResult.success(resultMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "采购申请转sku", notes = "采购申请转sku")
    @PostMapping("/api/device/purchaseApply/2sku")
    public PlatformResult apply2sku(@RequestBody List<PurchaseApply2SkuReq> list) {

        try {
            if (list == null || list.isEmpty()) {
                return PlatformResult.failure("请求列表不能为空");
            }

            Map<String, List<Sku>> res = new HashMap<>();

            // 遍历请求列表
            for (PurchaseApply2SkuReq req : list) {
                // 查询 SKU 列表
                List<Sku> skuList = skuService.searchSkuList(req.getSkuType(), req.getKeyword(), req.getSelectedId());
                res.put(req.getId(), skuList);
            }
            return PlatformResult.success(res);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }

    }

    @ApiOperation(value = "批量完成采购订单", notes = "批量完成采购订单")
    @PostMapping("/api/device/purchaseOrder/batchComplete")
    public PlatformResult batchComplete(@RequestBody List<String> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return PlatformResult.failure("采购订单ID列表不能为空");
            }
            Integer completedCount = purchaseOrderService.batchComplete(ids);
            return PlatformResult.success("批量完成采购订单成功，共完成 " + completedCount + " 个订单");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "未到货的采购订单列表接口", notes = "未到货的采购订单列表接口")
    @PostMapping("/api/device/purchaseOrder/waitDelivery/{skuType}")

    public PlatformResult waitDelivery(@PathVariable String skuType) {
        return PlatformResult.success(purchaseOrderService.waitDelivery(skuType));
    }


    @ApiOperation(value = "未到货的采购订单详情接口", notes = "未到货的采购订单详情接口")
    @PostMapping("/api/device/purchaseOrder/waitDelivery/detail/{id}")

    public PlatformResult waitDeliveryDetail(@PathVariable String id) {
        return PlatformResult.success(purchaseOrderService.waitDeliveryDetail(id));
    }


    @ApiOperation(value = "采购订单批量导出")
    @RequestMapping(value = "/api/device/purchaseOrder/export")
    public ResponseEntity<byte[]> export(@RequestBody List<String> idList) throws Exception {

        if (CollectionUtils.isEmpty(idList)) {
            throw new RuntimeException("请至少选择一个采购订单");
        }
        // excel 装载器
        List<Map<String, Object>> workbookList = new ArrayList<>();

        // 遍历每个采购订单ID，处理动态列配置和数据填充
        for (String id : idList) {
            // 获取采购订单基本信息
            PurchaseOrder purchaseOrder = purchaseOrderService.selectById(id);
            // 获取采购订单明细合并数据
            List<PurchaseOrderDetailMerge> purchaseOrderDetailMerges = purchaseOrderDetailMergeService.selectByPurchaseOrderId(id);

            // 初始化动态列名称，默认为"动态列1-4"
            String itemName1 = "动态列1";
            String itemName2 = "动态列2";
            String itemName3 = "动态列3";
            String itemName4 = "动态列4";

            // 根据是否匹配字典模式来设置列名
            if (CommonConst.YES.equals(purchaseOrder.getIsMapperDict())) {
                // 字典匹配模式：使用固定的列名
                itemName1 = SkuConst.SKU_TYPE_YLSB.equals(purchaseOrder.getSkuType()) ? "设备名称" : "资产名称";
                itemName2 = "规格型号";
                itemName3 = "品牌";
                itemName4 = "生产厂家";
            } else {
                // 非字典匹配模式：从自定义配置JSON中获取列名
                String definitionJson = purchaseOrder.getDefinitionJson();
                if (!StringUtils.isEmpty(definitionJson)) {
                    try {
                        // 解析自定义列配置
                        List<PurchaseOrderColumnConfig> configList = JSON.parseArray(definitionJson, PurchaseOrderColumnConfig.class);
                        if (configList != null && !configList.isEmpty()) {
                            // 根据配置数组顺序设置列名
                            if (configList.size() >= 1) {
                                itemName1 = configList.get(0).getLabel();
                            }
                            if (configList.size() >= 2) {
                                itemName2 = configList.get(1).getLabel();
                            }
                            if (configList.size() >= 3) {
                                itemName3 = configList.get(2).getLabel();
                            }
                            if (configList.size() >= 4) {
                                itemName4 = configList.get(3).getLabel();
                            }
                        }
                    } catch (Exception e) {
                        // JSON解析失败时保持默认列名，记录错误日志
                        logger.warn("解析采购订单{}的列配置JSON失败: {}", id, e.getMessage());
                    }
                }
            }

            Integer totalNums = 0;
            BigDecimal totalPrice = BigDecimal.ZERO;
            // 处理每个明细合并记录的数据填充
            List<PurchaseOrderExportRow> exportRows = new ArrayList<>();
            Integer index = 1;


            for (PurchaseOrderDetailMerge detailMerge : purchaseOrderDetailMerges) {
                PurchaseOrderExportRow exportRow = new PurchaseOrderExportRow();
                exportRow.setNo(index);
                if (CommonConst.YES.equals(purchaseOrder.getIsMapperDict())) {
                    // 字典匹配模式：从skuDesc字段中按#分割获取值
                    String skuDesc = detailMerge.getSkuDesc();
                    if (StringUtils.isEmpty(skuDesc)) {
                        continue;
                    }
                    // 按#分割skuDesc字符串
                    String[] skuDescArray = skuDesc.split("#");

                    // 根据数组长度设置对应的动态列值
                    if (skuDescArray.length >= 1) {
                        exportRow.setItemValue1(skuDescArray[0]);
                    }
                    if (skuDescArray.length >= 2) {
                        exportRow.setItemValue2(skuDescArray[1]);
                    }
                    if (skuDescArray.length >= 3) {
                        exportRow.setItemValue3(skuDescArray[2]);
                    }
                    if (skuDescArray.length >= 4) {
                        exportRow.setItemValue4(skuDescArray[3]);
                    }
                } else {
                    // 非字典匹配模式：从instance_field_kv_json中根据配置获取值

                    String instanceFieldKvJson = detailMerge.getInstanceFieldKvJson();
                    String definitionJson = purchaseOrder.getDefinitionJson();
                    if (StringUtils.isEmpty(instanceFieldKvJson) || StringUtils.isEmpty(definitionJson)) {
                        continue;
                    }

                    try {
                        // 解析实例字段键值对JSON
                        Map<String, String> instanceFieldKvMap = JSON.parseObject(instanceFieldKvJson, Map.class);
                        // 解析列配置JSON
                        List<PurchaseOrderColumnConfig> configList = JSON.parseArray(definitionJson, PurchaseOrderColumnConfig.class);

                        if (instanceFieldKvMap == null || configList == null || configList.isEmpty()) {
                            continue;
                        }
                        // 根据配置的prop值从instanceFieldKvMap中获取对应的值
                        if (configList.size() >= 1) {
                            exportRow.setItemValue1(instanceFieldKvMap.get(configList.get(0).getProp()));
                        }
                        if (configList.size() >= 2) {
                            exportRow.setItemValue2(instanceFieldKvMap.get(configList.get(1).getProp()));
                        }
                        if (configList.size() >= 3) {
                            exportRow.setItemValue3(instanceFieldKvMap.get(configList.get(2).getProp()));
                        }
                        if (configList.size() >= 4) {
                            exportRow.setItemValue4(instanceFieldKvMap.get(configList.get(3).getProp()));
                        }
                    } catch (Exception e) {
                        // JSON解析失败时记录错误日志
                        logger.warn("解析明细合并记录{}的实例字段JSON失败: {}", detailMerge.getId(), e.getMessage());
                    }
                }

                exportRow.setPrice(detailMerge.getPrice());
                exportRow.setNums(detailMerge.getNums());
                exportRow.setTotal(detailMerge.getTotal());
                index++;
                // 计算总数量和总价格
                totalNums += detailMerge.getNums() != null ? detailMerge.getNums() : 0;
                totalPrice = totalPrice.add(detailMerge.getPrice() != null ? detailMerge.getPrice().multiply(BigDecimal.valueOf(detailMerge.getNums())) : BigDecimal.ZERO);
                exportRows.add(exportRow);
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/purchaseOrderExportTpl.xlsx"));
            params.setColForEach(true);

            // 2. 构建数据源，"list" 是模板中的关键字
            Map<String, Object> map = new HashMap<>();
            map.put("orderType", purchaseOrder.getOrderType());
            map.put("name", purchaseOrder.getName());
            map.put("supplierName", purchaseOrder.getSupplierName());
            map.put("supplierContactor", purchaseOrder.getSupplierContactor());
            map.put("supplierMobile", purchaseOrder.getSupplierMobile());
            map.put("itemName1", itemName1);
            map.put("itemName2", itemName2);
            map.put("itemName3", itemName3);
            map.put("itemName4", itemName4);
            map.put("totalNums", totalNums);
            map.put("totalPrice", totalPrice);
            map.put("rOrgName", purchaseOrder.getROrgName());
            map.put("rDeptName", purchaseOrder.getRDeptName());
            map.put("rCreateUserName", purchaseOrder.getRCreateUserName());
            map.put("createDate", CommonUtil.dateFormate(purchaseOrder.getCreateDate(), "yyyy-MM-dd"));

            map.put("list", exportRows);

            // 3. 生成 Workbook
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            // 4. 构建文件名，例如 文件1.xlsx、文件2.xlsx...
            String fileName = purchaseOrder.getName() + "-【" + CommonUtil.dateFormate(purchaseOrder.getCreateDate(), "yyyy-MM-dd") + "】" + ".xlsx";

            // 5. 存入列表中待后续处理
            Map<String, Object> item = new HashMap<>();
            item.put("workbook", workbook);
            item.put("fileName", fileName);
            workbookList.add(item);

        }

        // 6. 将所有Excel文件打包成ZIP
        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(zipOutputStream)) {
            for (Map<String, Object> item : workbookList) {
                Workbook workbook = (Workbook) item.get("workbook");
                String fileName = (String) item.get("fileName");

                // 将Workbook转换为字节数组
                ByteArrayOutputStream workbookStream = new ByteArrayOutputStream();
                workbook.write(workbookStream);
                workbookStream.close();

                // 创建ZIP条目
                ZipEntry zipEntry = new ZipEntry(fileName);
                zos.putNextEntry(zipEntry);

                // 写入Excel文件内容
                zos.write(workbookStream.toByteArray());
                zos.closeEntry();

                // 关闭Workbook
                workbook.close();
            }
        }

        // 7. 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "采购订单导出.zip");

        return new ResponseEntity<>(zipOutputStream.toByteArray(), headers, HttpStatus.OK);
    }


}
