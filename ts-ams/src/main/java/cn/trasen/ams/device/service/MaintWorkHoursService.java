package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintWorkHours;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintWorkHoursService
 * @Description TODO
 * @date 2024年12月17日 下午4:33:52
 */
public interface MaintWorkHoursService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月17日 下午4:33:52
     * <AUTHOR>
     */
    Integer save(MaintWorkHours record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月17日 下午4:33:52
     * <AUTHOR>
     */
    Integer update(MaintWorkHours record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月17日 下午4:33:52
     * <AUTHOR>
     */
    Integer deleteById(String id);

    Integer deleteByTaskId(String taskId);


    /**
     * @return MaintWorkHours
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月17日 下午4:33:52
     * <AUTHOR>
     */
    MaintWorkHours selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MaintWorkHours>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月17日 下午4:33:52
     * <AUTHOR>
     */
    DataSet<MaintWorkHours> getDataSetList(Page page, MaintWorkHours record);


    List<MaintWorkHours> getList(MaintWorkHours record);
}
