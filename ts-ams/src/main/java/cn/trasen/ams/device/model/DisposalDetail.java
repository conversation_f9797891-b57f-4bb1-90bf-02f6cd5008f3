package cn.trasen.ams.device.model;

import cn.trasen.ams.device.constant.DisposalConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;

@Table(name = "d_disposal_detail")
@Setter
@Getter
public class DisposalDetail {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 主表ID
     */
    @Column(name = "disposal_id")
    @ApiModelProperty(value = "主表ID")
    private String disposalId;

    /**
     * 资产ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "资产ID")
    private String deviceId;

    /**
     * 处置类型
     */
    @DictExistValid(code = DisposalConst.DISPOSAL_TYPE, message = "处置类型不存在")
    @ApiModelProperty(value = "处置类型")
    private String type;


    @Transient
    @ApiModelProperty(value = "处置类型")
    private String typeShow;

    /**
     * 处置费用
     */
    @ApiModelProperty(value = "处置费用")
    private BigDecimal cost;

    /**
     * 处置收入
     */
    @ApiModelProperty(value = "处置收入")
    private BigDecimal revenue;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @Transient
    @ApiModelProperty(value = "设备名称")
    private String name;

    /**
     * 规格型号
     */
    @Transient
    @Excel(name = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String model;

    @Transient
    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @Transient
    @ApiModelProperty(value = "国有资产编码")
    private String gzwbh;

    @Transient
    @ApiModelProperty(value = "所属科室ID")
    private String belongToOrgId;

    @Transient
    @ApiModelProperty(value = "所属科室名称")
    private String belongToOrgName;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Transient
    @ApiModelProperty(value = "启用时间")
    private Date useDate;

    @Transient
    @ApiModelProperty(value = "使用寿命")
    private Integer lifespanVal;

    @Transient
    @ApiModelProperty(value = "寿命单位")
    private String lifespanUnit;

    @Transient
    @ApiModelProperty(value = "寿命单位翻译")
    private String lifespanUnitShow;

    @Transient
    @ApiModelProperty(value = "在役时长")
    private String zysc;


    @Transient
    @ApiModelProperty(value = "资产原位置")
    private String loc;

    @Transient
    @ApiModelProperty(value = "资产原值")
    BigDecimal originalValue;

    @Transient
    @ApiModelProperty(value = "帐面净值")
    BigDecimal bookVal;

    @Transient
    @ApiModelProperty(value = "累计折旧")
    BigDecimal totalDecVal;


}