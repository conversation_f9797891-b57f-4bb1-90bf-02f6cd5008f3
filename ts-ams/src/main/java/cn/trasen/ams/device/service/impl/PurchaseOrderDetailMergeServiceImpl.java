package cn.trasen.ams.device.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.Sku;
import cn.trasen.ams.device.service.PurchaseOrderService;
import cn.trasen.ams.device.service.SkuService;
import cn.trasen.ams.common.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.PurchaseOrderDetailMergeMapper;
import cn.trasen.ams.device.model.PurchaseOrderDetailMerge;
import cn.trasen.ams.device.service.PurchaseOrderDetailMergeService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderDetailMergeServiceImpl
 * @Description TODO
 * @date 2025年6月18日 上午9:39:56
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseOrderDetailMergeServiceImpl implements PurchaseOrderDetailMergeService {

    @Autowired
    private PurchaseOrderDetailMergeMapper mapper;

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SkuService skuService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseOrderDetailMerge record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseOrderDetailMerge record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseOrderDetailMerge record = new PurchaseOrderDetailMerge();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PurchaseOrderDetailMerge selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<PurchaseOrderDetailMerge> getDataSetList(Page page, PurchaseOrderDetailMerge record) {
        Example example = new Example(PurchaseOrderDetailMerge.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<PurchaseOrderDetailMerge> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchInsert(List<PurchaseOrderDetailMerge> list) {
        mapper.batchInsert(list);
    }

    @Override
    public void deleteByPurchaseOrderId(String purchaseOrderId) {
        Example example = new Example(PurchaseOrderDetailMerge.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("purchaseOrderId", purchaseOrderId);
        PurchaseOrderDetailMerge record = new PurchaseOrderDetailMerge();
        record.setIsDeleted("Y");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<PurchaseOrderDetailMerge> selectByPurchaseOrderId(String purchaseOrderId) {
        Example example = new Example(PurchaseOrderDetailMerge.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("purchaseOrderId", purchaseOrderId);
        List<PurchaseOrderDetailMerge> records = mapper.selectByExample(example);

        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        //records.forEach(this::dataFmt);
        return records;
    }

    private void dataFmt(PurchaseOrderDetailMerge record) {

        try {
            PurchaseOrder purchaseOrder = (PurchaseOrder) redisService.fetch("purchaseOrder:" + record.getPurchaseOrderId(), () -> purchaseOrderService.selectById(record.getPurchaseOrderId()), 300);

            if (CommonConst.NO.equals(purchaseOrder.getIsMapperDict())) {
                return;
            }

            Sku sku = (Sku) redisService.fetch("sku:" + record.getSkuId(), () -> skuService.selectOneById(record.getSkuId()), 300);

            record.setSku(sku);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
