package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.comm.CommApproveReq;
import cn.trasen.ams.device.bean.disposal.DisposalInsertReq;
import cn.trasen.ams.device.bean.transfer.TransferInsertReq;
import cn.trasen.ams.device.model.Transfer;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Disposal;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DisposalService
 * @Description TODO
 * @date 2025年4月27日 下午4:48:41
 */
public interface DisposalService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    Integer save(Disposal record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    Integer update(Disposal record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Disposal
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    Disposal selectById(String id);

    List<Disposal> getList(Disposal disposal);
    /**
     * @param page
     * @param record
     * @return DataSet<Disposal>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    DataSet<Disposal> getDataSetList(Page page, Disposal record);

    void insert(DisposalInsertReq disposalInsertReq);

    void edit(DisposalInsertReq disposalInsertReq);

    void approve(CommApproveReq commApproveReq);

    void dataFmt(Disposal disposal);


}
