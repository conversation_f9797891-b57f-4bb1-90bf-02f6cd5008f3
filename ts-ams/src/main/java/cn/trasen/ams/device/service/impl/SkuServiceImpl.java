package cn.trasen.ams.device.service.impl;

import java.util.*;
import java.util.function.Consumer;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.service.BrandService;
import cn.trasen.ams.common.service.CategoryService;
import cn.trasen.ams.common.service.ManufacturerService;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.device.service.*;
import cn.trasen.ams.common.bean.ImportErrRow;
import cn.trasen.ams.common.bean.ImportResp;
import cn.trasen.ams.common.bean.PermissionResp;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.SkuMapper;
import cn.trasen.ams.device.model.Sku;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SkuServiceImpl
 * @Description 设备字典业务实现
 * @date 2024年9月9日 下午3:43:43
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SkuServiceImpl implements SkuService {

    @Autowired
    private SkuMapper mapper;

    @Autowired
    private BrandService brandService;

    @Autowired
    private ManufacturerService manufacturerService;

    @Autowired
    private Category22Service category22Service;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DictService dictService;

    @Autowired
    private SerialNoGenService serialNoGenService;
    @Autowired
    private PermissionService permissionService;

    @Transactional(readOnly = false)
    @Override
    public String save(Sku record) {

        checkRepeat(record, "add");

        String skuId = IdGeneraterUtils.nextId();
        record.setId(skuId);
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        autoFillColumn(record);

        mapper.insertSelective(record);

        // 生成设备字典编码
        autoFillCode();

        return skuId;
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Sku record) {

        Assert.hasText(record.getId(), "ID不能为空.");

        // 检测重复性
        checkRepeat(record, "update");
        // 检测是否使用
        boolean hasUse = deviceService.hasUse("sku", record.getId());
        if (hasUse) {
            Sku sku = selectById(record.getId());
            if (sku.getSkuType() != null && !sku.getSkuType().equals(record.getSkuType())) {
                throw new RuntimeException("字典已被使用，无法修改资产类别");
            }
        }

        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        autoFillColumn(record);
        int res = mapper.updateByPrimaryKey(record);
        // 生成设备字典编码
        autoFillCode();

        return res;
    }

    private void autoFillColumn(Sku record) {
        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        boolean hasUse = deviceService.hasUse("sku", id);
        if (hasUse) {
            throw new RuntimeException("该设备字典已被使用，无法删除.");
        }


        Sku record = new Sku();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Sku selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public Sku selectOneById(String id) {
        return mapper.selectOnById(id);
    }

    @Override
    public DataSet<Sku> getDataSetList(Page page, Sku record) {
        // 把 selectedId 转换成 selectedIds
        if (!StringUtil.isEmpty(record.getSelectedId())) {
            record.setSelectedIds(Arrays.asList(CommonUtil.escapeSql(record.getSelectedId()).split(",")));
        }
        // 把 ignoreId 转换成 ignoreIds
        if (!StringUtil.isEmpty(record.getIgnoreId())) {
            record.setIgnoreIds(Arrays.asList(CommonUtil.escapeSql(record.getIgnoreId()).split(",")));
        }
        List<Sku> records = mapper.getList(page, record);
        // dataFmt
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    public void dataFmt(Sku record) {
        record.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, record.getSkuType()));
    }

    /**
     * @param record:
     * @return List<Sku>
     * <AUTHOR>
     * @description 查询列表（不分页）
     * @date 2024/9/11 08:55
     */


    @Override
    public List<Sku> getList(Sku record) {
        List<Sku> records = mapper.getListNoPage(record);
        return records;
    }

    /**
     * @param list:
     * @return ImportResp
     * <AUTHOR>
     * @description 导入
     * @date 2024/9/11 08:55
     */

    @Transactional(readOnly = false)
    @Override
    public ImportResp importByExcel(List<Sku> list) {
        ImportResp resp = new ImportResp();

        int succs = 0;
        int errs = 0;
        List<ImportErrRow> errRows = new ArrayList<>();


        if (list == null || list.isEmpty()) {
            resp.setErrs(errs);
            resp.setSuccs(succs);
            return resp;
        }

        Set<String> existingSkus = fetchAllSkuName();
        Set<String> selfSet = new HashSet<>();
        Map<String, String> brandMap = brandService.fetchAllBrandName2Map(CommonConst.SYS_TYPE_ZCSB);
        Map<String, String> manufacturerMap = manufacturerService.fetchAllManufacturerName2Map(CommonConst.SYS_TYPE_ZCSB);
        Map<String, String> category22Map = category22Service.fetchAllCategory22Name2Map();
        Map<String, String> categoryMap = categoryService.fetchAllCategoryName2Map(CommonConst.SYS_TYPE_ZCSB);
        // 记录category分类
        log.error("这是取出来的分类信息" + categoryMap.toString());

        Map<String, String> skuTypeMap = dictService.cgetSV(SkuConst.SKU_TYPE);
        Map<String, String> calibrationTypeMap = dictService.cgetSV(SkuConst.CALIBRATION_TYPE);
        Map<String, String> yesOrNoMap = dictService.cgetSV(CommonConst.YES_OR_NO);
        Map<String, String> nYRMap = dictService.cgetSV(SkuConst.YMD);


        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        List<Sku> cleanList = new ArrayList<>();

        for (int index = 0; index < list.size(); index++) {
            Sku record = list.get(index);

            // 处理资产类别
            if (!mapRecordField(record.getSkuTypeShow(), skuTypeMap, "资产类别", index, errRows, record::setSkuType)) {
                errs++;
                continue;
            }


            if (!mapRecordField(record.getBrandName(), brandMap, "品牌名称", index, errRows, record::setBrandId)) {
                errs++;
                continue;
            }

            if (!mapRecordField(record.getManufacturerName(), manufacturerMap, "厂家名称", index, errRows, record::setManufacturerId)) {
                errs++;
                continue;
            }

            // 只有医疗设备才必须要填写医疗器械分类
            if (SkuConst.SKU_TYPE_YLSB.equals(record.getSkuType()) && !StringUtil.isEmpty(record.getCategory22Name())) {
                if (!mapRecordField(record.getCategory22Name(), category22Map, "医疗器械分类", index, errRows, record::setCategory22Id)) {
                    errs++;
                    continue;
                }
            }

            if (!mapRecordField(record.getCategoryName(), categoryMap, "固定资产分类", index, errRows, record::setCategoryId)) {
                errs++;
                continue;
            }


            if (!StringUtil.isEmpty(record.getCalibrationType())) {
                if (!mapRecordField(record.getCalibrationType(), calibrationTypeMap, "是否强检", index, errRows, record::setCalibrationType)) {
                    errs++;
                    continue;
                }
            }

            //计量单位
            if (!StringUtil.isEmpty(record.getCalibrationCycleUnit())) {
                if (!mapRecordField(record.getCalibrationCycleUnit(), nYRMap, "计量周期单位", index, errRows, record::setCalibrationCycleUnit)) {
                    errs++;
                    continue;
                }
            }

            // 保养周期单位
            if (!StringUtil.isEmpty(record.getMaintCycleUnit())) {
                if (!mapRecordField(record.getMaintCycleUnit(), nYRMap, "保养周期单位", index, errRows, record::setMaintCycleUnit)) {
                    errs++;
                    continue;
                }
            }

            // 寿命单位
            if (!StringUtil.isEmpty(record.getLifespanUnit())) {
                if (!mapRecordField(record.getLifespanUnit(), nYRMap, "寿命单位", index, errRows, record::setLifespanUnit)) {
                    errs++;
                    continue;
                }
            }

            // 是否生命支持
            if (!StringUtil.isEmpty(record.getIsLifeSupport())) {
                if (!mapRecordField(record.getIsLifeSupport(), yesOrNoMap, "是否生命支持", index, errRows, record::setIsLifeSupport)) {
                    errs++;
                    continue;
                }
            } else {
                record.setIsLifeSupport("N");
            }

            // 是否特种设备
            if (!StringUtil.isEmpty(record.getIsSpecial())) {
                if (!mapRecordField(record.getIsSpecial(), yesOrNoMap, "是否特种设备", index, errRows, record::setIsSpecial)) {
                    errs++;
                    continue;
                }
            } else {
                record.setIsSpecial("N");
            }

            // 是否安装
            if (!StringUtil.isEmpty(record.getNeedInstall())) {
                if (!mapRecordField(record.getNeedInstall(), yesOrNoMap, "是否需要安装", index, errRows, record::setNeedInstall)) {
                    errs++;
                    continue;
                }
            } else {
                record.setNeedInstall("N");
            }

            String skuKey = String.format("%s,%s,%s,%s,%s,%s", record.getName(), record.getModel(), record.getBrandName(),
                    record.getManufacturerName(), record.getCategory22Name(), record.getCategoryName());
            String md5 = MD5.string2MD5(skuKey);

            if (existingSkus.contains(md5) || selfSet.contains(md5)) {
                addErrorRow(errRows, index, "资产名称,规格型号,品牌,厂家,固定资产分类,医疗器械分类", skuKey, "设备字典重复.");
                errs++;
                continue;
            }
            selfSet.add(md5);


            populateRecord(record, user);
            cleanList.add(record);
        }

        // 有错误则不导入
        if (errs == 0) {
            batchInsert(cleanList);
        }

        succs += cleanList.size();

        resp.setErrs(errs);
        resp.setSuccs(succs);
        resp.setErrRows(errRows);

        // 生成设备字典编码
        autoFillCode();

        return resp;
    }

    /**
     * @param field:
     * @param map:
     * @param columnName:
     * @param index:
     * @param errRows:
     * @param setIdFunction:
     * @return boolean
     * <AUTHOR>
     * @description 映射字段
     * @date 2024/9/11 08:55
     */
    private boolean mapRecordField(String field, Map<String, String> map, String columnName, int index,
                                   List<ImportErrRow> errRows, Consumer<String> setIdFunction) {
        if (field != null) {
            String id = map.get(field.trim());

            if (id == null) {
                // 把field 也放进错误信息
                addErrorRow(errRows, index, columnName, field, columnName + "[" + field + "]" + "不存在.");
                return false;
            }
            setIdFunction.accept(id);
        } else {
            addErrorRow(errRows, index, columnName, field, columnName + "[" + field + "]" + "不存在.");
            return false;
        }
        return true;
    }


    /**
     * @param errRows:
     * @param index:
     * @param columnName:
     * @param columnValue:
     * @param errMsg:
     * @return void
     * <AUTHOR>
     * @description 添加错误行
     * @date 2024/9/11 08:56
     */
    private void addErrorRow(List<ImportErrRow> errRows, int index, String columnName, String columnValue, String errMsg) {
        ImportErrRow importErrRow = new ImportErrRow();
        importErrRow.setIndex(index + 1); // Correct for 1-based index
        importErrRow.setColumnName(columnName);
        importErrRow.setColumnValue(columnValue);
        importErrRow.setErrMsg(errMsg);
        errRows.add(importErrRow);
    }

    /**
     * @param record:
     * @param user:
     * @return void
     * <AUTHOR>
     * @description 填充记录
     * @date 2024/9/11 08:56
     */
    private void populateRecord(Sku record, ThpsUser user) {

        autoFillColumn(record);
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
    }

    /**
     * @param cleanList:
     * @return void
     * <AUTHOR>
     * @description 批量插入
     * @date 2024/9/11 08:56
     */
    private void batchInsert(List<Sku> cleanList) {
        int size = cleanList.size();
        int batchSize = 1000;

        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            List<Sku> subList = cleanList.subList(i, end);
            mapper.batchInsert(subList);
        }
    }


    /**
     * @param :
     * @return Set
     * <AUTHOR>
     * @description 获取所有的SKU名称，主要用来逻辑过滤
     * @date 2024/9/11 08:57
     */

    private Set fetchAllSkuName() {

        List<Sku> list = getList(new Sku());
        Set<String> set = new HashSet<>();
        for (Sku sku : list) {
            // 这里字典名称+规格型号+品牌+厂家+分类 作为唯一性判断
            // 给他们做md5加密 插入到set中
            String skuKey = String.format("%s,%s,%s,%s,%s", sku.getName(), sku.getModel(), sku.getBrandName(),
                    sku.getManufacturerName(), sku.getCategory22Name());
            String md5 = MD5.string2MD5(skuKey);
            set.add(md5);
        }
        return set;
    }

    /**
     * @param record:
     * @param type:
     * @return void
     * <AUTHOR>
     * @description 用于新增或者修改时候的名称重复校验
     * @date 2024/9/11 08:58
     */
    private void checkRepeat(Sku record, String type) {
        Example example = new Example(Sku.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (!StringUtil.isEmpty(record.getName())) {
            criteria.andLike("name", record.getName());
        }

        if (!StringUtil.isEmpty(record.getModel())) {
            criteria.andLike("model", record.getModel());
        }

        if (!StringUtil.isEmpty(record.getBrandId())) {
            criteria.andLike("brandId", record.getBrandId());
        }

        if (!StringUtil.isEmpty(record.getManufacturerId())) {
            criteria.andLike("manufacturerId", record.getManufacturerId());
        }


        int count = mapper.selectCountByExample(example);

        if (count > 0 && "add".equals(type)) {
            throw new RuntimeException("设备字典重复.");
        }

        if (count > 1 && "update".equals(type)) {
            throw new RuntimeException("设备字典重复.");
        }
    }


    @Transactional(readOnly = false)
    @Async
    // 生成设备字典编码
    public void autoFillCode() {

        // 查出所有的未生成编码的设备字典
        Example exp = new Example(Sku.class);
        Example.Criteria criteria = exp.createCriteria();
        criteria.andIsNull("code");
        List<Sku> list = mapper.selectByExample(exp);

        list.forEach(sku -> {
            String code = "";
            if (SkuConst.SKU_TYPE_YLSB.equals(sku.getSkuType())) {
                code = serialNoGenService.genByDate("YLZD");
            } else {
                code = serialNoGenService.genByDate("YBZD");
            }
            sku.setCode(code);
            mapper.updateByPrimaryKeySelective(sku);
        });

    }

    @Override
    public Boolean hasUse(String relationType, String relationId) {
        Example example = new Example(Sku.class);
        Example.Criteria criteria = example.createCriteria();

        switch (relationType) {
            case "brand":
                criteria.andEqualTo("brandId", relationId);
                break;
            case "manufacturer":
                criteria.andEqualTo("manufacturerId", relationId);
                break;
            default:
                // 什么都不做
                break;
        }

        criteria.andEqualTo("isDeleted", "N");

        return mapper.selectCountByExample(example) > 0;
    }

    @Override
    public List<DictItemResp> getSkuTypeList(String logic) {
        PermissionResp permissionResp = new PermissionResp();
        switch (logic) {
            case "inventoryPlanCreate":
                permissionResp = permissionService.cgetPermission(PermissionConst.业务类型_资产盘点);
                break;
            case "inboundOrderCreate":
                permissionResp = permissionService.cgetPermission(PermissionConst.业务类型_审核入库单);
                break;
            case "outboundOrderCreate":
                permissionResp = permissionService.cgetPermission(PermissionConst.业务类型_审核出库单);
                break;
            default:
                permissionResp.setType(PermissionConst.TYPE_ALL);
                break;
        }

        List<DictItemResp> dictItemResps = dictService.cgetKL(SkuConst.SKU_TYPE);

        if (!PermissionConst.TYPE_ALL.equals(permissionResp.getType())) {
            // 获取
            List<String> skuTypeList = permissionResp.getSkuTypeList();
            if (skuTypeList != null) {
                // 只返回有权限的资产类别
                List<DictItemResp> filteredList = new ArrayList<>();
                for (DictItemResp dictItemResp : dictItemResps) {
                    if (skuTypeList.contains(dictItemResp.getItemNameValue())) {
                        filteredList.add(dictItemResp);
                    }
                }
                return filteredList;
            } else {
                return new ArrayList<>();
            }
        }

        return dictItemResps;
    }

    @Override
    public List<Sku> searchSkuList(String skuType, String keyword, String selectedId) {
        List<Sku> sku = mapper.searchSkuList(skuType, keyword, selectedId);
        sku.forEach(this::dataFmt);
        return sku;
    }
}
