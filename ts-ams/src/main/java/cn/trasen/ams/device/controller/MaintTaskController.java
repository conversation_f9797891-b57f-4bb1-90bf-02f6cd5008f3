package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.bean.maintTask.*;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.eventPublisher.maintTask.MaintTaskCompletedEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.ams.device.service.MaintTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MaintTaskController
 * @Description TODO
 * @date 2024年12月14日 下午2:07:59
 */
@RestController
@Api(tags = "MaintTaskController")
public class MaintTaskController {

    private transient static final Logger logger = LoggerFactory.getLogger(MaintTaskController.class);

    @Autowired
    private MaintTaskService maintTaskService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateMaintTask
     * @Description 编辑
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/maintTask/update")
    public PlatformResult<String> updateMaintTask(@RequestBody MaintTask record) {
        try {
            maintTaskService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<MaintTask>
     * @Title selectMaintTaskById
     * @Description 根据ID查询
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/maintTask/{id}")
    public PlatformResult<MaintTaskDetailResp> selectMaintTaskById(@PathVariable String id) {
        try {
            MaintTaskDetailResp record = maintTaskService.getDetailById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteMaintTaskById
     * @Description 根据ID删除
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/maintTask/delete/{id}")
    public PlatformResult<String> deleteMaintTaskById(@PathVariable String id) {
        try {
            maintTaskService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "工单报废", notes = "工单报废 act = 0 表示报废撤销 1 代表报废")
    @PostMapping("/api/device/maintTask/scrap/{id}/{act}")
    public PlatformResult<String> scrap(@PathVariable String id, @PathVariable String act) {
        try {
            maintTaskService.scrap(id, act);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<MaintTask>
     * @Title selectMaintTaskList
     * @Description 查询列表
     * @date 2024年12月14日 下午2:07:59
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/maintTask/list")
    public DataSet<MaintTaskListResp> selectMaintTaskList(Page page, MaintTaskListReq record) {
        return maintTaskService.getDataSetList(page, record);
    }




    @ApiOperation(value = "完成保养任务", notes = "完成保养任务")
    @PostMapping("/api/device/maintTask/complete")
    public PlatformResult<String> completeMaintTask(@RequestBody CompleteMaintTaskReq record) {
        try {
            maintTaskService.completeMaintTask(record);
            // 发布保养计划创建事件
            applicationEventPublisher.publishEvent(new MaintTaskCompletedEvent(maintTaskService.selectById(record.getMaintTask().getId())));
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "异常列表", notes = "异常列表")
    @GetMapping("/api/device/maintTask/explist")
    public DataSet<MaintTaskListResp> selectMaintTaskExpList(Page page, MaintTaskListReq record) {
        record.setExp(CommonConst.YES);
        return maintTaskService.getDataSetList(page, record);
    }
    @ApiOperation(value = "处理保养工单异常", notes = "处理保养工单异常")
    @PostMapping("/api/device/maintTask/solveExp")

    public PlatformResult<String> solveExpMaintTask(@RequestBody SolveExpMaintTaskReq record) {
        try {
            maintTaskService.solveExpMaintTask(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


}
