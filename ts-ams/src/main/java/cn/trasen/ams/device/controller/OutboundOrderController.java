package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.device.bean.outin.*;
import cn.trasen.ams.device.service.OutboundOrderDetailService;
import cn.trasen.ams.common.validator.right.RightValid;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.OutboundOrder;
import cn.trasen.ams.device.service.OutboundOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutboundOrderController
 * @Description TODO
 * @date 2025年2月11日 下午5:25:38
 */
@RestController
@Api(tags = "OutboundOrderController")
@Validated
public class OutboundOrderController {

    private transient static final Logger logger = LoggerFactory.getLogger(OutboundOrderController.class);

    @Autowired
    private OutboundOrderService outboundOrderService;

    @Autowired
    private OutboundOrderDetailService outboundOrderDetailService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveOutboundOrder
     * @Description 新增
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/outboundOrder/save")
    public PlatformResult<String> saveOutboundOrder(@Validated @RequestBody OutboundOrderInsertReq record) {
        try {
            String id = outboundOrderService.insert(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @RightValid(action = "审核出库单", message = "您没有审核出库单的权限")
    @PostMapping("/api/device/outboundOrder/confirm/{id}")
    public PlatformResult<String> confirm(@PathVariable String id) {
        try {
            // 根据逗号分隔 id 转 List<String>
            List<String> ids = java.util.Arrays.asList(id.split(","));
            outboundOrderService.batchConfirm(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateOutboundOrder
     * @Description 编辑
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/outboundOrder/update")
    public PlatformResult<String> updateOutboundOrder(@RequestBody OutboundOrderInsertReq record) {
        try {
            String id = outboundOrderService.edit(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<OutboundOrder>
     * @Title selectOutboundOrderById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/outboundOrder/{id}")
    public PlatformResult<OutBoundOrderDetailResp> selectOutboundOrderById(@PathVariable String id) {
        try {

            OutBoundOrderDetailResp record = new OutBoundOrderDetailResp();

            OutboundOrder outboundOrder = outboundOrderService.selectById(id);
            outboundOrderService.dataFmt(outboundOrder);

            // 查询明细
            List<OutBoundOrderDetailExtResp> outBoundOrderDetailExtRespList = outboundOrderDetailService.selectByOutboundOrderId(id);

            record.setOutboundOrder(outboundOrder);
            record.setOutboundOrderDetailExtResp(outBoundOrderDetailExtRespList);

            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteOutboundOrderById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/outboundOrder/delete/{id}")
    public PlatformResult<String> deleteOutboundOrderById(@PathVariable String id) {
        try {
            outboundOrderService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<OutboundOrder>
     * @Title selectOutboundOrderList
     * @Description 查询列表
     * @date 2025年2月11日 下午5:25:38
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/outboundOrder/list")
    public DataSet<OutboundOrder> selectOutboundOrderList(Page page, OutBoundOrderListReq record) {
        return outboundOrderService.getDataSetList(page, record);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/outboundOrder/export")
    public ResponseEntity<byte[]> export(OutBoundOrderListReq record) throws IOException {

        try {

            List<OutboundOrder> exportList = outboundOrderService.getListNoPage(record);
            // dataFmt
            for (OutboundOrder outboundOrder : exportList) {
                outboundOrderService.dataFmt(outboundOrder);
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/outLogExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("出库单.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
