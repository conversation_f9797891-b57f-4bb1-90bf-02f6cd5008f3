package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.SignoffDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SignoffDetailService
 * @Description TODO
 * @date 2025年7月7日 上午11:37:03
 */
public interface SignoffDetailService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月7日 上午11:37:03
     * <AUTHOR>
     */
    Integer save(SignoffDetail record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月7日 上午11:37:03
     * <AUTHOR>
     */
    Integer update(SignoffDetail record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月7日 上午11:37:03
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return SignoffDetail
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月7日 上午11:37:03
     * <AUTHOR>
     */
    SignoffDetail selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<SignoffDetail>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月7日 上午11:37:03
     * <AUTHOR>
     */
    DataSet<SignoffDetail> getDataSetList(Page page, SignoffDetail record);

    void deleteBySignoffId(String signofflId);

    List<SignoffDetail> selectBySignoffId(String signoffId);

    void batchInsert(List<SignoffDetail> signoffDetails);

    boolean checkDeviceHasSignoff(List<String> deviceIdList);
}
