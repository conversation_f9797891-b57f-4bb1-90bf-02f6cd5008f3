<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.InboundOrderDetailMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.InboundOrderDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="inbound_order_id" jdbcType="VARCHAR" property="inboundOrderId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="loc" jdbcType="VARCHAR" property="loc"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <select id="selectByInboundOrderId" resultType="cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp">
        select t1.*,
               t2.code             as code,
               t2.name             as name,
               t2.sku_type         as skuType,
               t2.need_install     as needInstall,
               t2.model            as model,
               t2.brand_id         as brandId,
               t3.name             as brandName,
               t2.manufacturer_id  as manufacturerId,
               t4.name             as manufacturerName,
               t2.category22_id    as category22Id,
               t5.name             as category22Name,
               t6.unique_no        as uniqueNo,
               t6.asset_code       as assetCode,
               t7.name             as supplierName,
               t7.id               as supplierId,
               t6.asset_code       as assetCode,
               t2.unit             as unit,
               t6.belong_to_org_id as belongToOrgId
        from d_inbound_order_detail t1
                 left join d_sku t2 on t1.sku_id = t2.id
                 left join c_brand t3 on t2.brand_id = t3.id
                 left join c_manufacturer t4 on t2.manufacturer_id = t4.id
                 left join d_category22 t5 on t2.category22_id = t5.id
                 left join d_device t6 on t1.device_id = t6.id
                 left join c_supplier t7 on t6.supplier_id = t7.id
        where t1.inbound_order_id = #{inboundOrderId}
          and t1.is_deleted = 'N'
    </select>

    <select id="getGoBackingDeviceIds" resultType="java.lang.String">
        select DISTINCT t1.device_id
        from d_inbound_order_detail t1
                 left join `d_inbound_order` t2 on t1.`inbound_order_id` = t2.id
        where t2.status = '0'
          and t2.`is_deleted` = 'N'
          and t1.`is_deleted` = 'N'
          and t1.device_id is not null
    </select>
</mapper>