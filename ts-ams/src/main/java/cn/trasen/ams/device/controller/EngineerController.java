package cn.trasen.ams.device.controller;

import cn.trasen.homs.core.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Engineer;
import cn.trasen.ams.device.service.EngineerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName EngineerController
 * @Description TODO
 * @date 2024年12月25日 下午4:45:40
 */
@RestController
@Api(tags = "EngineerController")
public class EngineerController {

    private transient static final Logger logger = LoggerFactory.getLogger(EngineerController.class);

    @Autowired
    private EngineerService engineerService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveEngineer
     * @Description 新增
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/engineer/save")
    public PlatformResult<String> saveEngineer(@RequestBody Engineer record) {
        try {
            // 判断record.getUserId 是否包含逗号
            if (StringUtil.isEmpty(record.getUserId())) {
                return PlatformResult.failure("用户ID不能为空");
            }

            if (record.getUserId().contains(",")) {
                String[] userIds = record.getUserId().split(",");
                for (String userId : userIds) {
                    Engineer copy = new Engineer();
                    copy.setUserId(userId);
                    copy.setItemSet(record.getItemSet());
                    copy.setSupplierId(record.getSupplierId());
                    engineerService.save(copy);
                }
            } else {
                engineerService.save(record);
            }

            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateEngineer
     * @Description 编辑
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/engineer/update")
    public PlatformResult<String> updateEngineer(@RequestBody Engineer record) {
        try {
            // 不允许修改UserId
            record.setUserId(null);
            engineerService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Engineer>
     * @Title selectEngineerById
     * @Description 根据ID查询
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/engineer/{id}")
    public PlatformResult<Engineer> selectEngineerById(@PathVariable String id) {
        try {
            Engineer record = engineerService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteEngineerById
     * @Description 根据ID删除
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/engineer/delete/{id}")
    public PlatformResult<String> deleteEngineerById(@PathVariable String id) {
        try {
            engineerService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Engineer>
     * @Title selectEngineerList
     * @Description 查询列表
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/engineer/list")
    public DataSet<Engineer> selectEngineerList(Page page, Engineer record) {
        return engineerService.getDataSetList(page, record);
    }


    @ApiOperation(value = "列表无分页", notes = "列表无分页")
    @GetMapping("/api/device/engineer/listNoPage")
    public PlatformResult<List<Engineer>> selectEngineerList(Engineer record) {
        try {
            List<Engineer> list = engineerService.getList(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
