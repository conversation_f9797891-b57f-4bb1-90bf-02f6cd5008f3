<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.DeviceStatsMapper">
    <resultMap id="SLimMap" type="cn.trasen.ams.device.model.DeviceStats">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="maints" jdbcType="INTEGER" property="maints"/>
        <result column="maintas" jdbcType="INTEGER" property="maintas"/>
        <result column="maintbs" jdbcType="INTEGER" property="maintbs"/>
        <result column="maintcs" jdbcType="INTEGER" property="maintcs"/>
        <result column="last_maint_time" jdbcType="TIMESTAMP" property="lastMaintTime"/>
        <result column="next_maint_time" jdbcType="TIMESTAMP" property="nextMaintTime"/>
        <result column="inspections" jdbcType="INTEGER" property="inspections"/>
        <result column="last_inspections_time" jdbcType="TIMESTAMP" property="lastInspectionsTime"/>
        <result column="next_inspections_time" jdbcType="TIMESTAMP" property="nextInspectionsTime"/>
        <result column="metrologys" jdbcType="INTEGER" property="metrologys"/>
        <result column="last_metrology_time" jdbcType="TIMESTAMP" property="lastMetrologyTime"/>
        <result column="next_metrology_time" jdbcType="TIMESTAMP" property="nextMetrologyTime"/>
    </resultMap>
</mapper>