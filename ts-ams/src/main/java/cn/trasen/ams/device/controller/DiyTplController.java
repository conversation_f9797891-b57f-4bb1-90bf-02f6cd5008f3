package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.DiyTpl;
import cn.trasen.ams.device.service.DiyTplService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DiyTplController
 * @Description TODO
 * @date 2024年12月19日 下午5:36:26
 */
@RestController
@Api(tags = "DiyTplController")
public class DiyTplController {

    private transient static final Logger logger = LoggerFactory.getLogger(DiyTplController.class);

    @Autowired
    private DiyTplService diyTplService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveDiyTpl
     * @Description 新增
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/diyTpl/save")
    public PlatformResult<String> saveDiyTpl(@RequestBody DiyTpl record) {
        try {
            diyTplService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateDiyTpl
     * @Description 编辑
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/diyTpl/update")
    public PlatformResult<String> updateDiyTpl(@RequestBody DiyTpl record) {
        try {
            diyTplService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<DiyTpl>
     * @Title selectDiyTplById
     * @Description 根据ID查询
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/diyTpl/{id}")
    public PlatformResult<DiyTpl> selectDiyTplById(@PathVariable String id) {
        try {
            DiyTpl record = diyTplService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteDiyTplById
     * @Description 根据ID删除
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/diyTpl/delete/{id}")
    public PlatformResult<String> deleteDiyTplById(@PathVariable String id) {
        try {
            diyTplService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "模板复制", notes = "模板复制")
    @PostMapping("/api/device/diyTpl/copy/{id}")
    public PlatformResult<String> copyDiyTplById(@PathVariable String id) {
        try {
            diyTplService.copyById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<DiyTpl>
     * @Title selectDiyTplList
     * @Description 查询列表
     * @date 2024年12月19日 下午5:36:26
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/diyTpl/list")
    public DataSet<DiyTpl> selectDiyTplList(Page page, DiyTpl record) {
        return diyTplService.getDataSetList(page, record);
    }


    @ApiOperation(value = "列表通过设备ID查询", notes = "列表通过设备ID查询")
    @GetMapping("/api/device/diyTpl/listByDeviceId/{deviceId}")
    public PlatformResult selectDiyTplListByDeviceId(@PathVariable String deviceId) {
        try {
            return PlatformResult.success(diyTplService.getListByDeviceId(deviceId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


}
