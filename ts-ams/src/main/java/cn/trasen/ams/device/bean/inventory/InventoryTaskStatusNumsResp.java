package cn.trasen.ams.device.bean.inventory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inventory
 * @className: InventoryTaskStatusNumsResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/2/23 15:44
 * @version: 1.0
 */

@Data
public class InventoryTaskStatusNumsResp {

    @ApiModelProperty(value = "总数")
    private int alls;

    @ApiModelProperty(value = "未盘点数量")
    private Integer wps;

    @ApiModelProperty(value = "盘到数量")
    private int pds;

    @ApiModelProperty(value = "盘亏数量")
    private Integer pks;

    @ApiModelProperty(value = "正常数量")
    private Integer zcs;

    @ApiModelProperty(value = "损坏数量")
    private Integer shs;

    @ApiModelProperty(value = "故障数量")
    private Integer gzs;

    @ApiModelProperty(value = "异常数量")
    private Integer ycs;

    @ApiModelProperty(value = "已盘点数")
    private Integer yps;

    @ApiModelProperty(value = "闲置数量")
    private Integer xzs;


}
