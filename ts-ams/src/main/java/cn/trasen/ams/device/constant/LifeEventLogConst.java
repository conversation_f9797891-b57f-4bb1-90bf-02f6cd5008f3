package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: LifeEventLogConst
 * @author: chenbin
 * @description: 生命周期事件日志常量
 * @date: 2024/9/13 10:59
 * @version: 1.0
 */

public class LifeEventLogConst {

    public static final String LIFE_EVENT_TYPE = "AMS_LIFE_EVENT_TYPE";

    // 1 转科 2 验收 3 报废 4 维修 5 保养 6 计量 7 巡检 8 转借 9 购入
    //转科
    public static final String TYPE_TRANSFER = "1";
    //验收
    public static final String TYPE_ACCEPTANCE = "2";

    //报废
    public static final String TYPE_SCRAP = "3";
    //维修
    public static final String TYPE_REPAIR = "4";

    //保养
    public static final String TYPE_MAINTENANCE = "5";

    //计量
    public static final String TYPE_CALIBRATION = "6";
    //巡检
    public static final String TYPE_INSPECTION = "7";

    //转借
    public static final String TYPE_BORROW = "8";

    //购入
    public static final String TYPE_PURCHASE = "9";
    
}
