package cn.trasen.ams.device.bean.outin;

import lombok.Data;

import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: DeviceGobackUpdate
 * @author: chenbin
 * @description: TODO
 * @date: 2025/2/17 09:43
 * @version: 1.0
 */

@Data
public class InboundOrderGobackUpdate {

    private String warehouseId;
    private String warehouseStatus;
    private String belongToOrgId;
    private String useOrgId;
    private String status;
    private String loc;
    private Date updateDate;
    private String updateUser;
    private String updateUserName;

}
