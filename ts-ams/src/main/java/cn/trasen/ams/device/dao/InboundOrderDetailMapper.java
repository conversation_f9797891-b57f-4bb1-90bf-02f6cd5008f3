package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.outin.InboundOrderDetailExtResp;
import cn.trasen.ams.device.model.InboundOrderDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface InboundOrderDetailMapper extends Mapper<InboundOrderDetail> {
    List<InboundOrderDetailExtResp> selectByInboundOrderId(@Param("inboundOrderId") String inboundOrderId);

    List<String> getGoBackingDeviceIds();
}