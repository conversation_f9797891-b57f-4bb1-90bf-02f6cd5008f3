package cn.trasen.ams.device.constant;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.constant
 * @className: DeviceConst
 * @author: chenbin
 * @description: 设备常量
 * @date: 2024/9/11 15:58
 * @version: 1.0
 */

public class DeviceConst {

    public static final String DEVICE_STATUS = "AMS_DEVICE_STATUS";

    //未安装
    public static final String STATUS_UN_INS = "0";

    //使用中
    public static final String STATUS_USE = "1";

    //故障中
    public static final String STATUS_FAULT = "2";

    //闲置
    public static final String STATUS_IDLE = "3";

    //报废
    public static final String STATUS_SCRAP = "4";

    //遗失
    public static final String STATUS_LOSS = "5";

    // 损坏
    public static final String STATUS_DAMAGE = "6";

    // 捐赠（出）
    public static final String STATUS_DONATE_OUT = "7";

    // 其他
    public static final String STATUS_OTHER = "8";


    public static final String AMS_DEPRECIATION_METHOD = "AMS_DEPRECIATION_METHOD";

    //直线法
    public static final String STRAIGHT_LINE = "0";
    //递减余额法
    public static final String DECLINING_BALANCE = "1";
    //年数总和法
    public static final String SUM_OF_YEARS_DIGITS = "2";


    public static final String AMS_MAINT_CONTRACT_STATUS = "AMS_MAINT_CONTRACT_STATUS";

    // 在库
    public static final String WAREHOUSE_STATUS_ZK = "0";

    // 在役
    public static final String WAREHOUSE_STATUS_ZY = "1";

    // 减少（处置完了）
    public static final String WAREHOUSE_STATUS_JS = "2";

    // 入库中
    public static final String WAREHOUSE_STATUS_RKZ = "3";

    // 出库中
    public static final String WAREHOUSE_STATUS_CKZ = "4";

    // 转移中
    public static final String WAREHOUSE_STATUS_ZYZ = "5";

    // 处置中
    public static final String WAREHOUSE_STATUS_CZZ = "6";

    public static final String AMS_DEVICE_WAREHOUSE_STATUS = "AMS_DEVICE_WAREHOUSE_STATUS";


}
