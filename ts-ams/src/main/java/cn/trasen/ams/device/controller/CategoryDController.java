package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.service.CategoryService;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.homs.core.model.TreeModel;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Category;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CategoryController
 * @Description TODO
 * @date 2025年3月6日 下午3:31:39
 */
@RestController
@Api(tags = "CategoryController")
public class CategoryDController {

    private transient static final Logger logger = LoggerFactory.getLogger(CategoryDController.class);

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private DeviceService deviceService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveCategory
     * @Description 新增
     * @date 2025年3月6日 下午3:31:39
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/category/save")
    public PlatformResult<String> saveCategory(@RequestBody Category record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            categoryService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateCategory
     * @Description 编辑
     * @date 2025年3月6日 下午3:31:39
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/category/update")
    public PlatformResult<String> updateCategory(@RequestBody Category record) {
        try {
            categoryService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Category>
     * @Title selectCategoryById
     * @Description 根据ID查询
     * @date 2025年3月6日 下午3:31:39
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/category/{id}")
    public PlatformResult<Category> selectCategoryById(@PathVariable String id) {
        try {
            Category record = categoryService.selectById(id);
            // 获取父类名称
            if (record != null && StringUtils.isNotBlank(record.getParentId())) {
                Category parent = categoryService.selectById(record.getParentId());
                if (parent != null) {
                    record.setParentName(parent.getName());
                }
            }
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteCategoryById
     * @Description 根据ID删除
     * @date 2025年3月6日 下午3:31:39
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/category/delete/{id}")
    public PlatformResult<String> deleteCategoryById(@PathVariable String id) {
        try {
            // TODO  这里需要优化
            int count = deviceService.countByCateId(id);
            if (count > 0) {
                throw new RuntimeException("该分类下有设备，不能删除");
            }

            categoryService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Category>
     * @Title selectCategoryList
     * @Description 查询列表
     * @date 2025年3月6日 下午3:31:39
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/category/list")
    public DataSet<Category> selectCategoryList(Page page, Category record) {
        record.setSysType(CommonConst.SYS_TYPE_ZCSB);
        return categoryService.getDataSetList(page, record);
    }

    @ApiOperation(value = "列表树形", notes = "列表树形")
    @RequestMapping(value = "/api/device/category/tree")
    public PlatformResult<List<TreeModel>> selectCategoryTree(HttpServletRequest request, HttpServletResponse response) {
        try {
            String levelObj = request.getParameter("level");
            String isEnable = request.getParameter("isEnable");
            Integer level = 0;
            if (!StringUtils.isBlank(levelObj)) {
                level = Integer.parseInt(levelObj);
            }
            return PlatformResult.success(categoryService.getTreeList(level, isEnable, CommonConst.SYS_TYPE_ZCSB));
        } catch (Exception e) {
            PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.failure();
    }


    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/category/export")
    public ResponseEntity<byte[]> export(Category record) throws IOException {

        try {
            record.setSysType(CommonConst.SYS_TYPE_ZCSB);
            List<Category> categoryList = categoryService.getList(null, record);

            int index = 0;
            for (Category category : categoryList) {
                index++;
                category.setNo(String.valueOf(index));
                if (category.getIsLeafNode().equals("Y")) {
                    category.setIsLeafNode("是");
                } else {
                    category.setIsLeafNode("否");
                }
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/exportDeviceCategory.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", categoryList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("设备分类表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
