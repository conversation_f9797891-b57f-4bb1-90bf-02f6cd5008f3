package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.device.constant.SkuConst;
import cn.trasen.ams.common.bean.ImportErrRow;
import cn.trasen.ams.common.bean.ImportResp;
import cn.trasen.ams.common.service.DictService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Sku;
import cn.trasen.ams.device.service.SkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SkuController
 * @Description 设备字典控制器
 * @date 2024年9月9日 下午5:04:48
 */
@RestController
@Api(tags = "SkuController")
public class SkuController {

    private transient static final Logger logger = LoggerFactory.getLogger(SkuController.class);

    @Autowired
    private SkuService skuService;

    @Autowired
    private DictService dictService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveSku
     * @Description 新增
     * @date 2024年9月9日 下午5:04:48
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/sku/save")
    public PlatformResult<String> saveSku(@Valid @RequestBody Sku record) {
        try {
            String skuId = skuService.save(record);
            return PlatformResult.success(skuId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateSku
     * @Description 编辑
     * @date 2024年9月9日 下午5:04:48
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/sku/update")
    public PlatformResult<String> updateSku(@Valid @RequestBody Sku record) {
        try {
            skuService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Sku>
     * @Title selectSkuById
     * @Description 根据ID查询
     * @date 2024年9月9日 下午5:04:48
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/sku/{id}")
    public PlatformResult<Sku> selectSkuById(@PathVariable String id) {
        try {
            Sku record = skuService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteSkuById
     * @Description 根据ID删除
     * @date 2024年9月9日 下午5:04:48
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/sku/delete/{id}")
    public PlatformResult<String> deleteSkuById(@PathVariable String id) {
        try {
            skuService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Sku>
     * @Title selectSkuList
     * @Description 查询列表
     * @date 2024年9月9日 下午5:04:48
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    // 兼容 get 和 post 请求 ("/api/device/sku/list")
    @RequestMapping(value = "/api/device/sku/list", method = {RequestMethod.GET, RequestMethod.POST})

    public DataSet<Sku> selectSkuList(Page page, Sku record) {
        return skuService.getDataSetList(page, record);
    }

    @RequestMapping(value = "/api/device/sku/search", method = {RequestMethod.GET, RequestMethod.POST})
    public PlatformResult<List<Sku>> searchSkuList(@RequestParam(required = false) String skuType, @RequestParam(required = false) String query, @RequestParam(required = false) String selectedId) {
        try {
            List<Sku> list = skuService.searchSkuList(skuType, query, selectedId);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @GetMapping(value = "/api/device/sku/tpl")
    @ApiOperation(value = "导入模板", notes = "品牌导入模板")
    public void tpl(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "设备字典导入模板.xlsx";
            String template = "template/skuImportTpl.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/api/device/sku/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {
        List<Sku> imports = (List<Sku>) ImportExcelUtil.getExcelDatas(file, Sku.class);

        try {

            ImportResp importResp = skuService.importByExcel(imports);

            StringBuffer sb = new StringBuffer("导入失败:");
            if (importResp.getErrs() > 0) {
                // 拼接错误消息
                for (ImportErrRow err : importResp.getErrRows()) {
                    sb.append("第" + err.getIndex() + "行" + err.getErrMsg());
                }
                return PlatformResult.failure(sb.toString());
            }

            if (importResp.getSuccs() == 0) {
                return PlatformResult.failure("导入失败:无数据");
            }

            return PlatformResult.success(importResp);

        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }

    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/sku/export")
    public ResponseEntity<byte[]> export(Sku record) throws IOException {

        try {

            List<Sku> exportList = skuService.getList(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/skuExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();


            for (Sku sku : exportList) {

                sku.setLifespanUnit(dictService.cgetNameByValue(SkuConst.YMD, sku.getLifespanUnit()));
                sku.setMaintCycleUnit(dictService.cgetNameByValue(SkuConst.YMD, sku.getMaintCycleUnit()));
                sku.setCalibrationCycleUnit(dictService.cgetNameByValue(SkuConst.YMD, sku.getCalibrationCycleUnit()));
                sku.setCalibrationType(dictService.cgetNameByValue(CommonConst.YES_OR_NO, sku.getCalibrationType()));
                sku.setIsLifeSupport(dictService.cgetNameByValue(CommonConst.YES_OR_NO, sku.getIsLifeSupport()));
                sku.setIsSpecial(dictService.cgetNameByValue(CommonConst.YES_OR_NO, sku.getIsSpecial()));
                // 资产类别
                sku.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, sku.getSkuType()));
                // 是否需要安装
                sku.setNeedInstallShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, sku.getNeedInstall()));
            }

            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("设备字典列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    @GetMapping("/api/device/sku/test")
    public PlatformResult test() throws Exception {

        skuService.autoFillCode();
        return PlatformResult.success("");
    }

}
