package cn.trasen.ams.device.bean.inventory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.inventory
 * @className: InventoryPlanCateResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/4/15 17:02
 * @version: 1.0
 */
@Data
public class InventoryPlanCateResp {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "任务数量")
    private Integer nums;

}
