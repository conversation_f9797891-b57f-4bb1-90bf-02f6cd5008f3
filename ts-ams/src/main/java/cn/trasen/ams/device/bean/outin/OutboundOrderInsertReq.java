package cn.trasen.ams.device.bean.outin;

import cn.trasen.ams.device.model.OutboundOrder;
import cn.trasen.ams.device.model.OutboundOrderDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.outin
 * @className: OutboundOrderInsertReq
 * @author: chenbin
 * @description: 出库单编辑和新增的结构体
 * @date: 2025/2/17 13:52
 * @version: 1.0
 */

@Data
@Validated
public class OutboundOrderInsertReq {

    @ApiModelProperty(value = "出库单结构体")
    @NotNull(message = "出库单对象必传")
    @Valid // 触发嵌套验证
    private OutboundOrder outboundOrder;

    @ApiModelProperty(value = "出库单明细结构体")
    @NotNull(message = "出库单明细数组必传")
    @Valid // 触发嵌套验证
    private List<OutboundOrderDetail> outboundOrderDetailList;

}
