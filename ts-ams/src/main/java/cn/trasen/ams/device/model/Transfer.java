package cn.trasen.ams.device.model;

import cn.trasen.ams.common.validator.dict.DictExistValid;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "d_transfer")
@Setter
@Getter
public class Transfer {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流水号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "流水号")
    private String flowNo;

    /**
     * 资产类型
     */
    @Column(name = "sku_type")
    @NotNull(message = "资产类型必传")
    @DictExistValid(message = "资产类型不存在", code = "SKU_TYPE")
    @ApiModelProperty(value = "资产类型")
    private String skuType;

    @Transient
    @ApiModelProperty(value = "资产类型名称")
    private String skuTypeShow;

    /**
     * 相关资产冗余字段设计
     */
    @Column(name = "sku_name_set")
    @ApiModelProperty(value = "相关资产冗余字段设计")
    private String skuNameSet;

    /**
     * 本次转移的数量
     */
    @ApiModelProperty(value = "本次转移的数量")
    private Integer nums;

    /**
     * 资产总价值
     */
    @Column(name = "asset_total_value")
    @ApiModelProperty(value = "资产总价值")
    private BigDecimal assetTotalValue;

    /**
     * 资产转移原因
     */
    @ApiModelProperty(value = "资产转移原因")
    private String note;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    private String status;

    /**
     * 审批状态名称
     */
    @Transient
    @ApiModelProperty(value = "审批状态名称")
    private String statusShow;

    /**
     * 审批时间
     */
    @Column(name = "do_date")
    @ApiModelProperty(value = "审批时间")
    private Date doDate;

    /**
     * 审批备注
     */
    @Column(name = "do_note")
    @ApiModelProperty(value = "审批备注")
    private String doNote;

    /**
     * 审批人
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "审批人")
    private String doerId;

    /**
     * 审批人名称
     */
    @Column(name = "doer_name")
    @ApiModelProperty(value = "审批人名称")
    private String doerName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Transient
    @ApiModelProperty(value = "创建人科室")
    private String createUserOrgName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 审批附件
     */
    @Column(name = "do_file_set")
    @ApiModelProperty(value = "审批附件")
    private String doFileSet;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}