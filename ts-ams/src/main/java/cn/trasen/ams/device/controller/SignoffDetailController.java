package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.SignoffDetail;
import cn.trasen.ams.device.service.SignoffDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName SignoffDetailController
 * @Description TODO
 * @date 2025年7月7日 上午11:37:03
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "SignoffDetailController")
public class SignoffDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(SignoffDetailController.class);

	@Autowired
	private SignoffDetailService signoffDetailService;

	/**
	 * @Title saveSignoffDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月7日 上午11:37:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/signoffDetail/save")
	public PlatformResult<String> saveSignoffDetail(@RequestBody SignoffDetail record) {
		try {
			signoffDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateSignoffDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月7日 上午11:37:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/signoffDetail/update")
	public PlatformResult<String> updateSignoffDetail(@RequestBody SignoffDetail record) {
		try {
			signoffDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectSignoffDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<SignoffDetail>
	 * @date 2025年7月7日 上午11:37:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/signoffDetail/{id}")
	public PlatformResult<SignoffDetail> selectSignoffDetailById(@PathVariable String id) {
		try {
			SignoffDetail record = signoffDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteSignoffDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月7日 上午11:37:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/signoffDetail/delete/{id}")
	public PlatformResult<String> deleteSignoffDetailById(@PathVariable String id) {
		try {
			signoffDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectSignoffDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<SignoffDetail>
	 * @date 2025年7月7日 上午11:37:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/signoffDetail/list")
	public DataSet<SignoffDetail> selectSignoffDetailList(Page page, SignoffDetail record) {
		return signoffDetailService.getDataSetList(page, record);
	}
}
