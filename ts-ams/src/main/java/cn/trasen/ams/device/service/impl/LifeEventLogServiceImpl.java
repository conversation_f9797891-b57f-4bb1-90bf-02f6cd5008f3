package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.constant.LifeEventLogConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.LifeEventLogMapper;
import cn.trasen.ams.device.model.LifeEventLog;
import cn.trasen.ams.device.service.LifeEventLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName LifeEventLogServiceImpl
 * @Description TODO
 * @date 2024年9月13日 上午10:51:12
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class LifeEventLogServiceImpl implements LifeEventLogService {

    @Autowired
    private LifeEventLogMapper mapper;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(LifeEventLog record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(LifeEventLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        LifeEventLog record = new LifeEventLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public LifeEventLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<LifeEventLog> getDataSetList(Page page, LifeEventLog record) {
        Example example = new Example(LifeEventLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        example.setOrderByClause("create_date desc");

        if (!StringUtil.isEmpty(record.getDeviceId())) {
            criteria.andEqualTo("deviceId", record.getDeviceId());
        }

        List<LifeEventLog> records = mapper.selectByExampleAndRowBounds(example, page);
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    private void dataFmt(LifeEventLog record) {
        if (record == null) {
            return;
        }

        try {
            record.setTypeShow(dictService.cgetNameByValue(LifeEventLogConst.LIFE_EVENT_TYPE, record.getType()));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
