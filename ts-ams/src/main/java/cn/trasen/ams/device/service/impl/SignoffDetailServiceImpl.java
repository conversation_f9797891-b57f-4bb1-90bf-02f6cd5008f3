package cn.trasen.ams.device.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.SignoffDetailMapper;
import cn.trasen.ams.device.model.SignoffDetail;
import cn.trasen.ams.device.service.SignoffDetailService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SignoffDetailServiceImpl
 * @Description TODO
 * @date 2025年7月7日 上午11:37:03
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SignoffDetailServiceImpl implements SignoffDetailService {

    @Autowired
    private SignoffDetailMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(SignoffDetail record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(SignoffDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        SignoffDetail record = new SignoffDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public SignoffDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<SignoffDetail> getDataSetList(Page page, SignoffDetail record) {
        Example example = new Example(SignoffDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<SignoffDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteBySignoffId(String signofflId) {
        Example example = new Example(SignoffDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("signoffId", signofflId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        SignoffDetail record = new SignoffDetail();
        record.setIsDeleted("Y");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<SignoffDetail> selectBySignoffId(String signoffId) {
        return mapper.selectBySignoffId(signoffId);
    }

    @Transactional(readOnly = false)
    public void batchInsert(List<SignoffDetail> signoffDetails) {
        mapper.batchInsert(signoffDetails);
    }

    @Override
    public boolean checkDeviceHasSignoff(List<String> deviceIdList) {
        Example example = new Example(SignoffDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("deviceId", deviceIdList);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        int count = mapper.selectCountByExample(example);
        return count == 0;
    }

}
