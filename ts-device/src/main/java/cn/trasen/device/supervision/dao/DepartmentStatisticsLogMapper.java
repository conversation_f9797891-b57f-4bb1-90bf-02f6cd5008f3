package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DepartmentStatisticsListResp;
import cn.trasen.device.supervision.bean.report.StatisticsResp;
import cn.trasen.device.supervision.model.DepartmentStatisticsLog;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DepartmentStatisticsLogMapper extends Mapper<DepartmentStatisticsLog> {
    List<DepartmentStatisticsListResp> departmentStatistics(Page page, BenefitReq benefitReq);

    StatisticsResp statisticsResp(Page page, BenefitReq benefitReq);
}