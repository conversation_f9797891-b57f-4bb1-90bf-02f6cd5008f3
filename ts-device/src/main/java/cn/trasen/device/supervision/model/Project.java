package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_device_project")
@Setter
@Getter
public class Project {
    @Id
    private String id;

    /**
     * 采购结果ID
     */
    @Column(name = "purchase_log_id")
    @ApiModelProperty(value = "采购结果ID")
    private String purchaseLogId;

    /**
     * 所属机构ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "所属机构ID")
    private String deptId;

    @Transient
    @ApiModelProperty(value = "机构名称")
    private String deptName;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 建设规模
     */
    @Column(name = "build_scale")
    @ApiModelProperty(value = "建设规模")
    private String buildScale;

    /**
     * 投资规模
     */
    @Column(name = "investment_scale")
    @ApiModelProperty(value = "投资规模")
    private String investmentScale;


    /**
     * 是否完善
     */
    @Column(name = "is_complete")
    @ApiModelProperty(value = "是否完善")
    private String isComplete;

    /**
     * 竣工验收备案时间
     */
    @Column(name = "accept_date")
    @ApiModelProperty(value = "竣工验收备案时间")
    private String acceptDate;

    /**
     * 资金来源
     */
    @Column(name = "funds_sources")
    @ApiModelProperty(value = "资金来源")
    private String fundsSources;

    /**
     * 单价
     */
    @Column(name = "unit_price")
    @ApiModelProperty(value = "单价")
    private String unitPrice;

    /**
     * 相关文件
     */
    @ApiModelProperty(value = "相关文件")
    private String files;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 扩展字段
     */
    @Column(name = "expand_field")
    @ApiModelProperty(value = "扩展字段")
    private String expandField;
}