package cn.trasen.device.supervision.bean;


import cn.trasen.device.supervision.model.workflow.DeviceWfDefinitionInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: GetDefinitionListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/1 15:38
 * @version: 1.0
 */
@Data
public class GetDefinitionListResp {
    private Map<String, Map<String, Integer>> numsDefinitionMap;

    private Map<String, Integer> numsStatusMap;

    private List<DeviceWfDefinitionInfo> wkDefinitionList;
}
