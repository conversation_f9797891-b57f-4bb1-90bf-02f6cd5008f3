CREATE or replace VIEW `toa_device_purchase_instance_info` AS
SELECT
    t1.wf_instance_id AS wf_instance_id,
    t1.wf_definition_id AS wf_definition_id,
    t1.type AS type,
    t1.apply_year AS apply_year,
    t1.effect_year AS effect_year,
    t1.effect_years AS effect_years,
    t1.zjly AS zjly,
    t1.apply_org AS apply_org,
    t1.device_or_project_name AS device_or_project_name,
    t1.model_or_service_period AS model_or_service_period,
    t1.nums AS nums,
    t1.unit_price AS unit_price,
    t1.total_price AS total_price,
    t1.quotation1 AS quotation1,
    t1.quotation2 AS quotation2,
    t1.quotation3 AS quotation3,
    t1.meeting_nums AS meeting_nums,
    t1.meeting_unit_price AS meeting_unit_price,
    t1.meeting_total_price AS meeting_total_price,
    t1.approval_date AS approval_date,
    t1.meeting_date AS meeting_date,
    t1.LAUNCH_DEPT_CODE AS LAUNCH_DEPT_CODE,
    t1.export AS export,
    t1.approval_nums AS approval_nums,
    t1.approval_unit_price AS approval_unit_price,
    t1.approval_total_price AS approval_total_price,
    t1.STATUS AS STATUS,
    t1.WORKFLOW_NO AS WORKFLOW_NO,
    t1.BUSINESS_ID AS BUSINESS_ID,
    t1.WORKFLOW_NUMBER AS WORKFLOW_NUMBER,
    t1.CURRENT_STEP_NAME AS CURRENT_STEP_NAME,
    t1.CURRENT_STEP_NO AS CURRENT_STEP_NO,
    t1.sonId AS sonId,
    t1.IS_DELETED AS IS_DELETED,
    t1.CREATE_DATE AS CREATE_DATE,
    t1.UPDATE_DATE AS UPDATE_DATE,
    t2.SJCG_ZJE AS SJCG_ZJE,
    t3.is_purchase AS is_purchase
FROM (
         (
             SELECT
                 t1.WF_INSTANCE_ID AS wf_instance_id,
                 t1.WF_DEFINITION_ID AS wf_definition_id,
                 '医疗专用设备采购申请表' AS type,
                 YEAR(t3.sbsj8) AS apply_year,
                 YEAR(t3.sbsj8) AS effect_year,
                 '1' AS effect_years,
                 t2.zjly AS zjly,
                 t3.sqsw0 AS apply_org,
                 t2.xmmc AS device_or_project_name,
                 t2.ggxh AS model_or_service_period,
                 t2.sl AS nums,
                 t2.dj AS unit_price,
                 t2.zje AS total_price,
                 t2.xjqk1 AS quotation1,
                 t2.xjqk2 AS quotation2,
                 t2.xjqk3 AS quotation3,
                 t2.shsl AS meeting_nums,
                 t2.shdj AS meeting_unit_price,
                 t2.shje AS meeting_total_price,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS approval_date,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS meeting_date,
                 t1.LAUNCH_DEPT_CODE AS LAUNCH_DEPT_CODE,
                 t1.export_ps AS export,
                 t2.pfsl AS approval_nums,
                 t2.pfdj AS approval_unit_price,
                 t2.pfje AS approval_total_price,
                 t1.STATUS AS STATUS,
                 t1.WORKFLOW_NO AS WORKFLOW_NO,
                 t1.BUSINESS_ID AS BUSINESS_ID,
                 t1.WORKFLOW_NUMBER AS WORKFLOW_NUMBER,
                 t1.CURRENT_STEP_NAME AS CURRENT_STEP_NAME,
                 t1.CURRENT_STEP_NO AS CURRENT_STEP_NO,
                 t2.ID AS sonId,
                 t1.IS_DELETED AS IS_DELETED,
                 t1.CREATE_DATE AS CREATE_DATE,
                 t1.UPDATE_DATE AS UPDATE_DATE
             FROM
                 wf_instance_info t1
                 JOIN zt_ylzysbcgjhsqb t2 ON t1.WF_INSTANCE_ID = t2.WORKFLOW_ID
                 JOIN zdy_ylzysbcgjhsqb20240201113802 t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
             WHERE
                 t1.WF_DEFINITION_ID = '06985B07055046639923C386071BA66E'

             UNION ALL

             SELECT
                 t1.WF_INSTANCE_ID AS wf_instance_id,
                 t1.WF_DEFINITION_ID AS wf_definition_id,
                 '一般设备采购申请表' AS type,
                 YEAR(t3.sbsj7) AS apply_year,
                 YEAR(t3.sbsj7) AS effect_year,
                 '1' AS effect_years,
                 t2.zjly AS zjly,
                 t3.sqsw0 AS apply_org,
                 t2.xmmc AS device_or_project_name,
                 t2.ggxh AS model_or_service_period,
                 t2.sl AS nums,
                 t2.dj AS unit_price,
                 t2.zje AS total_price,
                 t2.xjqk1 AS quotation1,
                 t2.xjqk2 AS quotation2,
                 t2.xjqk3 AS quotation3,
                 t2.shsl AS meeting_nums,
                 t2.shdj AS meeting_unit_price,
                 t2.shje AS meeting_total_price,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS approval_date,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS meeting_date,
                 t1.LAUNCH_DEPT_CODE AS LAUNCH_DEPT_CODE,
                 t1.export_ps AS export,
                 t2.pfsl AS approval_nums,
                 t2.pfdj AS approval_unit_price,
                 t2.pfje AS approval_total_price,
                 t1.STATUS AS STATUS,
                 t1.WORKFLOW_NO AS WORKFLOW_NO,
                 t1.BUSINESS_ID AS BUSINESS_ID,
                 t1.WORKFLOW_NUMBER AS WORKFLOW_NUMBER,
                 t1.CURRENT_STEP_NAME AS CURRENT_STEP_NAME,
                 t1.CURRENT_STEP_NO AS CURRENT_STEP_NO,
                 t2.ID AS sonId,
                 t1.IS_DELETED AS IS_DELETED,
                 t1.CREATE_DATE AS CREATE_DATE,
                 t1.UPDATE_DATE AS UPDATE_DATE
             FROM
                 wf_instance_info t1
                 JOIN zt_ybsbcgjhsqb t2 ON t1.WF_INSTANCE_ID = t2.WORKFLOW_ID
                 JOIN zdy_ylybsbcgjhsbb20240201151026 t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
             WHERE
                 t1.WF_DEFINITION_ID = 'A37FCFB6631B405EA3A790E32FE88655'

             UNION ALL

             SELECT
                 t1.WF_INSTANCE_ID AS wf_instance_id,
                 t1.WF_DEFINITION_ID AS wf_definition_id,
                 '工程项目采购申请表' AS type,
                 YEAR(t3.sbsj1) AS apply_year,
                 YEAR(t3.sbsj1) AS effect_year,
                 '1' AS effect_years,
                 t2.zjly AS zjly,
                 t3.sbsw0 AS apply_org,
                 t2.xmmc AS device_or_project_name,
                 t2.jzmj AS model_or_service_period,
                 '1' AS nums,
                 t2.zje AS unit_price,
                 t2.zje AS total_price,
                 '' AS quotation1,
                 '' AS quotation2,
                 '' AS quotation3,
                 '1' AS meeting_nums,
                 t2.shje AS meeting_unit_price,
                 t2.shje AS meeting_total_price,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS approval_date,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS meeting_date,
                 t1.LAUNCH_DEPT_CODE AS LAUNCH_DEPT_CODE,
                 t1.export_ps AS export,
                 '1' AS approval_nums,
                 t2.pfje AS approval_unit_price,
                 t2.pfje AS approval_total_price,
                 t1.STATUS AS STATUS,
                 t1.WORKFLOW_NO AS WORKFLOW_NO,
                 t1.BUSINESS_ID AS BUSINESS_ID,
                 t1.WORKFLOW_NUMBER AS WORKFLOW_NUMBER,
                 t1.CURRENT_STEP_NAME AS CURRENT_STEP_NAME,
                 t1.CURRENT_STEP_NO AS CURRENT_STEP_NO,
                 t2.ID AS sonId,
                 t1.IS_DELETED AS IS_DELETED,
                 t1.CREATE_DATE AS CREATE_DATE,
                 t1.UPDATE_DATE AS UPDATE_DATE
             FROM
                 wf_instance_info t1
                 JOIN zt_gcxmcgjhsqb t2 ON t1.WF_INSTANCE_ID = t2.WORKFLOW_ID
                 JOIN zdy_jtgcxmcgjhsbb20240201153016 t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
             WHERE
                 t1.WF_DEFINITION_ID = 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0'

             UNION ALL

             SELECT
                 t1.WF_INSTANCE_ID AS wf_instance_id,
                 t1.WF_DEFINITION_ID AS wf_definition_id,
                 '服务项目采购申请表' AS type,
                 YEAR(t3.sbsj1) AS apply_year,
                 YEAR(t2.fwkssj) AS effect_year,
                 IFNULL(t2.pfsl, IFNULL(t2.shsl, t2.sl)) AS effect_years,
                 t2.zjly AS zjly,
                 t3.sbsw0 AS apply_org,
                 t2.xmmc AS device_or_project_name,
                 CONCAT(REPLACE(t2.fwkssj, '-', '.'), '-', REPLACE(t2.fwjssj, '-', '.')) AS model_or_service_period,
                 CEILING(CAST(t2.zje AS DECIMAL(10, 2)) / CAST(t2.fwnfdj AS DECIMAL(10, 2))) AS nums,
                 t2.fwnfdj AS unit_price,
                 t2.zje AS total_price,
                 '' AS quotation1,
                 '' AS quotation2,
                 '' AS quotation3,
                 CEILING(CAST(t2.shje AS DECIMAL(10, 2)) / CAST(t2.shdj AS DECIMAL(10, 2))) AS meeting_nums,
                 t2.shdj AS meeting_unit_price,
                 t2.shje AS meeting_total_price,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS approval_date,
                 DATE_FORMAT(STR_TO_DATE(t2.shrq, '%Y-%m-%d'), '%Y.%m.%d') AS meeting_date,
                 t1.LAUNCH_DEPT_CODE AS LAUNCH_DEPT_CODE,
                 t1.export_ps AS export,
                 CEILING(CAST(t2.pfje AS DECIMAL(10, 2)) / CAST(t2.pfdj AS DECIMAL(10, 2))) AS approval_nums,
                 t2.pfdj AS approval_unit_price,
                 t2.pfje AS approval_total_price,
                 t1.STATUS AS STATUS,
                 t1.WORKFLOW_NO AS WORKFLOW_NO,
                 t1.BUSINESS_ID AS BUSINESS_ID,
                 t1.WORKFLOW_NUMBER AS WORKFLOW_NUMBER,
                 t1.CURRENT_STEP_NAME AS CURRENT_STEP_NAME,
                 t1.CURRENT_STEP_NO AS CURRENT_STEP_NO,
                 t2.ID AS sonId,
                 t1.IS_DELETED AS IS_DELETED,
                 t1.CREATE_DATE AS CREATE_DATE,
                 t1.UPDATE_DATE AS UPDATE_DATE
             FROM
                 wf_instance_info t1
                 JOIN zt_fwxmcgjhsbb t2 ON t1.WF_INSTANCE_ID = t2.WORKFLOW_ID
                 JOIN zdy_fwxmcgjhsbb20240201155028 t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
             WHERE
                 t1.WF_DEFINITION_ID = '1866B74A6BC3400F990AB9E3C2DAA1A6'
         ) t1
             LEFT JOIN (
             SELECT
                 t1.id AS id,
                 SUM(t3.purchase_total_price) AS SJCG_ZJE
             FROM
                 toa_device_purchase_result t1
                     JOIN toa_device_purchase_group t2 ON t2.purchase_result_id = t1.id
                     JOIN toa_device_purchase_log t3 ON t3.purchase_group_id = t2.id
             GROUP BY
                 t1.id
         ) t2 ON t1.wf_instance_id = t2.id
             LEFT JOIN toa_device_purchase_result t3 on t1.wf_instance_id = t3.wf_instance_id
         );



/* 17:53:05 246 ts_base_oa */ ALTER TABLE `toa_device_department_budget` DROP INDEX `dept_id`;
/* 17:54:48 246 ts_base_oa */ ALTER TABLE `toa_device_department_budget` ADD INDEX `uni` (`dept_id`, `year`, `is_deleted`);
