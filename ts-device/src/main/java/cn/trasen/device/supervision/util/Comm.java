package cn.trasen.device.supervision.util;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;

import cn.trasen.device.supervision.bean.comm.ImportErrRow;
import cn.trasen.device.supervision.bean.comm.ImportResp;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.util
 * @className: Comm
 * @author: chenbin
 * @description: 驼峰转蛇形命名
 * @date: 2024/3/12 13:21
 * @version: 1.0
 */

public class Comm {
    public static String convertCamelToSnake(String camelCase) {
        StringBuilder snakeCase = new StringBuilder();

        for (int i = 0; i < camelCase.length(); i++) {
            char currentChar = camelCase.charAt(i);

            if (Character.isUpperCase(currentChar) && i > 0) {
                // 如果当前字符是大写字母且不是第一个字符，则在前面加下划线
                snakeCase.append("_");
            }

            snakeCase.append(Character.toLowerCase(currentChar));
        }

        return snakeCase.toString();
    }

    public static Date string2Date(String dateString) {
        String pattern = "yyyy-MM-dd"; // 日期格式

        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);

        try {
            Date date = dateFormat.parse(dateString); // 将字符串解析为Date对象
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    public static String dateFormate(Date date, String format) {
        try {
            DateFormat dateFormat = new SimpleDateFormat(format);
            return dateFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static String getCurDate(String tpl) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        if (tpl == null) {
            // 定义日期时间格式化器
            tpl = "yyyyMMdd";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(tpl);
        // 格式化当前时间为指定格式的字符串
        String formattedDate = currentDate.format(formatter);
        // 将年、月、日拼接成字符串

        return formattedDate;
    }

    public static String getAbsolutePath(String resourceName) {
        try {
            // 使用ClassPathResource加载资源文件
            Resource resource = new ClassPathResource(resourceName);
            File file = resource.getFile();
            return file.getAbsolutePath();
        } catch (IOException e) {
            // 处理异常，例如打印错误日志或抛出自定义异常
            e.printStackTrace();
            return null; // 或者抛出自定义异常，视情况而定
        }
    }

    /*
     * @param str:  字符串
     * @return Integer
     * <AUTHOR>
     * @description 字符串转整数
     * @date 2024/3/26 17:43
     */
    public static Integer String2Integer(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return 0;
        }
    }

    /*
     * @param input: 输入字符串
     * @return int
     * <AUTHOR>
     * @description 提取字符串中的第一个数字
     * @date 2024/3/26 17:43
     */
    public static int extractFirstNumber(String input) {
        StringBuilder sb = new StringBuilder();
        boolean foundDigit = false;

        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (Character.isDigit(ch)) {
                sb.append(ch);
                foundDigit = true;
            } else if (foundDigit) {
                // 如果已经找到数字，并且当前字符不是数字，则退出循环
                break;
            }
        }
        if (sb.length() == 0) {
            return 0;
        }

        return Integer.parseInt(sb.toString());
    }

    public static BigDecimal removeZero(BigDecimal num) {
        try {
            return new BigDecimal(num.stripTrailingZeros().toPlainString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }


    /**
     * @param str: 字符串
     * @return boolean
     * @description 判断字符串能否转成数字
     * @date 2024/3/26 17:43
     */
    public static boolean tryDoubleRet(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String divide(String a, String b, int scale) {
        String ret;
        try {
            ret = new BigDecimal(a).divide(new BigDecimal(b), scale, BigDecimal.ROUND_HALF_UP).toString();
        } catch (Exception e) {
            ret = new BigDecimal(0).divide(new BigDecimal(1), scale, BigDecimal.ROUND_HALF_UP).toString();
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * @param input:
     * @param pattern:
     * @return String
     * <AUTHOR>
     * @description 传入类似于2024-06 获取首末日期
     * @date 2024/6/19 16:10
     */
    public static String[] getMonthRange(String input, String pattern) {
        // Define the date formatter based on the input pattern
        DateTimeFormatter formatter;
        try {
            formatter = DateTimeFormatter.ofPattern(pattern);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date pattern: " + pattern, e);
        }

        YearMonth yearMonth;
        try {
            DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM");
            // Parse the input string to get the YearMonth object
            yearMonth = YearMonth.parse(input, formatter1);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date input: " + input, e);
        }

        // Get the first and last day of the month with time
        LocalDateTime firstDay = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime lastDay = yearMonth.atEndOfMonth().atTime(23, 59, 59);

        // Format the dates back to strings using the same pattern
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern);
        String firstDayStr = firstDay.format(outputFormatter);
        String lastDayStr = lastDay.format(outputFormatter);

        return new String[]{firstDayStr, lastDayStr};
    }

    /**
     * @param input:
     * @param pattern:
     * @return String
     * <AUTHOR>
     * @description 获取年的首末日期
     * @date 2024/6/20 10:04
     */

    public static String[] getYearRange(String input, String pattern) {
        // Define the date formatter based on the input pattern
        DateTimeFormatter formatter;
        try {
            formatter = DateTimeFormatter.ofPattern(pattern);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date pattern: " + pattern, e);
        }

        Year year;
        try {
            DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy");
            // Parse the input string to get the Year object
            year = Year.parse(input, yearFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date input: " + input, e);
        }

        // Get the first and last day of the year with time
        LocalDateTime firstDay = year.atDay(1).atStartOfDay();
        LocalDateTime lastDay = year.atDay(year.length()).atTime(23, 59, 59);

        // Format the dates back to strings using the same pattern

        String firstDayStr = firstDay.format(formatter);
        String lastDayStr = lastDay.format(formatter);

        return new String[]{firstDayStr, lastDayStr};
    }


    public static String trimZeros(String number) {

        try {
            BigDecimal bigDecimal = new BigDecimal(number).stripTrailingZeros();
            return bigDecimal.toPlainString(); // 返回去掉尾随零的字符串
        } catch (Exception e) {
            return number;
        }

    }

    public static String importResp2String(ImportResp importResp) {
        StringBuffer improtMsg = new StringBuffer();//导入提示文本
        improtMsg.append("成功处理" + importResp.getSuccs() + "条数据<br>");
        improtMsg.append("失败处理" + importResp.getErrs() + "条数据<br>");
        List<ImportErrRow> errRows = importResp.getErrRows();
        if (errRows != null && errRows.size() > 0) {
            for (ImportErrRow importErrRow : errRows) {
                improtMsg.append("第" + importErrRow.getIndex() + "行：" + importErrRow.getErrMsg() + "<br>");
            }
        }
        return improtMsg.toString();
    }

    public static BigDecimal object2BigDecimal(Object obj) {
        try {
            return new BigDecimal(obj.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }


    public static Integer object2Integer(Object obj) {
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception e) {
            return 0;
        }
    }
}
