package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.GetWfShInstanceListReq;
import cn.trasen.device.supervision.model.WfShInstanceInfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DeviceWfShInstanceInfoMapper extends Mapper<WfShInstanceInfo> {
    List<WfShInstanceInfo> getWfShInstanceList(Page page, GetWfShInstanceListReq req);

    List<WfShInstanceInfo> getWfShInstanceListByIdLIst(@Param("wfInstanceIdList") List<String> wfInstanceIdList);

    void updateExportStatus(@Param("wfInstanceIdList") List<String> wfInstanceIdList);
}