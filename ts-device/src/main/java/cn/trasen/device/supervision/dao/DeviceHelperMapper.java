package cn.trasen.device.supervision.dao;

import cn.trasen.device.supervision.bean.payslip.EmployeeReq;
import cn.trasen.device.supervision.bean.payslip.EmployeeResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.GetInstanceListReq;
import cn.trasen.device.supervision.bean.WfInstanceJump;
import cn.trasen.device.supervision.model.organization.Employee;
import cn.trasen.device.supervision.model.organization.Organization;
import org.apache.ibatis.annotations.Param;


import java.util.List;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.dao
 * @className: UserMapper
 * @author: chenbin
 * @description: TODO
 * @date: 2024/1/29 17:50
 * @version: 1.0
 */

public interface DeviceHelperMapper {
    List<Employee> getEmployeeListByOrgIdList(@Param("orgIdList") List<String> orgIdList);

    List<Organization> getOrganizationListByOrgId(@Param("orgId") String orgId);

    List<Organization> getOrganizationList();

    List<Organization> getOrganizationListToFrontSelect();

    Organization getOrganizationByOrgId(@Param("orgId") String orgId);

    Integer getNumsByStatus(@Param("userCode") String userCode, @Param("dataViewRights") List<String> dataViewRights, @Param("definitionId") String definitionId, @Param("status") String status, @Param("deptCode") String deptCode);

    /**
     * @param page:
     * <AUTHOR>
     * @description 请绝对确保key => value 的安全性
     * @date 2024/2/4 10:24
     */
    List<Map<String, Object>> getInstanceList(Page page, GetInstanceListReq getInstanceListReq);

    String getUserParttimeOrgId(@Param("employeeId") String employeeId);


    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 自动计算单价
     * @date 2024/7/3 16:23
     */
    void autoCalcuUnitPrice();

    /**
     * @param wfInstanceId:
     * @return WfInstanceJump
     * <AUTHOR>
     * @description 获取流程的关键信息，专门为跳转提供参数的
     * @date 2024/7/3 16:22
     */
    WfInstanceJump getWfInstanceJump(@Param("wfInstanceId") String wfInstanceId);

    /**
     * @param workflowNo:
     * @return String
     * <AUTHOR>
     * @description 获取流程的起始步骤ID
     * @date 2024/7/3 16:22
     */
    String getStartStepIdByWorkflowNo(String workflowNo);

    String getRestartTaskIdByWfInstanceId(String wfInstanceId);

    /**
     * @param employeeReq:
     * @return List<Employee>
     * <AUTHOR>
     * @description 根据组织机构代码获取人员
     * warning 这里的编制类型存在注入风险，谨慎调用，不可信任客户端传如的参数，仅内部使用
     * @date 2024/7/3 16:21
     */
    List<EmployeeResp> getEmployeeByDeptId(EmployeeReq employeeReq);
}
