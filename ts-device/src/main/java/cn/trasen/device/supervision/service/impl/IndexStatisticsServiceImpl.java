package cn.trasen.device.supervision.service.impl;

import cn.trasen.device.supervision.util.CacheHelper;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.CountsGroupByDeptIdResp;
import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.DepartmentBudget;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.service.impl
 * @className: IndexStatisticsServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/22 09:47
 * @version: 1.0
 */

@Service
public class IndexStatisticsServiceImpl implements IndexStatisticsService {


    @Autowired
    private DepartmentBudgetService departmentBudgetService;

    @Autowired
    private ProcureApplyService procureApplyService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    IncomeOriginService incomeOriginService;

    @Autowired
    DeviceHelperService deviceHelperService;

    @Autowired
    DeviceOperateLogService deviceOperateLogService;

    @Autowired
    CacheHelper cacheHelper;

    private static final long cacheTimeout = 300;

    public static String genCacheKey(BaseReq req) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String callerMethodName = stackTrace[2].getMethodName();
        return String.format("%s:%s:%s", callerMethodName, req.getDeptId(), req.getYear());
    }

    @Override
    public IndexBlockDBCYResp getIndexBlockDBCYResp(IndexBlockDBCYReq indexBlockDBCYReq) {

        String cacheKey = genCacheKey(indexBlockDBCYReq);

        if (!indexBlockDBCYReq.isIgnoreCache()) {
            IndexBlockDBCYResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockDBCYResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }

        IndexBlockDBCYResp indexBlockDBCYResp = new IndexBlockDBCYResp();

        String yssqsl = departmentBudgetService.yssqsl(indexBlockDBCYReq).toString();
        String yssqzje = departmentBudgetService.yssqzje(indexBlockDBCYReq).toString();
        String ysyspsl = departmentBudgetService.ysyspsl(indexBlockDBCYReq).toString();
        String ysyspzje = departmentBudgetService.ysyspzje(indexBlockDBCYReq).toString();

        String ysxmswcb = Comm.divide(ysyspsl, yssqsl, 4);
        String ysjewcb = Comm.divide(ysyspzje, yssqzje, 4);
        // ysdspsl = yssqsl - ysyspsl
        String ysdspsl = new BigDecimal(yssqsl).subtract(new BigDecimal(ysyspsl)).toString();
        String ysdspzje = new BigDecimal(yssqzje).subtract(new BigDecimal(ysyspzje)).toString();

        indexBlockDBCYResp.setYssqsl(yssqsl);
        indexBlockDBCYResp.setYssqzje(yssqzje);
        indexBlockDBCYResp.setYsyspsl(ysyspsl);
        indexBlockDBCYResp.setYsyspzje(ysyspzje);
        indexBlockDBCYResp.setYsxmswcb(ysxmswcb);
        indexBlockDBCYResp.setYsjewcb(ysjewcb);
        indexBlockDBCYResp.setYsdspsl(ysdspsl);
        indexBlockDBCYResp.setYsdspzje(ysdspzje);


        String cgsqsl = procureApplyService.cgsqsl(indexBlockDBCYReq).toString();
        String cgsqje = procureApplyService.cgsqje(indexBlockDBCYReq).toString();

        String shspsl = procureApplyService.shspsl(indexBlockDBCYReq).toString();
        String shspje = procureApplyService.shspje(indexBlockDBCYReq).toString();

        String dshspsl = procureApplyService.dshspsl(indexBlockDBCYReq).toString();
        String dshspje = procureApplyService.dshspje(indexBlockDBCYReq).toString();


        String dwhspsl = procureApplyService.dwhspsl(indexBlockDBCYReq).toString();
        String dwhspje = procureApplyService.dwhspje(indexBlockDBCYReq).toString();

        String ddwhspsl = procureApplyService.ddwhspsl(indexBlockDBCYReq).toString();
        String ddwhspje = procureApplyService.ddwhspje(indexBlockDBCYReq).toString();

        String shspbl = Comm.divide(shspsl, cgsqsl, 4);
        String dwhspbl = Comm.divide(dwhspsl, cgsqsl, 4);


        String dshspbl = Comm.divide(dshspsl, cgsqsl, 4);
        String ddwhspbl = Comm.divide(ddwhspsl, cgsqsl, 4);


        indexBlockDBCYResp.setCgsqsl(cgsqsl);
        indexBlockDBCYResp.setCgsqje(cgsqje);
        indexBlockDBCYResp.setShspsl(shspsl);
        indexBlockDBCYResp.setShspje(shspje);
        indexBlockDBCYResp.setDwhspsl(dwhspsl);
        indexBlockDBCYResp.setDwhspje(dwhspje);
        indexBlockDBCYResp.setShspbl(shspbl);
        indexBlockDBCYResp.setDwhspbl(dwhspbl);


        indexBlockDBCYResp.setDdwhspsl(ddwhspsl);
        indexBlockDBCYResp.setDdwhspje(ddwhspje);
        indexBlockDBCYResp.setDdwhspbl(ddwhspbl);

        indexBlockDBCYResp.setDshspsl(dshspsl);
        indexBlockDBCYResp.setDshspje(dshspje);
        indexBlockDBCYResp.setDshspbl(dshspbl);


        String zysblcsl = procureApplyService.zysblcsl(indexBlockDBCYReq).toString();
        String ybsblcsl = procureApplyService.ybsblcsl(indexBlockDBCYReq).toString();
        String gcxmlcsl = procureApplyService.gcxmlcsl(indexBlockDBCYReq).toString();
        String fwxmlcsl = procureApplyService.fwxmlcsl(indexBlockDBCYReq).toString();

        String zysblcyshsl = procureApplyService.zysblcyshsl(indexBlockDBCYReq).toString();
        String ybsblcyshsl = procureApplyService.ybsblcyshsl(indexBlockDBCYReq).toString();
        String gcxmlcyshsl = procureApplyService.gcxmlcyshsl(indexBlockDBCYReq).toString();
        String fwxmlcyshsl = procureApplyService.fwxmlcyshsl(indexBlockDBCYReq).toString();


        indexBlockDBCYResp.setZysblcdspsl(zysblcsl);
        indexBlockDBCYResp.setYbsblcdspsl(ybsblcsl);
        indexBlockDBCYResp.setGcxmlcdspsl(gcxmlcsl);
        indexBlockDBCYResp.setFwxmlcdspsl(fwxmlcsl);


        indexBlockDBCYResp.setZysblcyshsl(zysblcyshsl);
        indexBlockDBCYResp.setYbsblcyshsl(ybsblcyshsl);
        indexBlockDBCYResp.setGcxmlcyshsl(gcxmlcyshsl);
        indexBlockDBCYResp.setFwxmlcyshsl(fwxmlcyshsl);

        cacheHelper.set(cacheKey, indexBlockDBCYResp, cacheTimeout);

        return indexBlockDBCYResp;

    }

    @Override
    public IndexBlockYSCGResp getIndexBlockYSCGResp(IndexBlockYSCGReq indexBlockYSCGReq) {

        String cacheKey = genCacheKey(indexBlockYSCGReq);

        if (!indexBlockYSCGReq.isIgnoreCache()) {
            IndexBlockYSCGResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockYSCGResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }

        IndexBlockYSCGResp indexBlockYSCGResp = new IndexBlockYSCGResp();
        String cgspzs = procureApplyService.cgspzs(indexBlockYSCGReq).toString();
        String cgxmzs = procureApplyService.cgxmzs(indexBlockYSCGReq).toString();
        String wcgysxms = new BigDecimal(cgspzs).subtract(new BigDecimal(cgxmzs)).toString();
        String zysje = departmentBudgetService.zysje(indexBlockYSCGReq).toString();
        String ysyyje = departmentBudgetService.ysyyje(indexBlockYSCGReq).toString();
        String yssyed = new BigDecimal(zysje).subtract(new BigDecimal(ysyyje)).toString();

        // cgxmzs / cgspzs 转成 decimal 后进行处理
        String cgwcbl = Comm.divide(cgxmzs, cgspzs, 4);
        String yssyjd = Comm.divide(ysyyje, zysje, 4);

        indexBlockYSCGResp.setCgspzs(cgspzs);
        indexBlockYSCGResp.setCgxmzs(cgxmzs);
        indexBlockYSCGResp.setCgwcbl(cgwcbl);
        indexBlockYSCGResp.setWcgysxms(wcgysxms);
        indexBlockYSCGResp.setZysje(zysje);
        indexBlockYSCGResp.setYsyyje(ysyyje);
        indexBlockYSCGResp.setYssyjd(yssyjd);
        indexBlockYSCGResp.setYssyed(yssyed);

        cacheHelper.set(cacheKey, indexBlockYSCGResp, cacheTimeout);

        return indexBlockYSCGResp;
    }

    @Override
    public DataSet<DepartmentBudget> getIndexBlockYYYSSYQKResp(IndexBlockYYYSSYQKReq indexBlockYYYSSYQKReq) {

        DepartmentBudget departmentBudget = new DepartmentBudget();
        departmentBudget.setPageNo(indexBlockYYYSSYQKReq.getPageNo());
        departmentBudget.setPageSize(indexBlockYYYSSYQKReq.getPageSize());
        departmentBudget.setYear(indexBlockYYYSSYQKReq.getYear());
        departmentBudget.setDeptId(indexBlockYYYSSYQKReq.getDeptId());

        Page page = new Page();
        int pageNo = 1;

        if (indexBlockYYYSSYQKReq.getPageNo() != null) {
            pageNo = indexBlockYYYSSYQKReq.getPageNo();
        }

        int pageSize = 100;
        if (indexBlockYYYSSYQKReq.getPageSize() != null) {
            pageSize = indexBlockYYYSSYQKReq.getPageSize();
        }

        page.setPageNo(pageNo);
        page.setPageSize(pageSize);


        List<DepartmentBudget> list = departmentBudgetService.getDepartmentBudgetStatisticsList(page, departmentBudget);

        return new DataSet<DepartmentBudget>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    @Override
    public IndexBlockYYYSSYQKHZResp getIndexBlockYYYSSYQKHZResp(IndexBlockYYYSSYQKHZReq indexBlockYYYSSYQKHZReq) {

        String cacheKey = genCacheKey(indexBlockYYYSSYQKHZReq);

        if (!indexBlockYYYSSYQKHZReq.isIgnoreCache()) {
            IndexBlockYYYSSYQKHZResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockYYYSSYQKHZResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }

        IndexBlockYYYSSYQKHZResp indexBlockYYYSSYQKHZResp = new IndexBlockYYYSSYQKHZResp();

        if (indexBlockYYYSSYQKHZReq.getYear() == null || indexBlockYYYSSYQKHZReq.getDeptId() == null) {
            return indexBlockYYYSSYQKHZResp;
        }

        DepartmentBudget req = new DepartmentBudget();
        req.setYear(indexBlockYYYSSYQKHZReq.getYear());
        req.setDeptId(indexBlockYYYSSYQKHZReq.getDeptId());

        DepartmentBudget resp = departmentBudgetService.getDepartmentBudgetHZ(req);

        if (resp == null) {
            return indexBlockYYYSSYQKHZResp;
        }

        BeanUtils.copyProperties(resp, indexBlockYYYSSYQKHZResp);

        cacheHelper.set(cacheKey, indexBlockYYYSSYQKHZResp, cacheTimeout);

        return indexBlockYYYSSYQKHZResp;

    }


    public IndexBlockWDSQResp getIndexBlockWDSQResp(IndexBlockDBCYReq indexBlockDBCYReq) {

        String cacheKey = genCacheKey(indexBlockDBCYReq);

        if (!indexBlockDBCYReq.isIgnoreCache()) {
            IndexBlockWDSQResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockWDSQResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }

        IndexBlockWDSQResp indexBlockWDSQResp = new IndexBlockWDSQResp();

        String yssqsl = departmentBudgetService.yssqsl(indexBlockDBCYReq).toString();
        String yssqbhsl = departmentBudgetService.yssqbhsl(indexBlockDBCYReq).toString();
        String cgsqsl = procureApplyService.cgsqsl(indexBlockDBCYReq).toString();
        String cgsqbhsl = procureApplyService.cgsqbhsl(indexBlockDBCYReq).toString();

        indexBlockWDSQResp.setYssqsl(yssqsl);
        indexBlockWDSQResp.setYssqbhsl(yssqbhsl);
        indexBlockWDSQResp.setCgsqsl(cgsqsl);
        indexBlockWDSQResp.setCgsqbhsl(cgsqbhsl);

        cacheHelper.set(cacheKey, indexBlockWDSQResp, cacheTimeout);
        return indexBlockWDSQResp;
    }


    public IndexBlockDBSXResp getIndexBlockDBSXResp(IndexBlockDBSXReq indexBlockDBSXReq) {

        String cacheKey = genCacheKey(indexBlockDBSXReq);

        // 这个地方比较个性化，需要按照当前用户进行查询，而不是根据科室查询
        if (indexBlockDBSXReq.getUserCode() != null) {
            cacheKey = cacheKey + ":" + indexBlockDBSXReq.getUserCode();
        }

        if (!indexBlockDBSXReq.isIgnoreCache()) {
            IndexBlockDBSXResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockDBSXResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }

        IndexBlockDBSXResp indexBlockDBSXResp = new IndexBlockDBSXResp();

        String dspcgsl = procureApplyService.dspcgsl(indexBlockDBSXReq).toString();
        String dspdwhsl = procureApplyService.dspdwhsl(indexBlockDBSXReq).toString();
        String dspyssl = departmentBudgetService.dspyssl(indexBlockDBSXReq).toString();

        indexBlockDBSXResp.setDspcgsl(dspcgsl);
        indexBlockDBSXResp.setDspdwhsl(dspdwhsl);
        indexBlockDBSXResp.setDspyssl(dspyssl);

        cacheHelper.set(cacheKey, indexBlockDBSXResp, cacheTimeout);
        return indexBlockDBSXResp;
    }

    @Override
    public IndexBlockYYZBResp getIndexBlockYYZBResp(IndexBlockYYZBReq indexBlockYYZBReq) {

        String cacheKey = genCacheKey(indexBlockYYZBReq);

        if (!indexBlockYYZBReq.isIgnoreCache()) {
            IndexBlockYYZBResp cacheValue = cacheHelper.get2Object(cacheKey, IndexBlockYYZBResp.class);
            if (cacheValue != null) {
                return cacheValue;
            }
        }


        IndexBlockYYZBResp indexBlockYYZBResp = new IndexBlockYYZBResp();

        // 设备总数
        String sbzs = deviceService.sbzs(indexBlockYYZBReq).toString();
        indexBlockYYZBResp.setSbzs(sbzs);

        //年度总收入
        String ndzsr = incomeOriginService.sr(indexBlockYYZBReq).toString();

        // 年度支出
        String ndzzc = deviceOperateLogService.zc(indexBlockYYZBReq).toString();

        // 获取当前月份的开始日期和结束日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        String start = currentDate.withDayOfMonth(1).format(formatter) + "-01";
        String end = currentDate.withDayOfMonth(currentDate.lengthOfMonth()).format(formatter) + "-" + currentDate.lengthOfMonth();

        indexBlockYYZBReq.setStart(start);
        indexBlockYYZBReq.setEnd(end);
        // 月度总收入
        String ydzsr = incomeOriginService.sr(indexBlockYYZBReq).toString();

        indexBlockYYZBResp.setNdzsr(ndzsr);
        indexBlockYYZBResp.setNdzzc(ndzzc);
        indexBlockYYZBResp.setYdzsr(ydzsr);
        indexBlockYYZBResp.setYdzzc("0.00");

        cacheHelper.set(cacheKey, indexBlockYYZBResp, cacheTimeout);

        return indexBlockYYZBResp;
    }

    @Override
    public List<IndexBlockSBYDXYFXResp> getIndexBlockSBYDXYFXResp(IndexBlockSBYDXYFXReq indexBlockSBYDXYFXReq) {

        // 比如当前月份是2024-06 包括当前月份，组装12个 IndexBlockSBYDXYFXReq
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        List<IndexBlockSBYDXYFXResp> ret = new ArrayList<>();

        for (int i = 0; i < 12; i++) {

            LocalDate date = currentDate.minusMonths(i);
            String start = date.withDayOfMonth(1).format(formatter) + "-01";

            String end = date.withDayOfMonth(date.lengthOfMonth()).format(formatter) + "-" + date.lengthOfMonth();

            IndexBlockSBYDXYFXReq req = new IndexBlockSBYDXYFXReq();

            req.setStart(start);
            req.setEnd(end);

            String month = date.withDayOfMonth(1).format(formatter);

            IndexBlockSBYDXYFXResp retRow = incomeOriginService.sbydxyfx(req);
            retRow.setMonth(month);

            ret.add(retRow);

        }

        return ret;
    }

    @Override
    public List<IndexBlockYYSBNDXYFXResp> getIndexBlockYYSBNDXYFXResp(IndexBlockYYSBNDXYFXReq indexBlockYYSBNDXYFXReq) {

        // 按照医院编码获取所有的医院
        List<Organization> organizationList = deviceHelperService.getOrganizationListToFrontSelect();
        // 获取医院的设备数量
        List<CountsGroupByDeptIdResp> countsGroupByDeptIdRespList = deviceService.countsGroupByDeptId();
        // 按照医院ID => devices 组装一个map，容错重复医院ID


        Map<String, Integer> devicesMap = countsGroupByDeptIdRespList.stream().collect(Collectors.toMap(CountsGroupByDeptIdResp::getDeptId, CountsGroupByDeptIdResp::getDevices, (existingValue, newValue) -> newValue // 处理键冲突的合并函数
        ));
        // 获取医院收入和人次
        List<IndexBlockYYSBNDXYFXResp> indexBlockSBYDXYFXRespList = incomeOriginService.yysbndxyfx(indexBlockYYSBNDXYFXReq);

        Map<String, IndexBlockYYSBNDXYFXResp> indexBlockYYSBNDXYFXRespMap = indexBlockSBYDXYFXRespList.stream().collect(Collectors.toMap(IndexBlockYYSBNDXYFXResp::getDeptId, indexBlockYYSBNDXYFXResp -> indexBlockYYSBNDXYFXResp, (existingValue, newValue) -> newValue // 处理键冲突的合并函数
        ));
        // 组装数据

        List<IndexBlockYYSBNDXYFXResp> ret = new ArrayList<>();
        for (Organization org : organizationList) {
            IndexBlockYYSBNDXYFXResp retRow = new IndexBlockYYSBNDXYFXResp();
            retRow.setYymc(org.getName());
            retRow.setDeptId(org.getCode());

            if (indexBlockYYSBNDXYFXRespMap.containsKey(org.getCode())) {
                IndexBlockYYSBNDXYFXResp indexBlockYYSBNDXYFXResp = indexBlockYYSBNDXYFXRespMap.get(org.getCode());
                retRow.setIncomes(indexBlockYYSBNDXYFXResp.getIncomes());
                retRow.setTimes(indexBlockYYSBNDXYFXResp.getTimes());
            } else {
                retRow.setIncomes("0");
                retRow.setTimes("0");
            }
            if (devicesMap.containsKey(org.getCode())) {
                retRow.setDevices(devicesMap.get(org.getCode()).toString());
            } else {
                retRow.setDevices("0");
            }
            ret.add(retRow);
        }
        return ret;
    }

}
