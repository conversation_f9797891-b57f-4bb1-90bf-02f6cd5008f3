package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: DeviceParamListResp
 * @author: chenbin
 * @description: 设备参数库
 * @date: 2024/5/15 11:49
 * @version: 1.0
 */
@Data
public class DeviceParamResp extends Device {

    @Column(name = "tender_parameter")
    @ApiModelProperty(value = "招标参数")
    private String tenderParameter;



}
