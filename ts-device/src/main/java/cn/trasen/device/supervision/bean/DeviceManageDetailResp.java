package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.model.PurchaseLog;
import cn.trasen.device.supervision.model.PurchaseResult;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: DeviceManageDetailResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/16 12:01
 * @version: 1.0
 */

@Data
public class DeviceManageDetailResp extends DeviceManageSaveReq {
    private PurchaseResult purchaseResult;
}
