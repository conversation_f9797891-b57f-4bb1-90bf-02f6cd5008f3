package cn.trasen.device.supervision.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import cn.trasen.device.supervision.bean.JdGridTableEntity;
import cn.trasen.device.supervision.bean.WfInstanceBatchDeleteReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.WfShSubtotal;
import cn.trasen.device.supervision.bean.index.IndexBlockDBCYReq;
import cn.trasen.device.supervision.bean.index.IndexBlockDBSXReq;
import cn.trasen.device.supervision.bean.index.IndexBlockYSCGReq;
import cn.trasen.device.supervision.model.ProcureApply;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

public interface ProcureApplyService {

    Map<String, Object> getMyApplyHeadWfDefinitionInfo();

    List<Map<String, Object>> getApplyLeftWfDefinitionInfo(ProcureApply procureApply);

    List<Map<String, Object>> getMyApplyWfDefinitionInfo(Page page, ProcureApply record);

    List<JdGridTableEntity> getMyApplyTableHeadCols(ProcureApply record);

    Map<String, Object> getApproveHeadWfDefinitionInfo();

    List<Map<String, Object>> getApproveInfoList(Page page, ProcureApply record);

    List<Map<String, Object>> getConsultLeftWfDefinitionInfo();

    List<Map<String, Object>> getConsultWfDefinitionList(Page page, ProcureApply record);

    List<Map<String, Object>> getWfDefinitionInfo();

    void batchTravelReject(ProcureApply record);

    void batchExamination(ProcureApply record);

    void batchDelete(WfInstanceBatchDeleteReq record);

    List<Map<String, Object>> getStepInfoByWfDefinitionId(String wfDefinitionId);

    void appendSubtotalRow(Workbook workbook, Sheet sheet, WfShSubtotal wfShSubtotal);

    Integer cgsqsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer cgsqbhsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal cgsqje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer shspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal shspje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer dshspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal dshspje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer dwhspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal dwhspje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ddwhspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal ddwhspje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer zysblcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ybsblcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer gcxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer fwxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer zysblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ybsblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer gcxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer fwxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);


    Integer cgspzs(IndexBlockYSCGReq indexBlockYSCGReq);

    Integer cgxmzs(IndexBlockYSCGReq indexBlockYSCGReq);


    Integer dspcgsl(IndexBlockDBSXReq indexBlockDBSXReq);

    Integer dspdwhsl(IndexBlockDBSXReq indexBlockDBSXReq);

}
