package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.model.Project;
import cn.trasen.device.supervision.model.Services;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ProjectService
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
public interface ProjectService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer save(Project record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer update(Project record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer deleteById(String id);

    void deleteByPurchaseLogId(String purchaseLogId);

    /**
     * @return Project
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Project selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Project>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    DataSet<ProjectManageListResp> getDataSetList(Page page, Project record);

    void submit(ProjectManageSaveReq record);

    ProjectManageDetailResp detail(String id);

}
