package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.PurchaseGroupMapper;
import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.service.PurchaseGroupService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseGroupServiceImpl
 * @Description TODO
 * @date 2024年4月9日 上午10:58:37
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseGroupServiceImpl implements PurchaseGroupService {

    @Autowired
    private PurchaseGroupMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseGroup record) {
        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseGroup record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseGroup record = new PurchaseGroup();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteByPurchaseResultId(String purchaseResultId) {
        Example example = new Example(PurchaseGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("purchaseResultId", purchaseResultId);
        mapper.deleteByExample(example);
    }

    @Override
    public PurchaseGroup selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<PurchaseGroup> getDataSetList(Page page, PurchaseGroup record) {
        Example example = new Example(PurchaseGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<PurchaseGroup> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public List<PurchaseGroup> getListByPurchaseResultId(String purchaseResultId) {
        Example example = new Example(PurchaseGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("purchaseResultId", purchaseResultId);
        List<PurchaseGroup> records = mapper.selectByExample(example);
        return records;
    }
}
