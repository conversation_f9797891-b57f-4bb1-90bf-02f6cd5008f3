package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockSBYDXYFXReq
 * @author: chenbin
 * @description: 设备月度效益分析请求结构体
 * @date: 2024/6/18 16:31
 * @version: 1.0
 */

@Data
public class IndexBlockSBYDXYFXResp {

    @ApiModelProperty(value = "收入")
    private String incomes;

    @ApiModelProperty(value = "人次")
    private String times;

    @ApiModelProperty(value = "月份")
    private String month;

}
