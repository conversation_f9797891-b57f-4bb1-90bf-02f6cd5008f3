package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.ProjectManageListResp;
import cn.trasen.device.supervision.bean.ServicesManageListResp;
import cn.trasen.device.supervision.model.Project;
import cn.trasen.device.supervision.model.Services;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ServicesMapper extends Mapper<Services> {
    List<ServicesManageListResp> getDataSetList(Page page, Services services);
}