package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockYYZBReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/6 17:28
 * @version: 1.0
 */

public class IndexBlockYYZBReq extends BaseReq {

    @ApiModelProperty(value = "月份")
    private String month;

    public void setMonth(String month) {

        this.month = month;

        if (this.month == null || this.month.isEmpty()) {
            int currentMonth = LocalDateTime.now().getMonthValue();
            this.month = String.format("%02d", currentMonth);
        }

        this.setStart(this.getYear() + "-" + this.month + "-01");
        this.setEnd(this.getYear() + "-" + this.month + "-31");

    }

}
