package cn.trasen.device.supervision.bean.payslip;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean.payslip
 * @className: EmployeeReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/7/3 16:28
 * @version: 1.0
 */

@Data
public class EmployeeReq {
    @ApiModelProperty(value = "机构ID")
    private String deptId;

    @ApiModelProperty(value = "编制类型，多个值 用in查询")
    private String employeeCategory;
}
