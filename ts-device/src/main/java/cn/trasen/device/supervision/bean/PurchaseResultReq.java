package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.PurchaseResult;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseResultReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/9 11:55
 * @version: 1.0
 */

@Setter
@Getter
public class PurchaseResultReq extends PurchaseResult {
    List<PurchaseGroupReq> purchaseGroupReqList;
}
