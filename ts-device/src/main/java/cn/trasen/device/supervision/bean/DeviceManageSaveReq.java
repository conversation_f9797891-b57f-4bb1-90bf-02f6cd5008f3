package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: SaveDeviceReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/15 15:52
 * @version: 1.0
 */


@Data
public class DeviceManageSaveReq {

    @ApiModelProperty(value = "设备信息")
    private Device device;

    @ApiModelProperty(value = "采购信息")
    private PurchaseLog purchaseLog;

    @ApiModelProperty(value = "招标信息")
    private PurchaseGroup purchaseGroup;

    @ApiModelProperty(value = "收费编码")
    private List<FeeCode> feeCodeList;

    @ApiModelProperty(value = "维保信息")
    private List<DeviceMaintenanceLog> deviceMaintenanceLogList;

    @ApiModelProperty(value = "运营信息")
    private List<DeviceOperateLog> deviceOperateLogList;

    @ApiModelProperty(value = "维修信息")
    private List<DeviceRepairLog> deviceRepairLogList;


}
