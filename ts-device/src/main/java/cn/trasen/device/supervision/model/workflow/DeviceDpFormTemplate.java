package cn.trasen.device.supervision.model.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.model
 * @className: DpFormTemplate
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/2 16:24
 * @version: 1.0
 */
@Table(name = "DP_FORM_TEMPLATE")
@Setter
@Getter
public class DeviceDpFormTemplate {
    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 模板名称
     */
    @Column(name = "TEMPLATE_NAME")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @Column(name = "classify_id")
    private String classifyId;

    @Column(name = "classify_name")
    private String classifyName;

    /**
     * 模板类型   默认空,1 发文 2 收文 3 信息发布 4 会议室 5考勤汇总上报  9自定义
     */
    @Column(name = "template_type")
    private String templateType;

    /**
     * 模板code
     */
    @Column(name = "TEMPLATE_CODE")
    @ApiModelProperty(value = "模板code")
    private String templateCode;

    /**
     * 配置菜单id
     */
    @Column(name = "MENU_ID")
    @ApiModelProperty(value = "配置菜单id")
    private String menuId;

    @Column(name = "javascript_text")
    @ApiModelProperty(value = "自定义脚本")
    private String javascriptText;


    /**
     * 菜单名称
     */
    @Column(name = "MENU_NAME")
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /**
     * 配置流程编号
     */
    @Column(name = "WORKFLOW_ID")
    @ApiModelProperty(value = "配置流程编号")
    private String workflowId;
    /**
     * 流程名称
     */
    @Column(name = "WORKFLOW_NAME")
    @ApiModelProperty(value = "流程名称")
    private String workflowName;


    @Column(name = "form_template")
    private String formTemplate;
    /**
     * 配置数据库表id
     */
    @Column(name = "TABLE_ID")
    @ApiModelProperty(value = "配置数据库表id")
    private String tableId;


    @ApiModelProperty(value = "打印模板html")
    @Column(name = "PRINT_TEMPLATE")
    private String printTemplate;

    @ApiModelProperty(value = "打印模板JSON")
    @Column(name = "PRINT_TEMPLATE_JSON")
    private String printTemplateJson;

    /**
     * 表单提交验证地址
     */
    @Column(name = "VERIFCATION")
    @ApiModelProperty(value = "表单提交验证地址")
    private String verifcation;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "CREATE_USER_NAME")
    private String createUserName;

    @Column(name = "UPDATE_DATE")
    private Date updateDate;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

    @Column(name = "IS_DELETED")
    private String isDeleted;

    @Column(name = "ORG_CODE")
    private String orgCode;

    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * json格式数据
     */
    @Column(name = "CONTENT_JSON")
    @ApiModelProperty(value = "json格式数据")
    private String contentJson;

    @Column(name = "CONTENT")
    private String content;

    /**
     * 审批查询
     */
    @Column(name = "EXAMINE_QUERY")
    @ApiModelProperty(value = "审批查询")
    private String examineQuery;


    @Column(name = "table_name")
    private String tableName;

    @Column(name = "version")
    private Integer version;


    @Transient
    private String tableComment;

    /**
     * 默认查询条件
     */
    @Transient
    private String condition;

    /**
     * 字段设置
     */
    @Transient
    private List<DeviceToaFieldSet> toaFieldSetList;

    @Transient
    private String wfFormClassifyId;

    @Transient
    @ApiModelProperty(value = "分类类型：1-自定义分类；2-常规分类")
    private Integer classifyType;

    @Transient
    @ApiModelProperty(value = "是否关联流程 1-是；2-否")
    private Integer isHaveWf;

    @Transient
    @ApiModelProperty(value = "仅显示未停用流程表单")
    private String isDisableWf;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

}