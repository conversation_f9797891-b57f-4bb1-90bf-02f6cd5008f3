package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Project;
import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.model.PurchaseLog;
import cn.trasen.device.supervision.model.Services;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.xml.ws.Service;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: ProjectManageSaveReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/17 14:23
 * @version: 1.0
 */

@Data
public class ServicesManageSaveReq {
    @ApiModelProperty(value = "服务信息")
    private Services services;
    @ApiModelProperty(value = "采购信息")
    private PurchaseLog purchaseLog;
    @ApiModelProperty(value = "招标信息")
    private PurchaseGroup purchaseGroup;
}
