package cn.trasen.device.supervision.bean;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: GetInstanceListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/27 17:35
 * @version: 1.0
 */

@Data
public class GetInstanceListReq {

    private String definitionId;
    private String status;
    private String tableName;

    private Map<String, String> mainParams;
    private Map<String, String> dynamicParams;
    private List<String> dataViewRights;
    private String deptCode;
    
    private String orderBy;
    private String userCode;

}
