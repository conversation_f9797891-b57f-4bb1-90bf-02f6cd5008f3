package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_device_payslip_upload")
@Setter
@Getter
public class PayslipUpload {
    @Id
    private String id;

    /**
     * 医院ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "医院ID")
    private String deptId;

    private String month;

    /**
     * 0 编制内 1 编制外
     */
    @ApiModelProperty(value = "0 编制内 1 编制外")
    private String type;


    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;
}