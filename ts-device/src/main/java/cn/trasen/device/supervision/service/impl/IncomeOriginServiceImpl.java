package cn.trasen.device.supervision.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.DepartmentStatisticsLog;
import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.DeviceOperateLog;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import cn.trasen.device.supervision.util.JDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.IncomeOriginMapper;
import cn.trasen.device.supervision.model.IncomeOrigin;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName IncomeOriginServiceImpl
 * @Description TODO
 * @date 2024年6月17日 下午5:00:50
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class IncomeOriginServiceImpl implements IncomeOriginService {

    @Autowired
    private IncomeOriginMapper mapper;

    @Autowired
    private BusinessLockService businessLockService;

    @Autowired
    private OrganizationMapperService organizationMapperService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceOperateLogService deviceOperateLogService;

    @Autowired
    private DepartmentStatisticsLogService departmentStatisticsLogService;


    private Integer syncRowsPerTime = 1000;

    private Integer consumeRowsPerTime = 1000;

    @Transactional(readOnly = false)
    @Override
    public Integer save(IncomeOrigin record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(IncomeOrigin record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        IncomeOrigin record = new IncomeOrigin();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public IncomeOrigin selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<IncomeOrigin> getDataSetList(Page page, IncomeOrigin record) {
        Example example = new Example(IncomeOrigin.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<IncomeOrigin> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public Integer counts() {
        Example example = new Example(IncomeOrigin.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        return mapper.selectCountByExample(example);
    }


    @Transactional(readOnly = false)
    @Override
    public void consume() {
        String bizKey = "consume";
        String bizValue = "1";
        // 加业务锁
        log.info("=================消费收入数据任务执行开始==================");
        try {
            Boolean hasLocked = businessLockService.acquireLock(bizKey, bizValue, Duration.ofSeconds(600));
            // 被锁了 直接return 终止运行
            if (!hasLocked) {
                log.error("上一波的消费收入数据任务还未执行完，本次任务终止执行");
                return;
            }

            // 取未被消费的数据

            Page page = new Page();
            page.setPageNo(1);
            page.setPageSize(consumeRowsPerTime);

            Example example = new Example(IncomeOrigin.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("status", 0);
            criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            example.orderBy("cdate").desc();

            List<IncomeOrigin> records = mapper.selectByExampleAndRowBounds(example, page);

            if (records.size() <= 0) {
                log.error("当前没有需要消费的数据");
                return;
            }

            // 查询医院编码映射
            Map<String, String> YYBMExchangeMap = organizationMapperService.exchangeMap();

            for (IncomeOrigin incomeOrigin : records) {
                // 确认医院编码 设备编码
                if (incomeOrigin.getDeviceCode() == null) {
                    // 暂时不按照收费编码查询 直接放弃
                    incomeOrigin.setStatus("2");
                    incomeOrigin.setRemark("设备编码为空");
                    update(incomeOrigin);
                    continue;
                }
                // 否则根据deviceCode 查询设备
                Device device = deviceService.getByDeviceCode(incomeOrigin.getDeviceCode());
                if (device == null) {
                    incomeOrigin.setStatus("2");
                    incomeOrigin.setRemark("定位不到设备");
                    update(incomeOrigin);
                    continue;
                }

                // 确认医院编码
                String deptId = YYBMExchangeMap.get(incomeOrigin.getHospitalCode());
                String deviceId = device.getId();

                incomeOrigin.setDeptId(deptId);
                incomeOrigin.setDeviceId(deviceId);
                incomeOrigin.setStatus("1");
                update(incomeOrigin);

            }


        } catch (Exception e) {
            log.error("消费收入数据任务执行失败", e);
        } finally {
            businessLockService.releaseLock(bizKey, bizValue);
            log.info("=================消费收入数据任务执行结束==================");
        }
    }

    @Override
    @Transactional(readOnly = false)
    public void sync() {

        String bizKey = "syncIncome";
        String bizValue = "1";
        // 加业务锁
        log.info("=================同步收入数据任务执行开始==================");
        try {
            Boolean hasLocked = businessLockService.acquireLock(bizKey, bizValue, Duration.ofSeconds(120));
            // 被锁了 直接return 终止运行
            if (!hasLocked) {
                log.error("上一波的同步收入数据任务还未执行完，本次任务终止执行");
                return;
            }
            // 获取当前origin的行数
            Integer counts = counts();


            String sql = "SELECT\n" + "    CAST([医院编码] AS VARCHAR(50)) AS [hospitalCode],\n" + "    CAST([收费编码] AS VARCHAR(50)) AS [chargeCode],\n" + "    CAST([设备编码] AS VARCHAR(50)) AS [deviceCode],\n" + "    CAST([项目编码] AS VARCHAR(50)) AS [itemCode],\n" + "    CAST([项目名称] AS VARCHAR(100)) AS [itemLabel],\n" + "    CAST([个人唯一识别ID] AS VARCHAR(50)) AS [userCode],\n" + "    CAST([单位] AS VARCHAR(50)) AS [unit],\n" + "    CAST([NUM] AS INT) AS [counts],\n" + "    CAST([单价] AS DECIMAL(10,2)) AS [costs],\n" + "    CAST([总金额] AS DECIMAL(10,2)) AS [total],\n" + "    [费用时间]AS [cdate]\n" + "FROM [trasen_jhrmyy].[dbo].[INCOME_4_DM]\n" + "ORDER BY [费用时间] ASC\n" + "OFFSET %d ROWS\n" + "FETCH NEXT %d ROWS ONLY;";

            sql = String.format(sql, counts, syncRowsPerTime);

            log.info(sql);
            List<IncomeOrigin> result = JDBCUtils.query(sql, IncomeOrigin.class);

            if (result == null || result.size() <= 0) {
                return;
            }

            // 生成ID 和 创建时间 状态
            result.forEach(item -> {
                item.setId(IdGeneraterUtils.nextId());
                item.setCreateDate(new Date());
                item.setUpdateDate(new Date());
                item.setIsDeleted("N");
                item.setDeviceCode(randDeviceCode());
                // 先暂时固定柏加镇卫生院
                item.setHospitalCode("44501355X43018112C22");
                ThpsUser user = UserInfoHolder.getCurrentUserInfo();
                if (user != null) {
                    item.setCreateUser(user.getUsercode());
                    item.setCreateUserName(user.getUsername());
                    item.setUpdateUser(user.getUsercode());
                    item.setUpdateUserName(user.getUsername());
                    item.setSsoOrgCode(user.getCorpcode());
                    item.setSsoOrgName(user.getOrgName());
                }
                item.setStatus("0");
            });

            if (result.size() <= 0) {
                return;
            }

            mapper.insertBatch(result);
            log.info(String.format("同步收入数据任务执行成功，共同步了%d条数据", result.size()));

        } catch (Exception e) {
            log.error("同步收入数据任务执行失败", e);
        } finally {
            businessLockService.releaseLock(bizKey, bizValue);
            log.info("=================同步收入数据任务执行结束==================");
        }
    }

    /**
     * @param req:
     * @return List<IndexBlockYYSBNDXYFXResp>
     * <AUTHOR>
     * @description 医院设备年度效益分析
     * @date 2024/6/18 18:44
     */
    @Override
    public List<IndexBlockYYSBNDXYFXResp> yysbndxyfx(IndexBlockYYSBNDXYFXReq req) {
        return mapper.yysbndxyfx(req);
    }

    /**
     * @param req:
     * @return IndexBlockSBYDXYFXResp
     * <AUTHOR>
     * @description 设备月度效益分析（实际上看传的时间跨度，查检查人数和收入）
     * @date 2024/6/18 18:44
     */

    @Override
    public IndexBlockSBYDXYFXResp sbydxyfx(IndexBlockSBYDXYFXReq req) {
        return mapper.sbydxyfx(req);
    }

    /**
     * @param indexBlockYYZBReq:
     * @return Integer
     * <AUTHOR>
     * @description 查询收入数据
     * @date 2024/6/19 11:42
     */
    @Override
    public BigDecimal sr(IndexBlockYYZBReq indexBlockYYZBReq) {
        return mapper.sr(indexBlockYYZBReq);
    }

    /**
     * @param deviceId:
     * @param month:
     * @return void
     * <AUTHOR>
     * @description 收入数据按照设备和月份拆分写入到运营数据表
     * @date 2024/6/19 17:18
     */

    @Transactional(readOnly = false)
    @Override
    public void incomeDataSplit(String deviceId, String month) {


        IndexBlockSBYDXYFXReq indexBlockSBYDXYFXReq = new IndexBlockSBYDXYFXReq();
        String[] range;
        try {
            range = Comm.getMonthRange(month, "yyyy-MM-dd");
        } catch (Exception e) {
            log.error(e.getMessage());
            return;
        }
        // 这里不需要容错，只要没抛异常 则range 必定有值
        indexBlockSBYDXYFXReq.setStart(range[0]);
        indexBlockSBYDXYFXReq.setEnd(range[1]);
        indexBlockSBYDXYFXReq.setDeviceId(deviceId);

        IndexBlockSBYDXYFXResp indexBlockSBYDXYFXResp = sbydxyfx(indexBlockSBYDXYFXReq);

        // 检查数据是否已经存在 如果存在就更新 如果不存在就新增一行
        DeviceOperateLog deviceOperateLog = new DeviceOperateLog();
        deviceOperateLog.setDeviceId(deviceId);


        BigDecimal incomes;
        Short checks;

        try {
            incomes = new BigDecimal(indexBlockSBYDXYFXResp.getIncomes());
            checks = Short.valueOf(indexBlockSBYDXYFXResp.getTimes());

        } catch (Exception e) {
            incomes = new BigDecimal(BigInteger.ZERO);
            checks = 0;
        }


        deviceOperateLog.setMonth(month);
        DeviceOperateLog exsit = deviceOperateLogService.checkRow(deviceOperateLog);
        if (exsit == null) {
            deviceOperateLog.setIncomes(incomes);
            deviceOperateLog.setChecks(checks);
            deviceOperateLog.setGranularity("month");
            deviceOperateLogService.save(deviceOperateLog);
        } else {
            exsit.setIncomes(incomes);
            exsit.setChecks(checks);
            deviceOperateLogService.update(exsit);
        }

    }

    @Transactional(readOnly = false)
    @Override
    public void departmentOperation(String deptId, String year) {
        IndexBlockYYZBReq indexBlockYYZBReq = new IndexBlockYYZBReq();
        String[] range;
        try {
            range = Comm.getYearRange(year, "yyyy-MM-dd");
        } catch (Exception e) {
            log.error(e.getMessage());
            return;
        }
        String start = range[0];
        String end = range[1];

        indexBlockYYZBReq.setYear(year);
        indexBlockYYZBReq.setStart(start);
        indexBlockYYZBReq.setEnd(end);
        indexBlockYYZBReq.setDeptId(deptId);

        // 设备总数
        Integer devices = deviceService.sbzs(indexBlockYYZBReq);

        IndexBlockSBYDXYFXReq indexBlockSBYDXYFXReq = new IndexBlockSBYDXYFXReq();

        indexBlockSBYDXYFXReq.setStart(start);
        indexBlockSBYDXYFXReq.setEnd(end);
        indexBlockSBYDXYFXReq.setDeptId(deptId);

        IndexBlockSBYDXYFXResp indexBlockSBYDXYFXResp = sbydxyfx(indexBlockSBYDXYFXReq);

        // 设备收入
        BigDecimal incomes = new BigDecimal(indexBlockSBYDXYFXResp.getIncomes());

        // 设备检查次数
        Integer checks = Integer.valueOf(indexBlockSBYDXYFXResp.getTimes());

        // 总支出
        BigDecimal costs = deviceOperateLogService.zc(indexBlockYYZBReq);

        // 收支结余
        BigDecimal surplus = incomes.subtract(costs);

        DepartmentStatisticsLog departmentStatisticsLog = departmentStatisticsLogService.checkRow(deptId, year);

        if (departmentStatisticsLog != null) {
            departmentStatisticsLog.setDevices(devices);
            departmentStatisticsLog.setIncomes(incomes);
            departmentStatisticsLog.setChecks(checks);
            departmentStatisticsLog.setCosts(costs);
            departmentStatisticsLog.setSurplus(surplus);
            departmentStatisticsLogService.update(departmentStatisticsLog);
        } else {
            departmentStatisticsLog = new DepartmentStatisticsLog();
            departmentStatisticsLog.setDeptId(deptId);
            departmentStatisticsLog.setGranularity("year");
            departmentStatisticsLog.setMonth(year);
            departmentStatisticsLog.setDevices(devices);
            departmentStatisticsLog.setIncomes(incomes);
            departmentStatisticsLog.setChecks(checks);
            departmentStatisticsLog.setCosts(costs);
            departmentStatisticsLog.setSurplus(surplus);
            departmentStatisticsLogService.save(departmentStatisticsLog);
        }
    }


    private String randDeviceCode() {
        // 获取当前日期
        String[] deviceCodes = {"S0001", "S0002", null, "S0003", "S0004", "S0005", "S0006", "S0007", null};
        // 随机取一个
        return deviceCodes[(int) (Math.random() * deviceCodes.length)];
    }

}
