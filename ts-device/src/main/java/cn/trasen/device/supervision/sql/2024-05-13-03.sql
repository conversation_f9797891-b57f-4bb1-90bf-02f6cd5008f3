CREATE TABLE `toa_device_category` (
                                       `id` varchar(50) NOT NULL COMMENT '主键ID',
                                       `code` varchar(50) DEFAULT NULL COMMENT '分类编码',
                                       `name` varchar(50) DEFAULT NULL COMMENT '分类名称',
                                       `parent_id` varchar(36) DEFAULT NULL COMMENT '父类ID',
                                       `parent_code` varchar(36) DEFAULT NULL COMMENT '父类',
                                       `tree_ids` varchar(500) DEFAULT NULL COMMENT '树ID',
                                       `is_enable` char(1) DEFAULT '1' COMMENT '是否启用: 1=是; 2=否;',
                                       `level` int DEFAULT NULL COMMENT '树结构中级别',
                                       `devices` int DEFAULT '0' COMMENT '设备数量',
                                       `seq_no` int DEFAULT NULL COMMENT '排序',
                                       `remark` text COMMENT '备注',
                                       `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                       `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                       `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                       `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                                       `update_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称',
                                       `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                       `sso_org_code` varchar(50) DEFAULT NULL,
                                       `sso_org_name` varchar(50) DEFAULT NULL,
                                       `is_deleted` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除标示',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT COMMENT='设备分类表';