CREATE TABLE `toa_device_device` (
                                     `id` varchar(36) NOT NULL DEFAULT '',
                                     `dept_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属机构ID',
                                     `purchase_log_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购结果ID',
                                     `device_code` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备编码',
                                     `sys_code` varchar(36) DEFAULT NULL COMMENT '系统编码',
                                     `asset_code` varchar(36) DEFAULT NULL COMMENT '资产编码',
                                     `common_name` varchar(36) DEFAULT NULL COMMENT '设备通用名',
                                     `ybsf_code` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '医保收费编码',
                                     `cate_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分类ID',
                                     `type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '0 一般设备 1 专用设备',
                                     `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备名称',
                                     `supplier` varchar(100) DEFAULT NULL COMMENT '供货单位名称',
                                     `producer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产厂家 ｜ 供应商 ｜服务商｜承建单位',
                                     `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品牌',
                                     `spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '型号',
                                     `funds_sources` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资金来源',
                                     `unit_price` varchar(20) DEFAULT NULL COMMENT '单价',
                                     `files` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备文档，多个逗号分开',
                                     `is_complete` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '0 未完善 1 已完善',
                                     `status` char(1) DEFAULT NULL COMMENT '设备状态 ',
                                     `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                     `create_date` datetime DEFAULT NULL,
                                     `create_user` varchar(50) DEFAULT NULL,
                                     `create_user_name` varchar(50) DEFAULT NULL,
                                     `update_date` datetime DEFAULT NULL,
                                     `update_user` varchar(50) DEFAULT NULL,
                                     `update_user_name` varchar(50) DEFAULT NULL,
                                     `sso_org_name` varchar(50) DEFAULT NULL,
                                     `is_deleted` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                     `sso_org_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `sys_code` (`sys_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


CREATE TABLE `toa_device_project` (
                                      `id` varchar(36) NOT NULL DEFAULT '',
                                      `purchase_log_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购结果ID',
                                      `dept_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属机构ID',
                                      `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目名称',
                                      `build_scale` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '建设规模',
                                      `investment_scale` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '投资规模',
                                      `accept_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '竣工验收备案时间',
                                      `funds_sources` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资金来源',
                                      `unit_price` varchar(20) DEFAULT NULL COMMENT '单价',
                                      `files` varchar(128) DEFAULT NULL COMMENT '相关文件',
                                      `is_complete` char(1) DEFAULT NULL COMMENT '0 未完善 1已完善',
                                      `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                      `create_date` datetime DEFAULT NULL,
                                      `create_user` varchar(50) DEFAULT NULL,
                                      `create_user_name` varchar(50) DEFAULT NULL,
                                      `update_date` datetime DEFAULT NULL,
                                      `update_user` varchar(50) DEFAULT NULL,
                                      `update_user_name` varchar(50) DEFAULT NULL,
                                      `is_deleted` char(1) DEFAULT NULL,
                                      `sso_org_code` varchar(50) DEFAULT NULL,
                                      `sso_org_name` varchar(50) DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


CREATE TABLE `toa_device_services` (
                                       `id` varchar(36) NOT NULL DEFAULT '',
                                       `purchase_log_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购结果ID',
                                       `dept_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '组织ID',
                                       `name` varchar(200) DEFAULT NULL COMMENT '服务名称',
                                       `service_start_at` date DEFAULT NULL COMMENT '服务开始时间',
                                       `service_end_at` date DEFAULT NULL COMMENT '服务结束时间',
                                       `funds_sources` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资金来源',
                                       `unit_price` varchar(20) DEFAULT NULL COMMENT '单价',
                                       `total_price` varchar(20) DEFAULT NULL COMMENT '总价',
                                       `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                       `files` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '相关文件',
                                       `is_complete` char(1) DEFAULT NULL COMMENT '是否完善',
                                       `create_date` datetime DEFAULT NULL,
                                       `create_user` varchar(50) DEFAULT NULL,
                                       `create_user_name` varchar(50) DEFAULT NULL,
                                       `update_date` datetime DEFAULT NULL,
                                       `update_user` varchar(50) DEFAULT NULL,
                                       `update_user_name` varchar(50) DEFAULT NULL,
                                       `is_deleted` char(1) DEFAULT NULL,
                                       `sso_org_code` varchar(50) DEFAULT NULL,
                                       `sso_org_name` varchar(50) DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
