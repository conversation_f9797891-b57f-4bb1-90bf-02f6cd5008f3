package cn.trasen.device.supervision.model;

import java.util.List;

import cn.trasen.device.supervision.util.Comm;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ProcureApply {

    private String orgName;

    private String applyYear;

    private String applyOrg;

    private String wfDefinitionId;

    private String tableName;

    private String childBusiness;

    private String deviceName;

    private String totalPriceMin;

    private String totalPriceMax;

    private String userCode;

    private String workflowStatus; //流程状态  approval审批中、draft 草稿箱、back 已退回、complete 已完结  inApproval 待我办理  approved 我已办理   copy 抄送给我

    private String table;

    private String field1;

    private String field2;

    private String fj1;

    private String fj2;

    private String fj3;

    private String assigneeNo;

    private List<String> deptCodes;

    private String handleMarkedWords;

    private String taskId;

    private List<ApproveData> approveData;

    private String handleAllottedTime;

    private String names;

    private String users;

    private String remark;

    private String urgencyLevel;

    private String currentStepId;

    private String wfStepId;

    private String wfStepName;

    private String sidx;

    private String sord;

    private Integer wfStatus;

    private String startDateAt;

    private String endDateAt;

    // 用于导出
    private String exportId;

    // 用于导出
    private List<String> exportIdList;

    public void setTotalPriceMin(String totalPriceMin) {
        // 防止接口不正常数据插入 防止注入安全事故
        if (Comm.tryDoubleRet(totalPriceMin) == false) {
            return;
        }
        this.totalPriceMin = totalPriceMin;
    }

    public void setTotalPriceMax(String totalPriceMax) {
        // 防止接口不正常数据插入 防止注入安全事故
        if (Comm.tryDoubleRet(totalPriceMax) == false) {
            return;
        }
        this.totalPriceMax = totalPriceMax;
    }
}
