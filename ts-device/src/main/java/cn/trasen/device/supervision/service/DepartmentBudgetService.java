package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.DepartmentBudget;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DepartmentBudgetService {

    DepartmentBudget getDepartmentBudget(String deptCode);

    BigDecimal calcuDepartmentBudgetCosted(String deptCode, String type);

    void calcuDepartmentBudgetCosted(PurchaseInstanceInfoListReq purchaseInstanceInfoListReq);

    void calcuDepartmentBudgetGetted(BudgetInstanceInfoListReq budgetInstanceInfoListReq);

    Boolean noCheckBudget(String deptCode);

    BigDecimal calcuBudgetCost(WfFormData wfFormData);

    WfInstanceInfoBudgetCostResp getWfInstanceInfo(String wfInstanceId, ProcureConfigInDict procureConfigInDict);

    void calculateProcureApplyPrice();

    List<DepartmentBudget> getDepartmentBudgetStatisticsList(Page page, DepartmentBudget record);

    List<Map<String, Object>> getDepartmentBudgetStatisticsDetailsList(Page page, PurchaseInstanceInfoListReq purchaseInstanceInfoListReq);

    Integer yssqsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer yssqbhsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal yssqzje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ysyspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal ysyspzje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal zysje(IndexBlockYSCGReq indexBlockDBCYReq);

    BigDecimal ysyyje(IndexBlockYSCGReq indexBlockDBCYReq);

    Integer dspyssl(IndexBlockDBSXReq indexBlockDBCYReq);

    DepartmentBudget getDepartmentBudgetHZ(DepartmentBudget departmentBudget);


}
