package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Device;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Transient;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: GetdeviceProcureLogResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/21 20:25
 * @version: 1.0
 */

@Setter
@Getter
public class GetdeviceProcureLogReq extends Device {


    @ApiModelProperty(value = "经销商")
    private String jxs;


    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;
}
