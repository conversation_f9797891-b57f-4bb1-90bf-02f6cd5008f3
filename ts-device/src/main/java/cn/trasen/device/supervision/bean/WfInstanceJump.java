package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: WfInstanceJump
 * @author: chenbin
 * @description: 专门用于提供流程跳转的参数
 * @date: 2024/5/27 10:53
 * @version: 1.0
 */
@Data
public class WfInstanceJump {
    @ApiModelProperty(value = "流程ID")
    private String wfInstanceId;
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;
    @ApiModelProperty(value = "流程状态")
    private String wfStatus;
    @ApiModelProperty(value = "流程编号")
    private String wfWorkflowNo;
    @ApiModelProperty(value = "业务ID")
    private String wfBusinessId;
    @ApiModelProperty(value = "任务ID")
    private String wfTaskId;
    @ApiModelProperty(value = "流程序号")
    private String wfWorkflowNumber;
}
