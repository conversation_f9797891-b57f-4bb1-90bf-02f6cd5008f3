package cn.trasen.device.supervision.bean;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.Year;
import java.time.format.DateTimeFormatter;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: WfInstanceInfoBudgetCostReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/5 11:36
 * @version: 1.0
 */
@Getter
@Setter
public class WfInstanceInfoBudgetCostReq {
    // 定位机构
    private String deptCode;
    private String deptName;
    // 定位时间
    // 这里很可能会按照业务需求变化 但是他一定是时间节点
    private String dateStart; // 开始时间
    private String dateEnd; // 结束时间

    private String definitionId; // 流程定义ID
    private String type; // 采购类型
    private String tableName; // 表名
    private String quotaFieldName; // 额度名称
    private String sourcesOfFundsFieldName; // 资金来源字段名
    private String tableNameP; // 表名 父表单
    private String applyDeptNameFieldName; // 申请部门字段名

    public void initProp(String configString) {

        if (configString == null) {
            throw new IllegalArgumentException("Input string cannot be null");
        }

        // Split the input string using '-' as the delimiter
        String[] parts = configString.split("-");

        // Check if the array has at least 4 elements
        if (parts.length >= 7) {
            // Assign values to the class attributes
            this.definitionId = parts[0];
            this.type = parts[1];
            this.tableName = parts[2];
            this.quotaFieldName = parts[3];
            this.sourcesOfFundsFieldName = parts[4];
            this.tableNameP = parts[5];
            this.applyDeptNameFieldName = parts[6];
        } else {
            // Handle the case where the input string doesn't have enough parts
            throw new IllegalArgumentException("Invalid input string format. Expected format: 'definitionId-type-tableName-quotaFieldName-sourcesOfFundsFieldName'");
        }

        // 获取当前年份
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = LocalDateTime.of(now.getYear(), 1, 1, 0, 0, 0);
        this.dateStart = formatDatetime(start);
        LocalDateTime end = LocalDateTime.of(now.getYear(), 12, 31, 23, 59, 59);
        this.dateEnd = formatDatetime(end);
    }

    private String formatDatetime(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }
}
