package cn.trasen.device.supervision.dao;

import cn.trasen.device.supervision.bean.comm.KvResp;
import cn.trasen.device.supervision.model.YearMultiReport;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import io.lettuce.core.dynamic.annotation.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface YearMultiReportMapper extends Mapper<YearMultiReport> {

    // 申报预算金额
    List<KvResp> getSbysje(@Param("year") String year);

    //通过审核项目个数
    List<KvResp> getTgshxmgs(@Param("year") String year);

    //通过审核预算金额
    List<KvResp> getTgshysje(@Param("year") String year);

    //审核降低预算金额
    List<KvResp> getShjdysje(@Param("year") String year);

    //审核不通过项目个数
    List<KvResp> getShbtgxmgs(@Param("year") String year);

    // 审核不通过预算金额
    List<KvResp> getShbtgysje(@Param("year") String year);

    List<YearMultiReport> getList(Page page, YearMultiReport yearMultiReport);

    List<YearMultiReport> getListNoPage(YearMultiReport yearMultiReport);

}