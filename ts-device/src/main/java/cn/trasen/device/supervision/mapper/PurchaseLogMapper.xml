<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.PurchaseLogMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.PurchaseLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="purchase_group_id" jdbcType="VARCHAR" property="purchaseGroupId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="purchase_date" jdbcType="DATE" property="purchaseDate"/>
        <result column="supplier" jdbcType="VARCHAR" property="supplier"/>
        <result column="producer" jdbcType="VARCHAR" property="producer"/>
        <result column="purchase_brand" jdbcType="VARCHAR" property="purchaseBrand"/>
        <result column="purchase_spec" jdbcType="VARCHAR" property="purchaseSpec"/>
        <result column="purchase_way" jdbcType="VARCHAR" property="purchaseWay"/>
        <result column="build_scale" jdbcType="VARCHAR" property="buildScale"/>
        <result column="accept_date" jdbcType="VARCHAR" property="acceptDate"/>
        <result column="purchase_numbers" jdbcType="VARCHAR" property="purchaseNumbers"/>
        <result column="purchase_price" jdbcType="VARCHAR" property="purchasePrice"/>
        <result column="purchase_total_price" jdbcType="VARCHAR" property="purchaseTotalPrice"/>
        <result column="funds_sources" jdbcType="VARCHAR" property="fundsSources"/>
        <result column="service_start_at" jdbcType="DATE" property="serviceStartAt"/>
        <result column="service_end_at" jdbcType="DATE" property="serviceEndAt"/>
        <result column="purchase_files" jdbcType="VARCHAR" property="purchaseFiles"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted"/>
        <result column="purchase_remark" jdbcType="LONGVARCHAR" property="purchaseRemark"/>
    </resultMap>
    <select id="getListByPurchaseResultId" resultType="cn.trasen.device.supervision.model.PurchaseLog">
        select *
        from toa_device_purchase_log t1
                 left join toa_device_purchase_group t2 on t1.purchase_group_id = t2.id and t2.is_deleted = 'N'
                 left join toa_device_purchase_result t3 on t2.purchase_result_id = t3.id and t3.is_deleted = 'N'
        where t3.id = #{purchaseResultId}
        order by t1.purchase_date desc
    </select>
    <select id="getListByPurchaseResultIdList" resultType="cn.trasen.device.supervision.model.PurchaseLog">
        select t1.*,t2.purchase_result_id
        from toa_device_purchase_log t1
        left join toa_device_purchase_group t2 on t1.purchase_group_id = t2.id and t2.is_deleted = 'N'
        left join toa_device_purchase_result t3 on t2.purchase_result_id = t3.id and t3.is_deleted = 'N'
        <choose>
            <when test="purchaseResultIdList != null and purchaseResultIdList.size() > 0">
                where t3.id in
                <foreach collection="purchaseResultIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                where 1 = 2
            </otherwise>
        </choose>
        order by t1.purchase_date desc
    </select>

    <select id="getListByPurchaseGroupId" resultType="cn.trasen.device.supervision.model.PurchaseLog">
        select t1.*, t2.name as cate_name
        from toa_device_purchase_log t1
                 left join toa_device_category t2 on t1.cate_id = t2.id
        where t1.purchase_group_id = #{purchaseGroupId}
          and t1.is_deleted = 'N'
    </select>
</mapper>