package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_income_origin")
@Setter
@Getter
public class IncomeOrigin {
    @Id
    private String id;

    /**
     * 医院编码
     */
    @Column(name = "hospital_code")
    @ApiModelProperty(value = "医院编码")
    private String hospitalCode;

    /**
     * 收费编码
     */
    @Column(name = "charge_code")
    @ApiModelProperty(value = "收费编码")
    private String chargeCode;

    /**
     * 设备编码
     */
    @Column(name = "device_code")
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;

    /**
     * 项目编码
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "项目编码")
    private String itemCode;

    /**
     * 项目名称
     */
    @Column(name = "item_label")
    @ApiModelProperty(value = "项目名称")
    private String itemLabel;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer counts;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal costs;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private BigDecimal total;


    @Column(name = "user_code")
    @ApiModelProperty(value = "费用时间")
    private String userCode;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    private Date cdate;

    /**
     * 状态 0 未消费 1 已消费 2 无法消费
     */
    @ApiModelProperty(value = "状态 0 未消费 1 已消费 2 无法消费")
    private String status;


    @ApiModelProperty(value = "错误描述")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "内部部门ID")
    @Column(name = "dept_id")
    private String deptId;

    @ApiModelProperty(value = "内部设备ID")
    @Column(name = "device_id")
    private String deviceId;


    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "is_deleted")
    private String isDeleted;
}