package cn.trasen.device.supervision.controller;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.device.supervision.bean.BudgetInstanceInfoListReq;
import cn.trasen.device.supervision.bean.PurchaseInstanceInfoListReq;
import cn.trasen.device.supervision.bean.WfInstanceJump;
import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.dao.DeviceHelperMapper;
import cn.trasen.device.supervision.dao.DeviceMapper;
import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.oa.device.controller
 * @className: IndexController
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/28 10:46
 * @version: 1.0
 */
@RestController
@Api(tags = "设备管理首页")
@RequestMapping("api/deviceIndex")
public class IndexController {

    @Autowired
    private DepartmentBudgetService departmentBudgetService;

    @Autowired
    private DeviceHelperMapper deviceHelperMapper;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private IndexStatisticsService indexStatisticsService;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private IncomeOriginService incomeOriginService;


    @GetMapping("buildDepartmentStatisticsLogHis")
    @ApiOperation(value = "构建医院历史运营收入数据", notes = "构建医院历史运营收入数据")
    public PlatformResult buildDepartmentStatisticsLogHis() {
        // 查出所有机构
        List<Organization> organizationList = deviceHelperMapper.getOrganizationList();

        // 现在要做 2012年到 2024 年6月的数据
        int startYear = 2012;
        int endYear = 2024;

        for (Organization organization : organizationList) {
            for (int year = startYear; year <= endYear; year++) {
                incomeOriginService.departmentOperation(organization.getOrganizationId(), String.valueOf(year));
            }
        }

        return PlatformResult.success("操作成功");
    }

    @GetMapping("buildOperateLogHis")
    @ApiOperation(value = "构建设备历史运营收入数据", notes = "构建设备历史运营收入数据")
    public PlatformResult buildOperateLogHis() {
        // 查询出所有的设备
        Example example = new Example(Device.class);
        example.createCriteria().andEqualTo("isDeleted", "N");
        List<Device> deviceList = deviceMapper.selectByExample(example);

        if (deviceList.size() < 0) {
            return PlatformResult.success("无需操作");
        }

        // 现在要做 2012年到 2024 年6月的数据
        int startYear = 2012;
        int endYear = 2024;
        int endMonth = 6;  // 结束月份

        for (Device device : deviceList) {
            for (int year = startYear; year <= endYear; year++) {
                // 如果是 2024 年，只遍历到 6 月
                int maxMonth = (year == endYear) ? endMonth : 12;
                for (int month = 1; month <= maxMonth; month++) {
                    // 处理数据的逻辑在这里
                    incomeOriginService.incomeDataSplit(device.getId(), String.format("%d-%02d", year, month));
                }
            }
        }


        return PlatformResult.success("操作成功");
    }

    @GetMapping("getWfInstanceInfo4Jump/{instanceId}")
    public PlatformResult<WfInstanceJump> getWfInstanceInfo4Jump(@PathVariable String instanceId) {
        return PlatformResult.success(deviceHelperService.getWfInstanceJump(instanceId));
    }

    @GetMapping("refreshDepartmentBudget")
    public PlatformResult refreshDepartmentBudget(@RequestParam(required = false) String year) {

        if (StringUtils.isEmpty(year)) {
            year = Comm.getCurDate("yyyy");
        }

        BudgetInstanceInfoListReq biilr = new BudgetInstanceInfoListReq();
        biilr.setEffectYear(year);
        departmentBudgetService.calcuDepartmentBudgetGetted(biilr);

        PurchaseInstanceInfoListReq piilr = new PurchaseInstanceInfoListReq();
        piilr.setEffectYear(year);
        departmentBudgetService.calcuDepartmentBudgetCosted(piilr);

        return PlatformResult.success("刷新成功");

    }

    @GetMapping("orgList")
    @ApiOperation(value = "组织机构列表", notes = "组织机构列表")
    public PlatformResult<List<Organization>> orgList() {
        String deptCode = deviceHelperService.getDeptCode();
        if (deptCode.equals("admin")) {
            return PlatformResult.success(deviceHelperMapper.getOrganizationListToFrontSelect());
        } else {
            return PlatformResult.success(new ArrayList<>());
        }
    }

    @PostMapping("statistics")
    @ApiOperation(value = "首页统计接口", notes = "首页统计接口")
    public PlatformResult<IndexResp> statistics(@RequestBody IndexReq record) {

        IndexBlockDBCYReq indexBlockDBCYReq = record.getIndexBlockDBCYReq();
        IndexBlockYSCGReq indexBlockYSCGReq = record.getIndexBlockYSCGReq();
        IndexBlockYYYSSYQKReq indexBlockYYYSSYQKReq = record.getIndexBlockYYYSSYQKReq();
        IndexBlockYYYSSYQKHZReq indexBlockYYYSSYQKHZReq = record.getIndexBlockYYYSSYQKHZReq();

        IndexBlockDBCYReq indexBlockWDSQReq = record.getIndexBlockWDSQReq();
        IndexBlockDBSXReq indexBlockDBSXReq = record.getIndexBlockDBSXReq();

        IndexBlockYYZBReq indexBlockYYZBReq = record.getIndexBlockYYZBReq();

        IndexBlockSBYDXYFXReq indexBlockSBYDXYFXReq = record.getIndexBlockSBYDXYFXReq();
        IndexBlockYYSBNDXYFXReq indexBlockYYSBNDXYFXReq = record.getIndexBlockYYSBNDXYFXReq();


        String deptId = deviceHelperService.getDeptCode();

        IndexResp indexResp = new IndexResp();

        String withBlock = record.getWithBlock();
        if (withBlock == null || withBlock.isEmpty()) {
            return PlatformResult.failure("参数错误");
        }

        // withBlock to array
        String[] withBlockList = withBlock.split(",");

        for (String block : withBlockList) {
            switch (block) {
                case "indexBlockDBCYReq":
                    if (indexBlockDBCYReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockDBCYReq.setDeptId("admin");
                    }
                    // 待办查阅
                    indexResp.setIndexBlockDBCYResp(indexStatisticsService.getIndexBlockDBCYResp(indexBlockDBCYReq));
                    break;
                case "indexBlockYSCGReq":
                    if (indexBlockYSCGReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockYSCGReq.setDeptId("admin");
                    }
                    // 年度预算采购分析
                    indexResp.setIndexBlockYSCGResp(indexStatisticsService.getIndexBlockYSCGResp(indexBlockYSCGReq));
                    break;
                case "indexBlockYYYSSYQKReq":
                    // 医院预算使用情况
                    if (indexBlockYYYSSYQKReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockYYYSSYQKReq.setDeptId("admin");
                    }
                    indexResp.setIndexBlockYYYSSYQKResp(indexStatisticsService.getIndexBlockYYYSSYQKResp(indexBlockYYYSSYQKReq));
                    break;
                case "indexBlockYYYSSYQKHZReq":
                    if (indexBlockYYYSSYQKHZReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockYYYSSYQKHZReq.setDeptId("admin");
                    }
                    indexResp.setIndexBlockYYYSSYQKHZResp(indexStatisticsService.getIndexBlockYYYSSYQKHZResp(indexBlockYYYSSYQKHZReq));
                    break;
                case "indexBlockWDSQReq":
                    if (indexBlockWDSQReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockWDSQReq.setDeptId("admin");
                    }
                    indexResp.setIndexBlockWDSQResp(indexStatisticsService.getIndexBlockWDSQResp(indexBlockWDSQReq));
                    break;
                case "indexBlockDBSXReq":
                    if (indexBlockDBSXReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockDBSXReq.setDeptId("admin");
                    }
                    indexBlockDBSXReq.setUserCode(UserInfoHolder.getCurrentUserCode());
                    indexResp.setIndexBlockDBSXResp(indexStatisticsService.getIndexBlockDBSXResp(indexBlockDBSXReq));
                    break;
                case "indexBlockYYZBReq":
                    if (indexBlockYYZBReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockYYZBReq.setDeptId("admin");
                    }
                    indexResp.setIndexBlockYYZBResp(indexStatisticsService.getIndexBlockYYZBResp(indexBlockYYZBReq));
                    break;
                case "indexBlockSBYDXYFXReq":
                    if (indexBlockSBYDXYFXReq == null) {
                        break;
                    }
                    if (deptId.equals("admin")) {
                        indexBlockSBYDXYFXReq.setDeptId("admin");
                    }
                    indexResp.setIndexBlockSBYDXYFXResp(indexStatisticsService.getIndexBlockSBYDXYFXResp(indexBlockSBYDXYFXReq));
                    break;
                case "indexBlockYYSBNDXYFXReq":
                    if (indexBlockYYSBNDXYFXReq == null) {
                        break;
                    }
                    // 非卫健局不查询
                    if (!deptId.equals("admin")) {
                        break;
                    }
                    indexResp.setIndexBlockYYSBNDXYFXResp(indexStatisticsService.getIndexBlockYYSBNDXYFXResp(indexBlockYYSBNDXYFXReq));
                    break;

                default:
                    break;
            }
        }
        return PlatformResult.success(indexResp);
    }


}
