<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.Device">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="purchase_log_id" jdbcType="VARCHAR" property="purchaseLogId"/>
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode"/>
        <result column="ybsf_code" jdbcType="VARCHAR" property="ybsfCode"/>
        <result column="cate_id" jdbcType="VARCHAR" property="cateId"/>
        <result column="type" jdbcType="CHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="supplier" jdbcType="VARCHAR" property="supplier"/>
        <result column="producer" jdbcType="VARCHAR" property="producer"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="spec" jdbcType="VARCHAR" property="spec"/>
        <result column="sources_funds" jdbcType="VARCHAR" property="sourcesFunds"/>
        <result column="unit_price" jdbcType="VARCHAR" property="unitPrice"/>
        <result column="files" jdbcType="VARCHAR" property="files"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="expand_field" jdbcType="LONGVARCHAR" property="expandField"/>
    </resultMap>
    <select id="getDataSetList" resultType="cn.trasen.device.supervision.bean.DevicePurchaseLogResp"
            parameterType="cn.trasen.device.supervision.model.Device">
        select t1.*,
        `t2`.`supplier` as pl_supplier,
        `t2`.`producer` as pl_producer,
        `t2`.`purchase_brand` as pl_brand,
        `t2`.`purchase_spec` as pl_spec,
        `t2`.`build_scale` as pl_build_scale,
        `t2`.`accept_date` as pl_accept_date,
        `t2`.`funds_sources` as pl_funds_sources,
        `t2`.`service_start_at` as pl_service_start_at,
        `t2`.`service_end_at` as pl_service_end_at,
        `t6`.`name` AS `dept_name`,
        `t7`.`name` as `cate_name`,
        `t4`.`wf_instance_id` as wf_instance_id
        from toa_device_device t1
        inner join toa_device_purchase_log t2 on t1.purchase_log_id = t2.id
        inner join toa_device_purchase_group t3 on t2.purchase_group_id = t3.id
        inner join toa_device_purchase_result t4 on t3.purchase_result_id = t4.id
        inner join comm_organization t6 on t1.dept_id = t6.organization_id
        left join toa_device_category t7 on t1.cate_id = t7.id
        where t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t1.dept_id = #{deptId}
        </if>
        <if test="name != null and name != ''">
            and t1.name like concat('%', #{name}, '%')
        </if>
        <if test="isComplete != null and isComplete != ''">
            and t1.is_complete = #{isComplete}
        </if>
        <if test="type != null and type != ''">
            and t1.type = #{type}
        </if>
        <if test="cateId != null and cateId != ''">
            and t7.cate_id = #{cateId}
        </if>

        order by t1.create_date desc
    </select>

    <select id="getDeviceParamList" resultType="cn.trasen.device.supervision.bean.DeviceParamResp"
            parameterType="cn.trasen.device.supervision.model.Device">
        select t1.*,
        `t2`.`supplier` as pl_supplier,
        `t2`.`producer` as pl_producer,
        `t2`.`purchase_brand` as pl_brand,
        `t2`.`purchase_spec` as pl_spec,
        `t2`.`build_scale` as pl_build_scale,
        `t2`.`accept_date` as pl_accept_date,
        `t2`.`funds_sources` as pl_funds_sources,
        `t2`.`service_start_at` as pl_service_start_at,
        `t2`.`service_end_at` as pl_service_end_at,
        `t6`.`name` AS `dept_name`,
        `t7`.`name` as `cate_name`,
        `t3`.`tender_parameter`
        from toa_device_device t1
        inner join toa_device_purchase_log t2 on t1.purchase_log_id = t2.id
        inner join toa_device_purchase_group t3 on t2.purchase_group_id = t3.id
        inner join comm_organization t6 on t1.dept_id = t6.organization_id
        left join toa_device_category t7 on t1.cate_id = t7.id
        where t1.is_deleted = 'N' and `t3`.`tender_parameter` is not null
        <if test="deptId != null and deptId != ''">
            and t1.dept_id = #{deptId}
        </if>
        <if test="name != null and name != ''">
            and (
            t1.name like concat('%', #{name}, '%')
            or t1.sys_code like concat('%', #{name}, '%')
            or t2.purchase_brand like concat('%', #{name}, '%')
            or t1.brand like concat('%', #{name}, '%')
            )
        </if>
        <if test="cateId != null and cateId != ''">
            and t7.id = #{cateId}
        </if>

        order by t1.create_date desc
    </select>
    <sql id="indexStatisticsWhere">
        <choose>
            <when test="deptId != null and deptId != ''">
                <choose>
                    <when test="deptId == 'admin'">
                    </when>
                    <otherwise>
                        and t1.`dept_id` = #{deptId}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                and 1=2
            </otherwise>
        </choose>
        <if test="start != null and start != ''">
            and <![CDATA[ t1.`CREATE_DATE` >= ]]> #{start}
        </if>
        <if test="end != null and end != ''">
            and <![CDATA[ t1.`CREATE_DATE` <= ]]> #{end}
        </if>
    </sql>
    <select id="sbzs" resultType="java.lang.Integer"
            parameterType="cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq">
        select count(1) from toa_device_device t1 where t1.is_deleted = 'N'
        <include refid="indexStatisticsWhere"/>
    </select>
    <select id="countsGroupByDeptId" resultType="cn.trasen.device.supervision.bean.CountsGroupByDeptIdResp">
        select count(*) as devices, dept_id
        from `toa_device_device`
        where is_deleted = 'N'
    </select>
    <select id="getListByPurchaseLogId" resultType="cn.trasen.device.supervision.model.Device">
        select t1.*, t2.name as cate_name
        from toa_device_device t1
                 left join toa_device_category t2 on t1.cate_id = t2.id
        where t1.purchase_log_id = #{purchaseLogId}
          and t1.is_deleted = 'N'
    </select>
</mapper>