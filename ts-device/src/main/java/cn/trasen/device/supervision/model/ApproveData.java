package cn.trasen.device.supervision.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ApproveData {

    private String approveDataId;

    private String tableName;

    private String wfInstanceId;

    /*------------------------特殊字段给前端传过来逻辑上处理实际字段 ------*/

    private String name;  // 项目名称 | 设备名称
    private String spec; // 规格型号

    /*--------------------------------------------------------------*/

    private String xmmc; // 项目名称 | 设备名称

    private String ggxh; // 规格型号

    private String fwkssj;// 服务开始时间

    private String fwjssj;// 服务结束时间

    private String jzmj; // 建筑面积


    private String pfsl; //批复数量

    private String pfdj; //批复单价

    private String pfje; //批复金额

    private String shsl; //上会金额

    private String shdj; //上会金额

    private String shje; //上会金额

    private String shrq; //上会日期

}
