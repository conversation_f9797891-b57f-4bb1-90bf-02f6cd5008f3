package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.OrganizationMapper;

import java.util.List;
import java.util.Map;

/**
 * @ClassName OrganizationService
 * @Description TODO
 * @date 2024年6月18日 下午2:03:33
 * <AUTHOR>
 * @version 1.0
 */
public interface OrganizationMapperService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024年6月18日 下午2:03:33
	 * <AUTHOR>
	 */
	Integer save(OrganizationMapper record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024年6月18日 下午2:03:33
	 * <AUTHOR>
	 */
	Integer update(OrganizationMapper record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024年6月18日 下午2:03:33
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return Organization
	 * @date 2024年6月18日 下午2:03:33
	 * <AUTHOR>
	 */
	OrganizationMapper selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<Organization>
	 * @date 2024年6月18日 下午2:03:33
	 * <AUTHOR>
	 */
	DataSet<OrganizationMapper> getDataSetList(Page page, OrganizationMapper record);

	List<OrganizationMapper> getList(OrganizationMapper record);

	Map<String,String> exchangeMap();
}
