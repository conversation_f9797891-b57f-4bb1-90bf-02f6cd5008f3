package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_device_department_statistics_log")
@Setter
@Getter
public class DepartmentStatisticsLog {
    @Id
    private String id;

    /**
     * year month day
     */
    @ApiModelProperty(value = "year month day")
    private String granularity;

    @Column(name = "dept_id")
    private String deptId;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private String month;

    /**
     * 设备总数
     */
    @ApiModelProperty(value = "设备总数")
    private Integer devices;

    /**
     * 检查人数
     */
    @ApiModelProperty(value = "检查人数")
    private Integer checks;

    /**
     * 总收入
     */
    @ApiModelProperty(value = "总收入")
    private BigDecimal incomes;

    /**
     * 总支出
     */
    @ApiModelProperty(value = "总支出")
    private BigDecimal costs;

    /**
     * 收支盈余
     */
    @ApiModelProperty(value = "收支盈余")
    private BigDecimal surplus;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "is_deleted")
    private String isDeleted;
}