package cn.trasen.device.supervision.controller;

import cn.trasen.device.supervision.bean.ProjectManageDetailResp;
import cn.trasen.device.supervision.bean.ProjectManageListResp;
import cn.trasen.device.supervision.bean.ProjectManageSaveReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.Project;
import cn.trasen.device.supervision.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ProjectController
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
@RestController
@Api(tags = "ProjectController")
public class ProjectController {

    private transient static final Logger logger = LoggerFactory.getLogger(ProjectController.class);

    @Autowired
    private ProjectService projectService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveProject
     * @Description 新增
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    @ApiOperation(value = "工程项目维护", notes = "工程项目维护")
    @PostMapping("/api/project/save")
    public PlatformResult<String> saveProject(@RequestBody ProjectManageSaveReq record) {
        try {
            projectService.submit(record);
            return PlatformResult.success("提交成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Project>
     * @Title selectProjectById
     * @Description 根据ID查询
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @PostMapping("/api/project/{id}")
    public PlatformResult<ProjectManageDetailResp> selectProjectById(@PathVariable String id) {
        try {
            return PlatformResult.success(projectService.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param page
     * @param record
     * @return DataSet<Project>
     * @Title selectProjectList
     * @Description 查询列表
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/project/list")
    public DataSet<ProjectManageListResp> selectProjectList(Page page, Project record) {
        return projectService.getDataSetList(page, record);
    }
}
