package cn.trasen.device.supervision.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @projectName: xtbg
 * @package: cn.trasen.oa.device.config
 * @className: AsyncConfiguration
 * @author: chenbin
 * @description: 开启异步调用配置
 * @date: 2024/2/23 16:01
 * @version: 1.0
 */
@Configuration
@EnableAsync
public class AsyncConfiguration {
    // 开启异步支持
}