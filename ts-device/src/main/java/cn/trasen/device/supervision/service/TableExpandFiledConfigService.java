package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.TableExpandFiledConfig;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName TableExpandFiledConfigService
 * @Description TODO
 * @date 2024年3月29日 上午9:23:58
 */
public interface TableExpandFiledConfigService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年3月29日 上午9:23:58
     * <AUTHOR>
     */
    Integer save(TableExpandFiledConfig record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年3月29日 上午9:23:58
     * <AUTHOR>
     */
    Integer update(TableExpandFiledConfig record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年3月29日 上午9:23:58
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return TableExpandFiledConfig
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年3月29日 上午9:23:58
     * <AUTHOR>
     */
    TableExpandFiledConfig selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<TableExpandFiledConfig>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年3月29日 上午9:23:58
     * <AUTHOR>
     */
    DataSet<TableExpandFiledConfig> getDataSetList(Page page, TableExpandFiledConfig record);

    /**
     * @param tableName
     * @return PlatformResult<String>
     * @return PlatformResult<String>
     * @Title generateTableExpandFiledConfig
     * @Description 根据表名初始化字段配置
     * @date 2024年3月29日 上午9:23:58
     */
    void generateTableExpandFiledConfig(String tableName);
}
