package cn.trasen.device.supervision.util;

import cn.trasen.homs.core.exception.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.util
 * @className: CacheHelper
 * @author: chenbin
 * @description: 简单的缓存类
 * @date: 2024/7/29 10:07
 * @version: 1.0
 */

@Component
@Slf4j
public class CacheHelper {

    private static final String CACHE_PREFIX = "device:supervision:";

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    public static String getCacheKey(String key) {
        return CACHE_PREFIX + key;
    }

    public <T> T get2Object(String key, Class<T> valueType) {
        Object value = redisTemplate.opsForValue().get(getCacheKey(key));
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            try {
                log.info("缓存命中" + key);
                return objectMapper.readValue((String) value, valueType);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                throw new BusinessException("读取缓存失败");
            }
        }
        return null;
    }

    public void set(String key, Object value, long timeout) {
        try {
            redisTemplate.opsForValue().set(getCacheKey(key), objectMapper.writeValueAsString(value), timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("写入缓存失败");
        }
    }


    /**
     * @param prefix:
     * @return void
     * <AUTHOR>
     * @description 按照前缀删除缓存
     * @date 2024/7/29 13:59
     */
    public void deleteKeysWithPrefix(String prefix) {

        Set<String> keys = redisTemplate.keys(CACHE_PREFIX + prefix + "*");
        if (keys != null && !keys.isEmpty()) {
            // 删除匹配的键
            redisTemplate.delete(keys);
        }
    }

}
