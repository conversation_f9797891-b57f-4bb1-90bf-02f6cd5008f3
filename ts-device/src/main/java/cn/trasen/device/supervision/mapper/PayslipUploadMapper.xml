<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.PayslipUploadMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.PayslipUpload">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="month" jdbcType="VARCHAR" property="month"/>
        <result column="type" jdbcType="CHAR" property="type"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
    </resultMap>

    <select id="uploadList" parameterType="cn.trasen.device.supervision.model.PayslipUpload"
            resultType="cn.trasen.device.supervision.bean.payslip.PayslipUploadListResp">
        select distinct t1.*,
        t2.`WF_INSTANCE_ID`,
        t2.`WF_DEFINITION_ID`,
        t2.`STATUS`,
        t2.`CURRENT_STEP_NAME`,
        (select GROUP_CONCAT(ASSIGNEE_NAME order by ASSIGNEE_NAME SEPARATOR ',')
        from wf_task
        where `WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
        ) as current_approver,
        t3.name as dept_name,
        (select REMARK from wf_task_his where WF_INSTANCE_ID = t2.`WF_INSTANCE_ID` order by CREATE_DATE desc LIMIT 1) as
        remark,
        t4.TASK_ID as task_id
        from toa_device_payslip_upload t1
        left join wf_instance_info t2 on t1.`id` = t2.`BUSINESS_ID`
        left join comm_organization t3 on t1.`dept_id` = t3.`organization_id`
        left join wf_task t4 on t2.`WF_INSTANCE_ID` = t4.`WF_INSTANCE_ID` and t4.`WF_STEP_NAME` = '重新提交'
        where t1.`is_deleted` = 'N' and t1.create_user = #{createUser}
        <if test="month != null and month != ''">
            and t1.`month` = #{month}
        </if>
        <if test="type != null and type != ''">
            and t1.`type` = #{type}
        </if>
        <if test="deptId != null and deptId !=''">
            and t1.`dept_id` = #{deptId}
        </if>
        order by t1.create_date desc
    </select>

    <select id="checkList" parameterType="cn.trasen.device.supervision.model.PayslipUpload"
            resultType="cn.trasen.device.supervision.bean.payslip.PayslipUploadListResp">
        select distinct t1.*,
        t2.`WF_INSTANCE_ID`,
        t2.`WF_DEFINITION_ID`,
        t2.`STATUS`,
        t2.`CURRENT_STEP_NAME`,
        (select GROUP_CONCAT(ASSIGNEE_NAME order by ASSIGNEE_NAME SEPARATOR ',')
        from wf_task
        where `WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
        ) as current_approver,
        t3.name as dept_name,
        (select REMARK from wf_task_his where WF_INSTANCE_ID = t2.`WF_INSTANCE_ID` order by CREATE_DATE desc LIMIT 1) as
        remark,
        t4.TASK_ID as task_id,
        (CASE WHEN t4.`ASSIGNEE_NO` = #{createUser} THEN '0' ELSE '1' END) as checked
        from toa_device_payslip_upload t1
        left join wf_instance_info t2 on t1.`id` = t2.`BUSINESS_ID`
        left join comm_organization t3 on t1.`dept_id` = t3.`organization_id`
        left join wf_task t4 on t2.`WF_INSTANCE_ID` = t4.`WF_INSTANCE_ID` and t4.`ASSIGNEE_NO` = #{createUser}
        left join wf_task_his t5 on t2.`WF_INSTANCE_ID` = t5.`WF_INSTANCE_ID` and t5.`ASSIGNEE_NO` = #{createUser}
        where t1.`is_deleted` = 'N'
            and ( t4.`ASSIGNEE_NO` = #{createUser} or t5.`ASSIGNEE_NO` = #{createUser}  )
        <if test="month != null and month != ''">
            and t1.`month` = #{month}
        </if>
        <if test="type != null and type != ''">
            and t1.`type` = #{type}
        </if>
        <if test="deptId != null and deptId !=''">
            and t1.`dept_id` = #{deptId}
        </if>
        order by t1.`create_date` desc
    </select>

    <select id="payslipStatusReport" parameterType="cn.trasen.device.supervision.model.PayslipUpload"
            resultType="cn.trasen.device.supervision.bean.payslip.PayslipStatusReportResp">
        select
        COALESCE(SUM(CASE WHEN t2.status IS NOT NULL THEN 1 ELSE 0 END),0) as uploadeds,
        COALESCE(SUM(CASE WHEN t2.status = 1 THEN 1 ELSE 0 END),0) as checkings ,
        COALESCE(SUM(CASE WHEN t2.status = 2 THEN 1 ELSE 0 END),0) as passeds,
        COALESCE(SUM(CASE WHEN t2.status = 1 AND t2.CURRENT_STEP_NAME = '重新提交' THEN 1 ELSE 0 END),0) as unpasses
        from toa_device_payslip_upload t1 left join `wf_instance_info` t2 on t1.`id` = t2.`BUSINESS_ID`
        where t1.`is_deleted` = 'N'
        <if test="month != null and month != ''">
            and t1.`month` = #{month}
        </if>
        <if test="type != null and type != ''">
            and t1.`type` = #{type}
        </if>
    </select>
</mapper>