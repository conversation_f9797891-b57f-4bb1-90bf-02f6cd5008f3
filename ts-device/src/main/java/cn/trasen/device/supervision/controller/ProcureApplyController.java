package cn.trasen.device.supervision.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.exception.DeviceBusinessException;
import cn.trasen.device.supervision.model.*;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import cn.trasen.device.supervision.util.WordUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;

import java.io.IOException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xwpf.usermodel.*;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.jeecgframework.poi.word.WordExportUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;


@RestController
@Api(tags = "采购申请")
public class ProcureApplyController {

    private transient static final Logger logger = LoggerFactory.getLogger(ProcureApplyController.class);

    @Autowired
    private ProcureApplyService procureApplyService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private DepartmentBudgetService departmentBudgetService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ServicesService servicesService;

    @Autowired
    private ProjectService projectService;


    @ApiOperation(value = "查询采购流程（申请页面）", notes = "查询采购流程（申请页面）")
    @PostMapping("/api/procureApply/getWfDefinitionInfo")
    public PlatformResult<List<Map<String, Object>>> getWfDefinitionInfo() {
        try {
            List<Map<String, Object>> record = procureApplyService.getWfDefinitionInfo();
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "查询头部数据（申请页面）", notes = "查询头部数据（申请页面）")
    @PostMapping("/api/procureApply/getApplyHeadWfDefinitionInfo")
    public PlatformResult<Map<String, Object>> getApplyHeadWfDefinitionInfo() {
        try {
            Map<String, Object> record = procureApplyService.getMyApplyHeadWfDefinitionInfo();
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "查询左边数据（申请页面）", notes = "查询左边数据（申请页面）")
    @PostMapping("/api/procureApply/getApplyLeftWfDefinitionInfo")
    public PlatformResult<List<Map<String, Object>>> getApplyLeftWfDefinitionInfo(@RequestBody ProcureApply procureApply) {
        try {
            List<Map<String, Object>> record = procureApplyService.getApplyLeftWfDefinitionInfo(procureApply);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表字段数据（申请页面）", notes = "列表字段数据（申请页面）")
    @PostMapping("/api/procureApply/getMyApplyTableHeadCols")
    public PlatformResult<List<JdGridTableEntity>> getMyApplyTableHeadCols(@RequestBody ProcureApply record) {
        try {
            return PlatformResult.success(procureApplyService.getMyApplyTableHeadCols(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表数据（申请页面）", notes = "列表数据（申请页面）")
    @PostMapping("/api/procureApply/getMyApplyWfDefinitionInfo")
    public DataSet<Map<String, Object>> getMyApplyWfDefinitionInfo(Page page, ProcureApply record) {
        try {
            List<Map<String, Object>> list = procureApplyService.getMyApplyWfDefinitionInfo(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "查询头部数据（审批页面）", notes = "查询头部数据（审批页面）")
    @PostMapping("/api/procureApply/getApproveHeadWfDefinitionInfo")
    public PlatformResult<Map<String, Object>> getApproveHeadWfDefinitionInfo() {
        try {
            Map<String, Object> record = procureApplyService.getApproveHeadWfDefinitionInfo();
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表数据（审批页面）", notes = "列表数据（审批页面）")
    @PostMapping("/api/procureApply/getApproveWfDefinitionList")
    public DataSet<Map<String, Object>> getApproveWfDefinitionList(Page page, ProcureApply record) {
        try {
            List<Map<String, Object>> list = procureApplyService.getApproveInfoList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "导出（审批页面）", notes = "导出（审批页面）")
    @GetMapping(value = "/api/procure/approve/export")
    public ResponseEntity<byte[]> approveExport(ProcureApply record) throws IOException {

        try {
            // 默认10000行 兼容方法分页导致的可能数据不全
            Page page = new Page();
            page.setPageSize(10000);
            page.setPageNo(1);

            // 处理 exportId
            if (record.getExportId() == null || record.getExportId().equals("")) {
                throw new DeviceBusinessException("exportId不能为空");
            }

            List<String> exportIdList = Arrays.asList(record.getExportId().split(","));
            record.setExportIdList(exportIdList);

            List<Map<String, Object>> list = procureApplyService.getApproveInfoList(page, record);

            List<ApproveExport> exportList = new ArrayList();

            int index = 0;
            for (Map<String, Object> map : list) {
                index++;
                String WF_DEFINITION_ID = map.get("WF_DEFINITION_ID").toString();

                ApproveExport ae = new ApproveExport();
                ae.setNo(String.valueOf(index));
                ae.setYear(map.get("applyYear").toString());
                ae.setApplyOrg(map.get("orgName").toString());

                switch (WF_DEFINITION_ID) {
                    // 专用
                    case "06985B07055046639923C386071BA66E":
                        ae.setRes(map.get("sbly").toString());
                        ae.setNum(map.get("sl").toString());
                        ae.setPrice(Comm.trimZeros(map.get("dj").toString()));
                        ae.setTotal(Comm.trimZeros(map.get("zje").toString()));
                        ae.setName(map.get("xmmc").toString());
                        ae.setSpec(map.get("ggxh").toString());
                        break;
                    // 一般设备
                    case "A37FCFB6631B405EA3A790E32FE88655":
                        ae.setRes(map.get("sbly").toString());
                        ae.setNum(map.get("sl").toString());
                        ae.setPrice(Comm.trimZeros(map.get("dj").toString()));
                        ae.setTotal(Comm.trimZeros(map.get("zje").toString()));
                        ae.setName(map.get("xmmc").toString());
                        ae.setSpec(map.get("ggxh").toString());
                        break;
                    //工程项目采购
                    case "ADA7FFF3AFBC46258C6A3D2E6A9D7DE0":
                        ae.setRes(map.get("sbly").toString());
                        ae.setNum("1");
                        ae.setPrice(Comm.trimZeros(map.get("zje").toString()));
                        ae.setTotal(Comm.trimZeros(map.get("zje").toString()));
                        ae.setName(map.get("xmmc").toString());
                        ae.setSpec(map.get("jzmj").toString());
                        break;
                    //服务项目采购
                    case "1866B74A6BC3400F990AB9E3C2DAA1A6":
                        ae.setRes(map.get("sbly").toString());
                        BigDecimal d = new BigDecimal(map.get("fwnfdj").toString());
                        BigDecimal t = new BigDecimal(map.get("zje").toString());
                        BigDecimal num = t.divide(d, 0, RoundingMode.CEILING);
                        ae.setNum(num.toString());
                        ae.setPrice(Comm.trimZeros(map.get("fwnfdj").toString()));
                        ae.setTotal(Comm.trimZeros(map.get("zje").toString()));
                        ae.setName(map.get("xmmc").toString());
                        ae.setSpec(map.get("fwkssj").toString() + map.get("fwjssj").toString());
                        break;
                }
                exportList.add(ae);
            }

            if (exportList == null || exportList.size() == 0) {
                throw new DeviceBusinessException("找不到数据");
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/purchaseApprovalExport.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("卫健局论证会议采购项目事项表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    @ApiOperation(value = "查询左边数据（办理查阅）", notes = "查询左边数据（办理查阅）")
    @PostMapping("/api/procureApply/getConsultLeftWfDefinitionInfo")
    public PlatformResult<List<Map<String, Object>>> getConsultLeftWfDefinitionInfo() {
        try {
            List<Map<String, Object>> record = procureApplyService.getConsultLeftWfDefinitionInfo();
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表数据（办理查阅）", notes = "列表数据（办理查阅）")
    @PostMapping("/api/procureApply/getConsultWfDefinitionList")
    public DataSet<Map<String, Object>> getConsultWfDefinitionList(Page page, ProcureApply record) {
        try {

            List<Map<String, Object>> list = procureApplyService.getConsultWfDefinitionList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "批量退回", notes = "批量退回")
    @PostMapping("/api/procureApply/batchTravelReject")
    public PlatformResult<String> batchTravelReject(@RequestBody ProcureApply record) {
        try {
            procureApplyService.batchTravelReject(record);
            return PlatformResult.success(null, "批量退回成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量同意", notes = "批量同意")
    @PostMapping("/api/procureApply/batchExamination")
    public PlatformResult<String> batchExamination(@RequestBody ProcureApply record) {
        try {
            procureApplyService.batchExamination(record);
            return PlatformResult.success(null, "批量审批成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @PostMapping("/api/procureApply/batchDelete")
    public PlatformResult<String> batchDelete(@RequestBody WfInstanceBatchDeleteReq record) {
        try {
            procureApplyService.batchDelete(record);
            return PlatformResult.success(null, "批量删除成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "上会批量同意", notes = "上会批量同意")
    @PostMapping("/api/procureApply/shBatchExamination")
    public PlatformResult<String> shBatchExamination(@RequestBody List<ProcureApply> record) {
        try {
            for (ProcureApply procureApply : record) {
                procureApplyService.batchExamination(procureApply);
            }
            return PlatformResult.success(null, "批量审批成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "查询流程节点信息", notes = "查询流程节点信息")
    @PostMapping("/api/procureApply/getStepInfoByWfDefinitionId")
    public PlatformResult<List<Map<String, Object>>> getStepInfoByWfDefinitionId(String wfDefinitionId) {
        try {
            List<Map<String, Object>> record = procureApplyService.getStepInfoByWfDefinitionId(wfDefinitionId);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "上会列表", notes = "上会列表")
    @GetMapping("/api/procure/selectShInstance/list")
    public DataSet selectWfShInstanceList(Page page, GetWfShInstanceListReq req) {
        String orderRule = ",asc,desc,";
        String sord = req.getSord();
        String sidx = req.getSidx();

        if (sidx == null || sidx.equals("")) {
            req.setSidx("apply_org");
        }

        if (sord == null || sord.equals("") || !("," + orderRule + ",").contains(req.getSord())) {
            req.setSord("asc");
        }


        String orderFieldRule = ",type,applyYear,applyOrg,meetingDate,export,";
        if (("," + orderFieldRule + ",").contains(req.getSidx())) {
            req.setSidx(Comm.convertCamelToSnake(req.getSidx()));
        } else {
            req.setSidx("apply_org");
        }
        return deviceHelperService.getWfShInstanceList(page, req);
    }


    @RequestMapping(value = "/api/procure/shInstanceList/export")
    public ResponseEntity<byte[]> shInstanceListExport(@RequestBody ShInstanceListExportReq shInstanceListExportReq) throws IOException {

        List<String> exportInstanceIdList = new ArrayList<>();

        String exportInstanceId = shInstanceListExportReq.getExportInstanceId();

        if (exportInstanceId == null || exportInstanceId.equals("")) {
            throw new DeviceBusinessException("exportInstanceId不能为空");
        }
        // 拆分 exportInstanceId  逗号分隔
        String[] exportInstanceIdArr = exportInstanceId.split(",");
        exportInstanceIdList.addAll(Arrays.asList(exportInstanceIdArr));

        // 查询需要上会打印的数据
        List<WfShInstanceInfo> wfShInstanceInfoList = deviceHelperService.getWfShInstanceListByIdLIst(exportInstanceIdList);


        if (wfShInstanceInfoList == null || wfShInstanceInfoList.isEmpty()) {
            throw new DeviceBusinessException("没有需要导出的数据");
        }

        // 拿出来所有的小计和总计，把no填充进去 loop wfShInstanceInfoList
        int initRowIndex = 2;
        int no = 1;
        int orgNum = 0;
        BigDecimal subtotal = new BigDecimal(0);
        BigDecimal total = new BigDecimal(0);
        List<WfShSubtotal> wfShSubtotalList = new ArrayList<>();
        String applyOrg = "";
        // 用来记录上一个applyOrg
        for (WfShInstanceInfo wfShInstanceInfo : wfShInstanceInfoList) {
            wfShInstanceInfo.setNo(no);
            wfShInstanceInfo.setMeetingUnitPrice(Comm.trimZeros(wfShInstanceInfo.getMeetingUnitPrice()));
            wfShInstanceInfo.setMeetingTotalPrice(Comm.trimZeros(wfShInstanceInfo.getMeetingTotalPrice()));

            // 说明机构切换了
            if ((!wfShInstanceInfo.getApplyOrg().equals(applyOrg) && !applyOrg.equals(""))) {
                // 重置小计
                WfShSubtotal wfShSubtotal = new WfShSubtotal();
                wfShSubtotal.setSubtotal(String.valueOf(subtotal));
                wfShSubtotal.setRowIndex(initRowIndex + no + orgNum - 1);
                wfShSubtotal.setOrgName(applyOrg);
                wfShSubtotalList.add(wfShSubtotal);
                orgNum++;
                subtotal = new BigDecimal(0);
            }

            no++;
            applyOrg = wfShInstanceInfo.getApplyOrg();

            // 做一个容错
            if (wfShInstanceInfo.getMeetingTotalPrice() == null || wfShInstanceInfo.getMeetingTotalPrice().equals("")) {
                wfShInstanceInfo.setMeetingTotalPrice("0");
            }

            subtotal = subtotal.add(new BigDecimal(wfShInstanceInfo.getMeetingTotalPrice()));
            total = total.add(new BigDecimal(wfShInstanceInfo.getMeetingTotalPrice()));
        }

        // 加结尾数据
        WfShSubtotal wfShSubtotalFinal = new WfShSubtotal();
        wfShSubtotalFinal.setSubtotal(String.valueOf(subtotal));
        wfShSubtotalFinal.setRowIndex(initRowIndex + no + orgNum - 1);
        wfShSubtotalFinal.setOrgName(applyOrg);
        wfShSubtotalList.add(wfShSubtotalFinal);

        // 保存链接，这个到时候需要根据配置动态起来
        TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/wfShInstanceExport.xlsx"));
        params.setColForEach(true);
        Map<String, Object> map = new HashMap<>();
        map.put("list", wfShInstanceInfoList);
        map.put("total", total);
        // 获取当前年月日 字符串
        map.put("date", Comm.getCurDate("yyyy年MM月dd日"));
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            Sheet sheet = workbook.getSheet("Sheet1");
            // 补充小计数据
            if (wfShSubtotalList.size() > 0) {
                for (WfShSubtotal wfShSubtotal : wfShSubtotalList) {
                    procureApplyService.appendSubtotalRow(workbook, sheet, wfShSubtotal);
                }
            }
            try {
                sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum() - 1, sheet.getLastRowNum() - 1, 1, 5));
            } catch (Exception e) {
                // 防止出现合并错误
            }

            try {
                sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(), sheet.getLastRowNum(), 0, 10));
            } catch (Exception e) {
                // 防止出现合并错误
            }
            workbook.write(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            outputStream.close();
            workbook.close();
        }

        byte[] contents = outputStream.toByteArray();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        String encodedFilename = new String("卫健局局务会议采购项目及其他待议事项表.xlsx".getBytes("UTF-8"), "ISO8859-1");
        headers.setContentDispositionFormData("attachment", encodedFilename);
        headers.setContentLength(contents.length);

        // 更新打印状态
        deviceHelperService.updateExportStatus(exportInstanceIdList);

        return new ResponseEntity<>(contents, headers, HttpStatus.OK);
    }

    @ApiOperation(value = "会议评审列表", notes = "会议评审列表")
    @GetMapping("/api/procure/selectPsInstance/list")
    public DataSet selectWfPsInstanceList(Page page, GetWfPsInstanceListReq req) {
        String orderRule = ",asc,desc,";
        String sord = req.getSord();

        String orderFieldRule = ",type,applyYear,applyOrg,meetingDate,approvalDate,export,";
        if (("," + orderFieldRule + ",").contains(req.getSidx())) {
            req.setSidx(Comm.convertCamelToSnake(req.getSidx()));
        } else {
            req.setSidx("");
        }

        if (sord == null || sord.equals("") || !("," + orderRule + ",").contains(req.getSord())) {
            req.setSord("asc");
        }

        return deviceHelperService.getWfPsInstanceList(page, req);
    }


    @RequestMapping(value = "/api/procure/psInstanceList/export")
    public ResponseEntity<byte[]> psInstanceListExport(@RequestBody ShInstanceListExportReq shInstanceListExportReq) throws Exception {

        List<String> exportInstanceIdList = new ArrayList<>();

        String exportInstanceId = shInstanceListExportReq.getExportInstanceId();

        if (exportInstanceId == null || exportInstanceId.equals("")) {
            throw new DeviceBusinessException("exportInstanceId不能为空");
        }
        // 拆分 exportInstanceId  逗号分隔
        String[] exportInstanceIdArr = exportInstanceId.split(",");
        exportInstanceIdList.addAll(Arrays.asList(exportInstanceIdArr));

        // 查询需要上会打印的数据
        List<WfPsInstanceInfo> wfPsInstanceInfoList = deviceHelperService.getWfPsInstanceListByIdLIst(exportInstanceIdList);


        if (wfPsInstanceInfoList == null || wfPsInstanceInfoList.isEmpty()) {
            throw new DeviceBusinessException("没有需要导出的数据");
        }
        // 这里需要保证顺序
        //Map<String, List<WfPsInstanceInfo>> groupedByDeptCode = wfPsInstanceInfoList.stream().collect(Collectors.groupingBy(WfPsInstanceInfo::getLaunchDeptCode));

        Map<String, List<WfPsInstanceInfo>> groupedByDeptCode = wfPsInstanceInfoList.stream().collect(Collectors.groupingBy(info -> info.getLaunchDeptCode(), LinkedHashMap::new, // 使用 LinkedHashMap 保留原始顺序
                Collectors.toList()));

        int index = 0;
        List<XWPFDocument> wordList = new ArrayList<>();

        String templatePath = Comm.getAbsolutePath("template/wfPsInstanceExport.docx");
        for (Map.Entry<String, List<WfPsInstanceInfo>> entry : groupedByDeptCode.entrySet()) {
            index++;
            List<WfPsInstanceInfo> wfPsInstanceInfo = entry.getValue();
            if (wfPsInstanceInfo == null || wfPsInstanceInfo.size() <= 0) {
                continue;
            }

            Map<String, Object> map = new HashMap<>();

            String dateStr = wfPsInstanceInfo.get(0).getMeetingDate();
            if (dateStr == null) {
                dateStr = "";
            } else {
                // 拆分出年月日
                String[] dateParts = dateStr.split("\\.");
                if (dateParts.length >= 3) {
                    map.put("year", dateParts[0]);
                    map.put("month", dateParts[1]);
                    map.put("day", dateParts[2]);
                }
            }

            // 转换单价的格式 去掉末尾的0

            if (wfPsInstanceInfo != null && wfPsInstanceInfo.size() != 0) {
                wfPsInstanceInfo.forEach(info -> {
                    info.setAup(Comm.trimZeros(info.getAup()));
                    info.setAtp(Comm.trimZeros(info.getAtp()));
                });
            }

            map.put("list", wfPsInstanceInfo);
            map.put("no", String.format("%s%s", dateStr.replace(".", ""), wfPsInstanceInfo.get(0).getCustomCode() != null ? wfPsInstanceInfo.get(0).getCustomCode() : ""));

            XWPFDocument tempDoc = WordExportUtil.exportWord07(ExportUtil.convertTemplatePath("template/wfPsInstanceExport.docx"), map);
            wordList.add(tempDoc);
            if (index < groupedByDeptCode.size()) {
                // 插入分页符 最后一页不需要分页符
                XWPFParagraph pageBreak = tempDoc.createParagraph();
                XWPFRun pageRun = pageBreak.createRun();
                pageRun.addBreak(BreakType.PAGE);
            }
        }

        XWPFDocument word = WordUtils.mergeWord(wordList);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        word.write(outputStream);
        byte[] contents = outputStream.toByteArray();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        String encodedFilename = new String("浏阳市卫生健康局投资项目会议审批单.docx".getBytes("UTF-8"), "ISO8859-1");
        headers.setContentDispositionFormData("attachment", encodedFilename);
        headers.setContentLength(contents.length);

        // 更新打印状态
        deviceHelperService.updateExportStatus(exportInstanceIdList);

        return new ResponseEntity<>(contents, headers, HttpStatus.OK);
    }

    @ApiOperation(value = "预算使用统计列表", notes = "预算使用统计列表")
    @PostMapping("/api/procureApply/getDepartmentBudgetStatisticsList")
    public DataSet<DepartmentBudget> getDepartmentBudgetStatisticsList(Page page, @RequestBody DepartmentBudget record) {
        try {
            logger.info("pageNo：" + record.getPageNo());
            logger.info("pageSize：" + record.getPageSize());
            if (null != record.getPageNo() && null != record.getPageSize()) {
                page.setPageNo(record.getPageNo());
                page.setPageSize(record.getPageSize());
            }
            List<DepartmentBudget> list = departmentBudgetService.getDepartmentBudgetStatisticsList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "预算使用统计详情列表", notes = "预算使用统计详情列表")
    @PostMapping("/api/procureApply/getDepartmentBudgetStatisticsDetailsList")
    public DataSet<Map<String, Object>> getDepartmentBudgetStatisticsDetailsList(Page page, @RequestBody DepartmentBudget record) {
        try {
            if (null != record.getPageNo() && null != record.getPageSize()) {
                page.setPageNo(record.getPageNo());
                page.setPageSize(record.getPageSize());
            }

            PurchaseInstanceInfoListReq purchaseInstanceInfoListReq = new PurchaseInstanceInfoListReq();
            purchaseInstanceInfoListReq.setDeptCode(record.getDeptId());
            purchaseInstanceInfoListReq.setEffectYear(record.getYear());
            purchaseInstanceInfoListReq.setType(record.getType());
            purchaseInstanceInfoListReq.setSord(record.getSord());
            purchaseInstanceInfoListReq.setSidx(record.getSidx());

            List<Map<String, Object>> list = departmentBudgetService.getDepartmentBudgetStatisticsDetailsList(page, purchaseInstanceInfoListReq);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
