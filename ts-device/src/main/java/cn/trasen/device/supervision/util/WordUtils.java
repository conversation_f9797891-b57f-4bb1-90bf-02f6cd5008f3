package cn.trasen.device.supervision.util;

import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @return null
 * @description TODO
 * @date 2024/3/16 16:05
 */
public final class WordUtils {

    /**
     * word文件合并
     *
     * @param wordList 要合并的文档集合
     * @return 合并后的文档对象
     * @throws Exception 合并过程中可能抛出异常
     */
    public static XWPFDocument mergeWord(List<XWPFDocument> wordList) throws Exception {
        if (CollectionUtils.isEmpty(wordList)) {
            throw new RuntimeException("待合并的word文档list为空");
        }

        XWPFDocument mergedDoc = new XWPFDocument();
        for (int i = 0; i < wordList.size(); i++) {
            XWPFDocument doc = wordList.get(i);
            merge(mergedDoc, doc);
        }


        return mergedDoc;
    }

    /**
     * 将sourceDoc合并到targetDoc中
     *
     * @param targetDoc 要合并到的目标文档
     * @param sourceDoc 要合并的源文档
     * @throws Exception 合并过程中可能抛出异常
     */
    private static void merge(XWPFDocument targetDoc, XWPFDocument sourceDoc) throws Exception {
        // 遍历源文档的所有段落和表格，逐个添加到目标文档中
        for (IBodyElement element : sourceDoc.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                XWPFParagraph sourcePar = (XWPFParagraph) element;
                XWPFParagraph targetPar = targetDoc.createParagraph();
                targetPar.getCTP().setPPr(sourcePar.getCTP().getPPr());
                for (XWPFRun run : sourcePar.getRuns()) {
                    XWPFRun newRun = targetPar.createRun();
                    newRun.getCTR().set(run.getCTR());
                }
            } else if (element instanceof XWPFTable) {
                XWPFTable sourceTable = (XWPFTable) element;
                XWPFTable targetTable = targetDoc.createTable();

//                for (XWPFTableRow row : sourceTable.getRows()) {
//                    for (XWPFTableCell cell : row.getTableCells()) {
//                        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
//                    }
//                }
                targetTable.getCTTbl().set(sourceTable.getCTTbl());

            }
        }
    }

    private static void removeEmptyPages(XWPFDocument document, Integer index) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = index; i < paragraphs.size(); i++) {
            document.removeBodyElement(document.getPosOfParagraph(paragraphs.get(i)));
        }
    }

    private static boolean containsTable(XWPFParagraph paragraph, List<XWPFTable> tables) {
        for (XWPFTable table : tables) {
            if (table.getCTTbl().getTblPr() == paragraph.getCTP().getPPr()) {
                return true;
            }
        }
        return false;
    }

    private static boolean containsPageBreak(XWPFParagraph paragraph) {

        List<XWPFRun> runs = paragraph.getRuns();
        // 遍历段落中的每个文本运行
        for (XWPFRun run : runs) {
            // 判断文本运行的文本内容是否包含分页符
            if (run.text().contains("\f") || run.text().contains("\u000c")) {
                return true;  // 如果找到分页符，返回true
            }
        }
        return false;  // 如果没有找到分页符，返回false
    }
}