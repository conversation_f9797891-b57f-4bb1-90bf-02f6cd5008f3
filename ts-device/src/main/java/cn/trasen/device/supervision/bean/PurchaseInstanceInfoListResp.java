package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.WfPsInstanceInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseInstanceInfoListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/5/10 15:13
 * @version: 1.0
 */

@Data

public class PurchaseInstanceInfoListResp extends WfPsInstanceInfo {

    @Column(name = "effect_year")
    @ApiModelProperty(value = "生效年份")
    private String effectYear;

    @Column(name = "SJCG_ZJE")
    @ApiModelProperty(value = "实际采购总金额")
    private String sjcgZje;

    @Column(name = "is_purchase")
    @ApiModelProperty(value = "是否采购")
    private String isPurchase;

}
