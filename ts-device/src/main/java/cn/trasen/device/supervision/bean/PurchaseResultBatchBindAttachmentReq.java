package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseResultBatchCheckReq
 * @author: chenbin
 * @description: 批量审核采购结果
 * @date: 2024/10/12 09:37
 * @version: 1.0
 */

@Data
public class PurchaseResultBatchBindAttachmentReq {

    @ApiModelProperty(value = "采购结果ID列表")
    private List<String> purchaseResultIdList;


    @ApiModelProperty(value = "采购合同文件")
    private String purchaseFiles;

    @ApiModelProperty(value = "招标文件")
    private String tenderFiles;

    @ApiModelProperty(value = "招标参数")
    private String tenderParameter;

    @ApiModelProperty(value = "采购备注")
    private String purchaseRemark;

}
