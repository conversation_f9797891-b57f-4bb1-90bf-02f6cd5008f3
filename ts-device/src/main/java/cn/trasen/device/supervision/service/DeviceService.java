package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq;
import cn.trasen.device.supervision.model.Device;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceService
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
public interface DeviceService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer save(Device record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer update(Device record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Integer deleteById(String id);

    void deleteByPurchaseLogId(String purchaseLogId);

    /**
     * @return Device
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    Device selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Device>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    DataSet<DeviceManageListResp> getDataSetList(Page page, Device record);

    List<Device> getListByPurchaseLogId(String purchaseLogId);

    DataSet<DeviceParamResp> getDeviceParamList(Page page, Device record);

    void submit(DeviceManageSaveReq record);


    DeviceManageDetailResp detail(String id);

    String genSysCode(String deptCode);

    List<Device> nullSysCodeList();

    Integer countByCateId(String cateId);

    Integer sbzs(IndexBlockYYZBReq req);

    Device getByDeviceCode(String deviceCode);

    List<CountsGroupByDeptIdResp> countsGroupByDeptId();

    List<Device> selectAll();

}
