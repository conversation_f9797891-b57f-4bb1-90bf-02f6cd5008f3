package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.index.IndexBlockDBCYReq;
import cn.trasen.device.supervision.bean.index.IndexBlockDBSXReq;
import cn.trasen.device.supervision.bean.index.IndexBlockYSCGReq;
import cn.trasen.device.supervision.model.DepartmentBudget;
import cn.trasen.device.supervision.model.WFBudgetInstanceInfo;

import org.apache.ibatis.annotations.Param;

import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DepartmentBudgetMapper extends Mapper<DepartmentBudget> {

    List<WfInstanceInfoBudgetCostResp> selectInstanceListByDefinitionIdList(WfInstanceInfoBudgetCostReq wfInstanceInfoBudgetCostReq);

    WfInstanceInfoBudgetCostResp getWfInstanceInfo(@Param("wfInstanceId") String wfInstanceId, @Param("dict") ProcureConfigInDict procureConfigInDict);

    List<Map<String, Object>> getProcureApplyPrice(@Param("wfDefinitionId") String wfDefinitionId, @Param("tableName") String tableName);

    List<Map<String, Object>> selectLaunchDeptInfo(@Param("code") String deptCode);

    List<Map<String, Object>> getProcureApplyServicePrice();

    List<DepartmentBudget> getDepartmentBudgetStatisticsList(Page page, DepartmentBudget record);

    List<Map<String, Object>> getDepartmentBudgetStatisticsDetailsList(Page page, PurchaseInstanceInfoListReq purchaseInstanceInfoListReq);

    Integer yssqsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer yssqbhsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ysyspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal yssqzje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal ysyspzje(IndexBlockDBCYReq indexBlockDBCYReq);


    BigDecimal zysje(IndexBlockYSCGReq indexBlockDBCYReq);

    BigDecimal ysyyje(IndexBlockYSCGReq indexBlockDBCYReq);

    Integer dspyssl(IndexBlockDBSXReq indexBlockDBCYReq);

    List<PurchaseInstanceInfoListResp> purchaseInstanceInfoListForCalcuBudget(PurchaseInstanceInfoListReq purchaseInstanceInfoListReq);

    List<WFBudgetInstanceInfo> budgetInstanceInfoListForCalcuBudget(BudgetInstanceInfoListReq budgetInstanceInfoListReq);

    DepartmentBudget getDepartmentBudgetHZ(DepartmentBudget departmentBudget);
}