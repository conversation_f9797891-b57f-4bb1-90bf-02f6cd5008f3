package cn.trasen.device.supervision.util;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.util
 * @className: ListUtils
 * @author: chenbin
 * @description: TODO
 * @date: 2024/7/3 14:54
 * @version: 1.0
 */

public class ListUtils {
    public static Object getElementOrEmptyString(List<Object> list, int index) {
        if (index >= 0 && index < list.size()) {
            return list.get(index);
        } else {
            return "";
        }
    }
}
