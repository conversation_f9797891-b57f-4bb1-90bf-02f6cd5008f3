package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockYYSBNDXYFXReq
 * @author: chenbin
 * @description: 医院设备年度效益分析请求结构体
 * @date: 2024/6/18 16:45
 * @version: 1.0
 */

@Data
public class IndexBlockYYSBNDXYFXResp {
    @ApiModelProperty(value = "收入")
    private String incomes;

    @ApiModelProperty(value = "设备数量")
    private String devices;

    @ApiModelProperty(value = "人次")
    private String times;

    @ApiModelProperty(value = "医院id")
    private String deptId;

    @ApiModelProperty(value = "医院名称")
    private String yymc;

}
