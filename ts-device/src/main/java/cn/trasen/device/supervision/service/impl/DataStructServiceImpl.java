package cn.trasen.device.supervision.service.impl;

import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.device.supervision.dao.DeviceWfDataStructsMapper;
import cn.trasen.device.supervision.exception.DeviceBusinessException;
import cn.trasen.device.supervision.model.DeviceWfDataStructs;
import cn.trasen.device.supervision.model.document.Attachment;
import cn.trasen.device.supervision.service.BusinessLockService;
import cn.trasen.device.supervision.service.DataStructService;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.util.PoiHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.time.Duration;
import java.util.*;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.service.impl
 * @className: DataStructServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/5 17:43
 * @version: 1.0
 */
@Slf4j
@Service
public class DataStructServiceImpl implements DataStructService {

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private DeviceWfDataStructsMapper deviceWfDataStructsMapper;

    @Autowired
    private BusinessLockService businessLockService;


    @Override
    public void scan() {
        log.info("开始扫描需要结构化的数据");
        // STRUCT_DATA_WK_DEFINITION_LIST
        // 获取需要结构化的流程定义ID
        Map<String, String> definitionList = deviceHelperService.getDictMapByCode("STRUCT_DATA_WK_DEFINITION_LIST");

        if (definitionList == null || definitionList.isEmpty()) {
            log.error("没有需要结构化的流程");
            return;
        }

        for (Map.Entry<String, String> entry : definitionList.entrySet()) {
            String value = entry.getValue();
            String[] parts = value.split("-");
            if (parts.length != 2) {
                log.error("流程定义ID和对应需要结构化的字段配置错误");
                continue;
            }
            // 获取实例列表
            List<Map<String, Object>> wfInstanceInfoList = deviceHelperService.getInstanceListByDefinitionId(parts[0]);

            if (wfInstanceInfoList == null || wfInstanceInfoList.isEmpty()) {
                continue;
            }

            for (Map<String, Object> wfInstanceInfo : wfInstanceInfoList) {
                // 状态：草稿 0 在办 1 办结 2 强制结束 3  撤销 4 终止 5 销毁 6
                // 不管什么状态都直接丢过去结构化
                dataStruct(wfInstanceInfo, parts[1]);
            }

        }
    }

    public static boolean containsAllKeys(Map<String, ?> map, String... keysToCheck) {
        for (String key : keysToCheck) {
            if (!map.containsKey(key)) {
                log.error("没有必要的参数" + key + "，跳过");
                return false;
            }
        }
        return true;
    }

    @Async
    @Override
    public void dataStruct(Map wfInstanceInfo, String structsField) {
        // structsField 后续可以支持多个字段的接口化。目前只做单字段支持 不过度设计
        // 因为scan已经过滤了不需要结构化的数据，所以这里不需要再次判断

        log.info("开始结构化流程数据");

        String[] filedCheckArr = {"WF_STATUS", structsField, "WF_INSTANCE_ID", "WF_DEFINITION_ID"};
        if (!containsAllKeys(wfInstanceInfo, filedCheckArr)) {
            log.error("没有必要的参数，跳过");
            return;
        }

        // 读取文件 返回大map 和特抽map
        Map<String, Object> detailDataMap = new HashMap<>();
        Map<String, Object> extractDetailDataMap = new HashMap<>();
        String fileId = "";
        String wfInstanceId = wfInstanceInfo.get("WF_INSTANCE_ID").toString();
        String wfDefinitionId = wfInstanceInfo.get("WF_DEFINITION_ID").toString();
        try {

            fileId = wfInstanceInfo.get(structsField).toString();
            if (fileId == null || fileId.isEmpty()) {
                log.error("没有文件，无需结构化");
                throw new DeviceBusinessException("没有文件，无需结构化");
            }


            // STRUCT_DATA_TPL_3CB722A5FD8B4A8795D57D6825CB0B42
            // 开始结构化数据

            // 这里需要考虑附件存在多个，只取第一个附件进行结构化
            if (fileId.contains(",")) {
                fileId = fileId.split(",")[0];
            }

            Attachment attachment = deviceHelperService.getAttachmentById(fileId);
            if (attachment == null) {
                log.error("附件不存在，无法结构化");
                throw new DeviceBusinessException("附件不存在，无法结构化");
            }


            Boolean hasLocked = businessLockService.acquireLock("dataStruct", fileId, Duration.ofSeconds(60));
            // 被锁了 直接return 终止运行
            if (!hasLocked) {
                log.error("文件正在结构化中，跳过");
                return;
            }
            // 全量抽取结构化数据
            String filePath = attachment.getRealPath();
            detailDataMap = PoiHelper.readExcel(filePath);
            if (detailDataMap.isEmpty()) {
                throw new DeviceBusinessException("excel 没有取到任何数据");
            }

            // 准备读取的坐标集合 不配置了 直接全量读取
            Map<String, String> coordinateMap = deviceHelperService.getDictMapByCode("STRUCT_DATA_TPL_" + wfDefinitionId);
            if (coordinateMap == null || coordinateMap.isEmpty()) {
                throw new DeviceBusinessException("没有配置结构化抽取模板");
            }

            // 特殊抽取结构化数据

            for (Map.Entry<String, String> entry : coordinateMap.entrySet()) {
                String key = entry.getKey();
                String coordinate = entry.getValue();
                String value = detailDataMap.containsKey(coordinate) ? detailDataMap.get(coordinate).toString() : "";
                extractDetailDataMap.put(key, value);
            }

        } catch (Exception e) {
            log.error("结构化数据出现异常", e);
        } finally {
            // 不管怎么样都需要释放锁
            businessLockService.releaseLock("dataStruct", fileId);
        }
        // 判断是insert 还是 update
        DeviceWfDataStructs hasDataStructed = hasDataStructed(wfInstanceId);
        // 把 wfInstanceInfo.get("WF_UPDATE_DATE").toString() 转成date 类型
        Date wfUpdateDate = new Date();

        try {
            wfUpdateDate = (Date) wfInstanceInfo.get("WF_UPDATE_DATE");
        } catch (Exception e) {
            // 按道理不可能有这个异常
            log.error("日期转换出现异常", e);
        }

        JSON detailDataMap2json = null;
        JSON extractDetailDataMap2json = null;


        try {
            detailDataMap2json = new JSONObject(detailDataMap);
            extractDetailDataMap2json = new JSONObject(extractDetailDataMap);
        } catch (Exception e) {
            log.error("json转换出现异常", e);
        }

        DeviceWfDataStructs deviceWfDataStructs = new DeviceWfDataStructs();
        deviceWfDataStructs.setWfInstanceLastUpdatedDate(wfUpdateDate);
        deviceWfDataStructs.setDetial(detailDataMap2json);
        deviceWfDataStructs.setExtractDetial(extractDetailDataMap2json);

        // 判断是否存在结构化记录，不存在的化直接插入，存在的话判断是否需要更新

        if (hasDataStructed != null) {
            // 更新
            deviceWfDataStructs.setId(hasDataStructed.getId());
            update(deviceWfDataStructs);
        } else {

            deviceWfDataStructs.setWfDefinitionId(wfInstanceInfo.get("WF_DEFINITION_ID").toString());
            deviceWfDataStructs.setWfInstanceId(wfInstanceId);
            deviceWfDataStructs.setStatus(1);

            // 插入
            insert(deviceWfDataStructs);
        }

    }

    private Integer insert(DeviceWfDataStructs record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return deviceWfDataStructsMapper.insertSelective(record);
    }

    private Integer update(DeviceWfDataStructs record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return deviceWfDataStructsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DeviceWfDataStructs hasDataStructed(String instanceId) {
        Example example = new Example(DeviceWfDataStructs.class);
        example.createCriteria().andEqualTo("wfInstanceId", instanceId);

        try {
            DeviceWfDataStructs deviceWfDataStructs = deviceWfDataStructsMapper.selectOneByExample(example);
            return deviceWfDataStructs;
        } catch (Exception e) {
            log.error("查询流程ID:" + instanceId + ",出现了多行记录的严重错误", e);
            // 删除当前的结构化数据，等待下一轮的自动创建
            deviceWfDataStructsMapper.deleteByExample(example);
            return null;
        }
    }


}
