package cn.trasen.device.supervision.task;

import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.service.IndexStatisticsService;
import cn.trasen.device.supervision.util.CacheHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.task
 * @className: GenIndexCacheTask
 * @author: chenbin
 * @description: 生成首页缓存 分开生成，避免某部分错误导致整个首页缓存生成失败
 * @date: 2024/7/29 11:45
 * @version: 1.0
 */

@Component
public class GenIndexCacheTask {
    private Logger logger = LoggerFactory.getLogger(SyncIncomeDataTask.class);

    // 每次同步数据的条数

    @Autowired
    private IndexStatisticsService indexStatisticsService;

    private CacheHelper cacheHelper;

    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 增量备份
     * @date 2024/6/17 16:02
     */

    @Scheduled(cron = "0 0/3 * * * ?")
    public void genCacheForIndexBlockDBCYResp() {


        // 暂时只解决领导首页慢的问题
        String deptId = "admin";

        int currentYear = LocalDateTime.now().getYear();
        String currentYearString = String.valueOf(currentYear);


        logger.info("==================开始生成首页缓存 indexBlockDBCYResp==================");

        IndexBlockDBCYReq indexBlockDBCYReq = new IndexBlockDBCYReq();
        indexBlockDBCYReq.setDeptId(deptId);
        indexBlockDBCYReq.setYear(currentYearString);
        indexBlockDBCYReq.setIgnoreCache(true);

        indexStatisticsService.getIndexBlockDBCYResp(indexBlockDBCYReq);

        logger.info("==================生成首页缓存结束 indexBlockDBCYResp==================");

    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void genCacheForIndexBlockYSCGResp() {


        // 暂时只解决领导首页慢的问题
        String deptId = "admin";

        int currentYear = LocalDateTime.now().getYear();
        String currentYearString = String.valueOf(currentYear);


        logger.info("==================开始生成首页缓存 indexBlockYSCGResp==================");

        IndexBlockYSCGReq indexBlockYSCGReq = new IndexBlockYSCGReq();
        indexBlockYSCGReq.setDeptId(deptId);
        indexBlockYSCGReq.setYear(currentYearString);
        indexBlockYSCGReq.setIgnoreCache(true);
        indexStatisticsService.getIndexBlockYSCGResp(indexBlockYSCGReq);

        logger.info("==================生成首页缓存结束 indexBlockYSCGResp==================");

    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void genCacheForIndexBlockYYZBResp() {


        // 暂时只解决领导首页慢的问题
        String deptId = "admin";

        logger.info("==================开始生成首页缓存 indexBlockYYZBResp==================");

        IndexBlockYYZBReq indexBlockYYZBReq = new IndexBlockYYZBReq();
        indexBlockYYZBReq.setDeptId(deptId);
        indexBlockYYZBReq.setIgnoreCache(true);
        indexStatisticsService.getIndexBlockYYZBResp(indexBlockYYZBReq);

        logger.info("==================生成首页缓存结束 indexBlockYYZBResp==================");

    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void genCacheForindexBlockYYZBResp() {


        // 暂时只解决领导首页慢的问题
        String deptId = "admin";

        int currentYear = LocalDateTime.now().getYear();
        String currentYearString = String.valueOf(currentYear);

        logger.info("==================开始生成首页缓存 indexBlockYYZBResp==================");

        IndexBlockDBSXReq indexBlockDBSXReq = new IndexBlockDBSXReq();
        indexBlockDBSXReq.setDeptId(deptId);
        indexBlockDBSXReq.setYear(currentYearString);
        indexBlockDBSXReq.setIgnoreCache(true);
        indexStatisticsService.getIndexBlockDBSXResp(indexBlockDBSXReq);

        logger.info("==================生成首页缓存结束 indexBlockYYZBResp==================");

    }
}
