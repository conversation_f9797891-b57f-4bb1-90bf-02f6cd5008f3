package cn.trasen.device.supervision.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.comm.ImportErrRow;
import cn.trasen.device.supervision.bean.comm.ImportResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.device.supervision.exception.DeviceBusinessException;
import cn.trasen.device.supervision.model.*;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.dao.PurchaseResultMapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseResultServiceImpl
 * @Description TODO
 * @date 2024��4��2�� ����7:27:49
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseResultServiceImpl implements PurchaseResultService {

    @Autowired
    private PurchaseResultMapper mapper;

    @Autowired
    private PurchaseGroupService purchaseGroupService;

    @Autowired
    private PurchaseLogService purchaseLogService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ServicesService servicesService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private CategoryService categoryService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseResult record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false, isolation = Isolation.READ_COMMITTED)
    @Override
    public void batchUpdate(PurchaseResultReq record) {
        // 按块读取 有ID 则更新 无ID 则新增
        PurchaseResult purchaseResultRow = this.selectById(record.getId());

        if (purchaseResultRow == null) {
            throw new DeviceBusinessException("采购记录不存在");
        }

        List<PurchaseGroupReq> purchaseGroupReqList = record.getPurchaseGroupReqList();

        if (purchaseGroupReqList == null || purchaseGroupReqList.size() == 0) {
            throw new DeviceBusinessException("请完善好数据再提交");
        }

        // loop purchaseGroupReqList to save or update

        for (PurchaseGroupReq purchaseGroupReq : purchaseGroupReqList) {

            purchaseGroupReq.setPurchaseResultId(record.getId());
            if (purchaseGroupReq.getId() == null || purchaseGroupReq.getId().isEmpty()) {
                // save
                purchaseGroupService.save(purchaseGroupReq);
            } else {
                // update
                purchaseGroupService.update(purchaseGroupReq);
            }

            List<PurchaseLog> purchaseLogList = purchaseGroupReq.getPurchaseLogList();
            if (purchaseLogList == null || purchaseLogList.size() == 0) {
                continue;
            }

            // loop purchaseLogList to save or update
            for (PurchaseLog purchaseLog : purchaseLogList) {
                purchaseLog.setPurchaseGroupId(purchaseGroupReq.getId());

                // TODO 这里的奇葩设计 为了兼容老版本的数据 有些字段是相等的 服务项目的时间和规格型号 一般设备的规格型号和建设规模
                // 对产品变来变去的无奈妥协
                // 本来设计了一堆的字段，后来又变来变去，只能通过这种方式来处理
                if (purchaseResultRow.getType().equals("服务项目采购申请表")) {
                    // 对 spec 进行拆分
                    if (purchaseLog.getPurchaseSpec() != null && purchaseLog.getPurchaseSpec().contains("-")) {
                        String[] split = purchaseLog.getPurchaseSpec().split("-");
                        if (split.length == 2) {
                            String start = split[0];
                            String end = split[1];
                            // 把 . 替换为 -
                            start = start.replace(".", "-");
                            end = end.replace(".", "-");
                            purchaseLog.setServiceStartAt(Comm.string2Date(start));
                            purchaseLog.setServiceEndAt(Comm.string2Date(end));
                        }
                    }
                    // 如果没有服务时间则清空 反向填充，兼容导入服务时间
                    if (purchaseLog.getPurchaseSpec() == null || purchaseLog.getPurchaseSpec().isEmpty()) {
                        if (purchaseLog.getServiceStartAt() != null || purchaseLog.getServiceEndAt() != null) {
                            purchaseLog.setPurchaseSpec(Comm.dateFormate(purchaseLog.getServiceStartAt(), "yyyy.MM.dd") + "-" + Comm.dateFormate(purchaseLog.getServiceEndAt(), "yyyy.MM.dd"));
                        }
                    }
                }

                // 处理字段相等的情况
                if (purchaseResultRow.getType().equals("工程项目采购申请表")) {

                    // 如果规格型号存在 则使用规格型号填充建设规模
                    if (purchaseLog.getPurchaseSpec() != null) {
                        purchaseLog.setBuildScale(purchaseLog.getPurchaseSpec());
                    }
                    // 如果规格型号不存在 则使用建设规模
                    if (purchaseLog.getPurchaseSpec() == null) {
                        purchaseLog.setPurchaseSpec(purchaseLog.getBuildScale());
                    }

                }

                // 获取设备集

                List<Device> deviceList = purchaseLog.getDeviceList();

                if (purchaseLog.getId() == null || purchaseLog.getId().isEmpty()) {
                    // save
                    purchaseLogService.save(purchaseLog);
                    // save device, project, services
                    Device device = new Device();
                    String deviceType = "一般设备采购申请表,医疗专用设备采购申请表";
                    if (deviceType.contains(purchaseResultRow.getType())) {
                        device.setPurchaseLogId(purchaseLog.getId());
                        device.setDeptId(purchaseResultRow.getDeptId());
                        device.setYbsfCode("");
                        device.setName(purchaseLog.getName());
                        device.setSupplier(purchaseLog.getSupplier());
                        device.setProducer(purchaseLog.getProducer());
                        device.setBrand(purchaseLog.getPurchaseBrand());
                        device.setSpec(purchaseLog.getPurchaseSpec());
                        device.setFundsSources(purchaseLog.getFundsSources());
                        device.setUnitPrice(purchaseLog.getPurchasePrice());
                        device.setIsComplete("1");
                        device.setStatus("0");

                        // 设备通用名和分类ID
                        device.setCommonName(purchaseLog.getCommonName());
                        device.setCateId(purchaseLog.getCateId());

                    }


                    switch (purchaseResultRow.getType()) {
                        case "一般设备采购申请表":

                            // 兼容
                            if (deviceList != null && deviceList.size() > 0) {
                                for (Device device1 : deviceList) {
                                    device.setDeviceCode(device1.getDeviceCode());
                                    if (device1.getName() != null) {
                                        device.setName(device1.getName());
                                    }
                                    device.setAssetCode(device1.getAssetCode());
                                    device.setStatus(device1.getStatus());
                                    deviceService.save(device);
                                }
                            } else {
                                device.setType("0");
                                device.setSysCode(deviceService.genSysCode(purchaseResultRow.getDeptId()));
                                deviceService.save(device);
                                break;
                            }

                        case "医疗专用设备采购申请表":
                            if (deviceList != null && deviceList.size() > 0) {
                                for (Device device1 : deviceList) {
                                    device.setType("1");
                                    device.setSysCode(deviceService.genSysCode(purchaseResultRow.getDeptId()));
                                    device.setDeviceCode(device1.getDeviceCode());
                                    if (device1.getName() != null) {
                                        device.setName(device1.getName());
                                    }
                                    device.setAssetCode(device1.getAssetCode());
                                    device.setStatus(device1.getStatus());
                                    deviceService.save(device);
                                }
                            } else {
                                // 兼容老版本
                                // 按照购买数量添加多个设备
                                int num = 1;
                                try {
                                    num = Integer.parseInt(purchaseLog.getPurchaseNumbers());
                                } catch (NumberFormatException e) {
                                    // 出现异常默认为 1
                                    num = 1;
                                }

                                for (int i = 0; i < num; i++) {
                                    device.setType("1");
                                    device.setSysCode(deviceService.genSysCode(purchaseResultRow.getDeptId()));
                                    deviceService.save(device);
                                }
                            }


                            break;
                        case "服务项目采购申请表":
                            Services services = new Services();
                            services.setPurchaseLogId(purchaseLog.getId());
                            services.setDeptId(purchaseResultRow.getDeptId());
                            services.setName(purchaseLog.getName());
                            services.setFundsSources(purchaseLog.getFundsSources());
                            services.setUnitPrice(purchaseLog.getPurchasePrice());
                            services.setTotalPrice(purchaseLog.getPurchaseTotalPrice());
                            services.setServiceStartAt(purchaseLog.getServiceStartAt());
                            services.setServiceEndAt(purchaseLog.getServiceEndAt());
                            services.setFiles(purchaseLog.getPurchaseFiles());
                            services.setIsComplete("1");
                            servicesService.save(services);
                            break;
                        case "工程项目采购申请表":
                            Project project = new Project();
                            project.setPurchaseLogId(purchaseLog.getId());
                            project.setDeptId(purchaseResultRow.getDeptId());
                            project.setName(purchaseLog.getName());
                            project.setBuildScale(purchaseLog.getBuildScale());
                            project.setInvestmentScale(purchaseResultRow.getPurchaseTotalPrice());
                            project.setAcceptDate(purchaseLog.getAcceptDate());
                            project.setFundsSources(purchaseLog.getFundsSources());
                            project.setUnitPrice(purchaseLog.getPurchasePrice());
                            project.setFiles(purchaseLog.getPurchaseFiles());
                            project.setIsComplete("1");
                            projectService.save(project);
                            break;
                    }

                } else {
                    // update
                    purchaseLogService.update(purchaseLog);

                    // 处理设备部分的特殊字段更新
                    if (deviceList != null && deviceList.size() > 0) {
                        for (Device device1 : deviceList) {
                            deviceService.update(device1);
                        }
                    }
                    // 配合前端更新设备 放在上面会出现覆盖现象
                    mapper.syncToEntity(purchaseResultRow.getType(), purchaseLog);
                }
            }
        }

        // update purchase result status
        PurchaseResult purchaseResult = new PurchaseResult();
        purchaseResult.setId(record.getId());
        purchaseResult.setStatus("1");

        String typeRule = ",医疗专用设备采购申请表,一般设备采购申请表,工程项目采购申请表,服务项目采购申请表,";
        // record.getType() 的值必须是 医疗专用设备采购申请表,一般设备采购申请表,服务项目采购申请表,工程项目采购申请表 中的一个
        if (purchaseResultRow.getType() != null && typeRule.contains("," + purchaseResultRow.getType() + ",")) {
            if (record.getType() != null && typeRule.contains("," + record.getType() + ",")) {
                purchaseResult.setType(record.getType());
                // 改变对应的definitionID
                if ("医疗专用设备采购申请表".equals(record.getType())) {
                    purchaseResult.setWfDefinitionId("06985B07055046639923C386071BA66E");
                } else if ("一般设备采购申请表".equals(record.getType())) {
                    purchaseResult.setWfDefinitionId("A37FCFB6631B405EA3A790E32FE88655");
                } else if ("工程项目采购申请表".equals(record.getType())) {
                    purchaseResult.setWfDefinitionId("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0");
                } else if ("服务项目采购申请表".equals(record.getType())) {
                    purchaseResult.setWfDefinitionId("1866B74A6BC3400F990AB9E3C2DAA1A6");
                }
            } else {
                throw new DeviceBusinessException("采购类型错误");
            }
        }


        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            purchaseResult.setUpdateUser(user.getUsercode());
            purchaseResult.setUpdateUserName(user.getUsername());
        }

        String deptCode = deviceHelperService.getDeptCode();

        // 非官方人员修改重制确认状态
        if (!deptCode.equals("admin")) {
            purchaseResult.setStatus("1");
        }
        mapper.updateByPrimaryKeySelective(purchaseResult);

    }


    /**
     * @param record:
     * @return ImportResp
     * <AUTHOR>
     * @description *
     * 1 先查询所有提交数据的ID，把所有能够维护的ID过滤出来
     * 2 对过滤出来本次更新的数据对老数据进行清空
     * 3 构造新数据调用 batchUpdate
     * @date 2024/10/10 10:26
     */
    @Transactional(readOnly = false)
    @Override
    public ImportResp importUpdate(List<PurchaseResultImport> record) {
        ImportResp importResp = new ImportResp();
        int succs = 0;
        int errs = 0;
        List<ImportErrRow> errRows = new ArrayList<>();

        if (record == null || record.size() == 0) {
            importResp.setSuccs(succs);
            importResp.setErrs(errs);
            importResp.setErrRows(errRows);
            return importResp;
        }

        // 查询所有分类
        List<Category> categoryList = categoryService.getList(null, new Category());
        // 用名称为key 用id 为value 做一个 map
        Map<String, String> categoryMap = categoryList.stream().collect(
                Collectors.toMap(
                        Category::getName,
                        Category::getId,
                        (existing, replacement) -> existing // or replacement, depending on your requirement
                )
        );

        // 获取所有的ID，得到一个List<String>
        List<String> purchaseResultIdList = record.stream().map(PurchaseResultImport::getId).collect(Collectors.toList());
        Set<String> okIdSet = mapper.getIdNeedMaintenance(purchaseResultIdList);

        Set<String> okIdSetBackUp = new HashSet<>();
        okIdSetBackUp.addAll(okIdSet);

        // 对record 进行循环，把采购项目名称为空的全部忽略掉，不进行处理，对okIdSet中的ID进行处理
        // 防止导出了所有的记录，但是只修改了其中的一部分，造成数据丢失

        Set<String> ignore = new HashSet<>();
        for (PurchaseResultImport purchaseResultImport : record) {
            if (StringUtils.isBlank(purchaseResultImport.getName())) {
                ignore.add(purchaseResultImport.getId());
            }
        }
        okIdSet.removeAll(ignore);


        // 先进行数据清空
        emptyOldData(okIdSet);

        int loopIndex = 1;
        for (PurchaseResultImport purchaseResultImport : record) {

            loopIndex++;

            // 通用名未空，忽略
            if (StringUtils.isBlank(purchaseResultImport.getName())) {
                ImportErrRow importErrRow = new ImportErrRow();
                importErrRow.setIndex(loopIndex);
                importErrRow.setErrMsg("空数据行，忽略");
                errs++;
                errRows.add(importErrRow);
                continue;
            }

            if (!okIdSetBackUp.contains(purchaseResultImport.getId())) {
                ImportErrRow importErrRow = new ImportErrRow();
                importErrRow.setIndex(loopIndex);
                importErrRow.setErrMsg("该行审核通过状态或是已取消采购状态，忽略");
                errs++;
                errRows.add(importErrRow);
                continue;
            }


            try {

                PurchaseResult purchaseResult = selectById(purchaseResultImport.getId());

                PurchaseResultReq prr = new PurchaseResultReq();
                prr.setId(purchaseResultImport.getId());
                prr.setType(purchaseResultImport.getType());

                List<PurchaseGroupReq> pgrList = new ArrayList<>();

                PurchaseGroupReq pgr = new PurchaseGroupReq();
                pgr.setPurchaseResultId(purchaseResultImport.getId());
                pgr.setPurchaseRemark(purchaseResultImport.getPurchaseRemark());
                // 放置别的机构给导数据 你无法决定客户的行为
                pgr.setDeptId(purchaseResult.getDeptId());

                List<PurchaseLog> plList = new ArrayList<>();
                PurchaseLog pl = new PurchaseLog();

                pl.setName(purchaseResultImport.getName());
                pl.setPurchaseDate(purchaseResultImport.getPurchaseDate());
                pl.setSupplier(purchaseResultImport.getSupplier());
                pl.setProducer(purchaseResultImport.getProducer());
                pl.setPurchaseBrand(purchaseResultImport.getPurchaseBrand());
                pl.setPurchaseSpec(purchaseResultImport.getPurchaseSpec());
                pl.setPurchaseWay(purchaseResultImport.getPurchaseWay());
                pl.setBuildScale(purchaseResultImport.getBuildScale());
                pl.setAcceptDate(purchaseResultImport.getAcceptDate());
                pl.setPurchaseNumbers(purchaseResultImport.getPurchaseNumbers());
                pl.setPurchasePrice(purchaseResultImport.getPurchasePrice());
                pl.setPurchaseTotalPrice(purchaseResultImport.getPurchaseTotalPrice());
                pl.setServiceStartAt(purchaseResultImport.getServiceStartAt());
                pl.setServiceEndAt(purchaseResultImport.getServiceEndAt());

                if (purchaseResultImport.getServiceYears() != null) {
                    pl.setPurchaseNumbers(purchaseResultImport.getServiceYears());
                }

                pl.setPurchaseRemark(purchaseResultImport.getPurchaseRemark());
                pl.setCateId(categoryMap.get(purchaseResultImport.getCateName().trim()));
                pl.setCommonName(purchaseResultImport.getCommonName());
                pl.setFundsSources(purchaseResult.getSourcesFunds());

                // 构造deviceList
                List<Device> dList = new ArrayList<>();

                String deviceCode = Optional.ofNullable(purchaseResultImport.getDeviceCode()).orElse("");
                String assetCode = Optional.ofNullable(purchaseResultImport.getAssetCode()).orElse("");

                // 根据逗号拆分 deviceCode 和 assetCode
                String[] deviceCodeArr = deviceCode.split("#");
                String[] assetCodeArr = assetCode.split("#");

                // 确保两个数组长度一致，取最小长度
                int length = Math.max(deviceCodeArr.length, assetCodeArr.length);


                // 循环拆分的数组
                for (int i = 0; i < length; i++) {
                    Device d = new Device();
                    String deviceCodeSingle = "";
                    String assetCodeSingle = "";
                    try {
                        deviceCodeSingle = deviceCodeArr[i];
                        assetCodeSingle = assetCodeArr[i];
                    } catch (Exception e) {
                        // 什么都不做
                    }
                    d.setDeviceCode(deviceCodeSingle);
                    d.setAssetCode(assetCodeSingle);
                    dList.add(d);
                }

                pl.setDeviceList(dList);
                plList.add(pl);
                pgr.setPurchaseLogList(plList);
                pgrList.add(pgr);
                prr.setPurchaseGroupReqList(pgrList);

                batchUpdate(prr);
                succs++;

            } catch (Exception e) {

                ImportErrRow importErrRow = new ImportErrRow();
                importErrRow.setIndex(loopIndex);
                importErrRow.setErrMsg(e.getMessage());
                errs++;
                errRows.add(importErrRow);

            }

        }

        importResp.setSuccs(succs);
        importResp.setErrs(errs);
        importResp.setErrRows(errRows);

        return importResp;
    }


    @Transactional(readOnly = false)
    public void emptyOldData(Set<String> idSet) {
        // loop idSet
        if (idSet == null || idSet.size() == 0) {
            return;
        }

        for (String id : idSet) {
            List<PurchaseGroup> pgList = purchaseGroupService.getListByPurchaseResultId(id);
            if (pgList != null && pgList.size() > 0) {
                for (PurchaseGroup pg : pgList) {
                    List<PurchaseLog> plList = purchaseLogService.getListByPurchaseGroupId(pg.getId());
                    if (plList != null && plList.size() > 0) {
                        for (PurchaseLog pl : plList) {
                            // 删除设备
                            deviceService.deleteByPurchaseLogId(pl.getId());
                            // 删除服务
                            servicesService.deleteByPurchaseLogId(pl.getId());
                            // 删除工程
                            projectService.deleteByPurchaseLogId(pl.getId());
                        }
                    }
                    // 通过组ID 删除
                    purchaseLogService.deleteByPurchaseGroupId(pg.getId());

                }
            }
            // 通过结果ID 删除
            purchaseGroupService.deleteByPurchaseResultId(id);
        }

    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseResult record) {

        // 这部分好想没什么作用了，因为准确的数据落实到了purchaseLog了
        //服务项目计算数量
        if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(record.getWfDefinitionId())) {

            if (StringUtils.isNotBlank(record.getPurchaseTotalPrice()) && StringUtils.isNotBlank(record.getPurchasePrice())) {
                BigDecimal purchaseTotalPrice = new BigDecimal(record.getPurchaseTotalPrice());
                BigDecimal purchasePrice = new BigDecimal(record.getPurchasePrice());
                BigDecimal divide = purchaseTotalPrice.divide(purchasePrice, 0, RoundingMode.UP);
                record.setPurchaseNumbers(divide.toString());
            }

        }
        //工程项目采购
        if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(record.getWfDefinitionId())) {
            if (StringUtils.isNotBlank(record.getPurchaseTotalPrice())) {
                record.setPurchasePrice(record.getPurchaseTotalPrice());
                record.setPurchaseNumbers("1");
            }
        }
        if (record.getStatus() == null) {
            record.setStatus("1");
        }

        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseResult record = new PurchaseResult();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PurchaseResult selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public PurchaseResultResp selectByIdV3(String id) {
        Assert.hasText(id, "ID不能为空.");

        PurchaseResultResp purchaseResultResp = new PurchaseResultResp();

        purchaseResultResp.setPurchaseResult(mapper.selectByPrimaryKey(id));

        List<PurchaseGroup> purchaseGroups = purchaseGroupService.getListByPurchaseResultId(id);
        purchaseResultResp.setPurchaseGroupList(purchaseGroups);

        List<PurchaseLog> purchaseLogs = new ArrayList<>();
        if (purchaseGroups != null && purchaseGroups.size() > 0) {
            for (PurchaseGroup purchaseGroup : purchaseGroups) {
                // 合并 purchaseLogs
                List<PurchaseLog> purchaseLogListCurGroup = purchaseLogService.getListByPurchaseGroupId(purchaseGroup.getId());
                // loop 往里面插入 device 数据
                for (PurchaseLog purchaseLog : purchaseLogListCurGroup) {
                    purchaseLog.setDeviceList(deviceService.getListByPurchaseLogId(purchaseLog.getId()));
                }
                purchaseLogs.addAll(purchaseLogListCurGroup);

            }
        }
        purchaseResultResp.setPurchaseLogList(purchaseLogs);
        return purchaseResultResp;
    }

    /**
     * @param page:
     * @param record:
     * @return DataSet<PurchaseResult>
     * <AUTHOR>
     * @description 最早期未拆表的版本 (废弃)
     * @date 2024/4/12 16:04
     */
    @Override
    public DataSet<PurchaseResult> getDataSetList(Page page, PurchaseResult record) {

        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员

        if (!right) {
            record.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }

        if (StringUtils.isEmpty(page.getSidx())) {
            page.setSidx("approval_date");
            page.setSord("desc");
        }

        if (StringUtils.isNotBlank(record.getKeyword())) {
            String output = record.getKeyword().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setKeyword(output.substring(1));
        }

        List<PurchaseResult> records = mapper.getDataSetList(record, page);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param page:
     * @param record:
     * @return DataSet<PurchaseResult>
     * <AUTHOR>
     * @description 返回采购结果数据平铺的版本（申请时候历史数据查询使用）
     * @date 2024/4/12 16:03
     */
    @Override
    public DataSet<Map> getDataSetListV25(Page page, PurchaseResult record) {

//        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员
//
//        if (!right) {
//            record.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
//        }

        if (StringUtils.isEmpty(page.getSidx())) {
            page.setSidx("approval_date");
            page.setSord("desc");
        }

        if (StringUtils.isNotBlank(record.getKeyword())) {
            String output = record.getKeyword().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setKeyword(output.substring(1));
        }


        List<Map> records = mapper.getDataSetListV3(record, page);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param page:
     * @param record:
     * @return DataSet<PurchaseResult>
     * <AUTHOR>
     * @description 返回采购数据内嵌到基础数据的版本
     * @date 2024/4/12 16:03
     */
    @Override
    public DataSet<PurchaseResult> getDataSetListV3(Page page, PurchaseResult record) {
        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员

        if (!right) {
            record.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }

        if (StringUtils.isEmpty(page.getSidx())) {
            page.setSidx("approval_date");
            page.setSord("desc");
        }

        if (StringUtils.isNotBlank(record.getKeyword())) {
            String output = record.getKeyword().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setKeyword(output.substring(1));
        }


        List<PurchaseResult> records = mapper.getDataSetList(record, page);
        if (records != null && records.size() > 0) {
            // 这里如果查询数据过多，轮训会造成多次的数据库查询 造成接口返回时间暴增
//            for (PurchaseResult purchaseResult : records) {
//                purchaseResult.setPurchaseLogList(purchaseLogService.getListByPurchaseResultId(purchaseResult.getId()));
//            }
            List<String> purchaseResultIdList = records.stream().map(PurchaseResult::getId).collect(Collectors.toList());
            List<PurchaseLog> purchaseLogs = purchaseLogService.getListByPurchaseResultIdList(purchaseResultIdList);
            // 采购记录按照采购结果ID分组
            Map<String, List<PurchaseLog>> purchaseLogMap = purchaseLogs.stream().collect(Collectors.groupingBy(PurchaseLog::getPurchaseResultId));
            for (PurchaseResult purchaseResult : records) {
                List<PurchaseLog> PurchaseLogListById = purchaseLogMap.get(purchaseResult.getId());
                if (PurchaseLogListById != null) {
                    purchaseResult.setPurchaseLogList(PurchaseLogListById);
                } else {
                    purchaseResult.setPurchaseLogList(new ArrayList<>());
                }
            }

        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<PurchaseResult> getDataSetListV3NoPage(PurchaseResult record) {
        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员

        if (!right) {
            record.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }

        if (StringUtils.isNotBlank(record.getKeyword())) {
            String output = record.getKeyword().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setKeyword(output.substring(1));
        }

        List<PurchaseResult> records = mapper.getDataSetListNoPage(record);
        // 对状态进行处理
        for (PurchaseResult purchaseResult : records) {
            // @ApiModelProperty(value = " 状态 -1 取消采购  0待维护  1已维护 待确认 2 已确认通过 3 已确认未通过 ")
            switch (purchaseResult.getStatus()) {
                case "-1":
                    purchaseResult.setStatusShow("已取消采购");
                    break;
                case "0":
                    purchaseResult.setStatusShow("待维护");
                    break;
                case "1":
                    purchaseResult.setStatusShow("已维护 待确认");
                    break;
                case "2":
                    purchaseResult.setStatusShow("审核通过");
                    break;
                case "3":
                    purchaseResult.setStatusShow("审核不通过");
                    break;
                default:
                    purchaseResult.setStatusShow("未知状态");
                    break;
            }
        }

        return records;
    }

    @Override
    @Transactional(readOnly = false)
    public void syncPurchaseResultData() {
        mapper.syncPurchaseResultData();
    }


    @Transactional(readOnly = false, isolation = Isolation.READ_COMMITTED)
    @Override
    public void dataV22V3() {
        // 查询所有的结果数据
        // 检测是否存在group数据 不存在则创建
        List<PurchaseResult> purchaseResults = mapper.selectAll();
        for (PurchaseResult purchaseResult : purchaseResults) {

            // 判断是否有采购数据 否则跳过
            if ("-1".equals(purchaseResult.getStatus())) {
                log.info("未采购，跳过：" + purchaseResult.getId());
                continue;
            }


            List<PurchaseGroup> purchaseGroups = purchaseGroupService.getListByPurchaseResultId(purchaseResult.getId());
            if (purchaseGroups != null && purchaseGroups.size() > 0) {
                log.info("数据存在，跳过：" + purchaseResult.getId());
                continue;
            }

            // 生成块数据
            PurchaseGroup purchaseGroup = new PurchaseGroup();
            purchaseGroup.setPurchaseResultId(purchaseResult.getId());
            purchaseGroup.setDeptId(purchaseResult.getDeptId());
            purchaseGroup.setPurchaseRemark(purchaseResult.getPurchaseRemark());
            purchaseGroup.setTenderFiles(purchaseResult.getTenderFiles());
            purchaseGroup.setTenderParameter(purchaseResult.getTenderParameter());
            purchaseGroupService.save(purchaseGroup);

            // 生成具体采购数据
            PurchaseLog purchaseLog = new PurchaseLog();
            purchaseLog.setPurchaseGroupId(purchaseGroup.getId());
            purchaseLog.setName(purchaseResult.getDeviceName());
            Date purchaseDate = Comm.string2Date(purchaseResult.getPurchaseDate());
            if (purchaseDate != null) {
                purchaseLog.setPurchaseDate(purchaseDate);
            }
            purchaseLog.setDeptId(purchaseResult.getDeptId());
            purchaseLog.setSupplier(purchaseResult.getSupplier());
            purchaseLog.setProducer(purchaseResult.getProducer());
            purchaseLog.setPurchaseBrand(purchaseResult.getPurchaseBrand());
            purchaseLog.setPurchaseSpec(purchaseResult.getPurchaseSpec());
            purchaseLog.setPurchaseWay("");// 采购方式之前数据没有
            purchaseLog.setBuildScale(purchaseResult.getBuildScale());
            purchaseLog.setAcceptDate(purchaseResult.getAcceptDate());
            purchaseLog.setPurchaseNumbers(purchaseResult.getPurchaseNumbers());
            purchaseLog.setPurchasePrice(purchaseResult.getPurchasePrice());
            purchaseLog.setPurchaseTotalPrice(purchaseResult.getPurchaseTotalPrice());
            purchaseLog.setFundsSources("");// 资金来源无数据
            purchaseLog.setPurchaseRemark(purchaseResult.getPurchaseRemark());
            purchaseLogService.save(purchaseLog);
            log.info("同步完成：" + purchaseResult.getId());

            // 生成具体（设备，工程，服务项目）


            Device device = new Device();
            String deviceType = "一般设备采购申请表,医疗专用设备采购申请表";
            if (deviceType.contains(purchaseResult.getType())) {
                device.setPurchaseLogId(purchaseLog.getId());
                device.setDeptId(purchaseResult.getDeptId());
                device.setYbsfCode("");
                device.setName(purchaseResult.getDeviceName());
                device.setSupplier(purchaseResult.getSupplier());
                device.setProducer(purchaseResult.getProducer());
                device.setBrand(purchaseResult.getPurchaseBrand());
                device.setSpec(purchaseResult.getSpec());
                device.setFundsSources(purchaseResult.getSourcesFunds());
                device.setUnitPrice(purchaseResult.getPurchasePrice());
                device.setIsComplete("0");
                device.setStatus("0");
            }

            switch (purchaseResult.getType()) {
                case "一般设备采购申请表":
                    device.setType("0");
                    device.setSysCode(deviceService.genSysCode(purchaseResult.getDeptId()));
                    deviceService.save(device);
                    break;
                case "医疗专用设备采购申请表":
                    // 按照购买数量添加多个设备
                    int num = 1;
                    try {
                        num = Integer.parseInt(purchaseLog.getPurchaseNumbers());
                    } catch (NumberFormatException e) {
                        // 出现异常默认为 1
                        num = 1;
                    }

                    for (int i = 0; i < num; i++) {
                        device.setType("1");
                        device.setSysCode(deviceService.genSysCode(purchaseResult.getDeptId()));
                        deviceService.save(device);
                    }

                    break;
                case "服务项目采购申请表":
                    Services services = new Services();
                    services.setPurchaseLogId(purchaseLog.getId());
                    services.setDeptId(purchaseResult.getDeptId());
                    services.setName(purchaseResult.getDeviceName());
                    services.setFundsSources(purchaseResult.getSourcesFunds());
                    services.setUnitPrice(purchaseResult.getPurchasePrice());
                    services.setTotalPrice(purchaseResult.getPurchaseTotalPrice());
                    services.setIsComplete("0");
                    servicesService.save(services);
                    break;
                case "工程项目采购申请表":
                    Project project = new Project();
                    project.setPurchaseLogId(purchaseLog.getId());
                    project.setDeptId(purchaseResult.getDeptId());
                    project.setName(purchaseResult.getDeviceName());
                    project.setFundsSources(purchaseResult.getSourcesFunds());
                    project.setUnitPrice(purchaseResult.getPurchasePrice());
                    project.setBuildScale(purchaseResult.getBuildScale());
                    project.setInvestmentScale(purchaseResult.getPurchasePrice());
                    project.setIsComplete("0");
                    projectService.save(project);
                    break;
            }
        }
    }


    @Transactional(readOnly = false, isolation = Isolation.READ_COMMITTED)
    @Override
    public void fixDeviceSysCode() {

        List<Device> devices = deviceService.nullSysCodeList();
        if (devices == null || devices.size() == 0) {
            return;
        }
        for (Device device : devices) {
            Device update = new Device();
            update.setId(device.getId());
            update.setSysCode(deviceService.genSysCode(device.getDeptId()));
            deviceService.update(update);
        }

    }

    /**
     * @param id:
     * @return void
     * <AUTHOR>
     * @description 取消采购
     * @date 2024/6/11 16:14
     */
    @Transactional(readOnly = false)
    @Override
    public void cancel(String id, String purchaseRemark) {
        PurchaseResult purchaseResult = new PurchaseResult();
        purchaseResult.setId(id);
        purchaseResult.setStatus("-1");
        purchaseResult.setPurchaseRemark(purchaseRemark);
        purchaseResult.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            purchaseResult.setUpdateUser(user.getUsercode());
            purchaseResult.setUpdateUserName(user.getUsername());
        }
        mapper.updateByPrimaryKeySelective(purchaseResult);
    }

    @Transactional(readOnly = false)
    @Override
    public void check(String id, String status, String remark) {
        String deptCode = deviceHelperService.getDeptCode();

        if (!"admin".equals(deptCode)) {
            throw new BusinessException("您没有确认权限");
        }

        Set<String> statusSet = Stream.of("2", "3").collect(Collectors.toSet());
        if (!statusSet.contains(status)) {
            throw new BusinessException("状态参数不正确");
        }

        PurchaseResult update = new PurchaseResult();
        update.setStatus(status);
        update.setId(id);
        update.setCheckRemark(remark);
        update(update);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchBindAttachment(PurchaseResultBatchBindAttachmentReq req) {
        if (req.getPurchaseResultIdList() == null || req.getPurchaseResultIdList().size() == 0) {
            throw new RuntimeException("请选择行后再提交");
        }
        mapper.batchBindAttachment(req);
    }


    @Transactional(readOnly = false)
    @Override
    public void batchCheck(PurchaseResultBatchCheckReq req) {

        if (req.getPurchaseResultIdList() == null || req.getPurchaseResultIdList().size() == 0) {
            throw new BusinessException("请选择行后再提交");
        }

        String deptCode = deviceHelperService.getDeptCode();

        if (!"admin".equals(deptCode)) {
            throw new BusinessException("您没有确认权限");
        }

        Set<String> statusSet = Stream.of("2", "3").collect(Collectors.toSet());
        if (!statusSet.contains(req.getStatus())) {
            throw new BusinessException("状态参数不正确");
        }

        mapper.batchCheck(req);

    }

}
