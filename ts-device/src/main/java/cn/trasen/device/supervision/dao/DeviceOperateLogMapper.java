package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DeviceCostListResp;
import cn.trasen.device.supervision.bean.report.DeviceIncomeListResp;
import cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq;
import cn.trasen.device.supervision.model.DeviceOperateLog;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;

public interface DeviceOperateLogMapper extends Mapper<DeviceOperateLog> {
    BigDecimal zc(IndexBlockYYZBReq indexBlockYYZBReq);

    List<DeviceIncomeListResp> deviceIncomeList(Page page, BenefitReq benefitReq);

    List<DeviceCostListResp> deviceCostList(Page page, BenefitReq benefitReq);
}