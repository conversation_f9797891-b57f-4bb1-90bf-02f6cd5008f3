package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: IndexReq
 * @author: chenbin
 * @description: 首页请求结构体
 * @date: 2024/4/19 16:13
 * @version: 1.0
 */
@Data
public class BaseReq {

    @ApiModelProperty(value = "医院id")
    private String deptId;

    @ApiModelProperty(value = "开始日期")
    private String start;

    @ApiModelProperty(value = "结束日期")
    private String end;

    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "是否忽略缓存")
    private boolean ignoreCache = false;


    public void setYear(String year) {
        this.year = year;

        if (this.year == null || this.year.isEmpty()) {

            int currentYear = LocalDateTime.now().getYear();

            // 将年份转换为字符串
            String yearString = String.valueOf(currentYear);

            this.year = yearString;
        }


        if (this.start == null || this.start.isEmpty()) {
            this.start = year + "-01-01";
        }

        if (this.end == null || this.end.isEmpty()) {
            this.end = year + "-12-31";
        }
    }
}
