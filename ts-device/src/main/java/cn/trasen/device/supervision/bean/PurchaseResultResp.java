package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.model.PurchaseLog;
import cn.trasen.device.supervision.model.PurchaseResult;
import lombok.Data;

import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseResultResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/9 19:44
 * @version: 1.0
 */

@Data
public class PurchaseResultResp {
    PurchaseResult purchaseResult;
    List<PurchaseGroup> purchaseGroupList;
    List<PurchaseLog> purchaseLogList;
}
