package cn.trasen.device.supervision.service.impl;

import cn.trasen.device.supervision.service.StepperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.toolbox.service.impl
 * @className: DaySteperServiceImpl
 * @author: chenbin
 * @description: 时间步进器
 * @date: 2024/9/18 17:51
 * @version: 1.0
 */

@Primary
@Service
public class SteperServiceImpl implements StepperService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public long step(String key, int expire) {

        long step = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, expire, TimeUnit.SECONDS);

        return step;
    }
}
