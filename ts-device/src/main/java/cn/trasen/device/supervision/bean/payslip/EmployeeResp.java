package cn.trasen.device.supervision.bean.payslip;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean.payslip
 * @className: EmployeeResp
 * @author: chenbin
 * @description: 用来接收通过机构查询人员关键信息，匹配工资条数据
 * @date: 2024/7/3 16:09
 * @version: 1.0
 */
@Data
public class EmployeeResp {

    @ApiModelProperty(value = "人员名称")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    @Column(name = "identity_number")
    private String identityNumber;

    @ApiModelProperty(value = "编制类型")
    @Column(name = "employee_category")
    private String employeeCategory;
}
