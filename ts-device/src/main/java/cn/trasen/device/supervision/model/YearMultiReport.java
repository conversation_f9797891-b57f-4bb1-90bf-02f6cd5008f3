package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_year_multi_report")
@Setter
@Getter
public class YearMultiReport {


    @Transient
    private String no;

    @Id
    private String id;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 医院ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "医院ID")
    private String orgId;


    /**
     * 医院名称
     */
    @Transient
    @ApiModelProperty(value = "医院名称")
    private String orgName;
    /**
     * 申报预算金额
     */
    @ApiModelProperty(value = "申报预算金额")
    private BigDecimal sbysje;

    /**
     * 通过审核项目个数
     */
    @ApiModelProperty(value = "通过审核项目个数")
    private Integer tgshxmgs;

    /**
     * 通过审核预算金额
     */
    @ApiModelProperty(value = "通过审核预算金额")
    private BigDecimal tgshysje;

    /**
     * 审核降低预算金额
     */
    @ApiModelProperty(value = "审核降低预算金额")
    private BigDecimal shjdysje;

    /**
     * 审核不通过项目个数
     */
    @ApiModelProperty(value = "审核不通过项目个数")
    private Integer shbtgxmgs;

    /**
     * 审核不通过预算金额
     */
    @ApiModelProperty(value = "审核不通过预算金额")
    private BigDecimal shbtgysje;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;
}