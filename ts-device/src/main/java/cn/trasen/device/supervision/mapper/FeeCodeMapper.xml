<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.FeeCodeMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.FeeCode">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
</mapper>