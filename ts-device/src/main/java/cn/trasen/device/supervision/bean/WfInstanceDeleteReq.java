package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean
 * @className: WfInstanceBatchDeleteReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/7/11 10:38
 * @version: 1.0
 */

@Data
public class WfInstanceDeleteReq {

    @ApiModelProperty(value = "业务id")
    private String businessId;
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;

}
