package cn.trasen.device.supervision.service;

import cn.trasen.device.supervision.bean.payslip.DoCheckReq;
import cn.trasen.device.supervision.bean.payslip.PayslipStatusReportResp;
import cn.trasen.device.supervision.bean.payslip.PayslipUploadListResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PayslipUpload;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.crypto.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ToaDevicePayslipUploadService
 * @Description TODO
 * @date 2024年6月28日 下午2:21:33
 */
public interface PayslipUploadService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    Integer save(PayslipUpload record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    Integer update(PayslipUpload record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return ToaDevicePayslipUpload
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    PayslipUpload selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<ToaDevicePayslipUpload>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    DataSet<PayslipUpload> getDataSetList(Page page, PayslipUpload record);

    void _import_(String type, String month, MultipartFile file) throws Exception;

    void upload(String id ,DoCheckReq doCheckReq);

    DataSet<PayslipUploadListResp> uploadList(PayslipUpload record, Page page);

    DataSet<PayslipUploadListResp> checkList(PayslipUpload record, Page page);

    void doCheck(DoCheckReq doCheckReq);

    PayslipStatusReportResp payslipStatusReport(PayslipUpload record);
}
