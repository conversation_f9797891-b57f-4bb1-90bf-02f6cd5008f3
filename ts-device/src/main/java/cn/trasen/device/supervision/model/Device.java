package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_device")
@Setter
@Getter
public class Device {
    @Id
    private String id;

    /**
     * 所属机构ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "所属机构ID")
    private String deptId;

    /**
     * 采购结果ID
     */
    @Column(name = "purchase_log_id")
    @ApiModelProperty(value = "采购结果ID")
    private String purchaseLogId;

    /**
     * 设备编码
     */
    @Column(name = "device_code")
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;


    /**
     * 设备编码
     */
    @Column(name = "sys_code")
    @ApiModelProperty(value = "设备系统编码")
    private String sysCode;

    /**
     * 设备编码
     */
    @Column(name = "asset_code")
    @ApiModelProperty(value = "设备资产编码")
    private String assetCode;


    /**
     * 设备编码
     */
    @Column(name = "common_name")
    @ApiModelProperty(value = "设备通用名")
    private String commonName;


    /**
     * 医保收费编码
     */
    @Column(name = "ybsf_code")
    @ApiModelProperty(value = "医保收费编码")
    private String ybsfCode;

    /**
     * 分类ID
     */
    @Column(name = "cate_id")
    @ApiModelProperty(value = "分类ID")
    private String cateId;

    /**
     * 0 一般设备 1 专用设备
     */
    @ApiModelProperty(value = "0 一般设备 1 专用设备")
    private String type;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String name;

    /**
     * 供货单位名称
     */
    @ApiModelProperty(value = "供货单位名称")
    private String supplier;

    /**
     * 生产厂家 ｜ 供应商 ｜服务商｜承建单位
     */
    @ApiModelProperty(value = "生产厂家 ｜ 供应商 ｜服务商｜承建单位")
    private String producer;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    private String spec;

    /**
     * 资金来源
     */
    @Column(name = "funds_sources")
    @ApiModelProperty(value = "资金来源")
    private String fundsSources;

    @Column(name = "is_complete")
    @ApiModelProperty(value = "0 未完善 1 已完善")
    private String isComplete;

    /**
     * 单价
     */
    @Column(name = "unit_price")
    @ApiModelProperty(value = "单价")
    private String unitPrice;

    /**
     * 设备文档，多个逗号分开
     */
    @ApiModelProperty(value = "设备文档，多个逗号分开")
    private String files;

    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态")
    private String status;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 扩展字段
     */
    @Column(name = "expand_field")
    @ApiModelProperty(value = "扩展字段")
    private String expandField;


    @Transient
    @Column(name = "cate_name")
    @ApiModelProperty(value = "设备分类名称")
    private String cateName;

    @Transient
    @Column(name = "dept_name")
    @ApiModelProperty(value = "机构名称")
    private String deptName;

}