package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseInstanceInfoListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/5/10 15:07
 * @version: 1.0
 */

@Data
public class PurchaseInstanceInfoListReq {

    @ApiModelProperty(value = "查询年份")
    private String effectYear;

    @ApiModelProperty(value = "组织机构代码")
    private String deptCode;

    @ApiModelProperty(value = "类别")
    private String type;

    @ApiModelProperty(value = "排序")
    private String sord;

    @ApiModelProperty(value = "排序字段")
    private String sidx;

}
