package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "device_base_info")
@Setter
@Getter
public class DeviceBaseInfo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 设备编号
     */
    @Column(name = "device_no")
    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    /**
     * 设备名称
     */
    @Column(name = "device_name")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备型号
     */
    @Column(name = "device_model")
    @ApiModelProperty(value = "设备型号")
    private String deviceModel;

    /**
     * 设备类型
     */
    @Column(name = "device_type")
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 品牌
     */
    @Column(name = "device_brand")
    @ApiModelProperty(value = "品牌")
    private String deviceBrand;

    /**
     * 生产厂商
     */
    @Column(name = "device_manufacturer")
    @ApiModelProperty(value = "生产厂商")
    private String deviceManufacturer;

    /**
     * 注册证号
     */
    @Column(name = "device_register_no")
    @ApiModelProperty(value = "注册证号")
    private String deviceRegisterNo;

    /**
     * 注册证号有效期限
     */
    @Column(name = "device_register_period")
    @ApiModelProperty(value = "注册证号有效期限")
    private String deviceRegisterPeriod;

    /**
     * 出厂日期
     */
    @Column(name = "device_of_production")
    @ApiModelProperty(value = "出厂日期")
    private Date deviceOfProduction;

    /**
     * 设备状态
     */
    @Column(name = "device_status")
    @ApiModelProperty(value = "设备状态")
    private String deviceStatus;

    /**
     * 使用年限
     */
    @Column(name = "device_life")
    @ApiModelProperty(value = "使用年限")
    private String deviceLife;

    /**
     * 使用科室
     */
    @Column(name = "device_dept")
    @ApiModelProperty(value = "使用科室")
    private String deviceDept;

    /**
     * 生产地址
     */
    @Column(name = "device_address")
    @ApiModelProperty(value = "生产地址")
    private String deviceAddress;

    /**
     * 进口/国产
     */
    @Column(name = "device_source")
    @ApiModelProperty(value = "进口/国产")
    private String deviceSource;

    /**
     * 是否重要设备
     */
    @Column(name = "device_important")
    @ApiModelProperty(value = "是否重要设备")
    private String deviceImportant;

    /**
     * 报废日期
     */
    @Column(name = "device_scrapped_date")
    @ApiModelProperty(value = "报废日期")
    private Date deviceScrappedDate;

    /**
     * 备注
     */
    @Column(name = "device_remark")
    @ApiModelProperty(value = "备注")
    private String deviceRemark;

    /**
     * 参数信息
     */
    @Column(name = "device_parameter")
    @ApiModelProperty(value = "参数信息")
    private String deviceParameter;

    /**
     * 设备文档
     */
    @Column(name = "device_files")
    @ApiModelProperty(value = "设备文档")
    private String deviceFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}