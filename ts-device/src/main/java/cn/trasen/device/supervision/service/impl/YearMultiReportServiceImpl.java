package cn.trasen.device.supervision.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.trasen.device.supervision.bean.comm.KvResp;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.util.Comm;
import io.lettuce.core.Consumer;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.YearMultiReportMapper;
import cn.trasen.device.supervision.model.YearMultiReport;
import cn.trasen.device.supervision.service.YearMultiReportService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName YearMultiReportServiceImpl
 * @Description TODO
 * @date 2024年12月31日 上午11:27:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class YearMultiReportServiceImpl implements YearMultiReportService {

    @Autowired
    private YearMultiReportMapper mapper;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(YearMultiReport record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(YearMultiReport record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        YearMultiReport record = new YearMultiReport();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public YearMultiReport selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<YearMultiReport> getDataSetList(Page page, YearMultiReport record) {
        List<YearMultiReport> records = mapper.getList(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<YearMultiReport> getList(YearMultiReport record) {
        return mapper.getListNoPage(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void calculateYearMultiReport(String year) {
        YearMultiReport yearMultiReport = new YearMultiReport();
        if (year == null) {
            year = Comm.getCurDate("yyyy");
        }

        // 获取所有科室
        List<Organization> organizationList = deviceHelperService.getOrganizationList();
        // 申报预算金额
        Map<String, Object> sbysje = kvList2Map(mapper.getSbysje(year));
        //通过审核项目个数
        Map<String, Object> tgshxmgs = kvList2Map(mapper.getTgshxmgs(year));
        //通过审核预算金额
        Map<String, Object> tgshysje = kvList2Map(mapper.getTgshysje(year));
        //审核降低预算金额
        Map<String, Object> shjdysje = kvList2Map(mapper.getShjdysje(year));
        //审核不通过项目个数
        Map<String, Object> shbtgxmgs = kvList2Map(mapper.getShbtgxmgs(year));
        // 审核不通过预算金额
        Map<String, Object> shbtgysje = kvList2Map(mapper.getShbtgysje(year));


        if (CollectionUtils.isEmpty(organizationList)) {
            return;
        }

        for (Organization organization : organizationList) {
            yearMultiReport.setYear(year);
            yearMultiReport.setOrgId(organization.getCode());

            if (sbysje != null) {
                yearMultiReport.setSbysje(Comm.object2BigDecimal(sbysje.get(organization.getCode())));
            } else {
                yearMultiReport.setSbysje(BigDecimal.ZERO);
            }

            if (tgshxmgs != null) {
                yearMultiReport.setTgshxmgs(Comm.object2Integer(tgshxmgs.get(organization.getCode())));
            } else {
                yearMultiReport.setTgshxmgs(0);
            }

            if (tgshysje != null) {
                yearMultiReport.setTgshysje(Comm.object2BigDecimal(tgshysje.get(organization.getCode())));
            } else {
                yearMultiReport.setTgshysje(BigDecimal.ZERO);
            }

            if (shjdysje != null) {
                yearMultiReport.setShjdysje(Comm.object2BigDecimal(shjdysje.get(organization.getCode())));
            } else {
                yearMultiReport.setShjdysje(BigDecimal.ZERO);
            }

            if (shbtgxmgs != null) {
                yearMultiReport.setShbtgxmgs(Comm.object2Integer(shbtgxmgs.get(organization.getCode())));
            } else {
                yearMultiReport.setShbtgxmgs(0);
            }

            if (shbtgysje != null) {
                yearMultiReport.setShbtgysje(Comm.object2BigDecimal(shbtgysje.get(organization.getCode())));
            } else {
                yearMultiReport.setShbtgysje(BigDecimal.ZERO);
            }

            if (checkExsit(year, organization.getCode())) {
                update(yearMultiReport);
            } else {
                save(yearMultiReport);
            }
        }
    }

    private Map<String, Object> kvList2Map(List<KvResp> list) {
        Map<String, Object> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(kvResp -> {
                map.put(kvResp.getK(), kvResp.getV());
            });
        }
        return map;
    }

    private boolean checkExsit(String year, String orgCode) {
        boolean flag = false;
        Example example = new Example(YearMultiReport.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("year", year);
        criteria.andEqualTo("orgId", orgCode);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        // count
        int count = mapper.selectCountByExample(example);
        if (count > 0) {
            flag = true;
        }
        return flag;
    }
}
