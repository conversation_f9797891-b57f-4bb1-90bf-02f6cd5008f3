package cn.trasen.device.supervision.bean.payslip;

import cn.trasen.device.supervision.model.PayslipUpload;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean.payslip
 * @className: PayslipListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/7/2 10:53
 * @version: 1.0
 */

@Data
public class PayslipUploadListResp extends PayslipUpload {


    @ApiModelProperty("流程实例ID")
    private String wfInstanceId;

    @ApiModelProperty("流程类型ID")
    private String wfDefinitionId;

    @ApiModelProperty("医院名称")
    private String deptName;

    @ApiModelProperty("流程状态")
    private String status;

    @ApiModelProperty("当前节点名称")
    private String currentStepName;

    @ApiModelProperty("当前审批人")
    private String currentApprover;

    @ApiModelProperty("审核说明")
    private String remark;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("是否审核过")
    private String checked;
}
