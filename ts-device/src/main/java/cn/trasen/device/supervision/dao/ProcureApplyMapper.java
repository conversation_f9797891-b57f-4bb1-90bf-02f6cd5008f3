package cn.trasen.device.supervision.dao;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import cn.trasen.device.supervision.bean.index.IndexBlockDBCYReq;
import cn.trasen.device.supervision.bean.index.IndexBlockDBSXReq;
import cn.trasen.device.supervision.bean.index.IndexBlockYSCGReq;
import org.apache.ibatis.annotations.Param;

import tk.mybatis.mapper.common.Mapper;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.ApproveData;
import cn.trasen.device.supervision.model.ProcureApply;

public interface ProcureApplyMapper extends Mapper<ProcureApply> {

    List<Map<String, Object>> selectMyDefinitionInfoNumbers(@Param("userCode") String userCode, @Param("workflowStatus") String workflowStatus);

    List<Map<String, Object>> getApplyLeftWfDefinitionInfo(ProcureApply procureApply);

    long selectTotalPrice(Map<String, String> params);

    List<Map<String, Object>> selectTableHeadCols(String tableId);

    List<Map<String, Object>> getMyApplyWfDefinitionInfo(Page page, ProcureApply record);

    List<Map<String, Object>> selectMyApprovalInfoNumbers(String currentUserCode);

    List<Map<String, Object>> selectMyApprovedInfoNumbers(String currentUserCode);

    List<Map<String, Object>> selectMyCopyInfoNumbers(String currentUserCode);

    List<Map<String, String>> selectFilesByBusinessId(String businessId);

    List<Map<String, Object>> getConsultLeftWfDefinitionInfo(@Param("deptCodes") List<String> deptCodes);

    List<Map<String, Object>> getConsultWfDefinitionList(Page page, ProcureApply record);

    List<Map<String, Object>> getWfDefinitionInfo();

    Map<String, String> selectStartStepInfo(String wfDefinitionId);

    String selectCommentField(@Param("wfDefinitionId") String wfDefinitionId, @Param("wfStepId") String wfStepId);

    void updateApproveData(ApproveData approveData);

    List<Map<String, Object>> getStepInfoByWfDefinitionId(String wfDefinitionId);

    List<Map<String, String>> selectFilesByFileIds(@Param("fileIds") String[] fileIds);

    Integer cgsqsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer cgsqbhsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer shspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer dshspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer dwhspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ddwhspsl(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal cgsqje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal shspje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal dshspje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal dwhspje(IndexBlockDBCYReq indexBlockDBCYReq);

    BigDecimal ddwhspje(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer zysblcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ybsblcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer gcxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer fwxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer zysblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer ybsblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer gcxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);

    Integer fwxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq);


    /*--------------------------采购预算分析------------------------*/
    Integer cgspzs(IndexBlockYSCGReq indexBlockYSCGReq);


    Integer cgxmzs(IndexBlockYSCGReq indexBlockYSCGReq);

    /*--------------------------待我审批数量统计------------------------*/
    Integer dspcgsl(IndexBlockDBSXReq indexBlockDBSXReq);

    Integer dspdwhsl(IndexBlockDBSXReq indexBlockDBSXReq);


}
