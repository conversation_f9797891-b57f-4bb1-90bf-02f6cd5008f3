package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DeviceCostListResp;
import cn.trasen.device.supervision.bean.report.DeviceIncomeListResp;
import cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq;
import cn.trasen.device.supervision.model.DeviceOperateLog;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceOperateLogService
 * @Description TODO
 * @date 2024年4月15日 下午5:52:02
 */
public interface DeviceOperateLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer save(DeviceOperateLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer update(DeviceOperateLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DeviceOperateLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DeviceOperateLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DeviceOperateLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DataSet<DeviceOperateLog> getDataSetList(Page page, DeviceOperateLog record);

    List<DeviceOperateLog> getListByDeviceId(String deviceId, String granularity);


    Boolean checkExist(DeviceOperateLog record);

    DeviceOperateLog checkRow(DeviceOperateLog record);

    BigDecimal zc(IndexBlockYYZBReq indexBlockYYZBReq);

    DataSet<DeviceIncomeListResp> deviceIncomeList(Page page, BenefitReq benefitReq);

    DataSet<DeviceCostListResp> deviceCostList(Page page, BenefitReq benefitReq);


}
