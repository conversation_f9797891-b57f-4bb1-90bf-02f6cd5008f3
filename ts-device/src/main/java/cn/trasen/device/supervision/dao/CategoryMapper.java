package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DeviceCategoryStatisticsListResp;
import cn.trasen.device.supervision.model.Category;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface CategoryMapper extends Mapper<Category> {

    void changeSonNode(@Param("parentId") String parentId, @Param("oldTreeIds") String oldTreeIds, @Param("newTreeIds") String newTreeIds, @Param("levelChange") int levelChange);

    List<DeviceCategoryStatisticsListResp> deviceCategoryStatistics(Page page, BenefitReq benefitReq);
}