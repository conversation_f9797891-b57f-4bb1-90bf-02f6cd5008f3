<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceWfPsInstanceInfoMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.WfPsInstanceInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="wf_instance_id" jdbcType="VARCHAR" property="wfInstanceId"/>
        <result column="wf_definition_id" jdbcType="VARCHAR" property="wfDefinitionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="apply_year" jdbcType="BIGINT" property="applyYear"/>
        <result column="apply_org" jdbcType="VARCHAR" property="applyOrg"/>
        <result column="device_or_project_name" jdbcType="VARCHAR" property="deviceOrProjectName"/>
        <result column="model_or_service_period" jdbcType="VARCHAR" property="modelOrServicePeriod"/>
        <result column="nums" jdbcType="VARCHAR" property="nums"/>
        <result column="unit_price" jdbcType="VARCHAR" property="unitPrice"/>
        <result column="total_price" jdbcType="VARCHAR" property="totalPrice"/>
        <result column="quotation1" jdbcType="VARCHAR" property="quotation1"/>
        <result column="quotation2" jdbcType="VARCHAR" property="quotation2"/>
        <result column="quotation3" jdbcType="VARCHAR" property="quotation3"/>
        <result column="meeting_nums" jdbcType="VARCHAR" property="meetingNums"/>
        <result column="meeting_unit_price" jdbcType="VARCHAR" property="meetingUnitPrice"/>
        <result column="meeting_total_price" jdbcType="VARCHAR" property="meetingTotalPrice"/>
        <result column="LAUNCH_DEPT_CODE" jdbcType="VARCHAR" property="launchDeptCode"/>
        <result column="export" jdbcType="CHAR" property="export"/>
        <result column="approval_date" jdbcType="VARCHAR" property="approvalDate"/>
        <result column="approval_nums" jdbcType="VARCHAR" property="approvalNums"/>
        <result column="approval_unit_price" jdbcType="VARCHAR" property="approvalUnitPrice"/>
        <result column="approval_total_price" jdbcType="VARCHAR" property="approvalTotalPrice"/>
    </resultMap>
    <select id="getWfPsInstanceList" resultType="cn.trasen.device.supervision.model.WfPsInstanceInfo"
            parameterType="cn.trasen.device.supervision.bean.GetWfPsInstanceListReq"
    >
        select t1.* from toa_device_ps_instance_info t1 left join comm_organization t2
        on t1.`LAUNCH_DEPT_CODE` = t2.`organization_id`
        where 1=1
        <choose>
            <when test="launchDeptCode != null and launchDeptCode != ''">
                <choose>
                    <when test="launchDeptCode == 'admin'">
                    </when>
                    <otherwise>
                        and t1.`LAUNCH_DEPT_CODE` = #{launchDeptCode}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                and 1=2
            </otherwise>
        </choose>
        <if test="applyOrg != null and applyOrg != ''">
            and t1.`apply_org` like concat('%',#{applyOrg},'%')
        </if>

        <if test="meetingDate != null and meetingDate != ''">
            and t1.`meeting_date` like concat('%',#{meetingDate},'%')
        </if>
        <if test="approvalDate != null and approvalDate != ''">
            and t1.`approval_date` like concat('%',#{approvalDate},'%')
        </if>

        <if test="type != null and type != ''">
            and t1.`type` like concat('%',#{type},'%')
        </if>

        <if test="deviceOrProjectName != null and deviceOrProjectName != ''">
            and t1.`device_or_project_name` like concat('%',#{deviceOrProjectName},'%')
        </if>

        <if test="export != null and export != ''">
            and t1.`export` like concat('%',#{export},'%')
        </if>

        group by t1.`wf_instance_id`

        <if test="sidx != null and sidx != ''">
            <choose>
                <when test="sidx == 'apply_org'">
                    order by t2.`seq_no` ${sord} , t1.wf_instance_id
                </when>
                <otherwise>
                    order by t1.`${sidx}` ${sord} , t1.wf_instance_id
                </otherwise>
            </choose>
        </if>
        <if test="sidx ==''">
            order by t1.`meeting_date` desc , t2.`seq_no` asc , FIELD(t1.`wf_definition_id`, '06985B07055046639923C386071BA66E', 'A37FCFB6631B405EA3A790E32FE88655', 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6') , t1.wf_instance_id
        </if>

    </select>

    <select id="getWfPsInstanceListByIdLIst"
            resultType="cn.trasen.device.supervision.model.WfPsInstanceInfo">
        select t1.*,t1.`apply_org` as ao,t1.`device_or_project_name` as dopn,t1.`model_or_service_period` as mosp,
        t1.`approval_nums` as
        `an`,t1.`approval_unit_price` as `aup`,t1.`approval_total_price` as `atp`,t2.`custom_code`
        from toa_device_ps_instance_info t1 left join
        comm_organization t2 on t1.`LAUNCH_DEPT_CODE` = t2.`organization_id`
        <if test="wfInstanceIdList != null and wfInstanceIdList.size() > 0">
            where t1.`wf_instance_id` IN
            <foreach item="id" collection="wfInstanceIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by t2.`custom_code` asc , t1.`meeting_date` desc , FIELD(t1.`wf_definition_id`, '06985B07055046639923C386071BA66E', 'A37FCFB6631B405EA3A790E32FE88655', 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6') , t1.wf_instance_id
    </select>
    <select id="updateExportStatus">
        update wf_instance_info set export_ps = '1'
        <choose>

            <when test="wfInstanceIdList != null and wfInstanceIdList.size() > 0">
                where WF_INSTANCE_ID in
                <foreach item="id" collection="wfInstanceIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                where 1=2
            </otherwise>
        </choose>
    </select>
</mapper>