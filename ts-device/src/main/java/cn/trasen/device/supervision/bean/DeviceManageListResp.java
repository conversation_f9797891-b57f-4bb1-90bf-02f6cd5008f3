package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.bean.DevicePurchaseLogResp;
import cn.trasen.device.supervision.model.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: DeviceManageListResp
 * @author: chenbin
 * @description: 因为设备需要跳转到申请单，所以需要补充流程数据
 * @date: 2024/4/16 15:37
 * @version: 1.0
 */

@Data
public class DeviceManageListResp extends DevicePurchaseLogResp {

    @ApiModelProperty(value = "机构名称")
    private String deptName;
    @ApiModelProperty(value = "流程ID")
    private String wfInstanceId;
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;
    @ApiModelProperty(value = "流程状态")
    private String wfStatus;
    @ApiModelProperty(value = "流程编号")
    private String wfWorkflowNo;
    @ApiModelProperty(value = "业务ID")
    private String wfBusinessId;
    @ApiModelProperty(value = "任务ID")
    private String wfTaskId;
    @ApiModelProperty(value = "流程序号")
    private String wfWorkflowNumber;

}
