<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.ProjectMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.Project">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="purchase_log_id" jdbcType="VARCHAR" property="purchaseLogId" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="build_scale" jdbcType="VARCHAR" property="buildScale" />
    <result column="investment_scale" jdbcType="VARCHAR" property="investmentScale" />
    <result column="accept_date" jdbcType="VARCHAR" property="acceptDate" />
    <result column="sources_funds" jdbcType="VARCHAR" property="sourcesFunds" />
    <result column="unit_price" jdbcType="VARCHAR" property="unitPrice" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="expand_field" jdbcType="LONGVARCHAR" property="expandField" />
  </resultMap>
  <select id="getDataSetList" resultType="cn.trasen.device.supervision.bean.ProjectManageListResp"
          parameterType="cn.trasen.device.supervision.model.Project">
    select t1.*,
    `t2`.`supplier` as pl_supplier,
    `t2`.`producer` as pl_producer,
    `t2`.`purchase_brand` as pl_brand,
    `t2`.`purchase_spec` as pl_spec,
    `t2`.`build_scale` as pl_build_scale,
    `t2`.`accept_date` as pl_accept_date,
    `t2`.`funds_sources` as pl_funds_sources,
    `t2`.`service_start_at` as pl_service_start_at,
    `t2`.`service_end_at` as pl_service_end_at,
    `t6`.`name` AS `dept_name`,
    `t4`.`wf_instance_id` as wf_instance_id
    from toa_device_project t1
    left join toa_device_purchase_log t2 on t1.purchase_log_id = t2.id
    left join toa_device_purchase_group t3 on t2.purchase_group_id = t3.id
    left join toa_device_purchase_result t4 on t3.purchase_result_id = t4.id
    left join comm_organization t6 on t1.dept_id = t6.organization_id
    where t1.is_deleted = 'N'
    <if test="deptId != null and deptId != ''">
      and t1.dept_id = #{deptId}
    </if>
    <if test="name != null and name != ''">
      and t1.name like concat('%', #{name}, '%')
    </if>
    <if test="isComplete != null and isComplete != ''">
      and t1.is_complete = #{isComplete}
    </if>
    order by t1.create_date desc
  </select>
</mapper>