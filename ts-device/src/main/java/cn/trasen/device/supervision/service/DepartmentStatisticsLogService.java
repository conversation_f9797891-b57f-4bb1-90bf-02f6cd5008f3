package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DepartmentStatisticsListResp;
import cn.trasen.device.supervision.bean.report.StatisticsResp;
import cn.trasen.device.supervision.model.DepartmentStatisticsLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DepartmentStatisticsLogService
 * @Description TODO
 * @date 2024年6月20日 上午11:09:14
 */
public interface DepartmentStatisticsLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年6月20日 上午11:09:14
     * <AUTHOR>
     */
    Integer save(DepartmentStatisticsLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年6月20日 上午11:09:14
     * <AUTHOR>
     */
    Integer update(DepartmentStatisticsLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年6月20日 上午11:09:14
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DepartmentStatisticsLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年6月20日 上午11:09:14
     * <AUTHOR>
     */
    DepartmentStatisticsLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DepartmentStatisticsLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年6月20日 上午11:09:14
     * <AUTHOR>
     */
    DataSet<DepartmentStatisticsLog> getDataSetList(Page page, DepartmentStatisticsLog record);

    DepartmentStatisticsLog checkRow(String deptId, String month);

    DataSet<DepartmentStatisticsListResp> departmentStatistics(Page page,BenefitReq benefitReq);

    StatisticsResp statisticsResp(Page page, BenefitReq benefitReq);
}
