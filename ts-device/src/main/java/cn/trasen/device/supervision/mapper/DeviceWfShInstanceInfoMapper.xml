<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceWfShInstanceInfoMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.WfShInstanceInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="wf_instance_id" jdbcType="VARCHAR" property="wfInstanceId"/>
        <result column="wf_definition_id" jdbcType="VARCHAR" property="wfDefinitionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="apply_year" jdbcType="VARCHAR" property="applyYear"/>
        <result column="apply_org" jdbcType="VARCHAR" property="applyOrg"/>
        <result column="device_or_project_name" jdbcType="VARCHAR" property="deviceOrProjectName"/>
        <result column="model_or_service_period" jdbcType="VARCHAR" property="modelOrServicePeriod"/>
        <result column="nums" jdbcType="VARCHAR" property="nums"/>
        <result column="unit_price" jdbcType="VARCHAR" property="unitPrice"/>
        <result column="total_price" jdbcType="VARCHAR" property="totalPrice"/>
        <result column="quotation1" jdbcType="VARCHAR" property="quotation1"/>
        <result column="quotation2" jdbcType="VARCHAR" property="quotation2"/>
        <result column="quotation3" jdbcType="VARCHAR" property="quotation3"/>
        <result column="meeting_nums" jdbcType="VARCHAR" property="meetingNums"/>
        <result column="meeting_unit_price" jdbcType="VARCHAR" property="meetingUnitPrice"/>
        <result column="meeting_total_price" jdbcType="VARCHAR" property="meetingTotalPrice"/>
        <result column="meeting_date" jdbcType="TIMESTAMP" property="meetingDate"/>
    </resultMap>
    <select id="getWfShInstanceList" resultType="cn.trasen.device.supervision.model.WfShInstanceInfo"
            parameterType="cn.trasen.device.supervision.bean.GetWfShInstanceListReq"
    >
        select t1.* from toa_device_sh_instance_info t1 left join comm_organization t2
        on t1.`LAUNCH_DEPT_CODE` = t2.`organization_id`
        where t1.ASSIGNEE_NO = #{assigneeNo}
        <if test="applyOrg != null and applyOrg != ''">
            and t1.`apply_org` like concat('%',#{applyOrg},'%')
        </if>

        <if test="meetingDate != null and meetingDate != ''">
            and t1.`meeting_date` like concat('%',#{meetingDate},'%')
        </if>

        <if test="type != null and type != ''">
            and t1.`type` like concat('%',#{type},'%')
        </if>

        <if test="deviceOrProjectName != null and deviceOrProjectName != ''">
            and t1.`device_or_project_name` like concat('%',#{deviceOrProjectName},'%')
        </if>

        <if test="export != null and export != ''">
            and t1.`export` like concat('%',#{export},'%')
        </if>
        <if test="sidx != null and sidx != ''">
            <choose>
                <when test="sidx == 'apply_org'">
                    order by t2.`custom_code` ${sord} , FIELD(t1.`wf_definition_id`, '06985B07055046639923C386071BA66E', 'A37FCFB6631B405EA3A790E32FE88655', 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6') , t1.CREATE_DATE DESC , t1.wf_instance_id
                </when>
                <otherwise>
                    order by t1.`${sidx}` ${sord} , FIELD(t1.`wf_definition_id`, '06985B07055046639923C386071BA66E', 'A37FCFB6631B405EA3A790E32FE88655', 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6') , t1.CREATE_DATE DESC , t1.wf_instance_id
                </otherwise>
            </choose>
        </if>

    </select>

    <select id="getWfShInstanceListByIdLIst"
            resultType="cn.trasen.device.supervision.model.WfShInstanceInfo">
        select distinct t1.* from toa_device_sh_instance_info t1
        LEFT JOIN comm_organization t2 ON t1.`apply_org` = t2.`name`
        <if test="wfInstanceIdList != null and wfInstanceIdList.size() > 0">
            where t1.`wf_instance_id` IN
            <foreach item="id" collection="wfInstanceIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY t1.wf_instance_id
        order by t2.`custom_code` asc, FIELD(t1.`wf_definition_id`, '06985B07055046639923C386071BA66E',
        'A37FCFB6631B405EA3A790E32FE88655', 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6'),
        t1.CREATE_DATE DESC, t1.wf_instance_id
    </select>
    <select id="updateExportStatus">
        update wf_instance_info set export_ps = '1'
        <choose>
            <!-- 使用 case 和 when 条件 -->
            <when test="wfInstanceIdList != null and wfInstanceIdList.size() > 0">
                where WF_INSTANCE_ID in
                <foreach item="id" collection="wfInstanceIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <!-- 默认条件 -->
                where 1=2
            </otherwise>
        </choose>
    </select>
</mapper>