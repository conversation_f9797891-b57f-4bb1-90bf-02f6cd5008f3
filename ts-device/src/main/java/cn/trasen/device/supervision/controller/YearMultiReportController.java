package cn.trasen.device.supervision.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.device.supervision.model.Category;
import cn.trasen.device.supervision.util.Comm;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.YearMultiReport;
import cn.trasen.device.supervision.service.YearMultiReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName YearMultiReportController
 * @Description TODO
 * @date 2024年12月31日 上午11:27:53
 */
@RestController
@Api(tags = "YearMultiReportController")
public class YearMultiReportController {

    private transient static final Logger logger = LoggerFactory.getLogger(YearMultiReportController.class);

    @Autowired
    private YearMultiReportService yearMultiReportService;


    /**
     * @param page
     * @param record
     * @return DataSet<YearMultiReport>
     * @Title selectYearMultiReportList
     * @Description 查询列表
     * @date 2024年12月31日 上午11:27:53
     * <AUTHOR>
     */
    @ApiOperation(value = "年度综合统计列表", notes = "年度综合统计列表")
    @GetMapping("/api/report/yearMulti/list")
    public DataSet<YearMultiReport> selectYearMultiReportList(Page page, YearMultiReport record) {
        return yearMultiReportService.getDataSetList(page, record);
    }

    @ApiOperation(value = "生成年度综合报表", notes = "生成年度综合报表")
    @GetMapping("/api/report/yearMulti/gen")
    public PlatformResult<YearMultiReport> gen(@RequestParam("date") String date) {
        date = date != null ? date : Comm.getCurDate("yyyy");
        yearMultiReportService.calculateYearMultiReport(date);
        return PlatformResult.success();
    }

    @ApiOperation(value = "导出年度综合报表", notes = "导出年度综合报表")
    @GetMapping("/api/report/yearMulti/export")
    public ResponseEntity<byte[]> export(YearMultiReport record) {

        try {

            List<YearMultiReport> list = yearMultiReportService.getList(record);

            int index = 0;
            BigDecimal tsbysje = BigDecimal.ZERO;
            Integer ttgshxmgs = 0;
            BigDecimal ttgshysje = BigDecimal.ZERO;
            BigDecimal tshjdysje = BigDecimal.ZERO;
            Integer tshbtgxmgs = 0;
            BigDecimal tshbtgysje = BigDecimal.ZERO;


            for (YearMultiReport row : list) {
                index++;
                row.setNo(String.valueOf(index));
                tsbysje = tsbysje.add(row.getSbysje());
                ttgshxmgs += row.getTgshxmgs();
                ttgshysje = ttgshysje.add(row.getTgshysje());
                tshjdysje = tshjdysje.add(row.getShjdysje());
                tshbtgxmgs += row.getShbtgxmgs();
                tshbtgysje = tshbtgysje.add(row.getShbtgysje());
            }

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/YearReportExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("tyear", "/");
            map.put("torgName", "/");
            map.put("tsbysje", tsbysje);
            map.put("ttgshxmgs", ttgshxmgs);
            map.put("ttgshysje", ttgshysje);
            map.put("tshjdysje", tshjdysje);
            map.put("tshbtgxmgs", tshbtgxmgs);
            map.put("tshbtgysje", tshbtgysje);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("年度综合报表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }


}
