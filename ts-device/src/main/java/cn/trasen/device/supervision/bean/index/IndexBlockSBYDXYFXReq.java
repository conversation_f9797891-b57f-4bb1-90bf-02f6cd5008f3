package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockSBYDXYFXReq
 * @author: chenbin
 * @description: 设备月度效益分析请求结构体
 * @date: 2024/6/18 16:31
 * @version: 1.0
 */

@Data
public class IndexBlockSBYDXYFXReq extends BaseReq {

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "本对象，前端什么都不用传，后台处理（传了也无效）")
    private String __remark;
}
