package cn.trasen.device.supervision.model;

import cn.trasen.device.supervision.model.workflow.DeviceWfInstanceInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.model
 * @className: WFBudgetInstanceInfo
 * @author: chenbin
 * @description: TODO
 * @date: 2024/5/13 09:14
 * @version: 1.0
 */

@Table(name = "toa_device_budget_instance_info")
@Setter
@Getter
public class WFBudgetInstanceInfo extends DeviceWfInstanceInfo {

    @ApiModelProperty(value = "资产类批复金额")
    private String zcpfje;

    @ApiModelProperty(value = "服务类批复金额")
    private String fwpfje;

    @ApiModelProperty(value = "总金额")
    private String zje;

}
