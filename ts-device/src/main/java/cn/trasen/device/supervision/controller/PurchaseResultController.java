package cn.trasen.device.supervision.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.comm.ImportResp;
import cn.trasen.device.supervision.util.Comm;
import cn.trasen.device.supervision.util.PoiHelper;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PurchaseResult;
import cn.trasen.device.supervision.service.PurchaseResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseResultController
 * @Description TODO
 * @date 2024年4月2日 下午7:27:49
 */
@RestController
@Api(tags = "PurchaseResultController")
public class PurchaseResultController {

    private transient static final Logger logger = LoggerFactory.getLogger(PurchaseResultController.class);

    @Autowired
    private PurchaseResultService purchaseResultService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title savePurchaseResult
     * @Description 板
     * @date 2024年4月2日 下午7:27:49
     * <AUTHOR>
     */
    @ApiOperation(value = "板", notes = "板")
    @PostMapping("/api/devicePurchaseResult/save")
    public PlatformResult<String> savePurchaseResult(@RequestBody PurchaseResult record) {
        try {
            purchaseResultService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updatePurchaseResult
     * @Description 缂杈
     * @date 2024年4月2日 下午7:27:49
     * <AUTHOR>
     */
    @ApiOperation(value = "缂杈", notes = "缂杈")
    @PostMapping("/api/devicePurchaseResult/update")
    public PlatformResult<String> updatePurchaseResult(@RequestBody PurchaseResult record) {
        try {
            purchaseResultService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "批量提交采购数据", notes = "批量提交采购数据")
    @PostMapping("/api/devicePurchaseResult/v3/update")
    public PlatformResult<String> batchUpdatePurchaseResult(@RequestBody PurchaseResultReq record) {
        try {
            purchaseResultService.batchUpdate(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量提交采购数据v4", notes = "批量提交采购数据v4")
    @PostMapping("/api/devicePurchaseResult/v4/update")
    public PlatformResult excelUpdatePurchaseResult(@RequestParam("file") MultipartFile file) {
        List<PurchaseResultImport> imports = (List<PurchaseResultImport>) ImportExcelUtil.getExcelDatas(file, PurchaseResultImport.class);
        try {
            ImportResp res = purchaseResultService.importUpdate(imports);
            if (res.getSuccs() == 0) {
                return PlatformResult.failure(Comm.importResp2String(res));
            } else {
                return PlatformResult.success(Comm.importResp2String(res));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<PurchaseResult>
     * @Title selectPurchaseResultById
     * @Description 规IDヨ
     * @date 2024年4月2日 下午7:27:49
     * <AUTHOR>
     */
    @ApiOperation(value = "璇", notes = "璇")
    @GetMapping("/api/devicePurchaseResult/{id}")
    public PlatformResult<PurchaseResult> selectPurchaseResultById(@PathVariable String id) {
        try {
            PurchaseResult record = purchaseResultService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "采购详情接口v3", notes = "采购详情接口v3")
    @GetMapping("/api/devicePurchaseResult/v3/{id}")
    public PlatformResult<PurchaseResultResp> selectPurchaseResultByIdV3(@PathVariable String id) {
        try {
            PurchaseResultResp record = purchaseResultService.selectByIdV3(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deletePurchaseResultById
     * @Description 规ID
     * @date 2024年4月2日 下午7:27:49
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "删除接口")
    @PostMapping("/api/devicePurchaseResult/delete/{id}")
    public PlatformResult<String> deletePurchaseResultById(@PathVariable String id) {
        try {
            purchaseResultService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseResult>
     * @Title selectPurchaseResultList
     * @Description ヨ㈠琛
     * @date 2024年4月2日 下午7:27:49
     * <AUTHOR>
     */
    @ApiOperation(value = "采购结果列表接口v2", notes = "采购结果列表接口v2")
    @GetMapping("/api/devicePurchaseResult/list")
    public DataSet<PurchaseResult> selectPurchaseResultList(Page page, PurchaseResult record) {
        return purchaseResultService.getDataSetList(page, record);
    }

    @ApiOperation(value = "采购结果列表接口v2.5", notes = "采购结果列表接口v2.5")
    @GetMapping("/api/devicePurchaseResult/v25/list")
    public DataSet<Map> selectPurchaseResultListV25(Page page, PurchaseResult record) {
        return purchaseResultService.getDataSetListV25(page, record);
    }


    @ApiOperation(value = "采购结果列表接口v3", notes = "采购结果列表接口v3")
    @GetMapping("/api/devicePurchaseResult/v3/list")
    public DataSet<PurchaseResult> selectPurchaseResultListV3(Page page, PurchaseResult record) {
        return purchaseResultService.getDataSetListV3(page, record);
    }

    @ApiOperation(value = "v2版本数据迁移到v3", notes = "v2版本数据迁移到v3")
    @GetMapping("/api/devicePurchaseResult/dataV22V3")
    public PlatformResult dataV22V3() {
        try {
            purchaseResultService.dataV22V3();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("同步完毕");
    }


    @ApiOperation(value = "修复已经导入的设备数据系统编码", notes = "修复已经导入的设备数据系统编码")
    @GetMapping("/api/devicePurchaseResult/fixDeviceSysCode")
    public PlatformResult fixDeviceSysCode() {
        try {
            purchaseResultService.fixDeviceSysCode();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("更新系统编码完毕");
    }


    @ApiOperation(value = "取消采购", notes = "取消采购")
    @PostMapping("/api/devicePurchaseResult/cancel/{id}")
    // form-data  提交
    public PlatformResult cancel(@PathVariable("id") String id, @RequestParam("remark") String remark) {
        try {
            purchaseResultService.cancel(id, remark);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("操作成功");
    }


    @ApiOperation(value = "确认维护信息", notes = "确认维护信息")
    @PostMapping("/api/devicePurchaseResult/check/{id}")
    // form-data  提交
    public PlatformResult check(@PathVariable("id") String id, @RequestParam("status") String status, @RequestParam("remark") String remark) {
        try {
            purchaseResultService.check(id, status, remark);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("操作成功");
    }

    @ApiOperation(value = "批量确认维护信息", notes = "批量确认维护信息")
    @PostMapping("/api/devicePurchaseResult/batchCheck")
    public PlatformResult check(@RequestBody PurchaseResultBatchCheckReq req) {
        try {
            purchaseResultService.batchCheck(req);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("操作成功");
    }

    @ApiOperation(value = "excel维护采购信息辅助数据导出", notes = "excel维护采购信息辅助数据导出")
    @RequestMapping(value = "/api/devicePurchaseResult/notMaintained/export")
    public ResponseEntity<byte[]> notMaintainedExport(@RequestBody PurchaseResult record) throws IOException {

        List<PurchaseResult> exportList = purchaseResultService.getDataSetListV3NoPage(record);
        TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/purchaseResultExportTpl.xlsx"));

        params.setColForEach(true);
        Map<String, Object> map = new HashMap<>();
        map.put("list", exportList); // 填充数据到第一个工作表
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);


        // 拉取第二个说明模板，一会儿进行合并
        String tpl1RealPath = ExportUtil.convertTemplatePath("template/purchaseResultExportTpl1.xlsx");

        FileInputStream excel2 = new FileInputStream(tpl1RealPath);
        Workbook workbook1 = new XSSFWorkbook(excel2);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {

            //TODO  此处曲线救国 。  有时间再优化
            Workbook mergedWorkbook = new XSSFWorkbook();
            // 将 workbook1 的内容复制到 mergedWorkbook 的第一个 sheet
            PoiHelper.copySheets(mergedWorkbook.createSheet("Sheet1"), workbook.getSheetAt(0));

            // 将 workbook2 的内容复制到 mergedWorkbook 的第二个 sheet
            PoiHelper.copySheets(mergedWorkbook.createSheet("说明"), workbook1.getSheetAt(0));

            mergedWorkbook.write(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            outputStream.close();
            workbook.close();
            workbook1.close();
        }

        byte[] contents = outputStream.toByteArray();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        String encodedFilename = new String("未维护采购申请单列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
        headers.setContentDispositionFormData("attachment", encodedFilename);
        headers.setContentLength(contents.length);

        return new ResponseEntity<>(contents, headers, HttpStatus.OK);
    }


    @ApiOperation(value = "批量绑定采购附件信息", notes = "批量绑定采购附件信息")
    @PostMapping("/api/devicePurchaseResult/batchBindAttachment")
    // form-data  提交
    public PlatformResult check(@RequestBody PurchaseResultBatchBindAttachmentReq req) {
        try {
            purchaseResultService.batchBindAttachment(req);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success("操作成功");
    }


}
