package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.model.*;

import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.ProjectMapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ProjectServiceImpl
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectMapper mapper;

    @Autowired
    private PurchaseLogService purchaseLogService;

    @Autowired
    private PurchaseGroupService purchaseGroupService;

    @Autowired
    private PurchaseResultService purchaseResultService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Project record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Project record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Project record = new Project();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByPurchaseLogId(String purchaseLogId) {
        Example example = new Example(Project.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("purchaseLogId", purchaseLogId);

        mapper.deleteByExample(example);
    }

    @Override
    public Project selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<ProjectManageListResp> getDataSetList(Page page, Project record) {
        String deptCode = deviceHelperService.getDeptCode();
        if (!deptCode.equals("admin")) {
            // 如果不是管理员，deptCode只允许为自身
            record.setDeptId(deptCode);
        }
        List<ProjectManageListResp> records = mapper.getDataSetList(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void submit(ProjectManageSaveReq record) {
        Project projectForm = record.getProject();

        if (projectForm == null) {
            throw new BusinessException("数据格式提交错误");
        }

        PurchaseLog purchaseLogForm = record.getPurchaseLog();
        PurchaseGroup purchaseGroupForm = record.getPurchaseGroup();

        if (projectForm.getId() == null) {
            // 新增设备的时候需要把 purchase_log purchase_group purchase_result 一并保存
            // save
            String deptCode = deviceHelperService.justDeptCode();
            Organization organization = deviceHelperService.getOrganizationByOrgId(deptCode);


            // 新增采购结果信息
            PurchaseResult purchaseResult = new PurchaseResult();
            purchaseResult.setDeptId(deptCode);
            purchaseResult.setApplyOrg(organization.getName());
            purchaseResult.setDeviceName(projectForm.getName());
            purchaseResult.setApplyDate(Comm.getCurDate("yyyy-MM-dd"));
            purchaseResult.setSpec(projectForm.getBuildScale());
            purchaseResult.setType("工程项目采购申请表");
            purchaseResult.setApplyNumbers("1");
            purchaseResult.setApprovalDate(Comm.getCurDate("yyyy-MM-dd"));

            if (purchaseLogForm != null) {
                purchaseResult.setApplyPrice(purchaseLogForm.getPurchasePrice());
                purchaseResult.setApplyTotalPrice(purchaseLogForm.getPurchaseTotalPrice());
            }

            purchaseResult.setStatus("1");
            purchaseResultService.save(purchaseResult);


            // 新增块信息


            String purchaseGroupId = "";
            if (purchaseGroupForm != null) {
                purchaseGroupForm.setPurchaseResultId(purchaseResult.getId());
                purchaseGroupForm.setDeptId(deptCode);
                purchaseGroupService.save(purchaseGroupForm);
                purchaseGroupId = purchaseGroupForm.getId();
            } else {
                PurchaseGroup purchaseGroup = new PurchaseGroup();
                purchaseGroup.setPurchaseResultId(purchaseResult.getId());
                purchaseGroup.setDeptId(deptCode);
                purchaseGroupService.save(purchaseGroup);
                purchaseGroupId = purchaseGroup.getId();
            }

            String purchaseLogId = "";
            // 容错采购日志不提交的问题
            if (purchaseLogForm != null) {
                purchaseLogForm.setPurchaseGroupId(purchaseGroupId);
                purchaseLogForm.setDeptId(deptCode);
                purchaseLogService.save(purchaseLogForm);
                purchaseLogId = purchaseLogForm.getId();
            } else {
                // 新增采购记录
                PurchaseLog purchaseLog = new PurchaseLog();
                purchaseLog.setPurchaseGroupId(purchaseGroupId);
                purchaseLog.setDeptId(deptCode);
                purchaseLogService.save(purchaseLog);
                purchaseLogId = purchaseLog.getId();
            }

            // 新增工程
            projectForm.setPurchaseLogId(purchaseLogId);
            projectForm.setDeptId(deptCode);
            projectForm.setIsComplete("1");
            save(projectForm);

        } else {
            // update
            update(projectForm);

            if (purchaseLogForm != null && purchaseLogForm.getId() != null) {
                purchaseLogService.update(purchaseLogForm);
            }

            if (purchaseGroupForm != null && purchaseGroupForm.getId() != null) {
                purchaseGroupService.update(purchaseGroupForm);
            }
        }
    }

    @Override
    public ProjectManageDetailResp detail(String id) {
        ProjectManageDetailResp resp = new ProjectManageDetailResp();
        Project project = selectById(id);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }
        resp.setProject(project);

        // 查询采购信息
        PurchaseLog purchaseLog = purchaseLogService.selectById(project.getPurchaseLogId());
        if (purchaseLog != null) {
            // 查询采购组信息 招标相关文件
            PurchaseGroup purchaseGroup = purchaseGroupService.selectById(purchaseLog.getPurchaseGroupId());
            PurchaseResult purchaseResult = purchaseResultService.selectById(purchaseGroup.getPurchaseResultId());
            resp.setPurchaseLog(purchaseLog);
            resp.setPurchaseGroup(purchaseGroup);
            resp.setPurchaseResult(purchaseResult);
        }
        return resp;
    }


}
