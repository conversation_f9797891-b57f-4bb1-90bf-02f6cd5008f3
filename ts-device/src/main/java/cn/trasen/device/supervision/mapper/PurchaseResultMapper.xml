<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.PurchaseResultMapper">

    <select id="getDataSetListV3" resultType="java.util.Map"
            parameterType="cn.trasen.device.supervision.model.PurchaseResult">
        select
        t1.id,
        t1.wf_definition_id as wfDefinitionId,
        t1.wf_instance_id as wfInstanceId,
        t1.apply_org as applyOrg,
        t1.device_name as deviceName,
        t1.type,
        t1.apply_date as applyDate,
        t1.spec,
        t1.apply_numbers as applyNumbers,
        t1.apply_price as applyPrice,
        t1.apply_total_price as applyTotalPrice,
        t1.approval_date as approvalDate,
        t1.status,
        t1.purchase_type as purchaseType,
        t4.name as purchaseName,
        t4.purchase_date as purchaseDate,
        t4.supplier,
        t4.producer,
        t4.purchase_brand as purchaseBrand,
        t4.purchase_spec as purchaseSpec,
        t4.purchase_way as purchaseWay,
        t4.build_scale as buildScale,
        t4.accept_date as acceptDate,
        t4.purchase_numbers as purchaseNumbers,
        t4.purchase_price as purchasePrice,
        t4.purchase_total_price as purchaseTotalPrice,
        t4.funds_sources as fundsSources,
        t4.service_start_at as serviceStartAt,
        t4.service_end_at as serviceEndAt,
        t4.purchase_files as purchaseFiles,
        t3.tender_parameter as tenderParameter,
        t3.tender_files as tenderFiles,
        t3.purchase_remark as purchaseRemark

        from toa_device_purchase_result t1
        LEFT JOIN hrms_employee t2 on t1.create_user = t2.employee_no
        LEFT JOIN toa_device_purchase_group t3 on t3.purchase_result_id = t1.id
        LEFT JOIN toa_device_purchase_log t4 on t4.purchase_group_id = t3.id
        where t1.is_deleted = 'N' and t1.wf_instance_id is not null
        <if test="deptCode != null and deptCode != ''">
            and t2.org_id = #{deptCode}
        </if>
        <if test="applyOrg != null and applyOrg != ''">
            and t1.apply_org like CONCAT('%', #{applyOrg}, '%')
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t1.device_name like CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="type != null and type != ''">
            <choose>
                <when test="type == &quot;设备&quot;">
                    AND t1.type in ('一般设备采购申请表','医疗专用设备采购申请表')
                </when>
                <otherwise>
                    and t1.type = #{type}
                </otherwise>
            </choose>
        </if>
        <if test="wfDefinitionId != null and wfDefinitionId != ''">
            and t1.wf_definition_id = #{wfDefinitionId}
        </if>
        <if test="applyYear != null and applyYear != ''">
            and YEAR (t1.apply_date) = #{applyYear}
        </if>
        <if test="approvalStartDate != null and approvalStartDate != ''">
            and t1.approval_date between #{approvalStartDate} and #{approvalEndDate}
        </if>
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            and (
            t1.device_name LIKE CONCAT('%', #{keyword}, '%')
            or t4.purchase_brand LIKE CONCAT('%', #{keyword}, '%')
            or t4.supplier LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        order by t1.apply_date desc,t1.id,t4.purchase_date desc
    </select>
    <sql id="getDataSetListSql">
        select t1.* from toa_device_purchase_result t1
        LEFT JOIN hrms_employee t2 on t1.create_user = t2.employee_no
        left join comm_organization t3 on t1.dept_id = t3.organization_id
        where t1.is_deleted = 'N' and t1.wf_instance_id is not null
        <if test="deptCode != null and deptCode != ''">
            and t2.org_id = #{deptCode}
        </if>
        <if test="applyOrg != null and applyOrg != ''">
            and t1.apply_org like CONCAT('%', #{applyOrg}, '%')
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t1.device_name like CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="type != null and type != ''">
            <choose>
                <when test="type == &quot;设备&quot;">
                    AND t1.type in ('一般设备采购申请表','医疗专用设备采购申请表')
                </when>
                <otherwise>
                    and t1.type = #{type}
                </otherwise>
            </choose>
        </if>
        <if test="wfDefinitionId != null and wfDefinitionId != ''">
            and t1.wf_definition_id = #{wfDefinitionId}
        </if>
        <if test="applyYear != null and applyYear != ''">
            and YEAR (t1.apply_date) = #{applyYear}
        </if>
        <if test="approvalStartDate != null and approvalStartDate != ''">
            and t1.approval_date between #{approvalStartDate} and #{approvalEndDate}
        </if>
        <if test="status != null and status != ''">
            and t1.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            and (
            t1.device_name LIKE CONCAT('%', #{keyword}, '%')
            or t1.purchase_brand LIKE CONCAT('%', #{keyword}, '%')
            or t1.supplier LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="idList != null and idList.size() > 0">
            and t1.id IN
            <foreach item="id" collection="idList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY t1.`approval_date` desc, t3.custom_code IS NULL, t3.custom_code asc, FIELD(t1.`wf_definition_id`,
        '06985B07055046639923C386071BA66E', 'A37FCFB6631B405EA3A790E32FE88655',
        'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0','1866B74A6BC3400F990AB9E3C2DAA1A6')
    </sql>
    <select id="getDataSetList" resultType="cn.trasen.device.supervision.model.PurchaseResult"
            parameterType="cn.trasen.device.supervision.model.PurchaseResult">
        <include refid="getDataSetListSql"/>
    </select>

    <select id="getDataSetListNoPage" resultType="cn.trasen.device.supervision.model.PurchaseResult"
            parameterType="cn.trasen.device.supervision.model.PurchaseResult">
        <include refid="getDataSetListSql"/>
    </select>

    <select id="syncPurchaseResultData">
        INSERT
        IGNORE INTO toa_device_purchase_result(id,wf_instance_id,wf_definition_id,dept_id,sources_funds,apply_org,device_name,type,apply_date,spec,apply_numbers,apply_price,apply_total_price,approval_date,status,
		create_date,create_user,create_user_name,is_deleted)
        SELECT t1.WF_INSTANCE_ID,
               t1.WF_INSTANCE_ID,
               t1.WF_DEFINITION_ID,
               t1.LAUNCH_DEPT_CODE,
               t3.zjly,
               t2.sqsw0,
               t3.xmmc,
               '医疗专用设备采购申请表'                                      AS `type`,
               t2.sbsj8,
               t3.ggxh,
               t3.pfsl,
               t3.pfdj,
               t3.pfje,
               date_format(str_to_date(`t3`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `approval_date`,
               '0'                                                           as status,
               t1.CREATE_DATE,
               t1.CREATE_USER,
               t1.CREATE_USER_NAME,
               t1.IS_DELETED
        FROM wf_instance_info t1
                 LEFT JOIN zdy_ylzysbcgjhsqb20240201113802 t2 ON `t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`
                 LEFT JOIN zt_ylzysbcgjhsqb t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
        WHERE `t1`.`WF_DEFINITION_ID` = '06985B07055046639923C386071BA66E'
          AND `t1`.`STATUS` = '2'
          AND `t1`.`IS_DELETED` = 'N'
          AND DATE (t1.UPDATE_DATE) = DATE (NOW());


        INSERT
        IGNORE INTO toa_device_purchase_result(id,wf_instance_id,wf_definition_id,dept_id,sources_funds,apply_org,device_name,type,apply_date,spec,apply_numbers,apply_price,apply_total_price,approval_date,status,
		create_date,create_user,create_user_name,is_deleted)
        SELECT t1.WF_INSTANCE_ID,
               t1.WF_INSTANCE_ID,
               t1.WF_DEFINITION_ID,
               t1.LAUNCH_DEPT_CODE,
               t3.zjly,
               t2.sqsw0,
               t3.xmmc,
               '一般设备采购申请表'                                          AS `type`,
               t2.sbsj7,
               t3.ggxh,
               t3.pfsl,
               t3.pfdj,
               t3.pfje,
               date_format(str_to_date(`t3`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `approval_date`,
               '0'                                                           as status,
               t1.CREATE_DATE,
               t1.CREATE_USER,
               t1.CREATE_USER_NAME,
               t1.IS_DELETED
        FROM wf_instance_info t1
                 LEFT JOIN zdy_ylybsbcgjhsbb20240201151026 t2 ON `t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`
                 LEFT JOIN zt_ybsbcgjhsqb t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
        WHERE `t1`.`WF_DEFINITION_ID` = 'A37FCFB6631B405EA3A790E32FE88655'
          AND `t1`.`STATUS` = '2'
          AND `t1`.`IS_DELETED` = 'N'
          AND DATE (t1.UPDATE_DATE) = DATE (NOW());

        INSERT
        IGNORE INTO toa_device_purchase_result(id,wf_instance_id,wf_definition_id,dept_id,sources_funds,apply_org,device_name,type,apply_date,spec,apply_numbers,apply_price,apply_total_price,approval_date,status,
		create_date,create_user,create_user_name,is_deleted)
        SELECT t1.WF_INSTANCE_ID,
               t1.WF_INSTANCE_ID,
               t1.WF_DEFINITION_ID,
               t1.LAUNCH_DEPT_CODE,
               t3.zjly,
               t2.sbsw0,
               t3.xmmc,
               '工程项目采购申请表'                                          AS `type`,
               t2.sbsj1,
               t3.jzmj,
               '1'                                                           AS pfsl,
               t3.pfje,
               t3.pfje,
               date_format(str_to_date(`t3`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `approval_date`,
               '0'                                                           as status,
               t1.CREATE_DATE,
               t1.CREATE_USER,
               t1.CREATE_USER_NAME,
               t1.IS_DELETED
        FROM wf_instance_info t1
                 LEFT JOIN zdy_jtgcxmcgjhsbb20240201153016 t2 ON `t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`
                 LEFT JOIN zt_gcxmcgjhsqb t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
        WHERE `t1`.`WF_DEFINITION_ID` = 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0'
          AND `t1`.`STATUS` = '2'
          AND `t1`.`IS_DELETED` = 'N'
          AND DATE (t1.UPDATE_DATE) = DATE (NOW());


        INSERT
        IGNORE INTO toa_device_purchase_result(id,wf_instance_id,wf_definition_id,dept_id,sources_funds,apply_org,device_name,type,apply_date,spec,apply_numbers,apply_price,apply_total_price,approval_date,status,
		create_date,create_user,create_user_name,is_deleted)
        SELECT t1.WF_INSTANCE_ID,
               t1.WF_INSTANCE_ID,
               t1.WF_DEFINITION_ID,
               t1.LAUNCH_DEPT_CODE,
               t3.zjly,
               t2.sbsw0,
               t3.xmmc,
               '服务项目采购申请表'                                                               AS `type`,
               t2.sbsj1,
               concat(replace(`t3`.`fwkssj`, '-', '.'), '-', replace(`t3`.`fwjssj`, '-', '.'))    AS spec,
               ceiling(cast(`t3`.`pfje` AS DECIMAL(10, 2)) / cast(`t3`.`pfdj` AS DECIMAL(10, 2))) AS `pfsl`,
               t3.pfdj,
               t3.pfje,
               date_format(str_to_date(`t3`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d')                      AS `approval_date`,
               '0'                                                                                as status,
               t1.CREATE_DATE,
               t1.CREATE_USER,
               t1.CREATE_USER_NAME,
               t1.IS_DELETED
        FROM wf_instance_info t1
                 LEFT JOIN zdy_fwxmcgjhsbb20240201155028 t2
                           ON `t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`
                 LEFT JOIN zt_fwxmcgjhsbb t3 ON t1.WF_INSTANCE_ID = t3.WORKFLOW_ID
        WHERE `t1`.`WF_DEFINITION_ID` = '1866B74A6BC3400F990AB9E3C2DAA1A6'
          AND `t1`.`STATUS` = '2'
          AND `t1`.`IS_DELETED` = 'N'
          AND DATE (t1.UPDATE_DATE) = DATE (NOW());
    </select>

    <update id="syncToEntity">
        <choose>
            <when test="type == &quot;服务项目采购申请表&quot;">
                update toa_device_services set
                `funds_sources` = #{purchaseLog.fundsSources},
                `service_start_at` = #{purchaseLog.serviceStartAt},
                `service_end_at` = #{purchaseLog.serviceEndAt},
                `unit_price` = #{purchaseLog.purchasePrice},
                `total_price` = #{purchaseLog.purchaseTotalPrice}
                where purchase_log_id = #{purchaseLog.id}
            </when>
            <when test="type == &quot;工程项目采购申请表&quot;">
                update toa_device_project set
                `funds_sources` = #{purchaseLog.fundsSources},
                `build_scale` = #{purchaseLog.buildScale},
                `investment_scale` = #{purchaseLog.purchaseTotalPrice},
                `unit_price` = #{purchaseLog.purchasePrice}
                where purchase_log_id = #{purchaseLog.id}
            </when>
            <otherwise>
                update toa_device_device set
                `supplier` = #{purchaseLog.supplier},
                `producer` = #{purchaseLog.producer},
                `brand` = #{purchaseLog.purchaseBrand},
                `spec` = #{purchaseLog.purchaseSpec},
                `funds_sources` = #{purchaseLog.fundsSources},
                `unit_price` = #{purchaseLog.purchasePrice},
                `cate_id` = #{purchaseLog.cateId},
                `common_name` = #{purchaseLog.commonName}
                where purchase_log_id = #{purchaseLog.id}
            </otherwise>
        </choose>
    </update>

    <select id="getIdNeedMaintenance" resultType="string">
        SELECT id
        FROM toa_device_purchase_result
        WHERE is_deleted = 'N'
        and status in ('0','1','3')
        <if test="ids != null and ids.size() > 0">
            and id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="batchBindAttachment"
            parameterType="cn.trasen.device.supervision.bean.PurchaseResultBatchBindAttachmentReq">
        update toa_device_purchase_group
        set tender_parameter = #{tenderParameter},
        tender_files = #{tenderFiles},
        update_date = now()
        where purchase_result_id in
        <foreach collection="purchaseResultIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>;

        update toa_device_purchase_log
        set purchase_files = #{purchaseFiles},
        update_date = now()
        where purchase_group_id in (
        select id from toa_device_purchase_group where purchase_result_id in
        <foreach collection="purchaseResultIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        );
    </update>

    <update id="batchCheck" parameterType="cn.trasen.device.supervision.bean.PurchaseResultBatchCheckReq" >
        update toa_device_purchase_result
        set status = #{status},
        update_date = now(),
        check_remark = #{remark}
        where id in
        <foreach collection="purchaseResultIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status = 1
    </update>

</mapper>