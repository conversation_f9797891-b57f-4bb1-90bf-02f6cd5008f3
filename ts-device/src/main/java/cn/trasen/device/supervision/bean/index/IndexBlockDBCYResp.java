package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: IndexBlockDBCY
 * @author: chenbin
 * @description: 待办查阅
 * @date: 2024/4/19 15:00
 * @version: 1.0
 */

@Data
public class IndexBlockDBCYResp {

    @ApiModelProperty(value = "预算申请项目数")
    private String yssqsl;

    @ApiModelProperty(value = "预算申请总金额")
    private String yssqzje;

    @ApiModelProperty(value = "预算已审批数量")
    private String ysyspsl;

    @ApiModelProperty(value = "预算已审批金额")
    private String ysyspzje;


    @ApiModelProperty(value = "预算待审批数量")
    private String ysdspsl;

    @ApiModelProperty(value = "预算待审批金额")
    private String ysdspzje;


    @ApiModelProperty(value = "预算项目数完成比")
    private String ysxmswcb;

    @ApiModelProperty(value = "预算金额完成比")
    private String ysjewcb;

    @ApiModelProperty(value = "采购申请数量")
    private String cgsqsl;

    @ApiModelProperty(value = "采购申请金额")
    private String cgsqje;

    @ApiModelProperty(value = "上会审批数量")
    private String shspsl;

    @ApiModelProperty(value = "上会审批金额")
    private String shspje;

    @ApiModelProperty(value = "待上会审批数量")
    private String dshspsl;

    @ApiModelProperty(value = "待上会审批金额")
    private String dshspje;


    @ApiModelProperty(value = "党委会审批数量")
    private String dwhspsl;

    @ApiModelProperty(value = "党委会审批金额")
    private String dwhspje;

    @ApiModelProperty(value = "待党委会审批数量")
    private String ddwhspsl;

    @ApiModelProperty(value = "待党委会审批金额")
    private String ddwhspje;


    @ApiModelProperty(value = "上会审批比例")
    private String shspbl;

    @ApiModelProperty(value = "党委会审批比例")
    private String dwhspbl;


    @ApiModelProperty(value = "上会审批比例")
    private String dshspbl;

    @ApiModelProperty(value = "党委会审批比例")
    private String ddwhspbl;


    @ApiModelProperty(value = "专用设备流程已审核数量")
    private String zysblcyshsl;

    @ApiModelProperty(value = "一般设备流程已审核数量")
    private String ybsblcyshsl;

    @ApiModelProperty(value = "工程项目流程已审核数量")
    private String gcxmlcyshsl;

    @ApiModelProperty(value = "服务项目流程已审核数量")
    private String fwxmlcyshsl;


    @ApiModelProperty(value = "专用设备流程待审批数量")
    private String zysblcdspsl;

    @ApiModelProperty(value = "一般设备流程待审批数量")
    private String ybsblcdspsl;

    @ApiModelProperty(value = "工程项目流程待审批数量")
    private String gcxmlcdspsl;

    @ApiModelProperty(value = "服务项目流程待审批数量")
    private String fwxmlcdspsl;



}
