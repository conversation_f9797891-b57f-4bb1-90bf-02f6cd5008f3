package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.DeviceOperateLog;
import cn.trasen.device.supervision.model.FeeCode;

import java.util.List;

/**
 * @ClassName FeeCodeService
 * @Description TODO
 * @date 2024年5月20日 下午2:31:43
 * <AUTHOR>
 * @version 1.0
 */
public interface FeeCodeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024年5月20日 下午2:31:43
	 * <AUTHOR>
	 */
	Integer save(FeeCode record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024年5月20日 下午2:31:43
	 * <AUTHOR>
	 */
	Integer update(FeeCode record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024年5月20日 下午2:31:43
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return FeeCode
	 * @date 2024年5月20日 下午2:31:43
	 * <AUTHOR>
	 */
	FeeCode selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<FeeCode>
	 * @date 2024年5月20日 下午2:31:43
	 * <AUTHOR>
	 */
	DataSet<FeeCode> getDataSetList(Page page, FeeCode record);

	List<FeeCode> getListByDeviceId(String deviceId);


	Boolean checkExist(FeeCode record);
}
