package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Project;
import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.model.PurchaseLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: ProjectManageSaveReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/17 14:23
 * @version: 1.0
 */

@Data
public class ProjectManageSaveReq {
    @ApiModelProperty(value = "项目信息")
    private Project project;
    @ApiModelProperty(value = "采购信息")
    private PurchaseLog purchaseLog;
    @ApiModelProperty(value = "招标信息")
    private PurchaseGroup purchaseGroup;
}
