package cn.trasen.device.supervision.bean;

import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: ProcureConfigInDict
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/5 09:25
 * @version: 1.0
 */

@Data
public class ProcureConfigInDict {

    private String definitionId; // 流程定义ID
    private String type; // 采购类型
    private String typeSmaller; // 采购类型小类
    private String tableName; // 表名 子表单
    private String quotaFieldName; // 额度名称
    private String sourcesOfFundsFieldName; // 资金来源字段名
    private String tableNameP; // 表名 父表单
    private String applyDeptNameFieldName; // 申请部门字段名

    // 2024-05-11 由于整体上不在能在配置的情况下走下去了，所以这部分配置的逻辑暂时弃用了，为了维护的简单
    // configString 可能是配置 可能是 definitionId
    public ProcureConfigInDict(String configString) {
        if (configString == null) {
            throw new IllegalArgumentException("Input string cannot be null");
        }

        // Split the input string using '-' as the delimiter
        String[] parts = configString.split("-");

        // Check if the array has at least 4 elements
        if (parts.length >= 8) {
            // Assign values to the class attributes
            this.definitionId = parts[0];
            this.type = parts[1];
            this.tableName = parts[2];
            this.quotaFieldName = parts[3];
            this.sourcesOfFundsFieldName = parts[4];
            this.tableNameP = parts[5];
            this.applyDeptNameFieldName = parts[6];
            this.typeSmaller = parts[7];
        } else {
            switch (configString) {
                case "06985B07055046639923C386071BA66E":
                    this.definitionId = "06985B07055046639923C386071BA66E";
                    this.type = "project";
                    this.typeSmaller = "device1";
                    break;
                case "A37FCFB6631B405EA3A790E32FE88655":
                    this.definitionId = "06985B07055046639923C386071BA66E";
                    this.type = "project";
                    this.typeSmaller = "device0";
                    break;
                case "ADA7FFF3AFBC46258C6A3D2E6A9D7DE0":
                    this.definitionId = "06985B07055046639923C386071BA66E";
                    this.type = "project";
                    this.typeSmaller = "project";
                    break;
                case "1866B74A6BC3400F990AB9E3C2DAA1A6":
                    this.definitionId = "06985B07055046639923C386071BA66E";
                    this.type = "service";
                    this.typeSmaller = "service";
                    break;
            }

        }
    }

}
