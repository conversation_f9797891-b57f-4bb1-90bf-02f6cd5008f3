package cn.trasen.device.supervision.controller;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.model.DepartmentBudget;
import cn.trasen.device.supervision.model.workflow.DeviceWfDefinitionInfo;
import cn.trasen.device.supervision.model.workflow.DeviceWfInstanceInfo;
import cn.trasen.device.supervision.service.DataStructService;
import cn.trasen.device.supervision.service.DepartmentBudgetService;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.util.Comm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Year;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.oa.device.controller
 * @className: WorkflowController
 * @author: chenbin
 * @description: TODO
 * @date: 2024/1/27 15:15
 * @version: 1.0
 */

@RestController
@Api(tags = "DeviceWorkflowController")
@RequestMapping("api/deviceBudgetWorkflow")
public class BudgetApplyController {

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private DataStructService dataStructService;

    @Autowired
    private DepartmentBudgetService departmentBudgetService;

    @ApiOperation(value = "获流程列表，且不同状态下面的流程实例数量", notes = "获流程列表，且不同状态下面的流程实例数量")
    @PostMapping("getDefinitioniListWithStatus/{classifyType}")
    public PlatformResult getDefinitioniListWithStatus(@RequestBody List<String> withStatus, @PathVariable("classifyType") String classifyType) {
        try {
            // 获取字典配置相
            Map<String, String> dictMap = deviceHelperService.getDictMapByCode(deviceHelperService.SPECIAL_FORM_CLASSIFY_ID);
            if (dictMap.get(classifyType) == null) {
                return PlatformResult.failure("非法的流程类型");
            }
            String classifyPid = dictMap.get(classifyType);
            List<DeviceWfDefinitionInfo> wkDefinitionList = deviceHelperService.getWkDefinitionListByClassifyId(classifyPid);

            Map<String, Map<String, Integer>> numsDefinitionMap = new HashMap<>();
            Map<String, Integer> numsStatusMap = new HashMap<>();
            for (DeviceWfDefinitionInfo wkDefinition : wkDefinitionList) {
                Map<String, Integer> numsStatusMapItem = deviceHelperService.getNumsWithStatus(wkDefinition.getWfDefinitionId(), withStatus);
                numsDefinitionMap.put(wkDefinition.getWfDefinitionId(), numsStatusMapItem);
                // 累加每个状态的数量
                for (Map.Entry<String, Integer> entry : numsStatusMapItem.entrySet()) {
                    String k = entry.getKey();
                    Integer v = entry.getValue();
                    if (numsStatusMap.containsKey(k)) {
                        numsStatusMap.put(k, numsStatusMap.get(k) + v);
                    } else {
                        numsStatusMap.put(k, v);
                    }
                }
            }

            GetDefinitionListResp resp = new GetDefinitionListResp();
            resp.setWkDefinitionList(wkDefinitionList);
            resp.setNumsStatusMap(numsStatusMap);
            resp.setNumsDefinitionMap(numsDefinitionMap);

            return PlatformResult.success(resp);
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取表单的字段配置", notes = "获取表单的字段配置")
    @PostMapping("getDefinitionFieldConfig/{formId}")
    public PlatformResult filedConfig(@PathVariable("formId") String formId) {
        try {
            return PlatformResult.success(deviceHelperService.getToaFieldSetListByTemplateId(formId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    //dataStructService.scan();
    @ApiOperation(value = "结构化扫描", notes = "结构化扫描")
    @GetMapping("dataStructScan")
    public PlatformResult scan() {
        try {
            dataStructService.scan();
            return PlatformResult.success("扫描成功");
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("selectInstance/list/{definitionId}/{status}")
    public DataSet selectInstanceList(Page page, @RequestParam Map<String, String> query, @RequestBody Map<String, String> params, @PathVariable("definitionId") String definitionId, @PathVariable("status") String status) {
        return deviceHelperService.getInstanceListByDefinitionId(page, definitionId, status, params, query);
    }

    @ApiOperation(value = "获取当前机构的预算额度", notes = "instanceId 查当前流程发起者的额度 ｜ definitionId 查当前机构的额度 ，两个都传的情况下 instanceId 优先")
    @PostMapping("getBudgetQuota")
    public PlatformResult getBudgetQuota(@RequestParam String definitionId, @RequestParam String instanceId) {


        GetBudgetQuotaResp getBudgetQuotaResp = new GetBudgetQuotaResp();


        String deptCode = deviceHelperService.getUserParttimeOrgId(UserInfoHolder.getCurrentUserId());

        if (deptCode == null || "".equals(deptCode)) {
            deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
        }

        if (instanceId != null && !"".equals(instanceId)) {
            // 查询流程信息
            DeviceWfInstanceInfo wfInstanceInfo = deviceHelperService.getWfInstanceInfoById(instanceId);
            if (wfInstanceInfo == null) {
                return PlatformResult.success(getBudgetQuotaResp);
            }
            definitionId = wfInstanceInfo.getWfDefinitionId();
            deptCode = wfInstanceInfo.getLaunchDeptCode();
        }

        // 先把dj全部同步一下
        deviceHelperService.autoCalcuUnitPrice();

        // 先实时算一边预算总额情况
        BudgetInstanceInfoListReq biilr = new BudgetInstanceInfoListReq();
        biilr.setEffectYear(Comm.getCurDate("yyyy"));
        departmentBudgetService.calcuDepartmentBudgetGetted(biilr);

        // 先实时算一遍预算使用情况
        PurchaseInstanceInfoListReq piilr = new PurchaseInstanceInfoListReq();
        piilr.setEffectYear(Comm.getCurDate("yyyy"));
        piilr.setDeptCode(deptCode);

        departmentBudgetService.calcuDepartmentBudgetCosted(piilr);


        ProcureConfigInDict procureConfigInDict = new ProcureConfigInDict(definitionId);
        DepartmentBudget departmentBudget = departmentBudgetService.getDepartmentBudget(deptCode);


        getBudgetQuotaResp.setDepartmentBudget(departmentBudget);
        getBudgetQuotaResp.setProcureConfigInDict(procureConfigInDict);

        return PlatformResult.success(getBudgetQuotaResp);

    }


    @ApiOperation(value = "检测当前表单内容是否超过申请额度", notes = "检测当前表单内容是否超过申请额度")
    @PostMapping("checkBudgetQuota")
    public PlatformResult checkBudgetQuota(@RequestBody WfFormData wfFormData) {

        String deptCode = deviceHelperService.getUserParttimeOrgId(UserInfoHolder.getCurrentUserId());

        if (deptCode == null || "".equals(deptCode)) {
            deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
        }

        // 增加绿色通道
        Boolean green = departmentBudgetService.noCheckBudget(deptCode);

        if (green) {
            return PlatformResult.success("ok");
        }

        String definitionId = wfFormData.getWfDefinitionId();

        String applyDateField = null;

        if ("06985B07055046639923C386071BA66E".equals(definitionId)) {
            applyDateField = "sbsj8";
        }
        //一般设备采购
        if ("A37FCFB6631B405EA3A790E32FE88655".equals(definitionId)) {
            applyDateField = "sbsj7";
        }
        //工程项目采购
        if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(definitionId)) {
            applyDateField = "sbsj1";
        }
        //服务项目采购
        if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(definitionId)) {
            applyDateField = "sbsj1";
        }

        // 时间格式是这样的 2024-03-30 拿出来年份跟当前年做比较，小于当前年份则不进行预算判断
        String applyDate = wfFormData.getDataMap().get(applyDateField);

        if (applyDate == null || "".equals(applyDate)) {
            return PlatformResult.failure("申请日期不能为空");
        }

        int currentYear = Year.now().getValue();
        // 获取当前年份
        int applyYear = Integer.parseInt(applyDate.substring(0, 4));
        if (applyYear < currentYear) {
            return PlatformResult.success("ok");
        }

        BigDecimal cost = departmentBudgetService.calcuBudgetCost(wfFormData);

        // 获取当前剩余的额度
        ProcureConfigInDict procureConfigInDict = new ProcureConfigInDict(definitionId);
        DepartmentBudget departmentBudget = departmentBudgetService.getDepartmentBudget(deptCode);

        BigDecimal left = new BigDecimal(0);
        if (procureConfigInDict.getType().equals("service")) {
            left = departmentBudget.getServiceQuotaLeft();
        } else {
            left = departmentBudget.getProjectQuotaLeft();
        }

        if (left.compareTo(cost) < 0) {
            return PlatformResult.failure(String.format("超过预算额度 %s (万元)", left.subtract(cost)));
        }

        return PlatformResult.success("ok");


    }
}
