package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.DeviceMaintenanceLogMapper;
import cn.trasen.device.supervision.model.DeviceMaintenanceLog;
import cn.trasen.device.supervision.service.DeviceMaintenanceLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceMaintenanceLogServiceImpl
 * @Description TODO
 * @date 2024年4月15日 下午5:52:02
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeviceMaintenanceLogServiceImpl implements DeviceMaintenanceLogService {

    @Autowired
    private DeviceMaintenanceLogMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DeviceMaintenanceLog record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DeviceMaintenanceLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DeviceMaintenanceLog record = new DeviceMaintenanceLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DeviceMaintenanceLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DeviceMaintenanceLog> getDataSetList(Page page, DeviceMaintenanceLog record) {
        Example example = new Example(DeviceMaintenanceLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<DeviceMaintenanceLog> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<DeviceMaintenanceLog> getListByDeviceId(String deviceId) {
        Example example = new Example(DeviceMaintenanceLog.class);
        example.createCriteria().andEqualTo("deviceId", deviceId).andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        List<DeviceMaintenanceLog> deviceMaintenanceLogList = mapper.selectByExample(example);

        return deviceMaintenanceLogList;
    }
}
