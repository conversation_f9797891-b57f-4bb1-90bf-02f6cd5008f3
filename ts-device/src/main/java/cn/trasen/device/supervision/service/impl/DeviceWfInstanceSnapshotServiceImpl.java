package cn.trasen.device.supervision.service.impl;

import java.util.*;

import cn.trasen.device.supervision.dao.DeviceWfInstanceSnapshotMapper;
import cn.trasen.device.supervision.model.ApproveData;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.model.WfInstanceSnapshot;
import cn.trasen.device.supervision.service.DeviceWfInstanceSnapshotService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WfInstanceSnapshotServiceImpl
 * @Description TODO
 * @date 2024年4月24日 下午4:35:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeviceWfInstanceSnapshotServiceImpl implements DeviceWfInstanceSnapshotService {

    @Autowired
    private DeviceWfInstanceSnapshotMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(WfInstanceSnapshot record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(WfInstanceSnapshot record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        WfInstanceSnapshot record = new WfInstanceSnapshot();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public WfInstanceSnapshot selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<WfInstanceSnapshot> getDataSetList(Page page, WfInstanceSnapshot record) {
        Example example = new Example(WfInstanceSnapshot.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<WfInstanceSnapshot> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void saveSnapShot(String taskId, ApproveData approveData) {
        ObjectMapper objectMapper = new ObjectMapper();

        String mainFormData = "";
        String sonFormData = "";

        try {
            List<Object> sonList = new ArrayList<>();
            Map<String, Object> sonFormDataMap = new HashMap<>();

            // 保持和正常流程一致的数据结构
            sonFormDataMap.put("tableName", approveData.getTableName());

            List<Object> childTableList = new ArrayList<>();
            childTableList.add(approveData);

            sonFormDataMap.put("childTableList", childTableList);
            sonList.add(objectMapper.writeValueAsString(sonFormDataMap));
            sonFormData = objectMapper.writeValueAsString(sonList);

        } catch (Exception e) {
            // ignore exception
            e.printStackTrace();
        }

        WfInstanceSnapshot record = new WfInstanceSnapshot();
        record.setWfInstanceId(approveData.getWfInstanceId());
        record.setWfTaskId(taskId);
        record.setMainFormData(mainFormData);
        record.setSonFormData(sonFormData);

        save(record);
    }
}
