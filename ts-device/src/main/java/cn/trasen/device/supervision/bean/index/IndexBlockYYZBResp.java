package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockYYZBResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/6 17:28
 * @version: 1.0
 */
@Data
public class IndexBlockYYZBResp {

    @ApiModelProperty(value = "设备总数")
    String sbzs;

    @ApiModelProperty(value = "年度总收入")
    String ndzsr;

    @ApiModelProperty(value = "年度总支出")
    String ndzzc;

    @ApiModelProperty(value = "月度总收入")
    String ydzsr;

    @ApiModelProperty(value = "月度总支出")
    String ydzzc;
}
