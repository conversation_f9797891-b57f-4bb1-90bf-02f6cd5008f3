package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_device_organization_mapper")
@Setter
@Getter
public class OrganizationMapper {
    @Id
    private String id;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String name;

    /**
     * 卫健委组织ID
     */
    @Column(name = "id_wjw")
    @ApiModelProperty(value = "卫健委组织ID")
    private String idWjw;

    /**
     * 健康浏阳组织ID
     */
    @Column(name = "id_jkly")
    @ApiModelProperty(value = "健康浏阳组织ID")
    private String idJkly;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}