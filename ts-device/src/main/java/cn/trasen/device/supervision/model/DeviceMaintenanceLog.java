package cn.trasen.device.supervision.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_device_maintenance_log")
@Setter
@Getter
public class DeviceMaintenanceLog {
    @Id
    private String id;

    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;


    /**
     * 合同编号
     */
    @Column(name = "contract_number")
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @Column(name = "contract_name")
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 保修开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "maintenance_start_at")
    @ApiModelProperty(value = "保修开始时间")
    private String maintenanceStartAt;


    /**
     * 保修结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "maintenance_end_at")
    @ApiModelProperty(value = "保修结束时间")
    private String maintenanceEndAt;


    /**
     * 保修类型
     */
    @Column(name = "maintenance_type")
    @ApiModelProperty(value = "保修类型")
    private String maintenanceType;


    /**
     * 服务商
     */
    @Column(name = "service_provider")
    @ApiModelProperty(value = "服务商")
    private String serviceProvider;


    /**
     * 维保合同
     */
    @Column(name = "maintenance_file")
    @ApiModelProperty(value = "维保合同")
    private String maintenanceFile;


    /**
     * 合同签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "contract_signing_at")
    @ApiModelProperty(value = "合同签订日期")
    private Date contractSigningAt;

    /**
     * 维保费用
     */
    @ApiModelProperty(value = "维保费用")
    private BigDecimal costs;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 扩展字段
     */
    @Column(name = "expand_field")
    @ApiModelProperty(value = "扩展字段")
    private String expandField;
}