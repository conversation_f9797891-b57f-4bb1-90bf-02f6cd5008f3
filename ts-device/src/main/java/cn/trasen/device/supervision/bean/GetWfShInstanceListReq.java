package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: GetWfShInstanceListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/12 11:08
 * @version: 1.0
 */

@Data
public class GetWfShInstanceListReq {

    @ApiModelProperty(value = "申报单位")
    private String applyOrg;

    @ApiModelProperty(value = "上会日期")
    private String meetingDate;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "设备或项目名称")
    private String deviceOrProjectName;

    @ApiModelProperty(value = "是否导出")
    private String export;

    @ApiModelProperty(value = "处理人ID")
    private String assigneeNo;

    @ApiModelProperty(value = "排序字段")
    private String sidx;
    @ApiModelProperty(value = "ASE/DESC排序方式")
    private String sord;

}
