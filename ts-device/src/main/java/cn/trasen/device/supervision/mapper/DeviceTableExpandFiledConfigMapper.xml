<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceTableExpandFiledConfigMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.TableExpandFiledConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="is_base" jdbcType="CHAR" property="isBase" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="placeholder" jdbcType="VARCHAR" property="placeholder" />
    <result column="is_required" jdbcType="CHAR" property="isRequired" />
    <result column="is_read" jdbcType="CHAR" property="isRead" />
    <result column="is_important" jdbcType="CHAR" property="isImportant" />
    <result column="length" jdbcType="SMALLINT" property="length" />
    <result column="formatter" jdbcType="VARCHAR" property="formatter" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="value_def" jdbcType="VARCHAR" property="valueDef" />
    <result column="seq_table" jdbcType="SMALLINT" property="seqTable" />
    <result column="seq_form" jdbcType="SMALLINT" property="seqForm" />
    <result column="seq_query" jdbcType="SMALLINT" property="seqQuery" />
    <result column="width_table" jdbcType="VARCHAR" property="widthTable" />
    <result column="width_form" jdbcType="VARCHAR" property="widthForm" />
    <result column="width_query" jdbcType="VARCHAR" property="widthQuery" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="value_set" jdbcType="LONGVARCHAR" property="valueSet" />
  </resultMap>
  <select id="generateTableExpandFiledConfig" resultType="cn.trasen.device.supervision.bean.TableInfo">
    SELECT column_name AS columnName, data_type AS columnType, column_comment AS columnComment
      FROM information_schema.columns
      WHERE table_name = #{tableName}
        AND table_schema = (SELECT DATABASE())
  </select>

  <select id="fieldExist" resultType="java.lang.Integer">
      SELECT COUNT(1)
      FROM `toa_device_table_expand_filed_config`
      WHERE table_name = #{tableName}
        AND field_name = #{fieldName}
  </select>
</mapper>