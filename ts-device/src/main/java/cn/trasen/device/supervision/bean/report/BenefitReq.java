package cn.trasen.device.supervision.bean.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: BenefitReq
 * @author: chenbin
 * @description: 效益分析查询结构体
 * @date: 2024/6/20 15:18
 * @version: 1.0
 */

@Data
public class BenefitReq {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "医院机构ID")
    private String deptId;

    @ApiModelProperty(value = "分类ID")
    private String cateId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "开始时间")
    private String start;

    @ApiModelProperty(value = "结束时间")
    private String end;

    @ApiModelProperty(value = "排序字段")
    private String sidx;

    @ApiModelProperty(value = "ASE/DESC排序方式")
    private String sord;
}
