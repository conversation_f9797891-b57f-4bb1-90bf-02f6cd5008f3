package cn.trasen.device.supervision.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.trasen.device.supervision.bean.JdGridTableEntity;
import cn.trasen.device.supervision.bean.WfInstanceBatchDeleteReq;
import cn.trasen.device.supervision.bean.WfInstanceDeleteReq;
import cn.trasen.device.supervision.bean.index.IndexBlockDBCYReq;
import cn.trasen.device.supervision.bean.index.IndexBlockDBSXReq;
import cn.trasen.device.supervision.bean.index.IndexBlockYSCGReq;
import cn.trasen.device.supervision.service.DeviceWfInstanceSnapshotService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.feign.workflow.service.WorkflowTaskService01;
import cn.trasen.homs.form.model.FormDataModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.WfShSubtotal;
import cn.trasen.device.supervision.dao.ProcureApplyMapper;
import cn.trasen.device.supervision.model.ApproveData;
import cn.trasen.device.supervision.model.ProcureApply;
import cn.trasen.device.supervision.service.ProcureApplyService;
import cn.trasen.homs.form.service.FormApiService;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ProcureApplyServiceImpl implements ProcureApplyService {

    @Autowired
    private ProcureApplyMapper procureApplyMapper;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Autowired
    private WorkflowTaskService01 workflowTaskService01;

    @Autowired
    private DeviceWfInstanceSnapshotService wfInstanceSnapshotService;


    @Autowired
    private FormApiService formApiService;

    @Override
    public List<Map<String, Object>> getWfDefinitionInfo() {
        return procureApplyMapper.getWfDefinitionInfo();
    }

    @Override
    public Map<String, Object> getMyApplyHeadWfDefinitionInfo() {

        Map<String, Object> result = new HashMap<>();
        //审批中
        Map<String, Object> approval = new HashMap<>();
        List<Map<String, Object>> approvalDefinitionInfo = procureApplyMapper.selectMyDefinitionInfoNumbers(UserInfoHolder.getCurrentUserCode(), "approval");

        long approvalNumbers = 0;
        for (Map<String, Object> map : approvalDefinitionInfo) {
            approvalNumbers += (long) map.get("numbers");
        }
        approval.put("numbers", approvalNumbers);
        approval.put("myDefinitionInfo", approvalDefinitionInfo);
        result.put("approval", approval);

        //已退回
        Map<String, Object> back = new HashMap<>();
        List<Map<String, Object>> backDefinitionInfo = procureApplyMapper.selectMyDefinitionInfoNumbers(UserInfoHolder.getCurrentUserCode(), "back");

        long backNumbers = 0;
        for (Map<String, Object> map : backDefinitionInfo) {
            backNumbers += (long) map.get("numbers");
        }
        back.put("numbers", backNumbers);
        back.put("myDefinitionInfo", backDefinitionInfo);
        result.put("back", back);

        //已完结
        Map<String, Object> complete = new HashMap<>();
        List<Map<String, Object>> completeDefinitionInfo = procureApplyMapper.selectMyDefinitionInfoNumbers(UserInfoHolder.getCurrentUserCode(), "complete");

        long completeNumbers = 0;
        for (Map<String, Object> map : completeDefinitionInfo) {
            completeNumbers += (long) map.get("numbers");
        }
        complete.put("numbers", completeNumbers);
        complete.put("myDefinitionInfo", completeDefinitionInfo);
        result.put("complete", complete);


        //草稿箱
        Map<String, Object> draft = new HashMap<>();
        List<Map<String, Object>> draftDefinitionInfo = procureApplyMapper.selectMyDefinitionInfoNumbers(UserInfoHolder.getCurrentUserCode(), "draft");

        long draftNumbers = 0;
        for (Map<String, Object> map : draftDefinitionInfo) {
            draftNumbers += (long) map.get("numbers");
        }
        draft.put("numbers", draftNumbers);
        draft.put("myDefinitionInfo", draftDefinitionInfo);
        result.put("draft", draft);


        return result;
    }

    @Override
    public List<Map<String, Object>> getApplyLeftWfDefinitionInfo(ProcureApply procureApply) {

        if (StringUtils.isEmpty(procureApply.getApplyYear())) {

        }

        procureApply.setUserCode(UserInfoHolder.getCurrentUserCode());
        List<Map<String, Object>> result = procureApplyMapper.getApplyLeftWfDefinitionInfo(procureApply);

        for (Map<String, Object> map : result) {
            //医疗专业设备采购
            if ("06985B07055046639923C386071BA66E".equals(procureApply.getWfDefinitionId())) {
                Map<String, String> params = new HashMap<>();
                params.put("childBusiness", (String) map.get("childBusiness"));
                params.put("tableName", "zt_ylzysbcgjhsqb");
                params.put("filedname", "zje");
                params.put("workflowStatus", procureApply.getWorkflowStatus());
                long totalPrice = procureApplyMapper.selectTotalPrice(params);
                map.put("totalPrice", totalPrice);
            }
            //一般设备采购
            if ("A37FCFB6631B405EA3A790E32FE88655".equals(procureApply.getWfDefinitionId())) {
                Map<String, String> params = new HashMap<>();
                params.put("childBusiness", (String) map.get("childBusiness"));
                params.put("tableName", "zt_ybsbcgjhsqb");
                params.put("filedname", "zje");
                params.put("workflowStatus", procureApply.getWorkflowStatus());
                long totalPrice = procureApplyMapper.selectTotalPrice(params);
                map.put("totalPrice", totalPrice);
            }
            //工程项目采购
            if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(procureApply.getWfDefinitionId())) {
                Map<String, String> params = new HashMap<>();
                params.put("childBusiness", (String) map.get("childBusiness"));
                params.put("tableName", "zt_gcxmcgjhsqb");
                params.put("filedname", "zje");
                params.put("workflowStatus", procureApply.getWorkflowStatus());
                long totalPrice = procureApplyMapper.selectTotalPrice(params);
                map.put("totalPrice", totalPrice);
            }
            //服务项目采购
            if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(procureApply.getWfDefinitionId())) {
                Map<String, String> params = new HashMap<>();
                params.put("childBusiness", (String) map.get("childBusiness"));
                params.put("tableName", "zt_fwxmcgjhsbb");
                params.put("filedname", "zje");
                params.put("workflowStatus", procureApply.getWorkflowStatus());
                long totalPrice = procureApplyMapper.selectTotalPrice(params);
                map.put("totalPrice", totalPrice);
            }
        }
        return result;
    }

    @Override
    public List<JdGridTableEntity> getMyApplyTableHeadCols(ProcureApply record) {

        List<JdGridTableEntity> result = Lists.newArrayList();

        List<Map<String, Object>> tableHeadCols = new ArrayList<>();
        //医疗专业设备采购
        if ("06985B07055046639923C386071BA66E".equals(record.getWfDefinitionId())) {
            tableHeadCols = procureApplyMapper.selectTableHeadCols("B1FB9782BD374D309E9958BDF5365F6F");
        }
        //一般设备采购
        if ("A37FCFB6631B405EA3A790E32FE88655".equals(record.getWfDefinitionId())) {
            tableHeadCols = procureApplyMapper.selectTableHeadCols("38F654705E464DD0BAF8DE1C30DEF013");
        }
        //工程项目采购
        if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(record.getWfDefinitionId())) {
            tableHeadCols = procureApplyMapper.selectTableHeadCols("2BFF0FEA7D0A46C6AAFF7D04B38F35CA");
        }
        //服务项目采购
        if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(record.getWfDefinitionId())) {
            tableHeadCols = procureApplyMapper.selectTableHeadCols("E96004DE641142C8B843BAF35361CC75");
        }

        for (Map<String, Object> map : tableHeadCols) {
            JdGridTableEntity entity = new JdGridTableEntity();
            entity.setLabel((String) map.get("remark"));
            entity.setName((String) map.get("fieldName"));
            entity.setSortable(false);
            entity.setEditable(false);
            entity.setAlign("center");
            result.add(entity);
        }

        JdGridTableEntity entityCreateDate = new JdGridTableEntity();
        entityCreateDate.setLabel("发起时间");
        entityCreateDate.setName("create_date");
        entityCreateDate.setWidth(100);
        entityCreateDate.setHidden(false);
        entityCreateDate.setSortable(false);
        entityCreateDate.setEditable(false);
        entityCreateDate.setAlign("center");
        result.add(entityCreateDate);

        JdGridTableEntity entitySTATUS = new JdGridTableEntity();
        entitySTATUS.setLabel("流程状态");
        entitySTATUS.setName("STATUS");
        entitySTATUS.setWidth(100);
        entitySTATUS.setHidden(false);
        entitySTATUS.setSortable(false);
        entitySTATUS.setEditable(false);
        entitySTATUS.setAlign("center");
        result.add(entitySTATUS);

        JdGridTableEntity entityCURRENTSTEPNAME = new JdGridTableEntity();
        entityCURRENTSTEPNAME.setLabel("当前节点");
        entityCURRENTSTEPNAME.setName("CURRENT_STEP_NAME");
        entityCURRENTSTEPNAME.setWidth(100);
        entityCURRENTSTEPNAME.setHidden(false);
        entityCURRENTSTEPNAME.setSortable(false);
        entityCURRENTSTEPNAME.setEditable(false);
        entityCURRENTSTEPNAME.setAlign("center");
        result.add(entityCURRENTSTEPNAME);

        JdGridTableEntity entityAssigneeNames = new JdGridTableEntity();
        entityAssigneeNames.setLabel("节点未审批人员");
        entityAssigneeNames.setName("assigneeNames");
        entityAssigneeNames.setWidth(100);
        entityAssigneeNames.setHidden(false);
        entityAssigneeNames.setSortable(false);
        entityAssigneeNames.setEditable(false);
        entityAssigneeNames.setAlign("center");
        result.add(entityAssigneeNames);

        JdGridTableEntity task_id = new JdGridTableEntity();
        task_id.setLabel("task_id");
        task_id.setName("task_id");
        task_id.setWidth(100);
        task_id.setHidden(true);
        task_id.setSortable(false);
        task_id.setEditable(false);
        task_id.setAlign("center");
        result.add(task_id);

        JdGridTableEntity CURRENT_STEP_NO = new JdGridTableEntity();
        CURRENT_STEP_NO.setLabel("CURRENT_STEP_NO");
        CURRENT_STEP_NO.setName("CURRENT_STEP_NO");
        CURRENT_STEP_NO.setWidth(100);
        CURRENT_STEP_NO.setHidden(true);
        CURRENT_STEP_NO.setSortable(false);
        CURRENT_STEP_NO.setEditable(false);
        CURRENT_STEP_NO.setAlign("center");
        result.add(CURRENT_STEP_NO);

        JdGridTableEntity WORKFLOW_NO = new JdGridTableEntity();
        WORKFLOW_NO.setLabel("WORKFLOW_NO");
        WORKFLOW_NO.setName("WORKFLOW_NO");
        WORKFLOW_NO.setWidth(100);
        WORKFLOW_NO.setHidden(true);
        WORKFLOW_NO.setSortable(false);
        WORKFLOW_NO.setEditable(false);
        WORKFLOW_NO.setAlign("center");
        result.add(WORKFLOW_NO);

        JdGridTableEntity WF_INSTANCE_ID = new JdGridTableEntity();
        WF_INSTANCE_ID.setLabel("WF_INSTANCE_ID");
        WF_INSTANCE_ID.setName("WF_INSTANCE_ID");
        WF_INSTANCE_ID.setWidth(100);
        WF_INSTANCE_ID.setHidden(true);
        WF_INSTANCE_ID.setSortable(false);
        WF_INSTANCE_ID.setEditable(false);
        WF_INSTANCE_ID.setAlign("center");
        result.add(WF_INSTANCE_ID);

        JdGridTableEntity WORKFLOW_NUMBER = new JdGridTableEntity();
        WORKFLOW_NUMBER.setLabel("WORKFLOW_NUMBER");
        WORKFLOW_NUMBER.setName("WORKFLOW_NUMBER");
        WORKFLOW_NUMBER.setWidth(100);
        WORKFLOW_NUMBER.setHidden(true);
        WORKFLOW_NUMBER.setSortable(false);
        WORKFLOW_NUMBER.setEditable(false);
        WORKFLOW_NUMBER.setAlign("center");
        result.add(WORKFLOW_NUMBER);

        JdGridTableEntity is_hide_content = new JdGridTableEntity();
        is_hide_content.setLabel("is_hide_content");
        is_hide_content.setName("is_hide_content");
        is_hide_content.setWidth(100);
        is_hide_content.setHidden(true);
        is_hide_content.setSortable(false);
        is_hide_content.setEditable(false);
        is_hide_content.setAlign("center");
        result.add(is_hide_content);

        return result;
    }

    @Override
    public List<Map<String, Object>> getMyApplyWfDefinitionInfo(Page page, ProcureApply record) {

        setQueryValue(record);

        record.setUserCode(UserInfoHolder.getCurrentUserCode());

        if (StringUtils.isBlank(record.getSidx())) {
            record.setSidx("t1.create_date");
            record.setSord("desc");
        }

        if (StringUtils.isNotBlank(record.getDeviceName())) {
            String output = record.getDeviceName().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setDeviceName(output.substring(1));
        }

        List<Map<String, Object>> result = procureApplyMapper.getMyApplyWfDefinitionInfo(page, record);

        getFilesInfo(result, record);

        return result;
    }

    @Override
    public Map<String, Object> getApproveHeadWfDefinitionInfo() {

        Map<String, Object> result = new HashMap<>();
        //待我审批
        Map<String, Object> approval = new HashMap<>();
        List<Map<String, Object>> myApprovalInfo = procureApplyMapper.selectMyApprovalInfoNumbers(UserInfoHolder.getCurrentUserCode());

        long approvalNumbers = 0;
        for (Map<String, Object> map : myApprovalInfo) {
            approvalNumbers += (long) map.get("numbers");
        }
        approval.put("numbers", approvalNumbers);
        approval.put("approvalInfo", myApprovalInfo);
        result.put("inApproval", approval);

        //我已审批
        Map<String, Object> approved = new HashMap<>();
        List<Map<String, Object>> approvedInfo = procureApplyMapper.selectMyApprovedInfoNumbers(UserInfoHolder.getCurrentUserCode());

        long approvedNumbers = 0;
        for (Map<String, Object> map : approvedInfo) {
            approvedNumbers += (long) map.get("numbers");
        }
        approved.put("numbers", approvedNumbers);
        approved.put("approvalInfo", approvedInfo);
        result.put("approved", approved);

        //抄送给我
        Map<String, Object> copy = new HashMap<>();
        List<Map<String, Object>> copyInfo = procureApplyMapper.selectMyCopyInfoNumbers(UserInfoHolder.getCurrentUserCode());

        long copyNumbers = 0;
        for (Map<String, Object> map : copyInfo) {
            copyNumbers += (long) map.get("numbers");
        }
        copy.put("numbers", copyNumbers);
        copy.put("approvalInfo", copyInfo);
        result.put("copy", copy);


        return result;
    }

    @Override
    public List<Map<String, Object>> getApproveInfoList(Page page, ProcureApply record) {

        setQueryValue(record);

        record.setAssigneeNo(UserInfoHolder.getCurrentUserCode());

        if (StringUtils.isBlank(record.getSidx())) {
            record.setSidx("t5.custom_code");
            record.setSord("asc");
        }

        if (StringUtils.isNotBlank(record.getDeviceName())) {
            String output = record.getDeviceName().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setDeviceName(output.substring(1));
        }

        List<Map<String, Object>> result = procureApplyMapper.getMyApplyWfDefinitionInfo(page, record);

        getFilesInfo(result, record);

        return result;
    }

    @Override
    public List<Map<String, Object>> getConsultLeftWfDefinitionInfo() {

        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员

        List<String> deptCodes = new ArrayList<>();
        if (!right) {
            deptCodes.add(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }

        return procureApplyMapper.getConsultLeftWfDefinitionInfo(deptCodes);
    }

    @Override
    public List<Map<String, Object>> getConsultWfDefinitionList(Page page, ProcureApply record) {

        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员

        List<String> deptCodes = new ArrayList<>();
        if (!right) {
            deptCodes.add(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }

        record.setDeptCodes(deptCodes);

        setQueryValue(record);

        if (StringUtils.isBlank(record.getSidx())) {
            record.setSidx(" t1.CREATE_DATE DESC, t5.`seq_no`");
            record.setSord("ASC");
        }

        if (StringUtils.isNotBlank(record.getDeviceName())) {
            String output = record.getDeviceName().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
            record.setDeviceName(output.substring(1));
        }

        List<Map<String, Object>> uniqueList = procureApplyMapper.getConsultWfDefinitionList(page, record);

        //去重
        /*List<Map<String, Object>> uniqueList = uniqueList.stream()
                .collect(Collectors.toMap(
                        m -> m.get("WF_INSTANCE_ID"),
                        m -> m,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new  // 使用LinkedHashMap来保留原始顺序
                ))
                .values()
                .stream()
                .collect(Collectors.toList());*/

        getFilesInfo(uniqueList, record);

        return uniqueList;
    }

    private void setQueryValue(ProcureApply record) {
        //医疗专业设备采购
        if ("06985B07055046639923C386071BA66E".equals(record.getWfDefinitionId())) {
            record.setTableName("zt_ylzysbcgjhsqb");
            record.setTable("zdy_ylzysbcgjhsqb20240201113802");
            record.setField1("sqsw0"); //申报单位
            record.setField2("sbsj8"); //申报时间
            record.setFj1("sqbg9");
            record.setFj2("jtyjhyjljt10");
            record.setFj3("zdktljlzdhtljl11");
        }
        //一般设备采购
        if ("A37FCFB6631B405EA3A790E32FE88655".equals(record.getWfDefinitionId())) {
            record.setTableName("zt_ybsbcgjhsqb");
            record.setTable("zdy_ylybsbcgjhsbb20240201151026");
            record.setField1("sqsw0");
            record.setField2("sbsj7");
            record.setFj1("sqbg8");
            record.setFj2("jtyjhyjljt9");
            record.setFj3("zdktljlzdhtljl10");
        }
        //工程项目采购
        if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(record.getWfDefinitionId())) {
            record.setTableName("zt_gcxmcgjhsqb");
            record.setTable("zdy_jtgcxmcgjhsbb20240201153016");
            record.setField1("sbsw0");
            record.setField2("sbsj1");
            record.setFj1("sqbg7");
            record.setFj2("jtyjhyjljt8");
            record.setFj3("zdktljlzdhtljl9");
        }
        //服务项目采购
        if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(record.getWfDefinitionId())) {
            record.setTableName("zt_fwxmcgjhsbb");
            record.setTable("zdy_fwxmcgjhsbb20240201155028");
            record.setField1("sbsw0");
            record.setField2("sbsj1");
            record.setFj1("sqbg7");
            record.setFj2("jtyjhyjljt8");
            record.setFj3("zdktljlzdhtljl9");
        }
    }

    private void getFilesInfo(List<Map<String, Object>> result, ProcureApply record) {
        for (Map<String, Object> map : result) {
            String xyfxb = (String) map.get("xyfxb");
            String kxxlzbg = (String) map.get("kxxlzbg");
            String sqbg = (String) map.get("sqbg2");
            String jtyjhyjl = (String) map.get("jtyjhyjl2");
            String zdhtljl = (String) map.get("zdhtljl2");

            if (StringUtils.isNotBlank(xyfxb)) {
                List<Map<String, String>> files = procureApplyMapper.selectFilesByBusinessId(xyfxb);
                if (CollectionUtils.isNotEmpty(files)) {
                    map.put("xyfxb", JSON.toJSONString(files));
                } else {
                    map.put("xyfxb", "");
                }
            }
            if (StringUtils.isNotBlank(kxxlzbg)) {
                List<Map<String, String>> files = procureApplyMapper.selectFilesByBusinessId(kxxlzbg);
                if (CollectionUtils.isNotEmpty(files)) {
                    map.put("kxxlzbg", JSON.toJSONString(files));
                } else {
                    map.put("kxxlzbg", "");
                }
            }
            if (StringUtils.isNotBlank(sqbg)) {
                String[] sqbgs = sqbg.split(",");
                List<Map<String, String>> files = procureApplyMapper.selectFilesByFileIds(sqbgs);
                if (CollectionUtils.isNotEmpty(files)) {
                    map.put("sqbg", JSON.toJSONString(files));
                } else {
                    map.put("sqbg", "");
                }
            }
            if (StringUtils.isNotBlank(jtyjhyjl)) {
                String[] jtyjhyjls = jtyjhyjl.split(",");
                List<Map<String, String>> files = procureApplyMapper.selectFilesByFileIds(jtyjhyjls);
                if (CollectionUtils.isNotEmpty(files)) {
                    map.put("jtyjhyjl", JSON.toJSONString(files));
                } else {
                    map.put("jtyjhyjl", "");
                }
            }
            if (StringUtils.isNotBlank(zdhtljl)) {
                String[] zdhtljls = zdhtljl.split(",");
                List<Map<String, String>> files = procureApplyMapper.selectFilesByFileIds(zdhtljls);
                if (CollectionUtils.isNotEmpty(files)) {
                    map.put("zdhtljl", JSON.toJSONString(files));
                } else {
                    map.put("zdhtljl", "");
                }
            }
        }
    }

    @Override
    @Transactional(readOnly = false)
    public void batchTravelReject(ProcureApply record) {

        String taskIdStr = record.getTaskId();

        if (StringUtils.isNotBlank(taskIdStr)) {
            String[] taskIds = taskIdStr.split(",");

            //Map<String,String> startStep = procureApplyMapper.selectStartStepInfo(record.getWfDefinitionId());

            for (String taskId : taskIds) {

                Map<String, Object> map = new HashMap<>();
                map.put("wfStepId", record.getWfStepId());
                map.put("wfStepName", record.getWfStepName());
                map.put("handleMarkedWords", record.getHandleMarkedWords());

                workflowTaskService01.doRejectTask(taskId, map);
            }
        }
    }

    @Override
    @Transactional(readOnly = false)
    public void batchExamination(ProcureApply record) {

        List<ApproveData> approveDataList = record.getApproveData();

        String taskIdStr = record.getTaskId();

        if (CollectionUtils.isNotEmpty(approveDataList)) {

            String[] taskIds = taskIdStr.split(",");

            //医疗专业设备采购
            if ("06985B07055046639923C386071BA66E".equals(record.getWfDefinitionId())) {
                int index = 0;
                for (ApproveData approveData : approveDataList) {
                    approveData.setTableName("zt_ylzysbcgjhsqb");

                    if (approveData.getName() != null) {
                        approveData.setXmmc(approveData.getName());
                    }
                    if (approveData.getSpec() != null) {
                        approveData.setGgxh(approveData.getSpec());
                    }

                    procureApplyMapper.updateApproveData(approveData);
                    wfInstanceSnapshotService.saveSnapShot(taskIds[index], approveData);
                    index++;
                }
            }

            //一般设备采购
            if ("A37FCFB6631B405EA3A790E32FE88655".equals(record.getWfDefinitionId())) {
                int index = 0;
                for (ApproveData approveData : approveDataList) {
                    approveData.setTableName("zt_ybsbcgjhsqb");

                    if (approveData.getName() != null) {
                        approveData.setXmmc(approveData.getName());
                    }
                    if (approveData.getSpec() != null) {
                        approveData.setGgxh(approveData.getSpec());
                    }

                    procureApplyMapper.updateApproveData(approveData);
                    wfInstanceSnapshotService.saveSnapShot(taskIds[index], approveData);
                    index++;
                }
            }

            //工程项目采购
            if ("ADA7FFF3AFBC46258C6A3D2E6A9D7DE0".equals(record.getWfDefinitionId())) {
                int index = 0;
                for (ApproveData approveData : approveDataList) {
                    approveData.setTableName("zt_gcxmcgjhsqb");

                    if (approveData.getName() != null) {
                        approveData.setXmmc(approveData.getName());
                    }

                    if (approveData.getSpec() != null) {
                        approveData.setJzmj(approveData.getSpec());
                    }

                    procureApplyMapper.updateApproveData(approveData);
                    wfInstanceSnapshotService.saveSnapShot(taskIds[index], approveData);
                    index++;
                }
            }

            //服务项目采购
            if ("1866B74A6BC3400F990AB9E3C2DAA1A6".equals(record.getWfDefinitionId())) {
                int index = 0;
                for (ApproveData approveData : approveDataList) {
                    approveData.setTableName("zt_fwxmcgjhsbb");

                    if (approveData.getName() != null) {
                        approveData.setXmmc(approveData.getName());
                    }
                    if (approveData.getSpec() != null) {
                        // 按照 | 分割
                        String[] spec = approveData.getSpec().split(",");
                        if (spec.length == 2) {
                            approveData.setFwkssj(spec[0]);
                            approveData.setFwjssj(spec[1]);
                        }
                    }

                    procureApplyMapper.updateApproveData(approveData);
                    wfInstanceSnapshotService.saveSnapShot(taskIds[index], approveData);
                    index++;
                }
            }
        }


        if (StringUtils.isNotBlank(taskIdStr)) {

            String[] taskIds = taskIdStr.split(",");

            String approvalFiled = procureApplyMapper.selectCommentField(record.getWfDefinitionId(), record.getCurrentStepId());

            for (String taskId : taskIds) {

                Map<String, Object> map = new HashMap<>();
                map.put("approvalFiled", approvalFiled);
                map.put("handleAllottedTime", record.getHandleAllottedTime());
                map.put("handleMarkedWords", record.getHandleMarkedWords());
                map.put("names", record.getNames());
                map.put("users", record.getUsers());
                map.put("L_TaskRemark", record.getRemark());
                map.put("taskId", taskId);
                map.put("urgencyLevel", record.getUrgencyLevel());
                map.put("wfDefinitionId", record.getWfDefinitionId());
                map.put("wfStepId", record.getWfStepId());

                workflowTaskService.completeTask(taskId, map);
            }

        }
    }

    @Transactional(readOnly = false)
    @Override
    public void batchDelete(WfInstanceBatchDeleteReq record) {

        List<WfInstanceDeleteReq> wfInstanceDeleteReqList = record.getWfInstanceDeleteReqList();
        if (wfInstanceDeleteReqList == null || wfInstanceDeleteReqList.size() == 0) {
            throw new BusinessException("至少选择一条进行删除");
        }

        FormDataModel formDataModel = new FormDataModel();

        for (WfInstanceDeleteReq wfInstanceDeleteReq : wfInstanceDeleteReqList) {

            try {
                formDataModel.setBusinessId(wfInstanceDeleteReq.getBusinessId());
                formDataModel.setWfDefinitionId(wfInstanceDeleteReq.getWfDefinitionId());
                formApiService.deleteById(formDataModel);
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }

        }
    }

    @Override
    public List<Map<String, Object>> getStepInfoByWfDefinitionId(String wfDefinitionId) {
        return procureApplyMapper.getStepInfoByWfDefinitionId(wfDefinitionId);
    }

    @Override
    public void appendSubtotalRow(Workbook workbook, Sheet sheet, WfShSubtotal wfShSubtotal) {

        sheet.shiftRows(wfShSubtotal.getRowIndex() + 1, sheet.getLastRowNum(), 1, true, true);
        Row newRow = sheet.createRow(wfShSubtotal.getRowIndex() + 1);
        newRow.setHeightInPoints(38);
        CellStyle borderstyle = workbook.createCellStyle();

        // 设置边框样式为粗线
        borderstyle.setBorderTop(BorderStyle.THIN);
        borderstyle.setBorderBottom(BorderStyle.THIN);
        borderstyle.setBorderLeft(BorderStyle.THIN);
        borderstyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色为黑色
        borderstyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        borderstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        borderstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        borderstyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        borderstyle.setWrapText(true);
        borderstyle.setAlignment(HorizontalAlignment.CENTER);
        borderstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 14);
        borderstyle.setFont(font);

        // 将样式应用到指定范围的单元格（A1 到 K1）
        for (int i = 0; i < 11; i++) {
            Cell tempCell = newRow.createCell(i);
            if (i == 1) {
                tempCell.setCellValue(wfShSubtotal.getOrgName());
            }

            if (i == 2) {
                tempCell.setCellValue("小     计");
            }

            if (i == 6) {
                tempCell.setCellValue(wfShSubtotal.getSubtotal());
            }

            tempCell.setCellStyle(borderstyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(wfShSubtotal.getRowIndex() + 1, wfShSubtotal.getRowIndex() + 1, 2, 5));
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 采购申请数量
     * @date 2024/4/22 17:11
     */
    @Override
    public Integer cgsqsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.cgsqsl(indexBlockDBCYReq);
    }

    @Override
    public Integer cgsqbhsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.cgsqbhsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 上会审批数量
     * @date 2024/4/22 17:11
     */
    @Override
    public Integer shspsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.shspsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 局党委会审批数量
     * @date 2024/4/22 17:12
     */
    @Override
    public Integer dwhspsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.dwhspsl(indexBlockDBCYReq);
    }


    /**
     * @param indexBlockDBCYReq:
     * @return BigDecimal
     * <AUTHOR>
     * @description 采购申请金额
     * @date 2024/4/22 17:17
     */
    @Override
    public BigDecimal cgsqje(IndexBlockDBCYReq indexBlockDBCYReq) {

        return procureApplyMapper.cgsqje(indexBlockDBCYReq);
    }


    /**
     * @param indexBlockDBCYReq:
     * @return BigDecimal
     * <AUTHOR>
     * @description 上会审批金额
     * @date 2024/4/22 17:18
     */
    @Override
    public BigDecimal shspje(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.shspje(indexBlockDBCYReq);
    }

    @Override
    public Integer dshspsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.dshspsl(indexBlockDBCYReq);
    }

    @Override
    public BigDecimal dshspje(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.dshspje(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return BigDecimal
     * <AUTHOR>
     * @description 局党委会审批金额
     * @date 2024/4/23 10:00
     */
    @Override
    public BigDecimal dwhspje(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.dwhspje(indexBlockDBCYReq);
    }

    @Override
    public Integer ddwhspsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.ddwhspsl(indexBlockDBCYReq);
    }

    @Override
    public BigDecimal ddwhspje(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.ddwhspje(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 专用设备流程数量
     * @date 2024/4/26 16:27
     */
    @Override
    public Integer zysblcsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.zysblcsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 一般设备流程数量
     * @date 2024/4/26 16:27
     */
    @Override
    public Integer ybsblcsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.ybsblcsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 工程项目流程数量
     * @date 2024/4/26 16:27
     */
    @Override
    public Integer gcxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.gcxmlcsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 服务项目流程数量
     * @date 2024/4/26 16:27
     */
    @Override
    public Integer fwxmlcsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.fwxmlcsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 专用设备流程已审核数量
     * @date 2024/4/26 16:28
     */
    @Override
    public Integer zysblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.zysblcyshsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 一般设备流程已审核数量
     * @date 2024/4/26 16:28
     */
    @Override
    public Integer ybsblcyshsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.ybsblcyshsl(indexBlockDBCYReq);
    }

    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 工程项目流程已审核数量
     * @date 2024/4/26 16:28
     */
    @Override
    public Integer gcxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.gcxmlcyshsl(indexBlockDBCYReq);
    }


    /**
     * @param indexBlockDBCYReq:
     * @return Integer
     * <AUTHOR>
     * @description 服务项目流程已审核数量
     * @date 2024/4/26 16:28
     */
    @Override
    public Integer fwxmlcyshsl(IndexBlockDBCYReq indexBlockDBCYReq) {
        return procureApplyMapper.fwxmlcyshsl(indexBlockDBCYReq);
    }


    /**
     * @param indexBlockYSCGReq:
     * @return BigDecimal
     * <AUTHOR>
     * @description 采购审批总数
     * @date 2024/4/23 11:20
     */
    @Override
    public Integer cgspzs(IndexBlockYSCGReq indexBlockYSCGReq) {

        return procureApplyMapper.cgspzs(indexBlockYSCGReq);
    }

    /**
     * @param indexBlockYSCGReq:
     * @return BigDecimal
     * <AUTHOR>
     * @description 采购项目总数
     * @date 2024/4/23 11:26
     */

    @Override
    public Integer cgxmzs(IndexBlockYSCGReq indexBlockYSCGReq) {
        return procureApplyMapper.cgxmzs(indexBlockYSCGReq);
    }

    /**
     * @param indexBlockDBSXReq:
     * @return Integer
     * <AUTHOR>
     * @description 待审批采购数量
     * @date 2024/5/29 15:32
     */
    @Override
    public Integer dspcgsl(IndexBlockDBSXReq indexBlockDBSXReq) {
        return procureApplyMapper.dspcgsl(indexBlockDBSXReq);
    }


    /**
     * @param indexBlockDBSXReq:
     * @return Integer
     * <AUTHOR>
     * @description 待审批局党委会数量
     * @date 2024/5/29 15:33
     */
    @Override
    public Integer dspdwhsl(IndexBlockDBSXReq indexBlockDBSXReq) {
        return procureApplyMapper.dspdwhsl(indexBlockDBSXReq);
    }


}
