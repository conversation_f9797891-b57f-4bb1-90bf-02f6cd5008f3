package cn.trasen.device.supervision.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.trasen.device.supervision.bean.payslip.*;
import cn.trasen.device.supervision.exception.DevicePayslipException;
import cn.trasen.device.supervision.model.PayslipLog;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.model.workflow.DeviceWfInstanceInfo;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.service.PayslipLogService;
import cn.trasen.device.supervision.util.ListUtils;
import cn.trasen.device.supervision.util.PoiHelper;
import cn.trasen.homs.bpm.model.WfTask;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.feign.workflow.service.WorkflowTaskService01;
import cn.trasen.homs.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.PayslipUploadMapper;
import cn.trasen.device.supervision.model.PayslipUpload;
import cn.trasen.device.supervision.service.PayslipUploadService;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ToaDevicePayslipUploadServiceImpl
 * @Description TODO
 * @date 2024年6月28日 下午2:21:33
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PayslipUploadServiceImpl implements PayslipUploadService {

    @Autowired
    private PayslipUploadMapper mapper;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private PayslipLogService payslipLogService;

    @Autowired
    private WorkflowInstanceService workflowInstanceService;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Autowired
    private WorkflowTaskService01 workflowTaskService01;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PayslipUpload record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PayslipUpload record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PayslipUpload record = new PayslipUpload();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PayslipUpload selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<PayslipUpload> getDataSetList(Page page, PayslipUpload record) {
        Example example = new Example(PayslipUpload.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<PayslipUpload> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void _import_(String type, String month, MultipartFile file) throws Exception {


        Set<String> typeSet = new HashSet<>(Arrays.asList("0", "1"));

        if (!typeSet.contains(type)) {
            throw new BusinessException("类型参数不正常");
        }

        // 获取医院机构ID
        String deptId = deviceHelperService.justDeptCode();
        String orgNameorgName = UserInfoHolder.getCurrentUserInfo().getOrgName();
        Example example = new Example(PayslipUpload.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE).andEqualTo("deptId", deptId).andEqualTo("type", type).andEqualTo("month", month);

        List<PayslipUpload> exsitList = mapper.selectByExample(example);
        PayslipUpload row;

        if (exsitList.size() > 0) {
            row = exsitList.get(0);
        } else {
            row = new PayslipUpload();
            row.setDeptId(deptId);
            row.setMonth(month);
            row.setType(type);
            save(row);
        }

        // 清空导入记录 不管有没有
        payslipLogService.emptyByPayslipUploadId(row.getId());
        // 读取数据
        List<List<Object>> excelList = PoiHelper.readExcel2ListFromStream(file.getInputStream());
        if (excelList.size() <= 4) {
            throw new BusinessException("导入文件数据为空");
        }


        // TODO
        // 获取机构所有人员信息
        EmployeeReq employeeReq = new EmployeeReq();
        employeeReq.setDeptId(deptId);

        if ("0".equals(type)) {
            employeeReq.setEmployeeCategory("'1','25'");
        } else {
            employeeReq.setEmployeeCategory("'2','6'");
        }

        List<EmployeeResp> employeeResps = deviceHelperService.getEmployeeByDeptId(employeeReq);

        if (employeeResps == null || employeeResps.size() <= 0) {
            throw new BusinessException("本医院未完善人员信息录入，无法导入工资条");
        }

        // 抽一个 identityNumber 的 hashSet 出来
        Set<String> identityNumbersSet = employeeResps.stream().map(EmployeeResp::getIdentityNumber)  // Map each EmployeeResp to its identity number
                .collect(Collectors.toSet());

        employeeReq.setEmployeeCategory("");
        // 每次插入1000 行
        List<String> error = new ArrayList<>();
        for (int i = 4; i < excelList.size(); i++) {
            List<Object> rowList = excelList.get(i);
            String identityNumber = (String) ListUtils.getElementOrEmptyString(rowList, 4);
            if (!identityNumbersSet.contains(identityNumber)) {
                error.add((String) ListUtils.getElementOrEmptyString(rowList, 1));
            }
        }

        if (error != null && error.size() > 0) {
            throw new DevicePayslipException(String.join(",", error));
        }

        List<PayslipLog> payslipLogList = new ArrayList<>();
        // 每次插入1000 行
        for (int i = 4; i < excelList.size(); i++) {
            List<Object> rowList = excelList.get(i);

            // Process your rowList and create PayslipLog objects
            PayslipLog payslipLog = payslipLogService.excel2PayslipLog(rowList, type);

            payslipLog.setDwmc(orgNameorgName);
            payslipLog.setPayslipUploadId(row.getId());
            payslipLogList.add(payslipLog);

            // Check if the batch size is reached and insert into database or perform batch operation
            if (payslipLogList.size() >= 1000) {
                // Insert payslipLogList into database or process the batch
                payslipLogService.batchInsert(payslipLogList); // Example method to insert batch into database
                // Clear the list for next batch
                payslipLogList.clear();
            }
        }

        // 检查是否有剩余数据未插入
        if (!payslipLogList.isEmpty()) {
            payslipLogService.batchInsert(payslipLogList); // 插入剩余的数据
        }

        // 更新上报时间
        PayslipUpload record = new PayslipUpload();
        record.setId(row.getId());
        update(record);

    }

    @Transactional(readOnly = false)
    @Override
    public void upload(String id, DoCheckReq doCheckReq) {
        // 检查是否已经上报过了，防止重复上报
        DeviceWfInstanceInfo wfInstanceInfo = deviceHelperService.getWfInstanceInfoByBusinessId(id);

        if (wfInstanceInfo != null) {
            if ("重新提交".equals(wfInstanceInfo.getCurrentStepName())) {

                PayslipUpload payslipUpload = selectById(id);

                if (payslipUpload.getUpdateDate().compareTo(wfInstanceInfo.getUpdateDate()) < 0) {
                    throw new BusinessException("请更新工资条数据后再提交");
                }

                doCheckReq.setBusinessId(id);
                doCheckReq.setRemark("");
                doCheckReq.setHandleMarkedWords("");

                doCheck(doCheckReq);
                return;
            }

            throw new BusinessException("已经上报过了");
        }

        // 获取字典 找到指定的流程编号
        Map<String, String> dictMap = deviceHelperService.getDictMapByCode("PAYSLIP_WKNO");
        String wkNo = dictMap.get("PAYSLIP_WKNO");
        if (wkNo == null) {
            throw new BusinessException("未找到工资条流程编号字典配置");
        }

        Map<String, Object> workflowParams = doCheckReq2Map(doCheckReq);

        //L_BusinessId 流程关联数据ID users 选择用户code 多个用逗号隔开   names 选择用户名称  wfStepId 节点ID
        workflowParams.put("L_BusinessId", id);//流程关联数据ID

        workflowInstanceService.doStartProcessInstance(wkNo, workflowParams);
    }

    @Override
    public DataSet<PayslipUploadListResp> uploadList(PayslipUpload record, Page page) {
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        List<PayslipUploadListResp> records = mapper.uploadList(record, page);
        if (records != null || records.size() > 0) {
            Map<String, String> dictMap = deviceHelperService.getDictMapByCode("PAYSLIP_WKNO");
            String wfdefId = dictMap.get("PAYSLIP_WFDEF_ID");
            if (wfdefId == null) {
                throw new BusinessException("未找到工资条流程定义ID字典配置");
            }

            // 循环records
            for (PayslipUploadListResp payslipUploadListResp : records) {
                // 获取流程实例信息
                if (payslipUploadListResp.getWfDefinitionId() == null) {
                    payslipUploadListResp.setWfDefinitionId(wfdefId);
                }
            }

        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);

    }

    @Override
    public DataSet<PayslipUploadListResp> checkList(PayslipUpload record, Page page) {
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        List<PayslipUploadListResp> records = mapper.checkList(record, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void doCheck(DoCheckReq doCheckReq) {

        String taskId = doCheckReq.getTaskId();
        String status = doCheckReq.getStatus();
        String remark = doCheckReq.getRemark();
        // 获取流程实例信息
        WfTask wfTask = workflowTaskService.selectTaskById(taskId);

        if (wfTask == null) {
            throw new BusinessException("错误的任务ID参数，未找到任务");
        }


        String wfInstanceId = wfTask.getWfInstanceId();
        DeviceWfInstanceInfo wfInstanceInfo = deviceHelperService.getWfInstanceInfoById(wfInstanceId);

        if (1 != wfInstanceInfo.getStatus()) {
            throw new BusinessException("流程已经结束，无法审批");
        }

        // 获取流程关联数据
        PayslipUpload payslipUpload = selectById(wfInstanceInfo.getBusinessId());
        if (payslipUpload == null) {
            throw new BusinessException("未找到流程关联数据");
        }

        Map<String, Object> paramsMap = new HashMap<>();

        if ("1".equals(status)) {
            paramsMap = doCheckReq2Map(doCheckReq);
            workflowTaskService.completeTask(taskId, paramsMap);
        } else {
            // 获取起始的stepId
            String startStepId = deviceHelperService.getStartStepIdByWorkflowNo(wfInstanceInfo.getWorkflowNo());
            if (startStepId == null) {
                throw new BusinessException("未找到流程起始节点");
            }
            paramsMap.put("handleMarkedWords", remark);
            paramsMap.put("wfStepId", startStepId);
            paramsMap.put("wfStepName", "重新提交");

            workflowTaskService01.doRejectTask(taskId, paramsMap);
        }

    }

    @Override
    public PayslipStatusReportResp payslipStatusReport(PayslipUpload record) {
        PayslipStatusReportResp ret = mapper.payslipStatusReport(record);

        int orgSize = 0;
        // 查询当前机构数量
        List<Organization> orgList = deviceHelperService.getOrganizationListToFrontSelect();
        if (orgList != null && orgList.size() > 0) {
            orgSize = orgList.size();
        }
        // 做差
        int uploadeds = Integer.parseInt(ret.getUploadeds());
        ret.setUnUploads(String.valueOf(orgSize - uploadeds));
        return mapper.payslipStatusReport(record);
    }

    private Map doCheckReq2Map(DoCheckReq doCheckReq) {

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("handleAllottedTime", doCheckReq.getHandleAllottedTime());
        paramsMap.put("handleMarkedWords", doCheckReq.getHandleMarkedWords());
        paramsMap.put("names", doCheckReq.getNames());
        paramsMap.put("users", doCheckReq.getUsers());
        paramsMap.put("L_TaskRemark", doCheckReq.getRemark());
        paramsMap.put("taskId", doCheckReq.getTaskId());
        paramsMap.put("urgencyLevel", doCheckReq.getUrgencyLevel());
        paramsMap.put("wfDefinitionId", doCheckReq.getWfDefinitionId());
        paramsMap.put("wfStepId", doCheckReq.getWfStepId());

        if (doCheckReq.getWfStepName() != null) {
            paramsMap.put("wfStepName", doCheckReq.getWfStepName());//流程关联数据ID
        }

        if (doCheckReq.getBusinessId() != null) {
            paramsMap.put("L_BusinessId", doCheckReq.getBusinessId());//流程关联数据ID
        }
        return paramsMap;
    }
}
