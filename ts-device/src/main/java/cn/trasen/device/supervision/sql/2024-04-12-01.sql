/* 14:25:11 246 ts_base_oa 增加机构ID */ ALTER TABLE `toa_device_purchase_result`
    ADD `dept_id` VARCHAR(50) NULL  DEFAULT NULL  COMMENT '部门ID'  AFTER `wf_instance_id`;
/* 14:27:04 246 ts_base_oa 增加资金来源 */ ALTER TABLE `toa_device_purchase_result`
    ADD `sources_funds` VARCHAR(20) NULL  DEFAULT NULL  COMMENT '资金来源'  AFTER `dept_id`;

/* 17:56:21 246 ts_base_oa */ ALTER TABLE `toa_device_purchase_result`
    ADD `sso_org_code` VARCHAR(50) NULL  DEFAULT NULL  AFTER `update_date`;

/* 17:56:21 246 ts_base_oa */ ALTER TABLE `toa_device_purchase_result`
    ADD `sso_org_name` VARCHAR(50) NULL  DEFAULT NULL  AFTER `sso_org_code`;

/* 同步dept_id到采购结果表*/

UPDATE toa_device_purchase_result a
    JOIN toa_device_ps_instance_info b
ON a.wf_instance_id = b.wf_instance_id
    SET a.dept_id = b.`LAUNCH_DEPT_CODE`;

CREATE TABLE `toa_device_purchase_group`
(
    `id`                 varchar(36) COLLATE utf8mb4_general_ci NOT NULL               DEFAULT '',
    `purchase_result_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '结果ID',
    `dept_id`            varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '部门ID',
    `tender_parameter`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '招标参数',
    `tender_files`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '招标文件',
    `purchase_remark`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '采购备注',
    `create_date`        datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `create_user`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '创建人',
    `create_user_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '创建人名称',
    `update_user`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '更新人',
    `update_user_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '更新人名称',
    `update_date`        datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `sso_org_code`       varchar(50) COLLATE utf8mb4_general_ci                        DEFAULT NULL,
    `sso_org_name`       varchar(50) COLLATE utf8mb4_general_ci                        DEFAULT NULL,
    `is_deleted`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '删除标示',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='采购分组表';



CREATE TABLE `toa_device_purchase_log`
(
    `id`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `purchase_group_id`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '采购结果ID',
    `dept_id`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '部门ID',
    `name`                 varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '项目名称',
    `purchase_date`        date                                                                  DEFAULT NULL COMMENT '采购（合同）时间',
    `supplier`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '供货单位名称',
    `producer`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '生产厂家 ｜ 供应商 ｜服务商｜承建单位',
    `purchase_brand`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '采购品牌',
    `purchase_spec`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '采购型号',
    `purchase_way`         varchar(100) COLLATE utf8mb4_general_ci                               DEFAULT NULL COMMENT '采购方式',
    `build_scale`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '建设规模',
    `accept_date`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '竣工验收备案时间',
    `purchase_numbers`     varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '采购数量',
    `purchase_price`       varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '采购单价',
    `purchase_total_price` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '采购金额',
    `funds_sources`        varchar(20) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '资金来源',
    `service_start_at`     date                                                                  DEFAULT NULL COMMENT '服务开始时间',
    `service_end_at`       date                                                                  DEFAULT NULL COMMENT '服务结束时间',
    `purchase_remark`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
    `purchase_files`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '采购附件',
    `create_date`          datetime                                                              DEFAULT NULL COMMENT '创建时间',
    `create_user`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '创建人',
    `create_user_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '创建人名称',
    `update_user`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人',
    `update_user_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '更新人名称',
    `update_date`          datetime                                                              DEFAULT NULL COMMENT '更新时间',
    `sso_org_code`         varchar(50) COLLATE utf8mb4_general_ci                                DEFAULT NULL,
    `sso_org_name`         varchar(50) COLLATE utf8mb4_general_ci                                DEFAULT NULL,
    `is_deleted`           varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '删除标示',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='采购记录';


/* 18:07:11 246 ts_base_oa */ ALTER TABLE `toa_device_purchase_result` CHANGE `purchase_spec` `purchase_spec` VARCHAR (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购型号';
/* 18:23:12 246 ts_base_oa */ ALTER TABLE `toa_device_purchase_log` CHANGE `purchase_spec` `purchase_spec` VARCHAR (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购型号';
