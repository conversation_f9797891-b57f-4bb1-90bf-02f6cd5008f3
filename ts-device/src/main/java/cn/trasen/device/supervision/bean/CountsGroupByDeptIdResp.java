package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: CountsGroupByDeptIdResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/18 19:46
 * @version: 1.0
 */

@Data
public class CountsGroupByDeptIdResp {

    @ApiModelProperty(value = "医院ID")
    private String deptId;

    @ApiModelProperty(value = "设备数量")
    private Integer devices;

}
