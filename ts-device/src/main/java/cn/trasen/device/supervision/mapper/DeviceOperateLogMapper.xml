<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceOperateLogMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.DeviceOperateLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="year" jdbcType="DATE" property="year"/>
        <result column="month" jdbcType="SMALLINT" property="month"/>
        <result column="incomes" jdbcType="DECIMAL" property="incomes"/>
        <result column="checks" jdbcType="SMALLINT" property="checks"/>
        <result column="consumables_costs" jdbcType="DECIMAL" property="consumablesCosts"/>
        <result column="repair_costs" jdbcType="DECIMAL" property="repairCosts"/>
        <result column="maintenance_costs" jdbcType="DECIMAL" property="maintenanceCosts"/>
        <result column="utility_costs" jdbcType="DECIMAL" property="utilityCosts"/>
        <result column="labor_costs" jdbcType="DECIMAL" property="laborCosts"/>
        <result column="housing_costs" jdbcType="DECIMAL" property="housingCosts"/>
        <result column="personnel_costs" jdbcType="DECIMAL" property="personnelCosts"/>
        <result column="other_costs" jdbcType="DECIMAL" property="otherCosts"/>
        <result column="depreciations" jdbcType="DECIMAL" property="depreciations"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="expand_field" jdbcType="LONGVARCHAR" property="expandField"/>
    </resultMap>
    <select id="zc" resultType="java.math.BigDecimal" parameterType="cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq">

        select COALESCE(SUM(COALESCE(t1.consumables_costs, 0) + COALESCE(t1.repair_costs, 0) +
        COALESCE(t1.maintenance_costs, 0) + COALESCE(t1.utility_costs, 0) + COALESCE(t1.labor_costs, 0) +
        COALESCE(t1.housing_costs, 0) + COALESCE(t1.depreciations, 0) + COALESCE(t1.other_costs, 0)),0)
        from toa_device_device_operate_log t1
        left join toa_device_device t2 on t1.`device_id` = t2.id
        where t1.`month` = #{year}
        and t2.is_deleted = 'N'
        <choose>
            <when test="deptId != null and deptId != ''">
                <choose>
                    <when test="deptId == 'admin'">
                    </when>
                    <otherwise>
                        and t2.`dept_id` = #{deptId}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </select>
    <select id="deviceIncomeList" parameterType="cn.trasen.device.supervision.bean.report.BenefitReq" resultType="cn.trasen.device.supervision.bean.report.DeviceIncomeListResp">
        select
        t1.*,
        t3.name as deptName,
        t4.`name` as cateName,
        t2.`name`,
        t2.`brand`,
        t2.`spec`
        from toa_device_device_operate_log t1
        join toa_device_device t2 on t1.device_id = t2.id
        join comm_organization t3 on t2.dept_id = t3.organization_id
        join toa_device_category t4 on t2.cate_id = t4.id
        where t1.is_deleted = 'N' and t1.granularity = 'month'
        <if test="start != null and start != '' and end != null and end != ''">
            and t1.month BETWEEN #{start} AND #{end}
        </if>
        <if test="deptId != null and deptId != ''">
            and t2.dept_id = #{deptId}
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.cate_id = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        order by t3.custom_code ASC,t1.month DESC,t1.`device_id` ASC

    </select>

    <select id="deviceCostList" parameterType="cn.trasen.device.supervision.bean.report.BenefitReq" resultType="cn.trasen.device.supervision.bean.report.DeviceCostListResp">
        select
        t1.*,
        t3.name as deptName,
        t4.`name` as cateName,
        t2.`name`,
        t2.`brand`,
        t2.`spec`,
        ROUND(
        IFNULL(t1.consumables_costs, 0) + IFNULL(t1.repair_costs, 0) + IFNULL(t1.maintenance_costs, 0) +
        IFNULL(t1.utility_costs, 0) + IFNULL(t1.labor_costs, 0) + IFNULL(t1.housing_costs, 0) +
        IFNULL(t1.personnel_costs, 0) + IFNULL(t1.other_costs, 0) + IFNULL(t1.depreciations, 0),
        4
        ) AS total_costs
        from toa_device_device_operate_log t1
        join toa_device_device t2 on t1.device_id = t2.id
        join comm_organization t3 on t2.dept_id = t3.organization_id
        join toa_device_category t4 on t2.cate_id = t4.id
        where t1.is_deleted = 'N' and t1.granularity = 'year'
        <if test="start != null and start != '' and end != null and end != ''">
            and t1.month BETWEEN #{start} AND #{end}
        </if>
        <if test="deptId != null and deptId != ''">
            and t2.dept_id = #{deptId}
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.cate_id = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        order by t3.custom_code ASC,t1.month DESC,t1.`device_id` ASC
    </select>
</mapper>