package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PurchaseLog;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseLogService
 * @Description TODO
 * @date 2024年4月9日 上午10:58:37
 */
public interface PurchaseLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer save(PurchaseLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer update(PurchaseLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer deleteById(String id);

    void deleteByPurchaseGroupId(String purchaseGroupId);

    /**
     * @return PurchaseLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    PurchaseLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    DataSet<PurchaseLog> getDataSetList(Page page, PurchaseLog record);

    List<PurchaseLog> getListByPurchaseGroupId(String purchaseGroupId);

    List<PurchaseLog> getListByPurchaseResultId(String purchaseResultId);

    List<PurchaseLog> getListByPurchaseResultIdList(List purchaseResultIdList);
}
