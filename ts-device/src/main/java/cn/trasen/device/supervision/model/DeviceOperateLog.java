package cn.trasen.device.supervision.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_device_operate_log")
@Setter
@Getter
public class DeviceOperateLog {
    @Id
    private String id;

    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;


    @ApiModelProperty(value = "统计颗粒度")
    private String granularity;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 月收入
     */
    @ApiModelProperty(value = "月收入")
    private BigDecimal incomes;

    /**
     * 检查人数
     */
    @ApiModelProperty(value = "检查人数")
    private Short checks;

    /**
     * 耗材支出
     */
    @Column(name = "consumables_costs")
    @ApiModelProperty(value = "耗材支出")
    private BigDecimal consumablesCosts;

    /**
     * 维修成本
     */
    @Column(name = "repair_costs")
    @ApiModelProperty(value = "维修成本")
    private BigDecimal repairCosts;

    /**
     * 保养成本
     */
    @Column(name = "maintenance_costs")
    @ApiModelProperty(value = "保养成本")
    private BigDecimal maintenanceCosts;

    /**
     * 水电成本
     */
    @Column(name = "utility_costs")
    @ApiModelProperty(value = "水电成本")
    private BigDecimal utilityCosts;

    /**
     * 人工成本
     */
    @Column(name = "labor_costs")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal laborCosts;

    /**
     * 房屋成本
     */
    @Column(name = "housing_costs")
    @ApiModelProperty(value = "房屋成本")
    private BigDecimal housingCosts;

    /**
     * 人员成本
     */
    @Column(name = "personnel_costs")
    @ApiModelProperty(value = "人员成本")
    private BigDecimal personnelCosts;

    /**
     * 其他成本
     */
    @Column(name = "other_costs")
    @ApiModelProperty(value = "其他成本")
    private BigDecimal otherCosts;

    /**
     * 设备折旧
     */
    @ApiModelProperty(value = "设备折旧")
    private BigDecimal depreciations;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 扩展字段
     */
    @Column(name = "expand_field")
    @ApiModelProperty(value = "扩展字段")
    private String expandField;
}