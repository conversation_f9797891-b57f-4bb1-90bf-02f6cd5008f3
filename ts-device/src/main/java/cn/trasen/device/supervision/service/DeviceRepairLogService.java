package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.DeviceRepairLog;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceRepairLogService
 * @Description TODO
 * @date 2024年4月15日 下午5:52:02
 */
public interface DeviceRepairLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer save(DeviceRepairLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer update(DeviceRepairLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DeviceRepairLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DeviceRepairLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DeviceRepairLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DataSet<DeviceRepairLog> getDataSetList(Page page, DeviceRepairLog record);

    List<DeviceRepairLog> getListByDeviceId(String deviceId);
}
