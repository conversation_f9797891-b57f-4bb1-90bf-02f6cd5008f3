package cn.trasen.device.supervision.controller;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.report.*;
import cn.trasen.device.supervision.service.CategoryService;
import cn.trasen.device.supervision.service.DepartmentStatisticsLogService;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.service.DeviceOperateLogService;
import cn.trasen.device.supervision.util.Comm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @projectName: xtbg
 * @package: cn.trasen.oa.device.controller
 * @className: BenefitController
 * @author: chenbin
 * @description: 效益分析
 * @date: 2024/6/20 15:12
 * @version: 1.0
 */

@RestController
@Api(tags = "BenefitController")
@RequestMapping("api/benefit/")
public class BenefitController {

    @Autowired
    private DeviceOperateLogService deviceOperateLogService;

    @Autowired
    private DepartmentStatisticsLogService departmentStatisticsLogService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @ApiOperation(value = "收入统计", notes = "收入统计")
    @GetMapping("deviceIncomeList")
    public DataSet<DeviceIncomeListResp> deviceIncomeList(Page page, BenefitReq benefitReq) {

        String start = benefitReq.getStart();
        String end = benefitReq.getEnd();

        if (start == null && end == null) {

            String tpl = "yyyy-MM";
            String curDate = Comm.getCurDate(tpl);
            String[] monthRange = Comm.getMonthRange(curDate, tpl);

            benefitReq.setStart(monthRange[0]);
            benefitReq.setEnd(monthRange[1]);

        }

        deptIdBind(benefitReq);

        return deviceOperateLogService.deviceIncomeList(page, benefitReq);
    }


    @ApiOperation(value = "支出统计", notes = "支出统计")
    @GetMapping("deviceCostsList")
    public DataSet<DeviceCostListResp> deviceCostsList(Page page, BenefitReq benefitReq) {

        String start = benefitReq.getStart();
        String end = benefitReq.getEnd();

        if (start == null && end == null) {

            String tpl = "yyyy-MM";
            String curDate = Comm.getCurDate(tpl);
            String[] monthRange = Comm.getMonthRange(curDate, tpl);

            benefitReq.setStart(monthRange[0]);
            benefitReq.setEnd(monthRange[1]);

        }

        deptIdBind(benefitReq);

        return deviceOperateLogService.deviceCostList(page, benefitReq);
    }


    @ApiOperation(value = "医院排行", notes = "医院排行")
    @GetMapping("departmentStatistics")
    public DataSet<DepartmentStatisticsListResp> departmentStatistics(Page page, BenefitReq benefitReq) {

        String start = benefitReq.getStart();
        String end = benefitReq.getEnd();

        if (start == null && end == null) {

            String curDate = benefitReq.getYear();
            if (curDate == null) {
                curDate = Comm.getCurDate("yyyy");
            }
            String[] yearRange = Comm.getYearRange(curDate, "yyyy-MM-dd");

            benefitReq.setStart(yearRange[0]);
            benefitReq.setEnd(yearRange[1]);
        }

        deptIdBind(benefitReq);

        return departmentStatisticsLogService.departmentStatistics(page, benefitReq);
    }

    @ApiOperation(value = "设备类型效益统计", notes = "设备类型效益统计")
    @GetMapping("deviceCategoryStatistics")
    public DataSet<DeviceCategoryStatisticsListResp> deviceCategoryStatistics(Page page, BenefitReq benefitReq) {

        String start = benefitReq.getStart();
        String end = benefitReq.getEnd();

        if (start == null && end == null) {

            String curDate = benefitReq.getYear();
            if (curDate == null) {
                curDate = Comm.getCurDate("yyyy");
            }
            String[] yearRange = Comm.getYearRange(curDate, "yyyy-MM-dd");

            benefitReq.setStart(yearRange[0]);
            benefitReq.setEnd(yearRange[1]);
        }

        deptIdBind(benefitReq);

        return categoryService.deviceCategoryStatistics(page, benefitReq);

    }


    @ApiOperation(value = "效益分析总值", notes = "效益分析总值")
    @GetMapping("statistics")
    public PlatformResult<StatisticsResp> statistics(Page page, BenefitReq benefitReq) {

        try {
            String start = benefitReq.getStart();
            String end = benefitReq.getEnd();

            if (start == null && end == null) {

                String curDate = benefitReq.getYear();
                if (curDate == null) {
                    curDate = Comm.getCurDate("yyyy");
                }
                String[] yearRange = Comm.getYearRange(curDate, "yyyy-MM-dd");

                benefitReq.setStart(yearRange[0]);
                benefitReq.setEnd(yearRange[1]);
            }

            deptIdBind(benefitReq);

            return PlatformResult.success(departmentStatisticsLogService.statisticsResp(page, benefitReq));
        } catch (Exception e) {

            return PlatformResult.failure(e.getMessage());
        }
    }

    private void deptIdBind(BenefitReq benefitReq) {

        String deptId = deviceHelperService.getDeptCode();

        // 非管理员角色只能看自己机构数据
        if (!deptId.equals("admin")) {
            benefitReq.setDeptId(deptId);
        }

    }
}
