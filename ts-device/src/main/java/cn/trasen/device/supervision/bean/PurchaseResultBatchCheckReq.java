package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseResultBatchCheckReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/10/12 11:17
 * @version: 1.0
 */
@Data
public class PurchaseResultBatchCheckReq {

    @ApiModelProperty(value = "采购结果ID列表")
    private List<String> purchaseResultIdList;

    @ApiModelProperty(value = "审核状态")
    private String status;

    @ApiModelProperty(value = "审核备注")
    private String remark;

}
