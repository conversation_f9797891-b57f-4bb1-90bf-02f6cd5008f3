package cn.trasen.device.supervision.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.DepartmentStatisticsLog;
import cn.trasen.device.supervision.service.DepartmentStatisticsLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName DepartmentStatisticsLogController
 * @Description TODO
 * @date 2024年6月20日 上午11:09:14
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "DepartmentStatisticsLogController")
public class DepartmentStatisticsLogController {

	private transient static final Logger logger = LoggerFactory.getLogger(DepartmentStatisticsLogController.class);

	@Autowired
	private DepartmentStatisticsLogService departmentStatisticsLogService;

	/**
	 * @Title saveDepartmentStatisticsLog
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年6月20日 上午11:09:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/incomeOrigin/save")
	public PlatformResult<String> saveDepartmentStatisticsLog(@RequestBody DepartmentStatisticsLog record) {
		try {
			departmentStatisticsLogService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateDepartmentStatisticsLog
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年6月20日 上午11:09:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/incomeOrigin/update")
	public PlatformResult<String> updateDepartmentStatisticsLog(@RequestBody DepartmentStatisticsLog record) {
		try {
			departmentStatisticsLogService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectDepartmentStatisticsLogById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<DepartmentStatisticsLog>
	 * @date 2024年6月20日 上午11:09:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/incomeOrigin/{id}")
	public PlatformResult<DepartmentStatisticsLog> selectDepartmentStatisticsLogById(@PathVariable String id) {
		try {
			DepartmentStatisticsLog record = departmentStatisticsLogService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteDepartmentStatisticsLogById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024年6月20日 上午11:09:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/incomeOrigin/delete/{id}")
	public PlatformResult<String> deleteDepartmentStatisticsLogById(@PathVariable String id) {
		try {
			departmentStatisticsLogService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectDepartmentStatisticsLogList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<DepartmentStatisticsLog>
	 * @date 2024年6月20日 上午11:09:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/incomeOrigin/list")
	public DataSet<DepartmentStatisticsLog> selectDepartmentStatisticsLogList(Page page, DepartmentStatisticsLog record) {
		return departmentStatisticsLogService.getDataSetList(page, record);
	}
}
