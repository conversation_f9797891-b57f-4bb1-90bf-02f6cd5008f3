<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.PayslipLogMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.PayslipLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="payslip_upload_id" jdbcType="VARCHAR" property="payslipUploadId"/>
        <result column="ddmc" jdbcType="VARCHAR" property="ddmc"/>
        <result column="xm" jdbcType="VARCHAR" property="xm"/>
        <result column="ks" jdbcType="VARCHAR" property="ks"/>
        <result column="rsxz" jdbcType="VARCHAR" property="rsxz"/>
        <result column="sfzh" jdbcType="VARCHAR" property="sfzh"/>
        <result column="zwgz" jdbcType="VARCHAR" property="zwgz"/>
        <result column="jbgz" jdbcType="VARCHAR" property="jbgz"/>
        <result column="gwgz" jdbcType="VARCHAR" property="gwgz"/>
        <result column="djgz" jdbcType="VARCHAR" property="djgz"/>
        <result column="jlhlgz" jdbcType="VARCHAR" property="jlhlgz"/>
        <result column="jcxjx" jdbcType="VARCHAR" property="jcxjx"/>
        <result column="jlxjx" jdbcType="VARCHAR" property="jlxjx"/>
        <result column="xfrygwjt" jdbcType="VARCHAR" property="xfrygwjt"/>
        <result column="gwjtbt" jdbcType="VARCHAR" property="gwjtbt"/>
        <result column="fnwsf" jdbcType="VARCHAR" property="fnwsf"/>
        <result column="jxjj" jdbcType="VARCHAR" property="jxjj"/>
        <result column="yfhj" jdbcType="VARCHAR" property="yfhj"/>
        <result column="zfgjj" jdbcType="VARCHAR" property="zfgjj"/>
        <result column="ylbxjj" jdbcType="VARCHAR" property="ylbxjj"/>
        <result column="ylbxjj1" jdbcType="VARCHAR" property="ylbxjj1"/>
        <result column="zynj" jdbcType="VARCHAR" property="zynj"/>
        <result column="grsds" jdbcType="VARCHAR" property="grsds"/>
        <result column="sybx" jdbcType="VARCHAR" property="sybx"/>
        <result column="kfhj" jdbcType="VARCHAR" property="kfhj"/>
        <result column="sfgz" jdbcType="VARCHAR" property="sfgz"/>
        <result column="bz" jdbcType="VARCHAR" property="bz"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
    </resultMap>
    <select id="batchInsert" parameterType="java.util.List">
        INSERT INTO toa_device_payslip_log (
        id, payslip_upload_id, dwmc, xm, ks, rsxz, sfzh,
        zwgz, jbgz, gwgz, djgz, jlhlgz, jcxjx, jlxjx,
        xfrygwjt, gwjtbt, fnwsf, jxjj, yfhj, zfgjj,
        ylbxjj, ylbxjj1, zynj, grsds, sybx, kfhj,
        sfgz, bz, is_deleted, create_date, create_user,
        create_user_name, update_date, update_user, update_user_name,
        sso_org_code, sso_org_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.payslipUploadId}, #{item.dwmc}, #{item.xm}, #{item.ks}, #{item.rsxz}, #{item.sfzh},
            #{item.zwgz}, #{item.jbgz}, #{item.gwgz}, #{item.djgz}, #{item.jlhlgz}, #{item.jcxjx}, #{item.jlxjx},
            #{item.xfrygwjt}, #{item.gwjtbt}, #{item.fnwsf}, #{item.jxjj}, #{item.yfhj}, #{item.zfgjj},
            #{item.ylbxjj}, #{item.ylbxjj1}, #{item.zynj}, #{item.grsds}, #{item.sybx}, #{item.kfhj},
            #{item.sfgz}, #{item.bz}, #{item.isDeleted}, #{item.createDate}, #{item.createUser},
            #{item.createUserName}, #{item.updateDate}, #{item.updateUser}, #{item.updateUserName},
            #{item.ssoOrgCode}, #{item.ssoOrgName}
            )
        </foreach>
    </select>
</mapper>