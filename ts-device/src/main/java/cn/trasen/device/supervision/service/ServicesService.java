package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.model.Services;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ServicesService
 * @Description TODO
 * @date 2024年3月21日 下午2:43:02
 */
public interface ServicesService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    Integer save(Services record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    Integer update(Services record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    void deleteByPurchaseLogId(String purchaseLogId);

    /**
     * @return Services
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    Services selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Services>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    DataSet<ServicesManageListResp> getDataSetList(Page page, Services record);

    void submit(ServicesManageSaveReq record);

    ServicesManageDetailResp detail(String id);
}
