package cn.trasen.device.supervision.dao;

import cn.trasen.device.supervision.model.PurchaseLog;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface PurchaseLogMapper extends Mapper<PurchaseLog> {
    List<PurchaseLog> getListByPurchaseResultId(@Param("purchaseResultId") String purchaseResultId);

    List<PurchaseLog> getListByPurchaseResultIdList(@Param("purchaseResultIdList") List<String> purchaseResultIdList);

    List<PurchaseLog> getListByPurchaseGroupId(@Param("purchaseGroupId") String purchaseGroupId);
}