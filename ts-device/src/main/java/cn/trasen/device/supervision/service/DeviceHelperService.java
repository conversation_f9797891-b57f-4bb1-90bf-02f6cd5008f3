package cn.trasen.device.supervision.service;


import cn.trasen.device.supervision.bean.payslip.EmployeeReq;
import cn.trasen.device.supervision.bean.payslip.EmployeeResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.GetWfPsInstanceListReq;
import cn.trasen.device.supervision.bean.GetWfShInstanceListReq;
import cn.trasen.device.supervision.bean.WfInstanceJump;
import cn.trasen.device.supervision.model.WfPsInstanceInfo;
import cn.trasen.device.supervision.model.WfShInstanceInfo;
import cn.trasen.device.supervision.model.document.Attachment;
import cn.trasen.device.supervision.model.organization.Employee;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.model.workflow.DeviceToaFieldSet;
import cn.trasen.device.supervision.model.workflow.DeviceWfDefinitionInfo;
import cn.trasen.device.supervision.model.workflow.DeviceWfInstanceInfo;

import java.util.List;
import java.util.Map;

public interface DeviceHelperService {
    static final String SPECIAL_FORM_CLASSIFY_ID = "SPECIAL_FORM_CLASSIFY_ID";

    // return the list of userids
    public List<String> getDataViewRights();

    List<Employee> getEmployeeListByOrgIdList(List<String> orgIdList);

    List<String> getEmployeeCodeListByOrgIdList(List<String> orgIdList);

    List<Organization> getOrganizationListByOrgId(String orgId);

    List<String> getOrganizationIdListByOrgId(String orgId);

    List<Organization> getOrganizationList();

    List<Organization> getOrganizationListToFrontSelect();

    Organization getOrganizationByOrgId(String orgId);

    Map<String, Integer> getNumsWithStatus(String definitionId, List<String> withStatus);

    Map<String, String> getDictMapByCode(String code);

    List<DeviceWfDefinitionInfo> getWkDefinitionListByClassifyId(String classifyId);

    List<DeviceToaFieldSet> getToaFieldSetListByTemplateId(String templateId);

    DataSet<Map<String, Object>> getInstanceListByDefinitionId(Page page, String definitionId, String status, Map<String, String> params, Map<String, String> query);

    DataSet<WfShInstanceInfo> getWfShInstanceList(Page page, GetWfShInstanceListReq req);

    List<WfShInstanceInfo> getWfShInstanceListByIdLIst(List<String> wfInstanceIdList);

    DataSet<WfPsInstanceInfo> getWfPsInstanceList(Page page, GetWfPsInstanceListReq req);

    List<WfPsInstanceInfo> getWfPsInstanceListByIdLIst(List<String> wfInstanceIdList);


    void updateExportStatus(List<String> wfInstanceIdList);

    void updatePsExportStatus(List<String> wfInstanceIdList);

    List<Map<String, Object>> getInstanceListByDefinitionId(String definitionId);

    Attachment getAttachmentById(String id);

    DeviceWfInstanceInfo getWfInstanceInfoById(String id);

    // 获取当前用户的兼职科室ID
    String getUserParttimeOrgId(String employeeId);

    String getDeptCode();


    String justDeptCode();

    void autoCalcuUnitPrice();

    WfInstanceJump getWfInstanceJump(String wfInstanceId);

    DeviceWfInstanceInfo getWfInstanceInfoByBusinessId(String businessId);

    String getStartStepIdByWorkflowNo(String workflowNo);

    String getRestartTaskIdByWfInstanceId(String wfInstanceId);

    List<EmployeeResp> getEmployeeByDeptId(EmployeeReq employeeReq);
}
