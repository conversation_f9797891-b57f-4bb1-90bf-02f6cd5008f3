package cn.trasen.device.supervision.task;

import cn.trasen.device.supervision.service.IncomeOriginService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @projectName: xtbg
 * @package: cn.trasen.oa.device.task
 * @className: ConsumeIncomeDataTask
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/18 15:48
 * @version: 1.0
 */
@Component
public class ConsumeIncomeDataTask {
    private Logger logger = LoggerFactory.getLogger(ConsumeIncomeDataTask.class);

    // 每次同步数据的条数

    @Autowired
    private IncomeOriginService incomeOriginService;

    @Autowired
    private GlobalSettingsFeignService globalSettingsFeignService;

    private String getOrgCode() {

        PlatformResult<GlobalSetting> result = globalSettingsFeignService.getSafeGlobalSetting("N");
        GlobalSetting globalSetting = result.getObject();
        return globalSetting.getOrgCode();

    }

    @Scheduled(cron = "0 0/2 * * * ?")
    public void consume() {

        if (!"lyswjj".equals(getOrgCode())) {
            return;
        }

        logger.info("==================开始消费收入数据==================");
        incomeOriginService.consume();
        logger.info("==================消费收入数据完毕==================");
    }
}
