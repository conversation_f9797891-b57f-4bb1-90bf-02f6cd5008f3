CREATE OR REPLACE VIEW `toa_device_sh_instance_info` AS
SELECT
    `t1`.`WF_INSTANCE_ID` AS `wf_instance_id`,
    `t1`.`WF_DEFINITION_ID` AS `wf_definition_id`,
    '医疗专用设备采购申请表' AS `type`,
    year(`t3`.`sbsj8`) AS `apply_year`,
    `t3`.`sqsw0` AS `apply_org`,
    `t2`.`xmmc` AS `device_or_project_name`,
    `t2`.`ggxh` AS `model_or_service_period`,
    `t2`.`sl` AS `nums`,
    `t2`.`dj` AS `unit_price`,
    `t2`.`zje` AS `total_price`,
    `t2`.`xjqk1` AS `quotation1`,
    `t2`.`xjqk2` AS `quotation2`,
    `t2`.`xjqk3` AS `quotation3`,
    `t2`.`shsl` AS `meeting_nums`,
    `t2`.`shdj` AS `meeting_unit_price`,
    `t2`.`shje` AS `meeting_total_price`,
    date_format(str_to_date(`t2`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `meeting_date`,
    `t1`.`LAUNCH_DEPT_CODE` AS `LAUNCH_DEPT_CODE`,
    `t1`.`export` AS `export`,
    `t1`.`STATUS` AS `STATUS`,
    `t1`.`WORKFLOW_NO` AS `WORKFLOW_NO`,
    `t1`.`BUSINESS_ID` AS `BUSINESS_ID`,
    `t4`.`TASK_ID` AS `TASK_ID`,
    `t4`.`ASSIGNEE_NO` AS `ASSIGNEE_NO`,
    `t1`.`WORKFLOW_NUMBER` AS `WORKFLOW_NUMBER`,
    `t1`.`CURRENT_STEP_NAME` AS `CURRENT_STEP_NAME`,
    `t1`.`CURRENT_STEP_NO` AS `CURRENT_STEP_NO`,
    `t2`.`ID` AS `sonId`,
    `t1`.`IS_DELETED` AS `IS_DELETED`,
    `t1`.`CREATE_DATE` AS `CREATE_DATE`
FROM
    (
    (
    (
    `wf_instance_info` `t1`
    left join `zt_ylzysbcgjhsqb` `t2` on((`t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`))
    )
    left join `zdy_ylzysbcgjhsqb20240201113802` `t3` on((`t1`.`WF_INSTANCE_ID` = `t3`.`WORKFLOW_ID`))
    )
    left join `wf_task` `t4` on((`t1`.`WF_INSTANCE_ID` = `t4`.`WF_INSTANCE_ID`))
    )
where
    (
    `t1`.`CURRENT_STEP_NO` in (
    select
    `wf_step_info`.`WF_STEP_NO`
    from
    `wf_step_info`
    where
    `wf_step_info`.`WF_STEP_ID` in (
    select
    `wf_line_info`.`LINE_FROM`
    from
    `wf_line_info`
    where
    (
    `wf_line_info`.`LINE_TO` = (
    select
    `wf_step_info`.`WF_STEP_ID`
    from
    `wf_step_info`
    where
    (
    (
    `wf_step_info`.`WF_DEFINITION_ID` = '06985B07055046639923C386071BA66E'
    )
  and (`wf_step_info`.`WF_STEP_TYPE` = '9')
    )
    )
    )
    )
    )
  and (`t1`.`STATUS` = 1)
  and (`t1`.`IS_DELETED` = 'N')
    )
union all
select
    `t1`.`WF_INSTANCE_ID` AS `wf_instance_id`,
    `t1`.`WF_DEFINITION_ID` AS `wf_definition_id`,
    '一般设备采购申请表' AS `type`,
    year(`t3`.`sbsj7`) AS `apply_year`,
    `t3`.`sqsw0` AS `apply_org`,
    `t2`.`xmmc` AS `device_or_project_name`,
    `t2`.`ggxh` AS `model_or_service_period`,
    `t2`.`sl` AS `nums`,
    `t2`.`dj` AS `unit_price`,
    `t2`.`zje` AS `total_price`,
    `t2`.`xjqk1` AS `quotation1`,
    `t2`.`xjqk2` AS `quotation2`,
    `t2`.`xjqk3` AS `quotation3`,
    `t2`.`shsl` AS `meeting_nums`,
    `t2`.`shdj` AS `meeting_unit_price`,
    `t2`.`shje` AS `meeting_total_price`,
    date_format(str_to_date(`t2`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `meeting_date`,
    `t1`.`LAUNCH_DEPT_CODE` AS `LAUNCH_DEPT_CODE`,
    `t1`.`export` AS `export`,
    `t1`.`STATUS` AS `STATUS`,
    `t1`.`WORKFLOW_NO` AS `WORKFLOW_NO`,
    `t1`.`BUSINESS_ID` AS `BUSINESS_ID`,
    `t4`.`TASK_ID` AS `TASK_ID`,
    `t4`.`ASSIGNEE_NO` AS `ASSIGNEE_NO`,
    `t1`.`WORKFLOW_NUMBER` AS `WORKFLOW_NUMBER`,
    `t1`.`CURRENT_STEP_NAME` AS `CURRENT_STEP_NAME`,
    `t1`.`CURRENT_STEP_NO` AS `CURRENT_STEP_NO`,
    `t2`.`ID` AS `sonId`,
    `t1`.`IS_DELETED` AS `IS_DELETED`,
    `t1`.`CREATE_DATE` AS `CREATE_DATE`
from
    (
    (
    (
    `wf_instance_info` `t1`
    left join `zt_ybsbcgjhsqb` `t2` on((`t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`))
    )
    left join `zdy_ylybsbcgjhsbb20240201151026` `t3` on((`t1`.`WF_INSTANCE_ID` = `t3`.`WORKFLOW_ID`))
    )
    left join `wf_task` `t4` on((`t1`.`WF_INSTANCE_ID` = `t4`.`WF_INSTANCE_ID`))
    )
where
    (
    `t1`.`CURRENT_STEP_NO` in (
    select
    `wf_step_info`.`WF_STEP_NO`
    from
    `wf_step_info`
    where
    `wf_step_info`.`WF_STEP_ID` in (
    select
    `wf_line_info`.`LINE_FROM`
    from
    `wf_line_info`
    where
    (
    `wf_line_info`.`LINE_TO` = (
    select
    `wf_step_info`.`WF_STEP_ID`
    from
    `wf_step_info`
    where
    (
    (
    `wf_step_info`.`WF_DEFINITION_ID` = 'A37FCFB6631B405EA3A790E32FE88655'
    )
  and (`wf_step_info`.`WF_STEP_TYPE` = '9')
    )
    )
    )
    )
    )
  and (`t1`.`STATUS` = 1)
  and (`t1`.`IS_DELETED` = 'N')
    )
union all
select
    `t1`.`WF_INSTANCE_ID` AS `wf_instance_id`,
    `t1`.`WF_DEFINITION_ID` AS `wf_definition_id`,
    '工程项目采购申请表' AS `type`,
    year(`t3`.`sbsj1`) AS `apply_year`,
    `t3`.`sbsw0` AS `apply_org`,
    `t2`.`xmmc` AS `device_or_project_name`,
    `t2`.`jzmj` AS `model_or_service_period`,
    '1' AS `nums`,
    `t2`.`zje` AS `unit_price`,
    `t2`.`zje` AS `total_price`,
    '' AS `quotation1`,
    '' AS `quotation2`,
    '' AS `quotation3`,
    '1' AS `meeting_nums`,
    `t2`.`shje` AS `meeting_unit_price`,
    `t2`.`shje` AS `meeting_total_price`,
    date_format(str_to_date(`t2`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `meeting_date`,
    `t1`.`LAUNCH_DEPT_CODE` AS `LAUNCH_DEPT_CODE`,
    `t1`.`export` AS `export`,
    `t1`.`STATUS` AS `STATUS`,
    `t1`.`WORKFLOW_NO` AS `WORKFLOW_NO`,
    `t1`.`BUSINESS_ID` AS `BUSINESS_ID`,
    `t4`.`TASK_ID` AS `TASK_ID`,
    `t4`.`ASSIGNEE_NO` AS `ASSIGNEE_NO`,
    `t1`.`WORKFLOW_NUMBER` AS `WORKFLOW_NUMBER`,
    `t1`.`CURRENT_STEP_NAME` AS `CURRENT_STEP_NAME`,
    `t1`.`CURRENT_STEP_NO` AS `CURRENT_STEP_NO`,
    `t2`.`ID` AS `sonId`,
    `t1`.`IS_DELETED` AS `IS_DELETED`,
    `t1`.`CREATE_DATE` AS `CREATE_DATE`
from
    (
    (
    (
    `wf_instance_info` `t1`
    left join `zt_gcxmcgjhsqb` `t2` on((`t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`))
    )
    left join `zdy_jtgcxmcgjhsbb20240201153016` `t3` on((`t1`.`WF_INSTANCE_ID` = `t3`.`WORKFLOW_ID`))
    )
    left join `wf_task` `t4` on((`t1`.`WF_INSTANCE_ID` = `t4`.`WF_INSTANCE_ID`))
    )
where
    (
    `t1`.`CURRENT_STEP_NO` in (
    select
    `wf_step_info`.`WF_STEP_NO`
    from
    `wf_step_info`
    where
    `wf_step_info`.`WF_STEP_ID` in (
    select
    `wf_line_info`.`LINE_FROM`
    from
    `wf_line_info`
    where
    (
    `wf_line_info`.`LINE_TO` = (
    select
    `wf_step_info`.`WF_STEP_ID`
    from
    `wf_step_info`
    where
    (
    (
    `wf_step_info`.`WF_DEFINITION_ID` = 'ADA7FFF3AFBC46258C6A3D2E6A9D7DE0'
    )
  and (`wf_step_info`.`WF_STEP_TYPE` = '9')
    )
    )
    )
    )
    )
  and (`t1`.`STATUS` = 1)
  and (`t1`.`IS_DELETED` = 'N')
    )
union all
select
    `t1`.`WF_INSTANCE_ID` AS `wf_instance_id`,
    `t1`.`WF_DEFINITION_ID` AS `wf_definition_id`,
    '服务项目采购申请表' AS `type`,
    year(`t3`.`sbsj1`) AS `apply_year`,
    `t3`.`sbsw0` AS `apply_org`,
    `t2`.`xmmc` AS `device_or_project_name`,
    concat(
    replace(`t2`.`fwkssj`, '-', '.'),
    '-',
    replace(`t2`.`fwjssj`, '-', '.')
    ) AS `model_or_service_period`,
    '1' AS `nums`,
    `t2`.`fwnfdj` AS `unit_price`,
    `t2`.`zje` AS `total_price`,
    '' AS `quotation1`,
    '' AS `quotation2`,
    '' AS `quotation3`,
    ceiling(
    (
    cast(`t2`.`shje` as decimal(10, 2)) / cast(`t2`.`shdj` as decimal(10, 2))
    )
    ) AS `meeting_nums`,
    `t2`.`shdj` AS `meeting_unit_price`,
    `t2`.`shje` AS `meeting_total_price`,
    date_format(str_to_date(`t2`.`shrq`, '%Y-%m-%d'), '%Y.%m.%d') AS `meeting_date`,
    `t1`.`LAUNCH_DEPT_CODE` AS `LAUNCH_DEPT_CODE`,
    `t1`.`export` AS `export`,
    `t1`.`STATUS` AS `STATUS`,
    `t1`.`WORKFLOW_NO` AS `WORKFLOW_NO`,
    `t1`.`BUSINESS_ID` AS `BUSINESS_ID`,
    `t4`.`TASK_ID` AS `TASK_ID`,
    `t4`.`ASSIGNEE_NO` AS `ASSIGNEE_NO`,
    `t1`.`WORKFLOW_NUMBER` AS `WORKFLOW_NUMBER`,
    `t1`.`CURRENT_STEP_NAME` AS `CURRENT_STEP_NAME`,
    `t1`.`CURRENT_STEP_NO` AS `CURRENT_STEP_NO`,
    `t2`.`ID` AS `sonId`,
    `t1`.`IS_DELETED` AS `IS_DELETED`,
    `t1`.`CREATE_DATE` AS `CREATE_DATE`
from
    (
    (
    (
    `wf_instance_info` `t1`
    left join `zt_fwxmcgjhsbb` `t2` on((`t1`.`WF_INSTANCE_ID` = `t2`.`WORKFLOW_ID`))
    )
    left join `zdy_fwxmcgjhsbb20240201155028` `t3` on((`t1`.`WF_INSTANCE_ID` = `t3`.`WORKFLOW_ID`))
    )
    left join `wf_task` `t4` on((`t1`.`WF_INSTANCE_ID` = `t4`.`WF_INSTANCE_ID`))
    )
where
    (
    `t1`.`CURRENT_STEP_NO` in (
    select
    `wf_step_info`.`WF_STEP_NO`
    from
    `wf_step_info`
    where
    `wf_step_info`.`WF_STEP_ID` in (
    select
    `wf_line_info`.`LINE_FROM`
    from
    `wf_line_info`
    where
    (
    `wf_line_info`.`LINE_TO` = (
    select
    `wf_step_info`.`WF_STEP_ID`
    from
    `wf_step_info`
    where
    (
    (
    `wf_step_info`.`WF_DEFINITION_ID` = '1866B74A6BC3400F990AB9E3C2DAA1A6'
    )
  and (`wf_step_info`.`WF_STEP_TYPE` = '9')
    )
    )
    )
    )
    )
  and (`t1`.`STATUS` = 1)
  and (`t1`.`IS_DELETED` = 'N')
    );

-----update