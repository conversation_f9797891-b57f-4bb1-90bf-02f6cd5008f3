package cn.trasen.device.supervision.bean.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.index
 * @className: IndexBlockDBSXResp
 * @author: chenbin
 * @description: 待办事项
 * @date: 2024/5/29 09:42
 * @version: 1.0
 */

@Data
public class IndexBlockDBSXResp {

    @ApiModelProperty(value = "待审批预算数量")
    private String dspyssl;

    @ApiModelProperty(value = "待审批采购数量")
    private String dspcgsl;

    @ApiModelProperty(value = "待审批党委会数量")
    private String dspdwhsl;

}
