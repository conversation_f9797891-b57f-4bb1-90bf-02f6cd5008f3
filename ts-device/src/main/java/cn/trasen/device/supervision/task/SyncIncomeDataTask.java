package cn.trasen.device.supervision.task;

import cn.trasen.device.supervision.service.IncomeOriginService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
public class SyncIncomeDataTask {

    private Logger logger = LoggerFactory.getLogger(SyncIncomeDataTask.class);

    // 每次同步数据的条数

    @Autowired
    private IncomeOriginService incomeOriginService;


    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 增量备份
     * @date 2024/6/17 16:02
     */

//    @Scheduled(cron = "0 0/10 * * * ?")
    public void sync() {

        logger.info("==================开始同步收入数据==================");
        incomeOriginService.sync();
        logger.info("==================同步收入数据结束==================");
    }
}
