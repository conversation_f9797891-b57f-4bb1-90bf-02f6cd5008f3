package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.DeviceMaintenanceLog;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceMaintenanceLogService
 * @Description TODO
 * @date 2024年4月15日 下午5:52:02
 */
public interface DeviceMaintenanceLogService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer save(DeviceMaintenanceLog record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer update(DeviceMaintenanceLog record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return DeviceMaintenanceLog
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DeviceMaintenanceLog selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<DeviceMaintenanceLog>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月15日 下午5:52:02
     * <AUTHOR>
     */
    DataSet<DeviceMaintenanceLog> getDataSetList(Page page, DeviceMaintenanceLog record);

    List<DeviceMaintenanceLog> getListByDeviceId(String deviceId);
}
