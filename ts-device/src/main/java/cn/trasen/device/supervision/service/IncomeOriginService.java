package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.IncomeOrigin;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName IncomeOriginService
 * @Description TODO
 * @date 2024年6月17日 下午5:00:50
 */
public interface IncomeOriginService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    Integer save(IncomeOrigin record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    Integer update(IncomeOrigin record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return IncomeOrigin
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    IncomeOrigin selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<IncomeOrigin>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    DataSet<IncomeOrigin> getDataSetList(Page page, IncomeOrigin record);


    Integer counts();

    void consume();

    void sync();

    List<IndexBlockYYSBNDXYFXResp> yysbndxyfx(IndexBlockYYSBNDXYFXReq req);

    IndexBlockSBYDXYFXResp sbydxyfx(IndexBlockSBYDXYFXReq req);

    BigDecimal sr(IndexBlockYYZBReq indexBlockYYZBReq);

    void incomeDataSplit(String deviceId, String month);

    void departmentOperation(String deptId, String year);

}
