<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DepartmentStatisticsLogMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.device.supervision.model.DepartmentStatisticsLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="granularity" jdbcType="VARCHAR" property="granularity"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="month" jdbcType="VARCHAR" property="month"/>
        <result column="devices" jdbcType="INTEGER" property="devices"/>
        <result column="checks" jdbcType="INTEGER" property="checks"/>
        <result column="incomes" jdbcType="DECIMAL" property="incomes"/>
        <result column="costs" jdbcType="DECIMAL" property="costs"/>
        <result column="surplus" jdbcType="DECIMAL" property="surplus"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <select id="statisticsResp" parameterType="cn.trasen.device.supervision.bean.report.BenefitReq"
            resultType="cn.trasen.device.supervision.bean.report.StatisticsResp">
        SELECT
          COALESCE(t2.devices, 0) as devices
        , COALESCE(t3.times, 0) as checks
        , COALESCE(t3.incomes, 0) as incomes
        , COALESCE(t4.costs, 0) as costs
        , ROUND(COALESCE(t3.incomes, 0) - COALESCE(t4.costs, 0), 2) as `surplus`
        FROM
        (SELECT 1 as dept_id, COUNT(*) AS devices
        FROM toa_device_device
        where `is_deleted` = 'N'
        <if test="deptId != null and deptId != ''">
            and `dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and `create_date` between concat(#{start}, ' 00:00:00') and concat(#{end}, ' 23:59:59')
        </if>
        <if test="cateId != null and cateId != ''">
            and `cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and `name` like concat('%',#{deviceName},'%')
        </if>
        ) t2  join

        (SELECT count(DISTINCT t1.`user_code`) AS times
        , IFNULL(SUM(t1.`total`), 0) AS incomes
        , 1 as dept_id
        FROM toa_device_income_origin t1
        JOIN toa_device_device t2 ON t1.`device_id` = t2.`id`
        where t1.status = 1 and t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t1.`dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and t1.`cdate` between concat(#{start}, ' 00:00:00') and concat(#{end}, ' 23:59:59')
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.`cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        )  t3 on t2.`dept_id` = t3.`dept_id` join

        (SELECT COALESCE(
        SUM(COALESCE(t1.consumables_costs, 0) + COALESCE(t1.repair_costs, 0) + COALESCE(t1.maintenance_costs, 0) +
        COALESCE(t1.utility_costs, 0) + COALESCE(t1.labor_costs, 0) + COALESCE(t1.housing_costs, 0) +
        COALESCE(t1.depreciations, 0) + COALESCE(t1.other_costs, 0)), 0) AS costs
        , 1 as dept_id
        FROM toa_device_device_operate_log t1
        LEFT JOIN toa_device_device t2 ON t1.`device_id` = t2.id
        where t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t2.`dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and t1.`granularity` = 'year' and t1.`month` between SUBSTRING(#{start},1,4) and SUBSTRING(#{end},1,4)
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.`cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        ) t4 on t3.`dept_id` = t4.`dept_id`
    </select>
    <select id="departmentStatistics" parameterType="cn.trasen.device.supervision.bean.report.BenefitReq"
            resultType="cn.trasen.device.supervision.bean.report.DepartmentStatisticsListResp">
        SELECT t1.name as deptName
        , COALESCE(t2.devices, 0) as devices
        , COALESCE(t3.times, 0) as checks
        , COALESCE(t3.incomes, 0) as incomes
        , COALESCE(t4.costs, 0) as costs
        , ROUND(COALESCE(t3.incomes, 0) - COALESCE(t4.costs, 0), 2) as `surplus`
        FROM comm_organization t1
        LEFT JOIN (SELECT dept_id, COUNT(*) AS devices
        FROM toa_device_device
        where `is_deleted` = 'N'
        <if test="deptId != null and deptId != ''">
            and `dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and `create_date` between concat(#{start}, ' 00:00:00') and concat(#{end}, ' 23:59:59')
        </if>
        <if test="cateId != null and cateId != ''">
            and `cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and `name` like concat('%',#{deviceName},'%')
        </if>
        GROUP BY dept_id) t2
        ON t1.organization_id = t2.dept_id
        LEFT JOIN (SELECT count(DISTINCT t1.`user_code`) AS times
        , IFNULL(SUM(t1.`total`), 0) AS incomes
        , t1.`dept_id`
        FROM toa_device_income_origin t1
        JOIN toa_device_device t2 ON t1.`device_id` = t2.`id`
        where t1.status = 1 and t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t1.`dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and t1.`cdate` between concat(#{start}, ' 00:00:00') and concat(#{end}, ' 23:59:59')
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.`cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        GROUP BY t1.`dept_id`) t3
        ON t1.`organization_id` = t3.`dept_id`
        LEFT JOIN (SELECT COALESCE(
        SUM(COALESCE(t1.consumables_costs, 0) + COALESCE(t1.repair_costs, 0) + COALESCE(t1.maintenance_costs, 0) +
        COALESCE(t1.utility_costs, 0) + COALESCE(t1.labor_costs, 0) + COALESCE(t1.housing_costs, 0) +
        COALESCE(t1.depreciations, 0) + COALESCE(t1.other_costs, 0)), 0) AS costs
        , t2.dept_id
        FROM toa_device_device_operate_log t1
        LEFT JOIN toa_device_device t2 ON t1.`device_id` = t2.id
        where t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t2.`dept_id` = #{deptId}
        </if>
        <if test="start != null and start !='' and end != null and end != ''">
            and t1.`granularity` = 'year' and t1.`month` between SUBSTRING(#{start},1,4) and SUBSTRING(#{end},1,4)
        </if>
        <if test="cateId != null and cateId != ''">
            and t2.`cate_id` = #{cateId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            and t2.`name` like concat('%',#{deviceName},'%')
        </if>
        GROUP BY t2.`dept_id`
        ) t4 ON t1.organization_id = t4.dept_id
        WHERE t1.is_deleted = 'N'
        <if test="deptId != null and deptId != ''">
            and t1.organization_id = #{deptId}
        </if>
        <choose>
            <when test="sidx == 'devices'">
                order by devices ${sord}
            </when>
            <when test="sidx == 'checks'">
                order by times ${sord}
            </when>
            <when test="sidx == 'incomes'">
                order by incomes ${sord}
            </when>
            <when test="sidx == 'costs'">
                order by costs ${sord}
            </when>
            <otherwise>
                order by t1.custom_code IS NULL, t1.custom_code asc
            </otherwise>
        </choose>
    </select>
</mapper>