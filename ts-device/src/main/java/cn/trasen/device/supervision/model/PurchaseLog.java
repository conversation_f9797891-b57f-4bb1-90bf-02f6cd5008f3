package cn.trasen.device.supervision.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.util.Date;
import java.util.List;
import javax.persistence.*;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

@Table(name = "toa_device_purchase_log")
@Setter
@Getter
public class PurchaseLog {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 所属机构ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "所属机构ID")
    private String deptId;

    /**
     * 采购结果ID
     */
    @Transient
    @ApiModelProperty(value = "采购结果ID")
    private String purchaseResultId;

    /**
     * 采购结果ID
     */
    @Column(name = "purchase_group_id")
    @ApiModelProperty(value = "采购分组结果ID")
    private String purchaseGroupId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 采购（合同）时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "purchase_date")
    @ApiModelProperty(value = "采购（合同）时间")
    private Date purchaseDate;

    /**
     * 供货单位名称
     */
    @ApiModelProperty(value = "供货单位名称")
    private String supplier;

    /**
     * 生产厂家 ｜ 供应商 ｜服务商｜承建单位
     */
    @ApiModelProperty(value = "生产厂家 ｜ 供应商 ｜服务商｜承建单位")
    private String producer;

    /**
     * 采购品牌
     */
    @Column(name = "purchase_brand")
    @ApiModelProperty(value = "采购品牌")
    private String purchaseBrand;

    /**
     * 采购型号
     */
    @Column(name = "purchase_spec")
    @ApiModelProperty(value = "采购型号")
    private String purchaseSpec;

    /**
     * 采购方式
     */
    @Column(name = "purchase_way")
    @ApiModelProperty(value = "采购方式")
    private String purchaseWay;

    /**
     * 建设规模
     */
    @Column(name = "build_scale")
    @ApiModelProperty(value = "建设规模")
    private String buildScale;

    /**
     * 竣工验收备案时间
     */
    @Column(name = "accept_date")
    @ApiModelProperty(value = "竣工验收备案时间")
    private String acceptDate;

    /**
     * 采购数量
     */
    @Column(name = "purchase_numbers")
    @ApiModelProperty(value = "采购数量")
    private String purchaseNumbers;

    /**
     * 采购单价
     */
    @Column(name = "purchase_price")
    @ApiModelProperty(value = "采购单价")
    private String purchasePrice;

    /**
     * 采购金额
     */
    @Column(name = "purchase_total_price")
    @ApiModelProperty(value = "采购金额")
    private String purchaseTotalPrice;

    /**
     * 资金来源
     */
    @Column(name = "funds_sources")
    @ApiModelProperty(value = "资金来源")
    private String fundsSources;

    /**
     * 服务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "service_start_at")
    @ApiModelProperty(value = "服务开始时间")
    private Date serviceStartAt;

    /**
     * 服务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "service_end_at")
    @ApiModelProperty(value = "服务结束时间")
    private Date serviceEndAt;

    /**
     * 采购附件
     */
    @Column(name = "purchase_files")
    @ApiModelProperty(value = "采购附件")
    private String purchaseFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 备注
     */
    @Column(name = "purchase_remark")
    @ApiModelProperty(value = "备注")
    private String purchaseRemark;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgName;


    /**
     * 设备通用名称 （冗余）
     */

    @Column(name = "common_name")
    @ApiModelProperty(value = "设备通用名称（采购信息维护冗余）")
    private String commonName;

    /**
     * 设备分类ID （冗余）
     */
    @Column(name = "cate_id")
    @ApiModelProperty(value = "设备分类ID（采购信息维护冗余）")
    private String cateId;


    /**
     * 设备分类名称 （冗余）
     */
    @Transient
    @ApiModelProperty(value = "设备分类ID（采购信息维护冗余）")
    private String cateName;

    /**
     * 设备特殊字段集合
     */
    @Transient
    @ApiModelProperty(value = "设备特殊字段集合（采购信息维护冗余）")
    private List<Device> deviceList;
}