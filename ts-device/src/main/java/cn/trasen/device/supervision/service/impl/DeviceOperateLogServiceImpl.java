package cn.trasen.device.supervision.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DeviceCostListResp;
import cn.trasen.device.supervision.bean.report.DeviceIncomeListResp;
import cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.DeviceOperateLogMapper;
import cn.trasen.device.supervision.model.DeviceOperateLog;
import cn.trasen.device.supervision.service.DeviceOperateLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceOperateLogServiceImpl
 * @Description TODO
 * @date 2024年4月15日 下午5:52:02
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeviceOperateLogServiceImpl implements DeviceOperateLogService {

    @Autowired
    private DeviceOperateLogMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DeviceOperateLog record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DeviceOperateLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DeviceOperateLog record = new DeviceOperateLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DeviceOperateLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DeviceOperateLog> getDataSetList(Page page, DeviceOperateLog record) {
        Example example = new Example(DeviceOperateLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<DeviceOperateLog> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<DeviceOperateLog> getListByDeviceId(String deviceId, String granularity) {
        Example example = new Example(DeviceOperateLog.class);
        example.createCriteria().andEqualTo("deviceId", deviceId).andEqualTo("granularity", granularity).andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        return mapper.selectByExample(example);
    }

    /**
     * @param record:
     * @return Boolean
     * <AUTHOR>
     * @description 根据年份月份和设备ID查询是否存在
     * @date 2024/4/16 10:37
     */
    @Override
    public Boolean checkExist(DeviceOperateLog record) {
        Example example = new Example(DeviceOperateLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deviceId", record.getDeviceId());
        criteria.andEqualTo("month", record.getMonth());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        Integer rows = mapper.selectCountByExample(example);

        if (rows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public DeviceOperateLog checkRow(DeviceOperateLog record) {

        Example example = new Example(DeviceOperateLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deviceId", record.getDeviceId());
        criteria.andEqualTo("month", record.getMonth());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        List<DeviceOperateLog> list = mapper.selectByExample(example);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public BigDecimal zc(IndexBlockYYZBReq indexBlockYYZBReq) {
        return mapper.zc(indexBlockYYZBReq);
    }

    @Override
    public DataSet<DeviceIncomeListResp> deviceIncomeList(Page page, BenefitReq benefitReq) {
        List<DeviceIncomeListResp> records = mapper.deviceIncomeList(page, benefitReq);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public DataSet<DeviceCostListResp> deviceCostList(Page page, BenefitReq benefitReq) {
        List<DeviceCostListResp> records = mapper.deviceCostList(page, benefitReq);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }
}
