package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_department_budget")
@Setter
@Getter
public class DepartmentBudget {
    @Id
    private String id;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private String year;

    /**
     * 总预算
     */
    @Column(name = "total_quota")
    @ApiModelProperty(value = "总预算")
    private BigDecimal totalQuota;

    /**
     * 服务预算
     */
    @Column(name = "service_quota")
    @ApiModelProperty(value = "服务预算")
    private BigDecimal serviceQuota;

    /**
     * 项目预算
     */
    @Column(name = "project_quota")
    @ApiModelProperty(value = "项目预算")
    private BigDecimal projectQuota;

    @Column(name = "total_quota_use")
    @ApiModelProperty(value = "使用总预算")
    private BigDecimal totalQuotaUse;

    @Column(name = "service_quota_use")
    @ApiModelProperty(value = "使用服务预算")
    private BigDecimal serviceQuotaUse;


    @Column(name = "project_quota_use")
    @ApiModelProperty(value = "使用项目预算")
    private BigDecimal projectQuotaUse;

    /**
     * 剩余总预算
     */
    @Column(name = "total_quota_left")
    @ApiModelProperty(value = "剩余总预算")
    private BigDecimal totalQuotaLeft;

    /**
     * 剩余服务预算
     */
    @Column(name = "service_quota_left")
    @ApiModelProperty(value = "剩余服务预算")
    private BigDecimal serviceQuotaLeft;

    /**
     * 剩余项目预算
     */
    @Column(name = "project_quota_left")
    @ApiModelProperty(value = "剩余项目预算")
    private BigDecimal projectQuotaLeft;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "机构名称")
    private String deptName;

    @Transient
    @ApiModelProperty(value = "预算总使用率")
    private String totalRate;

    @Transient
    @ApiModelProperty(value = "服务类总使用率")
    private String serviceRate;

    @Transient
    @ApiModelProperty(value = "项目类总使用率")
    private String projectRate;

    @Transient
    private String type;

    @Transient
    private Integer pageNo;

    @Transient
    private Integer pageSize;

    @Transient
    @ApiModelProperty(value = "排序字段")
    private String sidx;

    @Transient
    @ApiModelProperty(value = "ASE/DESC排序方式")
    private String sord;


    /**
     * 手动维护的预算，防止后期有一些附加需求造成不能正常使用
     */
    @Column(name = "project_quota_by_hands")
    @ApiModelProperty(value = "手动维护的资产类预算")
    private BigDecimal projectQuotaByHands;

    @Column(name = "total_quota_by_hands")
    @ApiModelProperty(value = "手动维护的总预算")
    private BigDecimal totalQuotaByHands;

    @Column(name = "service_quota_by_hands")
    @ApiModelProperty(value = "使用服务预算")
    private BigDecimal serviceQuotaByHands;

}