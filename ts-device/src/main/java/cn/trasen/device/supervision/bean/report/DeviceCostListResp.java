package cn.trasen.device.supervision.bean.report;

import cn.trasen.device.supervision.model.DeviceOperateLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.benefit
 * @className: DeviceCostListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/20 17:08
 * @version: 1.0
 */

@Data
public class DeviceCostListResp extends DeviceOperateLog {


    @ApiModelProperty(value = "总支出")
    private String totalCosts;

    @ApiModelProperty(value = "医院名称")
    private String deptName;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "设备型号")
    private String spec;

    @ApiModelProperty(value = "分类名称")
    private String cateName;

}
