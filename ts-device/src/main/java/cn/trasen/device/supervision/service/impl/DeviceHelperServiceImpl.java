package cn.trasen.device.supervision.service.impl;

import cn.trasen.device.supervision.bean.payslip.EmployeeReq;
import cn.trasen.device.supervision.bean.payslip.EmployeeResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.GetInstanceListReq;
import cn.trasen.device.supervision.bean.GetWfPsInstanceListReq;
import cn.trasen.device.supervision.bean.GetWfShInstanceListReq;
import cn.trasen.device.supervision.bean.WfInstanceJump;
import cn.trasen.device.supervision.dao.*;
import cn.trasen.device.supervision.exception.DeviceBusinessException;
import cn.trasen.device.supervision.model.WfPsInstanceInfo;
import cn.trasen.device.supervision.model.WfShInstanceInfo;
import cn.trasen.device.supervision.model.document.Attachment;
import cn.trasen.device.supervision.model.organization.Employee;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.model.workflow.DeviceDpFormTemplate;
import cn.trasen.device.supervision.model.workflow.DeviceToaFieldSet;
import cn.trasen.device.supervision.model.workflow.DeviceWfDefinitionInfo;
import cn.trasen.device.supervision.model.workflow.DeviceWfInstanceInfo;
import cn.trasen.device.supervision.service.DeviceHelperService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.service.impl
 * @className: UserServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/1/29 16:07
 * @version: 1.0
 */

@Service
@Slf4j
public class DeviceHelperServiceImpl implements DeviceHelperService {

    @Autowired
    private DeviceHelperMapper deviceHelperMapper;

    @Autowired
    private DeviceWkDefinitionInfoMapper wkDefinitionInfoMapper;

    @Autowired
    private DeviceToaFieldSetMapper toaFieldSetMapper;

    @Autowired
    private DeviceDpFormTemplateMapper dpFormTemplateMapper;

    @Autowired
    private DictItemFeignService dictItemFeignService;

    @Autowired
    private DeviceAttachmentMapper attachmentMapper;


    @Autowired
    private DeviceWfInstanceInfoMapper wfInstanceInfoMapper;

    @Autowired
    private DeviceWfShInstanceInfoMapper wfShInstanceInfoMapper;

    @Autowired
    private DeviceWfPsInstanceInfoMapper wfPsInstanceInfoMapper;


    @Override
    public List<String> getDataViewRights() {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String orgRang = user.getOrgRang();
        // ('SELF','SELF_DEPT','SELF_SUB_DEPT','lyswjj','0001','002','20888576','27932160','97344512') to list

        if (orgRang == null || orgRang.isEmpty()) {
            // 直接返回当前用户的userID
            return Collections.singletonList(user.getUsercode());
        }

        // remove special characters
        String[] values = orgRang.replaceAll("[()']", "").split(",");
        List<String> orgIdlist = Arrays.asList(values);


        List<String> userIdList = new ArrayList();
        for (String orgId : orgIdlist) {
            // 特殊字符 'SELF' 'SELF_DEPT' 'SELF_SUB_DEPT
            switch (orgId) {
                case "SELF":
                    userIdList.add(user.getUsercode());
                    break;
                case "SELF_DEPT":
                    // just self org
                    List<String> userIdInOrg = getEmployeeCodeListByOrgIdList(Collections.singletonList(user.getDeptId()));
                    if (userIdInOrg == null || userIdInOrg.size() == 0) {
                        continue;
                    }
                    // merge list
                    userIdList.addAll(userIdInOrg);
                    break;
                case "SELF_SUB_DEPT":
                    // just self org and sub org
                    List<String> orgIdList = getOrganizationIdListByOrgId(user.getDeptId());
                    if (orgIdList == null || orgIdList.size() == 0) {
                        continue;
                    }
                    List<String> userIdInSubOrg = getEmployeeCodeListByOrgIdList(orgIdList);
                    if (userIdInSubOrg == null || userIdInSubOrg.size() == 0) {
                        continue;
                    }
                    // merge list
                    userIdList.addAll(userIdInSubOrg);
                    break;
            }

        }
        // Remove duplicates
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        return userIdList;
    }

    @Override
    public List<Employee> getEmployeeListByOrgIdList(List<String> orgIdList) {
        if (orgIdList != null && orgIdList.size() > 0) {
            return deviceHelperMapper.getEmployeeListByOrgIdList(orgIdList);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getEmployeeCodeListByOrgIdList(List<String> orgIdList) {

        // sure return list struts
        // friendly for frontend

        List emptyList = Collections.emptyList();

        if (orgIdList != null && orgIdList.size() > 0) {
            List<Employee> employeeList = deviceHelperMapper.getEmployeeListByOrgIdList(orgIdList);
            if (employeeList == null || employeeList.size() == 0) {
                return emptyList;
            }
            return deviceHelperMapper.getEmployeeListByOrgIdList(orgIdList).stream().map(Employee::getEmployeeNo).collect(Collectors.toList());
        }

        return emptyList;
    }

    @Override
    public List<Organization> getOrganizationListByOrgId(String orgId) {
        if (orgId != null && !orgId.isEmpty()) {
            return deviceHelperMapper.getOrganizationListByOrgId(orgId);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getOrganizationIdListByOrgId(String orgId) {

        if (orgId != null && !orgId.isEmpty()) {
            List<Organization> organizationList = deviceHelperMapper.getOrganizationListByOrgId(orgId);

            if (organizationList == null || organizationList.size() == 0) {
                return Collections.emptyList();
            }

            return organizationList.stream().map(Organization::getOrganizationId).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<Organization> getOrganizationList() {
        return deviceHelperMapper.getOrganizationList();
    }

    @Override
    public List<Organization> getOrganizationListToFrontSelect() {
        return deviceHelperMapper.getOrganizationListToFrontSelect();
    }

    @Override
    public Organization getOrganizationByOrgId(String orgId) {
        return deviceHelperMapper.getOrganizationByOrgId(orgId);
    }

    @Override
    public Map<String, Integer> getNumsWithStatus(String definitionId, List<String> withStatus) {
        Set<String> statusSet = new HashSet<>(Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007", "10008"));
        for (String s : withStatus) {
            if (!statusSet.contains(s)) {
                throw new DeviceBusinessException("非法的状态请求值");
            }
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user == null) {
            throw new DeviceBusinessException("用户未登录");
        }

        List<String> dataViewRights = getDataViewRights();
        String userCode = user.getUsercode();

        Map<String, Integer> res = new HashMap<>();

        for (String status : withStatus) {
            Integer nums = deviceHelperMapper.getNumsByStatus(userCode, dataViewRights, definitionId, status, getDeptCode());
            res.put(status, nums);
        }

        return res;
    }

    public Map<String, String> getDictMapByCode(String code) {
        log.info("获取字典配置项" + code);
        PlatformResult<List<DictItemResp>> pr = dictItemFeignService.getDictItemByTypeCode(code);
        List<DictItemResp> dictItemResps = pr.getObject();
        if (dictItemResps == null || dictItemResps.size() <= 0) {
            throw new BusinessException("字典配置项出现问题");
        }
        Map<String, String> dictMap = new HashMap<>();
        // 把字典值list 转换成 map
        for (DictItemResp dir : dictItemResps) {
            dictMap.put(dir.getItemCode(), dir.getItemNameValue());
        }
        return dictMap;
    }

    @Override
    public List<DeviceWfDefinitionInfo> getWkDefinitionListByClassifyId(String classifyId) {

        // 查询分类下流程列表
        Example example = new Example(DeviceWfDefinitionInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workflowClassify", classifyId);
        criteria.andEqualTo("status", 1);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        return wkDefinitionInfoMapper.selectByExample(example);
    }

    @Override
    public List<DeviceToaFieldSet> getToaFieldSetListByTemplateId(String templateId) {
        Example example = new Example(DeviceToaFieldSet.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("templateId", templateId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        return toaFieldSetMapper.selectByExample(example);
    }


    @Override
    public DataSet<Map<String, Object>> getInstanceListByDefinitionId(Page page, String definitionId, String status, Map<String, String> params, Map<String, String> query) {
        Set<String> statusSet = new HashSet<>(Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007", "10008"));

        if (!statusSet.contains(status)) {
            throw new DeviceBusinessException("非法的状态请求值");
        }


        // 查出来对应的表ID
        DeviceWfDefinitionInfo wkDefinitionInfo = wkDefinitionInfoMapper.selectByPrimaryKey(definitionId);

        if (wkDefinitionInfo == null) {
            throw new DeviceBusinessException("流程不存在");

        }

        DeviceDpFormTemplate dpFormTemplate = dpFormTemplateMapper.selectByPrimaryKey(wkDefinitionInfo.getFormId());

        if (dpFormTemplate == null) {
            throw new DeviceBusinessException("没有找到对应的表单数据");
        }

        // 目前冗余了一部分结构化的字段查询 这部不是一一对应的关系，这个在之前没考虑太好，实际上应该做一个构造好的where list 进去
        // 而不是到了xml里面还去做逻辑上的区分映射表，当时主要是为了不同情况下表的名称问题
        // 实际上不存在表的名称过多造成不同sql 表名称不一致的问题

        String mainRule = "start_cost,end_cost,start_income,end_income,start_surplus,end_surplus,launch_dept_name,status,WF_CREATE_DATE,WF_CURRENT_STEP_NAME,WF_STATUS"; // 主表字段
        String dynamicRule = parseformTemplate2String(dpFormTemplate.getFormTemplate()); // 表单字段
        String structDataRule = "income,cost,surplus"; // 结构化部分的 主要用于排序目前

        String orderRule = "desc,asc";
        // 检测排序方式 和排序字段是否规范
        List<String> mainRuleList = Arrays.asList(mainRule.split(","));
        List<String> dynamicRuleList = Arrays.asList(dynamicRule.split(","));
        List<String> structDataRuleList = Arrays.asList(structDataRule.split(","));

        List<String> orderRuleList = Arrays.asList(orderRule.split(","));
        String sidx = query.get("sidx");
        String sord = query.get("sord");

        if (!orderRuleList.contains(sord)) {
            sord = "desc";
        }

        String orderBy = "";
        if (mainRuleList.contains(sidx)) {
            // 由于怕字段冲突的问题，统一给t1加了WF_前缀，这里需要替换成空
            sidx = sidx.replace("WF_", "");
            orderBy = String.format("ORDER BY `t1`.`%s` %s", sidx, sord);
        } else if (structDataRuleList.contains(sidx)) {
            switch (sidx) {
                case "income":
                    sidx = "BQSR_1";
                    break;
                case "cost":
                    sidx = "BQFY_2";
                    break;
                case "surplus":
                    sidx = "BQYY_3";
                    break;
            }
            orderBy = String.format("ORDER BY `tdwds`.`extract_detial`->> '$.%s' %s", sidx, sord);
        } else if (dynamicRuleList.contains(sidx)) {
            orderBy = String.format("ORDER BY `dynamic_table`.`%s` %s", sidx, sord);
        } else {
            orderBy = "order by `t1`.`WF_CREATE_DATE` desc";
        }

        // 拆分参数成 主要查询 和 动态查询两个部分
        Map<String, String> mainParams = new HashMap<>();
        Map<String, String> dynamicParams = new HashMap<>();

        // 拆分参数
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();

            if (mainRuleList.contains(k)) {
                mainParams.put(k, v);
            } else if (dynamicRuleList.contains(k)) {
                dynamicParams.put(k, v);
            }
        }

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        String userCode = user.getUsercode();
        List<String> dataViewRight = getDataViewRights();

        //page, definitionId, status, dpFormTemplate.getTableName(), mainParams, dynamicParams, userCode, dataViewRight, getDeptCode()
        // 状态参数
        GetInstanceListReq getInstanceListReq = new GetInstanceListReq();
        getInstanceListReq.setDefinitionId(definitionId);
        getInstanceListReq.setStatus(status);
        getInstanceListReq.setTableName(dpFormTemplate.getTableName());
        getInstanceListReq.setMainParams(mainParams);
        getInstanceListReq.setDynamicParams(dynamicParams);
        getInstanceListReq.setUserCode(userCode);
        getInstanceListReq.setDataViewRights(dataViewRight);
        getInstanceListReq.setDeptCode(getDeptCode());
        getInstanceListReq.setOrderBy(orderBy);


        List<Map<String, Object>> records = deviceHelperMapper.getInstanceList(page, getInstanceListReq);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public DataSet<WfShInstanceInfo> getWfShInstanceList(Page page, GetWfShInstanceListReq req) {

        if (StringUtils.isNotBlank(req.getDeviceOrProjectName())) {
            String output = req.getDeviceOrProjectName().chars()
                    .mapToObj(c -> (char) c)
                    .map(c -> "%" + c)
                    .collect(Collectors.joining());
            req.setDeviceOrProjectName(output.substring(1));
        }
        req.setAssigneeNo(UserInfoHolder.getCurrentUserCode());

        List<WfShInstanceInfo> wfShInstanceInfoList = wfShInstanceInfoMapper.getWfShInstanceList(page, req);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), wfShInstanceInfoList);
    }

    @Override
    public List<WfShInstanceInfo> getWfShInstanceListByIdLIst(List<String> wfInstanceIdList) {
        List<WfShInstanceInfo> wfShInstanceInfoList = wfShInstanceInfoMapper.getWfShInstanceListByIdLIst(wfInstanceIdList);
        return wfShInstanceInfoList;
    }

    @Override
    public DataSet<WfPsInstanceInfo> getWfPsInstanceList(Page page, GetWfPsInstanceListReq req) {
        req.setLaunchDeptCode(getDeptCode());

        if (StringUtils.isNotBlank(req.getDeviceOrProjectName())) {
            String output = req.getDeviceOrProjectName().chars()
                    .mapToObj(c -> (char) c)
                    .map(c -> "%" + c)
                    .collect(Collectors.joining());
            req.setDeviceOrProjectName(output.substring(1));
        }

        List<WfPsInstanceInfo> wfPsInstanceInfoList = wfPsInstanceInfoMapper.getWfPsInstanceList(page, req);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), wfPsInstanceInfoList);

    }

    @Override
    public List<WfPsInstanceInfo> getWfPsInstanceListByIdLIst(List<String> wfInstanceIdList) {
        return wfPsInstanceInfoMapper.getWfPsInstanceListByIdLIst(wfInstanceIdList);
    }

    @Override
    public void updateExportStatus(List<String> wfInstanceIdList) {
        if (wfInstanceIdList == null || wfInstanceIdList.size() == 0) {
            return;
        }
        wfShInstanceInfoMapper.updateExportStatus(wfInstanceIdList);
    }

    @Override
    public void updatePsExportStatus(List<String> wfInstanceIdList) {
        if (wfInstanceIdList == null || wfInstanceIdList.size() == 0) {
            return;
        }
        wfPsInstanceInfoMapper.updateExportStatus(wfInstanceIdList);
    }

    @Override
    public List<Map<String, Object>> getInstanceListByDefinitionId(String definitionId) {

        // 查出来对应的表ID
        DeviceWfDefinitionInfo wkDefinitionInfo = wkDefinitionInfoMapper.selectByPrimaryKey(definitionId);

        if (wkDefinitionInfo == null) {
            throw new DeviceBusinessException("流程不存在");

        }

        DeviceDpFormTemplate dpFormTemplate = dpFormTemplateMapper.selectByPrimaryKey(wkDefinitionInfo.getFormId());

        if (dpFormTemplate == null) {
            throw new DeviceBusinessException("没有找到对应的表单数据");
        }

        // 这里不new 一个page 会跑出空指针
        Page page = new Page();
        GetInstanceListReq getInstanceListReq = new GetInstanceListReq();
        getInstanceListReq.setDefinitionId(definitionId);
        getInstanceListReq.setStatus("99999");
        getInstanceListReq.setTableName(dpFormTemplate.getTableName());
        getInstanceListReq.setDeptCode(getDeptCode());


        List<Map<String, Object>> records = deviceHelperMapper.getInstanceList(page, getInstanceListReq);
        return records;
    }

    @Override
    public Attachment getAttachmentById(String id) {
        return attachmentMapper.selectByPrimaryKey(id);
    }

    @Override
    public DeviceWfInstanceInfo getWfInstanceInfoById(String id) {
        return wfInstanceInfoMapper.selectByPrimaryKey(id);
    }


    @Override
    public String getUserParttimeOrgId(String employeeId) {
        return deviceHelperMapper.getUserParttimeOrgId(employeeId);
    }


    /**
     * @param :
     * @return String
     * <AUTHOR>
     * @description 如果是卫建角色返回admin, 其他情况返回部门编码，适用与数据库查询
     * @date 2024/4/17 10:56
     */
    public String getDeptCode() {
        Boolean right = UserInfoHolder.getRight("WJJ_MASTER");//卫健局管理员
        // 判断管理员
        try {
            if (!right) {
                String deptCode = getUserParttimeOrgId(UserInfoHolder.getCurrentUserId());
                if (deptCode == null || "".equals(deptCode)) {
                    deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
                }
                return deptCode;
            }
        } catch (Exception e) {
            log.info("定时任务无身份，默认所有权限");
        }
        return "admin";
    }

    /**
     * @param :
     * @return String
     * <AUTHOR>
     * @description 仅仅获取部门编码
     * @date 2024/4/17 10:54
     */
    @Override
    public String justDeptCode() {
        String deptCode = getUserParttimeOrgId(UserInfoHolder.getCurrentUserId());
        if (deptCode == null || "".equals(deptCode)) {
            deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
        }
        return deptCode;
    }

    @Override
    public void autoCalcuUnitPrice() {
        deviceHelperMapper.autoCalcuUnitPrice();
    }

    /**
     * @param wfInstanceId:
     * @return WfInstanceJump
     * <AUTHOR>
     * @description 暂时只能查已经结束的，因为与党委会评审的视图，如果要支持到全部流程则需要修改sql语句
     * @date 2024/5/27 14:02
     */

    @Override
    public WfInstanceJump getWfInstanceJump(String wfInstanceId) {
        return deviceHelperMapper.getWfInstanceJump(wfInstanceId);
    }

    @Override
    public DeviceWfInstanceInfo getWfInstanceInfoByBusinessId(String businessId) {
        Example example = new Example(DeviceWfInstanceInfo.class);
        example.createCriteria()
                .andEqualTo("businessId", businessId)
                .andEqualTo("isDeleted", "N");
        List<DeviceWfInstanceInfo> wfInstanceInfoList = wfInstanceInfoMapper.selectByExample(example);

        if (wfInstanceInfoList == null || wfInstanceInfoList.size() == 0) {
            return null;
        }
        return wfInstanceInfoList.get(0);
    }

    @Override
    public String getStartStepIdByWorkflowNo(String workflowNo) {
        return deviceHelperMapper.getStartStepIdByWorkflowNo(workflowNo);
    }

    @Override
    public String getRestartTaskIdByWfInstanceId(String wfInstanceId) {
        return deviceHelperMapper.getRestartTaskIdByWfInstanceId(wfInstanceId);
    }

    @Override
    public List<EmployeeResp> getEmployeeByDeptId(EmployeeReq employeeReq) {
        return deviceHelperMapper.getEmployeeByDeptId(employeeReq);
    }

    private static String parseformTemplate2String(String input) {
        // 去除字符串中的方括号和双引号
        String cleanedInput = input.replace("[", "").replace("]", "").replace("\"", "");

        // 使用逗号分割字符串
        String[] parts = cleanedInput.split(",");

        // 使用StringBuilder拼接字符串
        StringBuilder resultBuilder = new StringBuilder();
        for (String part : parts) {
            resultBuilder.append(part.trim()).append(",");
        }

        // 删除最后一个逗号
        if (resultBuilder.length() > 0) {
            resultBuilder.deleteCharAt(resultBuilder.length() - 1);
        }

        return resultBuilder.toString();
    }


}
