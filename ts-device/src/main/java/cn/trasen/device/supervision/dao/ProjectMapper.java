package cn.trasen.device.supervision.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.bean.DeviceManageListResp;
import cn.trasen.device.supervision.bean.ProjectManageListResp;
import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.Project;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ProjectMapper extends Mapper<Project> {
    List<ProjectManageListResp> getDataSetList(Page page, Project project);
}