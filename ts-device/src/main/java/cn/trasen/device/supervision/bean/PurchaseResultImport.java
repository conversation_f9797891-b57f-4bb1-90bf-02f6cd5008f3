package cn.trasen.device.supervision.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import java.util.Date;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision
 * @className: PurchaseResultImport
 * @author: chenbin
 * @description: TODO
 * @date: 2024/10/10 08:31
 * @version: 1.0
 */
@Data
public class PurchaseResultImport {

    @Excel(name = "标志(请勿修改或删除)")
    private String id;

    @ApiModelProperty(value = "类型")
    @Excel(name = "类型")
    private String type;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "采购项目名称")
    @Excel(name = "采购项目名称")
    private String name;

    /**
     * 采购（合同）时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "采购(合同)时间")
    @Excel(name = "采购(合同)时间", format = "yyyy-MM-dd")
    private Date purchaseDate;

    /**
     * 供货单位名称
     */
    @ApiModelProperty(value = "供货单位名称")
    @Excel(name = "供应商/承建商/服务商")
    private String supplier;

    /**
     * 生产厂家 ｜ 供应商 ｜服务商｜承建单位
     */
    @ApiModelProperty(value = "生产厂家 ｜ 供应商 ｜服务商｜承建单位")
    @Excel(name = "生产厂商")
    private String producer;

    /**
     * 采购品牌
     */
    @ApiModelProperty(value = "采购品牌")
    @Excel(name = "品牌")
    private String purchaseBrand;

    /**
     * 采购型号
     */
    @ApiModelProperty(value = "采购型号")
    @Excel(name = "规格型号")
    private String purchaseSpec;

    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    @Excel(name = "采购方式")
    private String purchaseWay;

    /**
     * 建设规模
     */
    @ApiModelProperty(value = "建设面积(平方米)")
    @Excel(name = "建设面积(平方米)")
    private String buildScale;

    /**
     * 竣工验收备案时间
     */
    @ApiModelProperty(value = "竣工验收备案时间")
    @Excel(name = "竣工验收备案时间", format = "yyyy-MM-dd")
    private String acceptDate;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @Excel(name = "数量")
    private String purchaseNumbers;

    /**
     * 采购单价
     */
    @ApiModelProperty(value = "采购单价")
    @Excel(name = "单价(万元)")
    private String purchasePrice;

    /**
     * 采购金额
     */
    @ApiModelProperty(value = "采购金额")
    @Excel(name = "金额(万元)")
    private String purchaseTotalPrice;


    /**
     * 服务开始时间
     */
    @ApiModelProperty(value = "服务开始时间")
    @Excel(name = "服务开始时间", format = "yyyy-MM-dd")
    private Date serviceStartAt;

    /**
     * 服务结束时间
     */
    @ApiModelProperty(value = "服务结束时间")
    @Excel(name = "服务结束时间", format = "yyyy-MM-dd")
    private Date serviceEndAt;


    // 等于采购数量
    @ApiModelProperty(value = "服务年份(年)")
    @Excel(name = "服务年份(年)")
    private String serviceYears;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String purchaseRemark;


    @ApiModelProperty(value = "设备分类")
    @Excel(name = "设备分类")
    private String cateName;


    @ApiModelProperty(value = "设备通用名")
    @Excel(name = "设备通用名")
    private String commonName;


    @ApiModelProperty(value = "设备编码")
    @Excel(name = "设备编码")
    private String deviceCode;


    @ApiModelProperty(value = "资产编码")
    @Excel(name = "资产编码")
    private String assetCode;
}
