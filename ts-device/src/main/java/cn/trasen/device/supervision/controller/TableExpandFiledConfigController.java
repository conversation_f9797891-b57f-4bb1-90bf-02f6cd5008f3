package cn.trasen.device.supervision.controller;


import cn.trasen.homs.core.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.device.supervision.service.TableExpandFiledConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName TableExpandFiledConfigController
 * @Description TODO
 * @date 2024年3月29日 上午9:23:58
 */
@RestController
@Api(tags = "TableExpandFiledConfigController")
@RequestMapping("api/tableExpandFiledConfig")
public class TableExpandFiledConfigController {

    private transient static final Logger logger = LoggerFactory.getLogger(TableExpandFiledConfigController.class);

    @Autowired
    private TableExpandFiledConfigService tableExpandFiledConfigService;

    @ApiOperation(value = "根据表名初始化字段配置", notes = "根据表名初始化字段配置")
    @GetMapping("/generateTableExpandFiledConfig/{tableName}")
    public PlatformResult<String> generateTableExpandFiledConfig(@PathVariable String tableName) {
        try {
            if (StringUtil.isEmpty(tableName)) {
                return PlatformResult.failure("表名不能为空");
            }
            tableExpandFiledConfigService.generateTableExpandFiledConfig(tableName);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
