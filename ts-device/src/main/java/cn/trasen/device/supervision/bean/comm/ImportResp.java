package cn.trasen.device.supervision.bean.comm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.toolbox.bean
 * @className: ImportResp
 * @author: chenbin
 * @description: 标准导出表
 * @date: 2024/9/10 10:32
 * @version: 1.0
 */

@Data
public class ImportResp {

    @ApiModelProperty(value = "成功数量")
    int succs;

    @ApiModelProperty(value = "失败数量")
    int errs;

    @ApiModelProperty(value = "失败行")
    List<ImportErrRow> errRows;
}
