package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PurchaseGroup;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseGroupService
 * @Description TODO
 * @date 2024年4月9日 上午10:58:37
 */
public interface PurchaseGroupService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer save(PurchaseGroup record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer update(PurchaseGroup record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    Integer deleteById(String id);

    void deleteByPurchaseResultId(String purchaseResultId);

    /**
     * @return PurchaseGroup
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    PurchaseGroup selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseGroup>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月9日 上午10:58:37
     * <AUTHOR>
     */
    DataSet<PurchaseGroup> getDataSetList(Page page, PurchaseGroup record);

    List<PurchaseGroup> getListByPurchaseResultId(String purchaseResultId);
}
