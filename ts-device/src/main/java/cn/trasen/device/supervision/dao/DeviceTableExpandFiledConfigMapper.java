package cn.trasen.device.supervision.dao;

import cn.trasen.device.supervision.bean.TableInfo;
import cn.trasen.device.supervision.model.TableExpandFiledConfig;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DeviceTableExpandFiledConfigMapper extends Mapper<TableExpandFiledConfig> {
    List<TableInfo> generateTableExpandFiledConfig(@Param("tableName") String tableName);

    Integer fieldExist(@Param("tableName") String tableName, @Param("fieldName") String fieldName);
}