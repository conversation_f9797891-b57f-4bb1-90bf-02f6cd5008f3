package cn.trasen.device.supervision.controller;

import cn.trasen.device.supervision.bean.DeviceManageDetailResp;
import cn.trasen.device.supervision.bean.DeviceManageListResp;
import cn.trasen.device.supervision.bean.DeviceManageSaveReq;
import cn.trasen.device.supervision.bean.DeviceParamResp;
import cn.trasen.device.supervision.service.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceController
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
@RestController
@Api(tags = "DeviceController")
public class DeviceController {

    private transient static final Logger logger = LoggerFactory.getLogger(DeviceController.class);

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private CategoryService categoryService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveDevice
     * @Description 新增
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    @ApiOperation(value = "提交", notes = "提交")
    @PostMapping("/api/device/save")
    public PlatformResult<String> submit(@RequestBody DeviceManageSaveReq record) {
        try {
            deviceService.submit(record);
            categoryService.updateDevices(record.getDevice().getCateId());
            return PlatformResult.success("提交成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "详情", notes = "详情")
    @PostMapping("/api/device/{id}")
    public PlatformResult<DeviceManageDetailResp> detail(@PathVariable String id) {
        try {
            return PlatformResult.success(deviceService.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Device>
     * @Title selectDeviceList
     * @Description 查询列表
     * @date 2024年3月21日 下午2:42:16
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/list")
    public DataSet<DeviceManageListResp> selectDeviceList(Page page, Device record) {
        return deviceService.getDataSetList(page, record);
    }

    /**
     * @param page:
     * @param record:
     * @return DataSet<DeviceManageListResp>
     * <AUTHOR>
     * @description 设备参数库
     * @date 2024/5/15 18:06
     */
    @ApiOperation(value = "设备参数库", notes = "设备参数库")
    @GetMapping("/api/device/param/list")
    public DataSet<DeviceParamResp> selectDeviceParamList(Page page, Device record) {
        return deviceService.getDeviceParamList(page, record);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/delete/{id}")
    public PlatformResult<String> delete(@PathVariable String id) {
        try {
            // 判断是不是手动添加的设备
            deviceService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
