/*!
 * Viewer v1.0.0
 * https://github.com/fengyuanchen/viewer
 *
 * Copyright (c) 2015-2018 <PERSON>
 * Released under the MIT license
 *
 * Date: 2018-04-01T06:11:06.751Z
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],i):i(t.jQuery)}(this,function(d){"use strict";d=d&&d.hasOwnProperty("default")?d.default:d;var n={inline:!1,button:!0,navbar:!0,title:!0,toolbar:!0,tooltip:!0,movable:!0,zoomable:!0,rotatable:!0,scalable:!0,transition:!0,fullscreen:!0,interval:5e3,keyboard:!0,backdrop:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,zIndex:2015,zIndexInline:0,url:"src",container:"body",filter:null,ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null},s="undefined"!=typeof window,o=s?window:{},p="viewer",l="move",c="switch",u="zoom",f=p+"-active",w=p+"-fade",b=p+"-fixed",a=p+"-fullscreen-exit",y=p+"-hide",i=p+"-hide-md-down",e=p+"-hide-sm-down",r=p+"-hide-xs-down",m=p+"-in",x=p+"-invisible",v=p+"-loading",h=p+"-open",g=p+"-show",D=p+"-transition",z="ready",k="show",E="shown",S="hide",I="hidden",T="view",L="viewed",C="click",M="dragstart",N="keydown",q="load",Y=o.PointerEvent?"pointerdown":"touchstart mousedown",X=o.PointerEvent?"pointermove":"touchmove mousemove",R=o.PointerEvent?"pointerup pointercancel":"touchend touchcancel mouseup",O="resize",F="transitionend",W="wheel mousewheel DOMMouseScroll",P=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"],A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j=function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")},t=function(){function n(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,i,e){return i&&n(t.prototype,i),e&&n(t,e),t}}();function H(t){return"string"==typeof t}var B=Number.isNaN||o.isNaN;function V(t){return"number"==typeof t&&!B(t)}function K(t){return void 0===t}function U(t){return"object"===(void 0===t?"undefined":A(t))&&null!==t}var Z=Object.prototype.hasOwnProperty;function $(t){if(!U(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&Z.call(e,"isPrototypeOf")}catch(t){return!1}}function _(t){return"function"==typeof t}function Q(i,e){if(i&&_(e))if(Array.isArray(i)||V(i.length)){var t=i.length,n=void 0;for(n=0;n<t&&!1!==e.call(i,i[n],n,i);n+=1);}else U(i)&&Object.keys(i).forEach(function(t){e.call(i,i[t],t,i)});return i}var G=Object.assign||function(e){for(var t=arguments.length,i=Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return U(e)&&0<i.length&&i.forEach(function(i){U(i)&&Object.keys(i).forEach(function(t){e[t]=i[t]})}),e},J=/^(?:width|height|left|top|marginLeft|marginTop)$/;function tt(t,i){var e=t.style;Q(i,function(t,i){J.test(i)&&V(t)&&(t+="px"),e[i]=t})}function it(t,i){return t.classList?t.classList.contains(i):-1<t.className.indexOf(i)}function et(t,i){if(i)if(V(t.length))Q(t,function(t){et(t,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className=e+" "+i):t.className=i}}function nt(t,i){i&&(V(t.length)?Q(t,function(t){nt(t,i)}):t.classList?t.classList.remove(i):0<=t.className.indexOf(i)&&(t.className=t.className.replace(i,"")))}function st(t,i,e){i&&(V(t.length)?Q(t,function(t){st(t,i,e)}):e?et(t,i):nt(t,i))}var ot=/([a-z\d])([A-Z])/g;function at(t){return t.replace(ot,"$1-$2").toLowerCase()}function rt(t,i){return U(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-"+at(i))}function ht(t,i,e){U(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-"+at(i),e)}var lt=/\s\s*/,dt=function(){var t=!1;if(s){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(t){i=t}});o.addEventListener("test",e,n),o.removeEventListener("test",e,n)}return t}();function ct(e,t,n){var s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},o=n;t.trim().split(lt).forEach(function(t){if(!dt){var i=e.listeners;i&&i[t]&&i[t][n]&&(o=i[t][n],delete i[t][n],0===Object.keys(i[t]).length&&delete i[t],0===Object.keys(i).length&&delete e.listeners)}e.removeEventListener(t,o,s)})}function ut(o,t,a){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},h=a;t.trim().split(lt).forEach(function(n){if(r.once&&!dt){var t=o.listeners,s=void 0===t?{}:t;h=function(){for(var t=arguments.length,i=Array(t),e=0;e<t;e++)i[e]=arguments[e];delete s[n][a],o.removeEventListener(n,h,r),a.apply(o,i)},s[n]||(s[n]={}),s[n][a]&&o.removeEventListener(n,s[n][a],r),s[n][a]=h,o.listeners=s}o.addEventListener(n,h,r)})}function ft(t,i,e){var n=void 0;return _(Event)&&_(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function mt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,s=t.translateX,o=t.translateY,a=[];V(s)&&0!==s&&a.push("translateX("+s+"px)"),V(o)&&0!==o&&a.push("translateY("+o+"px)"),V(i)&&0!==i&&a.push("rotate("+i+"deg)"),V(e)&&1!==e&&a.push("scaleX("+e+")"),V(n)&&1!==n&&a.push("scaleY("+n+")");var r=a.length?a.join(" "):"none";return{WebkitTransform:r,msTransform:r,transform:r}}var vt=o.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(o.navigator.userAgent);function gt(t,i){var e=document.createElement("img");if(t.naturalWidth&&!vt)return i(t.naturalWidth,t.naturalHeight),e;var n=document.body||document.documentElement;return e.onload=function(){i(e.width,e.height),vt||n.removeChild(e)},e.src=t.src,vt||(e.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(e)),e}function pt(t){switch(t){case 2:return r;case 3:return e;case 4:return i;default:return""}}function wt(t,i){var e=t.pageX,n=t.pageY,s={endX:e,endY:n};return i?s:G({startX:e,startY:n},s)}var bt={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t=this.options,i=this.parent,e=void 0;t.inline&&(e={width:Math.max(i.offsetWidth,t.minWidth),height:Math.max(i.offsetHeight,t.minHeight)},this.parentData=e),!this.fulled&&e||(e=this.containerData),this.viewerData=G({},e)},renderViewer:function(){this.options.inline&&!this.fulled&&tt(this.viewer,this.viewerData)},initList:function(){var a=this,t=this.element,r=this.options,i=this.list,h=[];Q(this.images,function(t,i){var e,n=t.src,s=t.alt||(H(e=n)?e.replace(/^.*\//,"").replace(/[?&#].*$/,""):""),o=r.url;H(o)?o=t.getAttribute(o):_(o)&&(o=o.call(a,t)),(n||o)&&h.push('<li><img src="'+(n||o)+'" role="button" data-action="view" data-index="'+i+'" data-original-url="'+(o||n)+'" alt="'+s+'"></li>')}),i.innerHTML=h.join(""),this.items=i.getElementsByTagName("li"),Q(this.items,function(i){var t=i.firstElementChild;ht(t,"filled",!0),r.loading&&et(i,v),ut(t,q,function(t){r.loading&&nt(i,v),a.loadImage(t)},{once:!0})}),r.transition&&ut(t,L,function(){et(i,D)},{once:!0})},renderList:function(t){var i=t||this.index,e=this.items[i].offsetWidth||30,n=e+1;tt(this.list,G({width:n*this.length},mt({translateX:(this.viewerData.width-e)/2-n*i})))},resetList:function(){var t=this.list;t.innerHTML="",nt(t,D),tt(t,mt({translateX:0}))},initImage:function(r){var h=this,l=this.options,t=this.image,i=this.viewerData,e=this.footer.offsetHeight,d=i.width,c=Math.max(i.height-e,e),u=this.imageData||{},n=void 0;this.imageInitializing={abort:function(){n.onload=null}},n=gt(t,function(t,i){var e=t/i,n=d,s=c;h.imageInitializing=!1,d<c*e?s=d/e:n=c*e;var o={naturalWidth:t,naturalHeight:i,aspectRatio:e,ratio:(n=Math.min(.9*n,t))/t,width:n,height:s=Math.min(.9*s,i),left:(d-n)/2,top:(c-s)/2},a=G({},o);l.rotatable&&(o.rotate=u.rotate||0,a.rotate=0),l.scalable&&(o.scaleX=u.scaleX||1,o.scaleY=u.scaleY||1,a.scaleX=1,a.scaleY=1),h.imageData=o,h.initialImageData=a,r&&r()})},renderImage:function(t){var i=this,e=this.image,n=this.imageData;if(tt(e,G({width:n.width,height:n.height,marginLeft:n.left,marginTop:n.top},mt(n))),t)if(this.viewing&&this.options.transition){var s=function(){i.imageRendering=!1,t()};this.imageRendering={abort:function(){ct(e,F,s)}},ut(e,F,s,{once:!0})}else t()},resetImage:function(){if(this.viewing||this.viewed){var t=this.image;this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null}}},yt={bind:function(){var t=this.element,i=this.viewer;ut(i,C,this.onClick=this.click.bind(this)),ut(i,W,this.onWheel=this.wheel.bind(this)),ut(i,M,this.onDragStart=this.dragstart.bind(this)),ut(this.canvas,Y,this.onPointerDown=this.pointerdown.bind(this)),ut(t.ownerDocument,X,this.onPointerMove=this.pointermove.bind(this)),ut(t.ownerDocument,R,this.onPointerUp=this.pointerup.bind(this)),ut(t.ownerDocument,N,this.onKeyDown=this.keydown.bind(this)),ut(window,O,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.viewer;ct(i,C,this.onClick),ct(i,W,this.onWheel),ct(i,M,this.onDragStart),ct(this.canvas,Y,this.onPointerDown),ct(t.ownerDocument,X,this.onPointerMove),ct(t.ownerDocument,R,this.onPointerUp),ct(t.ownerDocument,N,this.onKeyDown),ct(window,O,this.onResize)}},xt={click:function(t){var i=t.target,e=this.options,n=this.imageData;switch(rt(i,"action")){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.hide();break;case"view":this.view(rt(i,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-n.scaleX||-1);break;case"flip-vertical":this.scaleY(-n.scaleY||-1);break;default:this.played&&this.stop()}},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var i=this.element,e=this.options,n=this.image,s=this.index,o=this.viewerData;nt(n,x),e.loading&&nt(this.canvas,v),n.style.cssText="height:0;margin-left:"+o.width/2+"px;margin-top:"+o.height/2+"px;max-width:none!important;position:absolute;width:0;",this.initImage(function(){st(n,"viewer-move",e.movable),st(n,D,e.transition),t.renderImage(function(){t.viewed=!0,t.viewing=!1,_(e.viewed)&&ut(i,L,e.viewed,{once:!0}),ft(i,L,{originalImage:t.images[s],index:s,image:n})})})},loadImage:function(t){var o=t.target,i=o.parentNode,a=i.offsetWidth||30,r=i.offsetHeight||50,h=!!rt(o,"filled");gt(o,function(t,i){var e=t/i,n=a,s=r;a<r*e?h?n=r*e:s=a/e:h?s=a/e:n=r*e,tt(o,G({width:n,height:s},mt({translateX:(a-n)/2,translateY:(r-s)/2})))})},keydown:function(t){var i=this.options;if(this.fulled&&i.keyboard)switch(t.keyCode||t.which||t.charCode){case 27:this.played?this.stop():i.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.prev(i.loop);break;case 38:t.preventDefault(),this.zoom(i.zoomRatio,!0);break;case 39:this.next(i.loop);break;case 40:t.preventDefault(),this.zoom(-i.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle())}},dragstart:function(t){"img"===t.target.tagName.toLowerCase()&&t.preventDefault()},pointerdown:function(t){var i=this.options,e=this.pointers;if(this.viewed&&!this.showing&&!this.viewing&&!this.hiding){t.changedTouches?Q(t.changedTouches,function(t){e[t.identifier]=wt(t)}):e[t.pointerId||0]=wt(t);var n=!!i.movable&&l;1<Object.keys(e).length?n=u:"touch"!==t.pointerType&&"touchstart"!==t.type||!this.isSwitchable()||(n=c),this.action=n}},pointermove:function(t){var i=this.options,e=this.pointers,n=this.action,s=this.image;this.viewed&&n&&(t.preventDefault(),t.changedTouches?Q(t.changedTouches,function(t){G(e[t.identifier],wt(t,!0))}):G(e[t.pointerId||0],wt(t,!0)),n===l&&i.transition&&it(s,D)&&nt(s,D),this.change(t))},pointerup:function(t){var i=this.action,e=this.pointers;t.changedTouches?Q(t.changedTouches,function(t){delete e[t.identifier]}):delete e[t.pointerId||0],i&&(i===l&&this.options.transition&&et(this.image,D),this.action=!1)},resize:function(){var i=this;if(this.isShown&&!this.hiding&&(this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){i.renderImage()}),this.played)){if(this.options.fullscreen&&this.fulled&&!document.fullscreenElement&&!document.mozFullScreenElement&&!document.webkitFullscreenElement&&!document.msFullscreenElement)return void this.stop();Q(this.player.getElementsByTagName("img"),function(t){ut(t,q,i.loadImage.bind(i),{once:!0}),ft(t,q)})}},wheel:function(t){var i=this;if(this.viewed&&(t.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50);var e=Number(this.options.zoomRatio)||.1,n=1;t.deltaY?n=0<t.deltaY?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=0<t.detail?1:-1),this.zoom(-n*e,!0,t)}}},Dt={show:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=this.element,e=this.options;if(e.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(_(e.show)&&ut(i,k,e.show,{once:!0}),!1===ft(i,k)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(nt(n,y),e.transition&&!t){var s=this.shown.bind(this);this.transitioning={abort:function(){ct(n,F,s),nt(n,m)}},et(n,D),n.offsetWidth,ut(n,F,s,{once:!0}),et(n,m)}else et(n,m),this.shown();return this},hide:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=this.element,e=this.options;if(e.inline||this.hiding||!this.isShown&&!this.showing)return this;if(_(e.hide)&&ut(i,S,e.hide,{once:!0}),!1===ft(i,S))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var n=this.viewer;if(e.transition&&!t){var s=this.hidden.bind(this),o=function(){ut(n,F,s,{once:!0}),nt(n,m)};this.transitioning={abort:function(){this.viewed?ct(this.image,F,o):ct(n,F,s)}},this.viewed?(ut(this.image,F,o,{once:!0}),this.zoomTo(0,!1,!1,!0)):o()}else nt(n,m),this.hidden();return this},view:function(){var i=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;if(t=Number(t)||0,!this.isShown)return this.index=t,this.show();if(this.hiding||this.played||t<0||t>=this.length||this.viewed&&t===this.index)return this;this.viewing&&this.viewing.abort();var e=this.element,n=this.options,s=this.title,o=this.canvas,a=this.items[t],r=a.querySelector("img"),h=rt(r,"originalUrl"),l=r.getAttribute("alt"),d=document.createElement("img");if(d.src=h,d.alt=l,_(n.view)&&ut(e,T,n.view,{once:!0}),!1===ft(e,T,{originalImage:this.images[t],index:t,image:d})||!this.isShown||this.hiding||this.played)return this;this.image=d,nt(this.items[this.index],f),et(a,f),this.viewed=!1,this.index=t,this.imageData={},et(d,x),n.loading&&et(o,v),o.innerHTML="",o.appendChild(d),this.renderList(),s.innerHTML="";var c=function(){var t=i.imageData;s.textContent=l+" ("+t.naturalWidth+" × "+t.naturalHeight+")"},u=void 0;return ut(e,L,c,{once:!0}),this.viewing={abort:function(){ct(e,L,c),d.complete?this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort():(ct(d,q,u),this.timeout&&clearTimeout(this.timeout))}},d.complete?this.load():(ut(d,q,u=this.load.bind(this),{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){nt(d,x),i.timeout=!1},1e3)),this},prev:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=this.index-1;return i<0&&(i=t?this.length-1:0),this.view(i),this},next:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=this.length-1,e=this.index+1;return i<e&&(e=t?0:i),this.view(e),this},move:function(t,i){var e=this.imageData;return this.moveTo(K(t)?t:e.left+Number(t),K(i)?i:e.top+Number(i)),this},moveTo:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,e=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&this.options.movable){var n=!1;V(t)&&(e.left=t,n=!0),V(i)&&(e.top=i,n=!0),n&&this.renderImage()}return this},zoom:function(t){var i=1<arguments.length&&void 0!==arguments[1]&&arguments[1],e=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,n=this.imageData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(n.width*t/n.naturalWidth,i,e),this},zoomTo:function(t){var n,s,o,i,e,a=1<arguments.length&&void 0!==arguments[1]&&arguments[1],r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,h=3<arguments.length&&void 0!==arguments[3]&&arguments[3],l=this.options,d=this.pointers,c=this.imageData;if(V(t=Math.max(0,t))&&this.viewed&&!this.played&&(h||l.zoomable)){if(!h){var u=Math.max(.01,l.minZoomRatio),f=Math.min(100,l.maxZoomRatio);t=Math.min(Math.max(t,u),f)}r&&.95<t&&t<1.05&&(t=1);var m=c.naturalWidth*t,v=c.naturalHeight*t;if(r){var g=(i=this.viewer,{left:(e=i.getBoundingClientRect()).left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}),p=d&&Object.keys(d).length?(o=s=n=0,Q(d,function(t){var i=t.startX,e=t.startY;n+=i,s+=e,o+=1}),{pageX:n/=o,pageY:s/=o}):{pageX:r.pageX,pageY:r.pageY};c.left-=(m-c.width)*((p.pageX-g.left-c.left)/c.width),c.top-=(v-c.height)*((p.pageY-g.top-c.top)/c.height)}else c.left-=(m-c.width)/2,c.top-=(v-c.height)/2;c.width=m,c.height=v,c.ratio=t,this.renderImage(),a&&this.tooltip()}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var i=this.imageData;return V(t=Number(t))&&this.viewed&&!this.played&&this.options.rotatable&&(i.rotate=t,this.renderImage()),this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,e=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&this.options.scalable){var n=!1;V(t)&&(e.scaleX=t,n=!0),V(i)&&(e.scaleY=i,n=!0),n&&this.renderImage()}return this},play:function(){var i=this,t=0<arguments.length&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var s=this.options,o=this.player,a=this.loadImage.bind(this),r=[],h=0,l=0;if(this.played=!0,this.onLoadWhenPlay=a,t&&this.requestFullscreen(),et(o,g),Q(this.items,function(t,i){var e=t.querySelector("img"),n=document.createElement("img");n.src=rt(e,"originalUrl"),n.alt=e.getAttribute("alt"),h+=1,et(n,w),st(n,D,s.transition),it(t,f)&&(et(n,m),l=i),r.push(n),ut(n,q,a,{once:!0}),o.appendChild(n)}),V(s.interval)&&0<s.interval){var e=function t(){i.playing=setTimeout(function(){nt(r[l],m),et(r[l=(l+=1)<h?l:0],m),t()},s.interval)};1<h&&e()}return this},stop:function(){var i=this;if(!this.played)return this;var t=this.player;return this.played=!1,clearTimeout(this.playing),Q(t.getElementsByTagName("img"),function(t){ct(t,q,i.onLoadWhenPlay)}),nt(t,g),t.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,i=this.options,e=this.viewer,n=this.image,s=this.list;return!this.isShown||this.played||this.fulled||!i.inline||(this.fulled=!0,this.open(),et(this.button,a),i.transition&&(nt(s,D),this.viewed&&nt(n,D)),et(e,b),e.setAttribute("style",""),tt(e,{zIndex:i.zIndex}),this.initContainer(),this.viewerData=G({},this.containerData),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){et(n,D),et(s,D)},0)})})),this},exit:function(){var t=this,i=this.options,e=this.viewer,n=this.image,s=this.list;return this.isShown&&!this.played&&this.fulled&&i.inline&&(this.fulled=!1,this.close(),nt(this.button,a),i.transition&&(nt(s,D),this.viewed&&nt(n,D)),nt(e,b),tt(e,{zIndex:i.zIndexInline}),this.viewerData=G({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){et(n,D),et(s,D)},0)})})),this},tooltip:function(){var t=this,i=this.options,e=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&i.tooltip&&(e.textContent=Math.round(100*n.ratio)+"%",this.tooltipping?clearTimeout(this.tooltipping):i.transition?(this.fading&&ft(e,F),et(e,g),et(e,w),et(e,D),e.offsetWidth,et(e,m)):et(e,g),this.tooltipping=setTimeout(function(){i.transition?(ut(e,F,function(){nt(e,g),nt(e,w),nt(e,D),t.fading=!1},{once:!0}),nt(e,m),t.fading=!0):nt(e,g),t.tooltipping=!1},1e3)),this},toggle:function(){return 1===this.imageData.ratio?this.zoomTo(this.initialImageData.ratio,!0):this.zoomTo(1,!0),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=G({},this.initialImageData),this.renderImage()),this},update:function(){var t=this.element,i=this.options,e=this.isImg;if(e&&!t.parentNode)return this.destroy();var s=[];if(Q(e?[t]:t.querySelectorAll("img"),function(t){i.filter?i.filter(t)&&s.push(t):s.push(t)}),!s.length)return this;if(this.images=s,this.length=s.length,this.ready){var o=[];if(Q(this.items,function(t,i){var e=t.querySelector("img"),n=s[i];n?n.src!==e.src&&o.push(i):o.push(i)}),tt(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var n=o.indexOf(this.index);0<=n?(this.viewed=!1,this.view(Math.max(this.index-(n+1),0))):et(this.items[this.index],f)}}else this.image=null,this.viewed=!1,this.index=0,this.imageData=null,this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var t=this.element,i=this.options;return rt(t,p)&&(this.destroyed=!0,this.ready?(this.played&&this.stop(),i.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):i.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),i.inline||ct(t,C,this.onStart),function(i,e){if(U(i[e]))try{delete i[e]}catch(t){i[e]=void 0}else if(i.dataset)try{delete i.dataset[e]}catch(t){i.dataset[e]=void 0}else i.removeAttribute("data-"+at(e))}(t,p)),this}},zt={open:function(){var t=this.body;et(t,h),t.style.paddingRight=this.scrollbarWidth+(parseFloat(this.initialBodyPaddingRight)||0)+"px"},close:function(){var t=this.body;nt(t,h),t.style.paddingRight=this.initialBodyPaddingRight},shown:function(){var t=this.element,i=this.options;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,_(i.shown)&&ut(t,E,i.shown,{once:!0}),!1!==ft(t,E)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,i=this.options;this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),et(this.viewer,y),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(_(i.hidden)&&ut(t,I,i.hidden,{once:!0}),ft(t,I))},requestFullscreen:function(){var t=this.element.ownerDocument;if(this.fulled&&!t.fullscreenElement&&!t.mozFullScreenElement&&!t.webkitFullscreenElement&&!t.msFullscreenElement){var i=t.documentElement;i.requestFullscreen?i.requestFullscreen():i.msRequestFullscreen?i.msRequestFullscreen():i.mozRequestFullScreen?i.mozRequestFullScreen():i.webkitRequestFullscreen&&i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}},exitFullscreen:function(){if(this.fulled){var t=this.element.ownerDocument;t.exitFullscreen?t.exitFullscreen():t.msExitFullscreen?t.msExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.webkitExitFullscreen&&t.webkitExitFullscreen()}},change:function(t){var i,e,h,n=this.options,s=this.pointers,o=s[Object.keys(s)[0]],a=o.endX-o.startX,r=o.endY-o.startY;switch(this.action){case l:this.move(a,r);break;case u:this.zoom((e=G({},i=s),h=[],Q(i,function(r,t){delete e[t],Q(e,function(t){var i=Math.abs(r.startX-t.startX),e=Math.abs(r.startY-t.startY),n=Math.abs(r.endX-t.endX),s=Math.abs(r.endY-t.endY),o=Math.sqrt(i*i+e*e),a=(Math.sqrt(n*n+s*s)-o)/o;h.push(a)})}),h.sort(function(t,i){return Math.abs(t)<Math.abs(i)}),h[0]),!1,t);break;case c:this.action="switched",this.pointers={},Math.abs(a)>Math.abs(r)&&(1<a?this.prev(n.loop):a<-1&&this.next(n.loop))}Q(s,function(t){t.startX=t.endX,t.startY=t.endY})},isSwitchable:function(){var t=this.imageData,i=this.viewerData;return 1<this.length&&0<=t.left&&0<=t.top&&t.width<=i.width&&t.height<=i.height}},kt=o.Viewer,Et=function(){function e(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(j(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=G({},n,$(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.index=0,this.isImg=!1,this.length=0,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.isShown=!1,this.wheeling=!1,this.init()}return t(e,[{key:"init",value:function(){var e=this,t=this.element,i=this.options;if(!rt(t,p)){ht(t,p,this);var n="img"===t.tagName.toLowerCase(),s=[];if(Q(n?[t]:t.querySelectorAll("img"),function(t){_(i.filter)?i.filter.call(e,t)&&s.push(t):s.push(t)}),s.length){this.isImg=n,this.length=s.length,this.images=s;var o=t.ownerDocument,a=o.body||o.documentElement;if(this.body=a,this.scrollbarWidth=window.innerWidth-o.documentElement.clientWidth,this.initialBodyPaddingRight=window.getComputedStyle(a).paddingRight,K(document.createElement(p).style.transition)&&(i.transition=!1),i.inline){var r=0,h=function(){if((r+=1)===e.length){var t=void 0;e.initializing=!1,e.delaying={abort:function(){clearTimeout(t)}},t=setTimeout(function(){e.delaying=!1,e.build()},0)}};this.initializing={abort:function(){Q(s,function(t){t.complete||ct(t,q,h)})}},Q(s,function(t){t.complete?h():ut(t,q,h,{once:!0})})}else ut(t,C,this.onStart=function(t){var i=t.target;"img"===i.tagName.toLowerCase()&&e.view(e.images.indexOf(i))})}}}},{key:"build",value:function(){if(!this.ready){var t=this.element,h=this.options,i=t.parentNode,e=document.createElement("div");e.innerHTML='<div class="viewer-container" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list"></ul></div></div><div class="viewer-tooltip"></div><div role="button" class="viewer-button" data-action="mix"></div><div class="viewer-player"></div></div>';var n=e.querySelector("."+p+"-container"),s=n.querySelector("."+p+"-title"),o=n.querySelector("."+p+"-toolbar"),a=n.querySelector("."+p+"-navbar"),r=n.querySelector("."+p+"-button"),l=n.querySelector("."+p+"-canvas");if(this.parent=i,this.viewer=n,this.title=s,this.toolbar=o,this.navbar=a,this.button=r,this.canvas=l,this.footer=n.querySelector("."+p+"-footer"),this.tooltipBox=n.querySelector("."+p+"-tooltip"),this.player=n.querySelector("."+p+"-player"),this.list=n.querySelector("."+p+"-list"),et(s,h.title?pt(h.title):y),et(a,h.navbar?pt(h.navbar):y),st(r,y,!h.button),h.backdrop&&(et(n,p+"-backdrop"),h.inline||!0!==h.backdrop||ht(l,"action","hide")),h.toolbar){var d=document.createElement("ul"),c=$(h.toolbar),u=P.slice(0,3),f=P.slice(7,9),m=P.slice(9);c||et(o,pt(h.toolbar)),Q(c?h.toolbar:P,function(t,i){var e=c&&$(t),n=c?at(i):t,s=e&&!K(t.show)?t.show:t;if(s&&(h.zoomable||-1===u.indexOf(n))&&(h.rotatable||-1===f.indexOf(n))&&(h.scalable||-1===m.indexOf(n))){var o=e&&!K(t.size)?t.size:t,a=e&&!K(t.click)?t.click:t,r=document.createElement("li");r.setAttribute("role","button"),et(r,p+"-"+n),_(a)||ht(r,"action",n),V(s)&&et(r,pt(s)),-1!==["small","large"].indexOf(o)?et(r,p+"-"+o):"play"===n&&et(r,p+"-large"),_(a)&&ut(r,C,a),d.appendChild(r)}}),o.appendChild(d)}else et(o,y);if(!h.rotatable){var v=o.querySelectorAll('li[class*="rotate"]');et(v,x),Q(v,function(t){o.appendChild(t)})}if(h.inline)et(r,"viewer-fullscreen"),tt(n,{zIndex:h.zIndexInline}),"static"===window.getComputedStyle(i).position&&tt(i,{position:"relative"}),i.insertBefore(n,t.nextSibling);else{et(r,"viewer-close"),et(n,b),et(n,w),et(n,y),tt(n,{zIndex:h.zIndex});var g=h.container;H(g)&&(g=t.ownerDocument.querySelector(g)),g||(g=this.body),g.appendChild(n)}h.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,_(h.ready)&&ut(t,z,h.ready,{once:!0}),!1!==ft(t,z)?this.ready&&h.inline&&this.view():this.ready=!1}}}],[{key:"noConflict",value:function(){return window.Viewer=kt,e}},{key:"setDefaults",value:function(t){G(n,$(t)&&t)}}]),e}();if(G(Et.prototype,bt,yt,xt,Dt,zt),d.fn){var St=d.fn.viewer,It="viewer";d.fn.viewer=function(r){for(var t=arguments.length,h=Array(1<t?t-1:0),i=1;i<t;i++)h[i-1]=arguments[i];var l=void 0;return this.each(function(t,i){var e=d(i),n="destroy"===r,s=e.data(It);if(!s){if(n)return;var o=d.extend({},e.data(),d.isPlainObject(r)&&r);s=new Et(i,o),e.data(It,s)}if("string"==typeof r){var a=s[r];d.isFunction(a)&&((l=a.apply(s,h))===s&&(l=void 0),n&&e.removeData(It))}}),void 0!==l?l:this},d.fn.viewer.Constructor=Et,d.fn.viewer.setDefaults=Et.setDefaults,d.fn.viewer.noConflict=function(){return d.fn.viewer=St,this}}});