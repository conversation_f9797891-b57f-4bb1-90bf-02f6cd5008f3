"use strict";
; (function ($, window, document, undefined) {

    var defaults = {
    }
    var SelectPullDownData;//数据存储
    var pubdata = {};
    var focusVal = '';

    var selectOwn = function (dom, options) {
        this.init(dom, options);
    }

    selectOwn.prototype.init = function (dom, options) {
        var that = this;
        var self = dom;
        var text = '',
            placeholder = '--请选择--',
            textname = options.textName || '',
            valname = options.valName || '';
        var searValue = '';
        var inpTextId = options.inpTextId || options.inpTextName || '';
        var inpValId = options.inpValId || options.inpValName || '';
        pubdata[dom] = options;
        pubdata.choice = [];
        var html = '';
        if(options.choice === true){
            html = '<div class="tra-lay-select-title pubSelectPesonBox"><input type="text"  autocomplete="off" data-key="' + dom + '" placeholder="' + placeholder + '" value="' + text + '" name="' + options.inpTextName + '" class="layui-input SelectPullDown" id="' + inpTextId + '"><input type="hidden" placeholder=""  autocomplete="off" name="' + options.inpValName + '" value="" id="' + inpValId + '"><i class="icon"></i></div>';
        }else{
            html = '<div class="tra-lay-select-title pubSelectPesonBox"><input type="text"  autocomplete="off" data-key="' + dom + '" placeholder="' + placeholder + '" value="' + text + '" name="' + options.inpTextName + '" class="layui-input SelectPullDown" id="' + inpTextId + '"><input type="hidden" placeholder=""  autocomplete="off" name="' + options.inpValName + '" value="" id="' + inpValId + '"><i class="icon"></i></div>';   
        }

        $(self).html(html);
        if(options.callback){
            options.callback(false);
        }
        button(dom, options);
    }

    
    //数据请求
    var ajaxSend = function(opt){
        var data = opt.data;
        if(opt.contentType){
            $.ajax({
                type:opt.datatype || 'post',
                url:opt.url,
                contentType:opt.contentType,
                data:data,
                success:function (res) {
                    opt.success(res);
                }
            });
        }else{
            $.ajax({
                type:opt.datatype || 'post',
                url:opt.url,
                data:data,
                success:function (res) {
                    opt.success(res);
                }
            });
        }
    }

    var button = function(dom, options){

        //加载下拉框
        $('body').off('click','.SelectPullDown').on('click','.SelectPullDown',function(e){
            e.stopPropagation();
            $('#pubSelectPesonDl').remove();
            $('.SelectPullDown').removeClass('SelectPullDownId');

            var $box = $(this).closest('.pubSelectPesonBox');
            var _left = $(this).offset().left;
            var _top = $(this).offset().top;
            var h = $(window).height();
            var w = $(this).width() + 5;
            var tb = '';
            var keys = $(this).attr('data-key');
            var options = pubdata[keys];
            focusVal = $(this).val();
            pubdata.selDatas = [];
            pubdata.choice = [];
            pubdata.keys = keys;
            pubdata.pagetype = true;
            if($('#pubSelectPesonDl').length > 0){
                return false
            }
            $('.SelectPullDown').removeClass('SelectPullDownId');
            // $(this).attr('id','SelectPullDown');
            $(this).addClass('SelectPullDownId');
            if(w < 200){
                w = 200;
            }
            if(_top < h - 200){
                tb = 'top:' + _top + 'px;';
            }else{
                var _w = h - _top + 235;
                tb = 'bottom:' + _w + 'px;top:auto;';
            }
            $('#pubSelectPesonDl').remove();
            var data = {}
            if(options.data){
                // data = options.data;
            }
            data.pageNo = 1;
            var htmls = '<div class="tra-lay-select-dl" id="pubSelectPesonDl" page="0" style="width:' + w + 'px;left:' + _left +'px;' + tb +'">\
                            <dl class="layui-anim layui-anim-upbit" style="" >\
                                <dd lay-value="" class="layui-select-tips ">加载中.....</dd>\
                            </dl>\
                        </div>';
            $('body').append(htmls);
            // var datasss = localStorage.getItem('SelectPullData');
            
            if(options.searchType == 'json'){
                ajaxSend({
                    data:data,
                    url:options.url,
                    contentType:options.contentType,
                    datatype:options.dataType,
                    success:function(res){
                        if (res.rows != null && options.searchType != 'local') {
                            //后台检索
                            var html = '';
                            pubdata.selDatas = res.rows; //存数据
                            $.each(res.rows,function(i,v){
                                var t = v[options.textName];
                                if(options.layout == '-'){
                                    t = v[options.valName] + '-' + t;
                                }
                                // html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                                if(options.choice === true){
                                    var vs =  $('[name="'+ options.inpValName +'"]').val()
                                    vs = vs.split(',');
                                    var c = '';
                                    for(var n in vs){
                                        if(v[options.valName] == vs[n]){
                                            c = 'select';
                                            continue
                                        }
                                    }
                                    html += '<dd lay-value="' + v[options.valName] + '" class="' + c + '">' + t + '</dd>';    
                                }else{
                                    html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                                }
                            })
                            $('#pubSelectPesonDl').attr('page',res.pageNo);
                            $('#pubSelectPesonDl dl').html(html);
                            scrollfun(dom, options);
                        }else if(res.object != null && options.searchType == 'local') {
                            //本地检索
                            SelectPullDownData = res.object;
                            pubdata.selDatas = res.object
                            $.each(SelectPullDownData,function(i,v){
                                var z = v.pinying.toUpperCase();
                                var c = v.pinying.toLowerCase();
                                v.pinying = z + c;
                                SelectPullDownData[i].pinying = v.pinying;
                            })
                            var html = '';
                            var p = $('#pubSelectPesonDl').attr('page');
                            localStorage.setItem('SelectPullData', JSON.stringify(res.object));
                            if(p == 0) p = 1
                            var num = 20 * parseInt(p);
                            $.each(res.object,function(i,v){
                                if(i < num){
                                    var t = v[options.textName];
                                    if(options.layout == '-'){
                                        t = v[options.valName] + '-' + t;
                                    }
                                    if(options.choice === true){
                                        var vs =  $('[name="'+ options.inpValName +'"]').val()
                                        vs = vs.split(',');
                                        var c = '';
                                        for(var n in vs){
                                            if(v[options.valName] == vs[n]){
                                                c = 'select';
                                                continue
                                            }
                                        }
                                        html += '<dd lay-value="' + v[options.valName] + '" class="' + c + '">' + t + '</dd>';    
                                    }else{
                                        html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                                    }
                                    
                                }
                            })
                            p ++
                            $('#pubSelectPesonDl').attr('page',p);
                            $('#pubSelectPesonDl dl').html(html);
                            scrollfun(dom, options);
                        } else if (res.object != null) {
                            var html = '';
                            pubdata.selDatas = res.object; //存数据
                            $.each(res.object,function(i,v){
                                if(options.choice === true){
                                    var vs =  $('[name="'+ options.inpValName +'"]').val()
                                    vs = vs.split(',');
                                    var c = '';
                                    for(var n in vs){
                                        if(v[options.valName] == vs[n]){
                                            c = 'select';
                                            continue
                                        }
                                    }
                                    html += '<dd lay-value="' + v[options.valName] + '" class="' + c + '">' + v[options.textName] + '</dd>';    
                                }else{
                                    html += '<dd lay-value="' + v[options.valName] + '" class="">' + v[options.textName] + '</dd>';
                                }
                            })
                            $('#pubSelectPesonDl').attr('page',res.pageNo);
                            $('#pubSelectPesonDl dl').html(html);
                            scrollfun(dom, options);
                        } else {
                            console.log('数据获取失败！');
                        }
                    }
                });
            }else if(options.searchType == 'local'){
                var html = '';
                var p = $('#pubSelectPesonDl').attr('page');
                if(p == 0) p = 1
                var num = 20 * parseInt(p);
                SelectPullDownData = options.datas
                pubdata.selDatas = options.datas
                $.each(SelectPullDownData,function(i,v){
                    if(i < num){
                        var t = v[options.textName];
                        if(options.layout == '-'){
                            t = v[options.valName] + '-' + t;
                        }
                        if(options.choice === true){
                            var vs =  $('[name="'+ options.inpValName +'"]').val()
                            vs = vs.split(',');
                            var c = '';
                            for(var n in vs){
                                if(v[options.valName] == vs[n]){
                                    c = 'select';
                                    pubdata.choice.push(v);
                                    continue
                                }
                            }
                            html += '<dd lay-value="' + v[options.valName] + '" class="' + c + '">' + t + '</dd>';    
                        }else{
                            html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                        }
                        // html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                    }
                })
                p ++
                $('#pubSelectPesonDl').attr('page',p);
                $('#pubSelectPesonDl dl').html(html);
                scrollfun(dom, options);
            }
        }).off('blur','.SelectPullDown').on('blur','.SelectPullDown',function(){
            $(this).val(focusVal);
        });

        //下拉框数据点击事件
        $(document).off('click','#pubSelectPesonDl dd').on('click','#pubSelectPesonDl dd',function(e){
            e.stopPropagation();
            var value = $(this).attr('lay-value');
            var name = $(this).text();
            var data = {};
            var keys = pubdata.keys;
            var options = pubdata[keys];
            
            $('.SelectPullDownId').val(name);
            $('.SelectPullDownId').next().val(value);
            $.each(pubdata.selDatas,function(i,v){
                if(v[options.valName] == value){
                    data = v;
                }
            })
            if(options.choice === true){
                var s = $(this).hasClass('select');
                var vl = $(this).attr('lay-value');
                if(s === true){
                    $(this).removeClass('select');
                    for(var i in pubdata.choice){
                        if(vl == pubdata.choice[i][options.valName]){
                            pubdata.choice.splice(i,1);
                        }
                    }
                }else{
                    $(this).addClass('select');
                    pubdata.choice.push(data);
                }
                var _n = '',
                    _v = '';
                $.each(pubdata.choice,function(i,v){
                    if(i == 0){
                        _n += v[options.textName];
                        _v += v[options.valName];
                    }else{
                        _n += ',' + v[options.textName];
                        _v += ',' + v[options.valName];
                    }
                })
                $('[name="' + options.inpTextName + '"]').val(_n);
                $('[name="' + options.inpValName + '"]').val(_v);
                if(options.callback){
                    options.callback(pubdata.choice);
                }
            }else{
                $('#pubSelectPesonDl').remove();
                // $('.SelectPullDown').addClass('SelectPullDownId');
                $('.SelectPullDown').removeClass('SelectPullDownId');
                if(options.callback){
                    options.callback(data);
                }
            }
            
        })

        $(document).on('click',function(){
            $('#pubSelectPesonDl').remove();
            $('.SelectPullDown').removeClass('SelectPullDownId');
            // localStorage.removeItem('SelectPullData');
        })

    }

    var scrollfun = function(dom, options){
        //滚动加载
        $('#pubSelectPesonDl dl').on('scroll',function(){
            var yScroll = $(this).scrollTop();
            var h = $(this)[0].scrollHeight;
            var $s = $(this);
            var keys = pubdata.keys;
            var options = pubdata[keys];
            var nval = $('.SelectPullDownId').val();
            if(pubdata.scorllbull === false){
                return false;
            }
            if(h - 200 < yScroll && options.searchType != 'local'){
                var page = $('#pubSelectPesonDl').attr('page');
                page ++
                if(pubdata.pagetype != false){
                    var data = {}
                    if(options.data){
                        data = options.data;
                    }
                    data.pageNo = page;
                    if(options.searName){
                        data[options.searName] = searValue;
                    }
                    pubdata.scorllbull = false;
                    ajaxSend({
                        data:data,
                        url:options.url,
                        contentType:options.contentType,
                        datatype:options.dataType,
                        success:function(res){
                            if (res.rows != null) {
                                pubdata.scorllbull = true;
                                if(res.rows.length == 0){
                                    $('#pubSelectPesonDl dl').append('<dt style="color:#999999;text-align:center;">已加载完成</dt>');
                                    pubdata.pagetype = false;
                                }else{
                                    var html = '';
                                    $('#pubSelectPesonDl').attr('page',res.pageNo);
                                    $.each(res.rows,function(i,v){
                                        var t = v[options.textName];
                                        if(options.layout == '-'){
                                            t = v[options.valName] + '-' + t;
                                        }
                                        html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                                        pubdata.selDatas.push(v);
                                    })
                                    $('#pubSelectPesonDl dl').append(html);
                                }
                            } else if (res.object != null) {
                            } else {
                                console.log('数据获取失败！');
                            }
                        }
                    });
                }
            }else if(h - 200 < yScroll && options.searchType == 'local'){
                var p = $('#pubSelectPesonDl').attr('page');
                var data = localStorage.getItem('SelectPullData');
                data = JSON.parse(data);
                data = inputChange(data,searValue);
                pubdata.selDatas = data;
                pubdata.scorllbull = true;
                if(pubdata.pagetype != false){
                    pubdata.pagetype = false;
                    if(p == 1) p = 2
                    var num = 20 * parseInt(p);
                    // var html = '<dd lay-value="" class="layui-select-tips">---请选择---</dd>';
                    var html = '';
                    var len = data.length;
                    $.each(data,function(i,v){
                        if(i < num){
                            var t = v[options.textName];
                            if(options.layout == '-'){
                                t = v[options.valName] + '-' + t;
                            }
                            html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                        }else{
                            
                        }
                    })

                    if(num >= len) {
                        html += '<dt style="color:#999999;text-align:center;">加载完成</dt>';
                        pubdata.scorllbull = false;
                    }else{
                        p ++
                        pubdata.scorllbull = true;
                    }
                    $('#pubSelectPesonDl').attr('page',p);
                    $('#pubSelectPesonDl dl').html(html);
                    setTimeout(function(){
                        pubdata.pagetype = true;
                    },500)
                }
            }
        });
        //输入搜索
        $('.SelectPullDownId').on('keyup',function(){
            var $s = $(this);
            var txt = $(this).val();
            var keys = pubdata.keys;
            var options = pubdata[keys];
            if(options.searchType != 'local'){
                var page = $('#pubSelectPesonDl').attr('page');
                searValue = txt;
                pubdata.scorllbull = true;
                var data = {}
                page ++
                if(options.data){
                    data = options.data;
                }
                data.pageNo = 1;
                if(options.condition){
                    data[options.condition] = txt;
                }else{
                    data[options.textName] = txt;
                }
                ajaxSend({
                    data:data,
                    url:options.url,
                    contentType:options.contentType,
                    datatype:options.dataType,
                    success:function(res){
                        if (res.rows != null) {
                            pubdata.selDatas = res.rows;
                            // var html = '<dd lay-value="" class="layui-select-tips">---请选择---</dd>';
                            var html = '';
                            $('#pubSelectPesonDl').attr('page',res.pageNo);
                            $.each(res.rows,function(i,v){
                                var t = v[options.textName];
                                if(options.layout == '-'){
                                    t = v[options.valName] + '-' + t;
                                }
                                html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                            })
                            $('#pubSelectPesonDl dl').html(html);
                        } else if (res.object != null) {
                            pubdata.selDatas = res.object;
                            // var html = '<dd lay-value="" class="layui-select-tips">---请选择---</dd>';
                            var html = '';
                            $('#pubSelectPesonDl').attr('page',res.pageNo);
                            $.each(res.object,function(i,v){
                                var t = v[options.textName];
                                if(options.layout == '-'){
                                    t = v[options.valName] + '-' + t;
                                }
                                html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                            })
                            $('#pubSelectPesonDl dl').html(html);
                        } else {
                            console.log('数据获取失败！');
                        }
                    }
                });
            }else if(options.searchType == 'local'){
                var data = localStorage.getItem('SelectPullData');
                data = JSON.parse(data);
                data = inputChange(data,txt);
                searValue = txt;

                // var html = '<dd lay-value="" class="layui-select-tips">---请选择---</dd>';
                // $.each(data,function(i,v){
                //     var t = v[options.textName];
                //     if(options.layout == '-'){
                //         t = v[options.valName] + '-' + t;
                //     }
                //     html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                // })

                // $('#pubSelectPesonDl dl').html(html);
                // var html = '<dd lay-value="" class="layui-select-tips">---请选择---</dd>';
                var html = '';
                $.each(data,function(i,v){
                    if(i < 20){
                        var t = v[options.textName];
                        if(options.layout == '-'){
                            t = v[options.valName] + '-' + t;
                        }
                        html += '<dd lay-value="' + v[options.valName] + '" class="">' + t + '</dd>';
                    }
                })
                $('#pubSelectPesonDl').attr('page',1);
                $('#pubSelectPesonDl dl').html(html);
            }
        });
    } 

    var inputChange = function(datas, inputValue) {
        if(inputValue != ''){
            var matcher = new RegExp(inputValue);
            var arr =  $.grep(datas, function(value) {
                return matcher.test(value.projectName);
            });
            var arr1 = $.grep(datas, function(value) {
                return matcher.test(value.pinying);
            });
            return arr.concat(arr1)
        }else{
            return datas
        }
    };
    
    window.selectOwn = selectOwn;
    $.selectOwn = selectOwn;
})(jQuery, window, document);