package cn.trasen.homs.base.controller;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.FriendlyLink;
import cn.trasen.homs.base.service.FriendlyLinkService;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * @ClassName:  FriendlyLinkController   
 * @Description:友情链接
 * @author: YueC
 * @date:   2020年4月20日 下午4:52:20      
 * @Copyright:
 */
@Api(tags = "友情链接管理")
@RestController
public class FriendlyLinkController {

	private static final Logger logger = LoggerFactory.getLogger(FriendlyLinkController.class);
	
	@Resource
	private FriendlyLinkService friendlyLinkService;
	
	/**
	 * 
	 * @Title: insert   
	 * @Description: 新增友情链接   
	 * @param: @param record
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: YueC
	 * @date:   2020年4月20日 下午5:10:45    
	 * @throws
	 */
	@ApiOperation(value = "新增友情链接", notes = "新增友情链接")
	@PostMapping("/friendlyLink/save")
	@NoRepeatSubmit(lockTime = 2)
	public PlatformResult<String> insert(FriendlyLink record) {
		try {
			friendlyLinkService.insert(record);
			return PlatformResult.success("新增成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("新增失败,失败原因:"+e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title: update   
	 * @Description: 修改友情链接   
	 * @param: @param record
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: YueC
	 * @date:   2020年4月20日 下午5:10:50    
	 * @throws
	 */
	@ApiOperation(value = "修改友情链接", notes = "修改友情链接")
	@PostMapping("/friendlyLink/update")
	@NoRepeatSubmit(lockTime = 2)
	public PlatformResult<String> update(FriendlyLink record) {
		try {
			friendlyLinkService.update(record);
			return PlatformResult.success("修改成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("修改失败,失败原因:"+e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title: getDataList   
	 * @Description: 查询信息栏目列表   
	 * @param: @param page
	 * @param: @param record
	 * @param: @return      
	 * @return: DataSet<InformationChannel>  
	 * @author: YueC
	 * @date:   2020年2月3日 下午3:12:29    
	 * @throws
	 */
	@ApiOperation(value = "分页查询友情链接列表", notes = "分页查询友情链接列表")
	@GetMapping("/friendlyLink/list")
	public DataSet<FriendlyLink> getDataList(Page page, FriendlyLink record) {
		try {
			List<FriendlyLink> list = friendlyLinkService.getDataList(page, record);
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
		}catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
	}
	
	/**
	 * 
	 * @Title: deleteById   
	 * @Description: 删除友情链接   
	 * @param: @param id
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: YueC
	 * @date:   2020年4月20日 下午6:21:13    
	 * @throws
	 */
	@ApiOperation(value = "删除友情链接", notes = "删除友情链接  ")
	@PostMapping("/friendlyLink/deletedById")
	@NoRepeatSubmit(lockTime = 2)
	public PlatformResult<String> deleteById(String id) {
		try {
			friendlyLinkService.deleted(id);
			return PlatformResult.success("删除成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("删除失败,失败原因:"+e.getMessage());
		}
	}
	
	/**
	 * 
	 * @param record
	 * @return
	 */
	@ApiOperation(value = "批量更新", notes = "批量更新")
	@PostMapping("/friendlyLink/batchUpdate")
	public PlatformResult<String> batchUpdate(@RequestBody List<FriendlyLink> record) {
		try {
			friendlyLinkService.batchUpdate(record);
			return PlatformResult.success(null, "批量更新成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("修改失败,失败原因:"+e.getMessage());
		}
	}
}
