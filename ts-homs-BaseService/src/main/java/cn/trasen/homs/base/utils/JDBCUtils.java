package cn.trasen.homs.base.utils;


import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.trasen.homs.base.schudler.JobSchudler;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class JDBCUtils {
	

    /**
     * 获取一个连接
     * @return
     * @throws Exception
     */
    public static Connection getConnection(Map<String,String> dataMap) throws Exception {

        // 1.获取配置文件信息
        //InputStream resource = ClassLoader.getSystemClassLoader().getResourceAsStream("base.yml");

        //Properties properties = new Properties();
        //properties.load(resource);
//        String user = properties.getProperty("his2username");
//        String password = properties.getProperty("his2pwd");
//        String url = properties.getProperty("his2url");
//        String driverClass = properties.getProperty("his2driver");
    	String user = dataMap.get("his2username");
    	String password = dataMap.get("his2pwd");
    	String url = dataMap.get("his2url");
    	String driverClass = dataMap.get("his2driver");

        // 2. 加载驱动
        Class.forName(driverClass);

        Connection connection = DriverManager.getConnection(url, user, password);
        return connection;
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement) {
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement, ResultSet resultSet) {
        if (resultSet != null){
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }


    /**
     * <AUTHOR>
     * @date: 21/1/12 11:49
     * @Description: query
     * @param sql 执行的sql
     * @param clazz 查询的结果的类型
     * @param args sql的参数
     * @return: List<T>
     */
    public static  <T> List<T> query(String sql,Map<String,String> dataMap, Class<T> clazz, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<T> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection(dataMap);

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                T t = clazz.newInstance();
                for (int i = 0; i < columnCount; i++) {
                    // 7.1获取列值
                    Object object = resultSet.getObject(i + 1);
                    // 7.2获取列别名
                    String columnLabel = metaData.getColumnLabel(i + 1);
                    // 7.3获取属性并设置属性的值
                    Field field = clazz.getDeclaredField(columnLabel);
                    field.setAccessible(true);
                    field.set(t, object);
                }
                list.add(t);
            }
        } catch (Exception e) {
        	log.info("链接异常" + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }

        return list;
    }

    /**
     * @auther: ADMIN
     * @date: 21/1/12 11:52
     * @Description: update 更新
     * @param sql 执行的sql
     * @param obj 可变参数
     * @return: int 改变的条数
     */
//    public static int update(String sql, Object ...obj){
//        Connection connection = null;
//        PreparedStatement preparedStatement = null;
//        try {
//            // 1.获取连接
//            connection = getConnection();
//            // 2.预编译sql，返回一个PrepareStatement实例
//            preparedStatement = connection.prepareStatement(sql);
//            // 3.填充占位符
//            for (int i = 0; i < obj.length; i++) {
//                preparedStatement.setObject(i+1, obj[i]);
//            }
//            // 4.执行
//            return preparedStatement.executeUpdate();
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            // 5.关闭资源
//            closeConnection(connection, preparedStatement);
//        }
//        return 0;
//    }
}
