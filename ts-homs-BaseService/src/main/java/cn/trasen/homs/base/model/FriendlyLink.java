package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "COMM_FRIENDLY_LINK")
@Setter
@Getter
public class FriendlyLink {
    /**
     * 主键
     */
	@Id
    @Column(name = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 链接地址
     */
    @Column(name = "LINK_URL")
    @ApiModelProperty(value = "链接地址")
    private String linkUrl;

    /**
     * 链接描述
     */
    @Column(name = "LINK_NAME")
    @ApiModelProperty(value = "链接描述")
    private String linkName;

    /**
     * 链接图片
     */
    @Column(name = "LINK_IMAGE")
    @ApiModelProperty(value = "链接图片")
    private String linkImage;

    /**
     * 排序
     */
    @Column(name = "LINK_SORT")
    @ApiModelProperty(value = "排序")
    private Integer linkSort;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    
    /**
     * 状态
     */
    @Column(name = "STATUS")
    @ApiModelProperty(value = "状态")
    private Integer status;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}