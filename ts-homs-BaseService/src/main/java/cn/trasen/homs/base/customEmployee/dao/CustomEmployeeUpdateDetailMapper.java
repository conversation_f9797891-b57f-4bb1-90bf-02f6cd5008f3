package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateDetail;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeUpdateDetailMapper extends Mapper<CustomEmployeeUpdateDetail> {

	List<CustomEmployeeUpdateDetail> selectByAuditStatus(@Param("operationId")String operationId, @Param("auditStatus")String auditStatus);

	Map<String, String> selectWorkFlowInfo(@Param("operationId")String operationId, @Param("isApprove")String isApprove,@Param("approveUser")String approveUser);
}