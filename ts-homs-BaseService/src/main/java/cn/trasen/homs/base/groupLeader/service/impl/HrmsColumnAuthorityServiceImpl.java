package cn.trasen.homs.base.groupLeader.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.groupLeader.mapper.HrmsColumnAuthorityMapper;
import cn.trasen.homs.base.groupLeader.model.HrmsColumnAuthority;
import cn.trasen.homs.base.groupLeader.service.HrmsColumnAuthorityService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.util.StringUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsColumnAuthorityServiceImpl
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsColumnAuthorityServiceImpl implements HrmsColumnAuthorityService {

    @Autowired
    private HrmsColumnAuthorityMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(HrmsColumnAuthority record) {

        if (!StringUtil.isEmpty(record.getColumnId())) {   //新增编辑
            HrmsColumnAuthority del = new HrmsColumnAuthority();
            del.setColumnId(record.getColumnId());
            mapper.delete(del);
            if (!StringUtil.isEmpty(record.getPersonalIdentity())) {//删除岗位的所有授权
                List<String> personalIdentity = Arrays.asList(record.getPersonalIdentity().split(","));
                List<String> personalIdentityName = Arrays.asList(record.getPersonalIdentityName().split(","));
                for (int i = 0; i < personalIdentity.size(); i++) {
                    HrmsColumnAuthority _newBean = new HrmsColumnAuthority();
                    _newBean.setColumnId(record.getColumnId());
                    _newBean.setPersonalIdentity(personalIdentity.get(i));
                    _newBean.setPersonalIdentityName(personalIdentityName.get(i));
                    _newBean.setId(String.valueOf(IdWork.id.nextId()));
                    _newBean.setCreateDate(new Date());
                    _newBean.setUpdateDate(new Date());
                    _newBean.setIsDeleted("N");
                    ThpsUser user = UserInfoHolder.getCurrentUserInfo();
                    if (user != null) {
                        _newBean.setCreateUser(user.getUsercode());
                        _newBean.setCreateUserName(user.getUsername());
                        _newBean.setUpdateUser(user.getUsercode());
                        _newBean.setUpdateUserName(user.getUsername());
                    }
                    mapper.insertSelective(_newBean);

                }
                return personalIdentity.size();
            }
        }
        return 0;
    }


    @Override
    public List<HrmsColumnAuthority> selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
		HrmsColumnAuthority sel = new HrmsColumnAuthority();
		sel.setPersonalIdentity(id);
		sel.setIsDeleted("N");
        return mapper.select(sel);
    }


	@Override
	public List<HrmsColumnAuthority> bycolumnId(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsColumnAuthority sel = new HrmsColumnAuthority();
		sel.setColumnId(id);
		sel.setIsDeleted("N");
		return mapper.select(sel);
	}
}
