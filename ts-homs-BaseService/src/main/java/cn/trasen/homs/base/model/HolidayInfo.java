package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * @ClassName: HrHolidayInfo
 * @Description: 节假日Model
 * <AUTHOR>
 * @date 2018年4月26日 下午4:17:37
 *
 */
@Table(name = "comm_holiday_info")
@Setter
@Getter
@ToString
public class HolidayInfo {
    /**
     * holiday_info_id
     */
    @Id
    @Column(name = "holiday_info_id")
    @ApiModelProperty(value = "holiday_info_id")
    private String holidayInfoId;

    /**
     * 年-月
     */
    @ApiModelProperty(value = "年-月")
    private String month;

    /**
     * 调休日期
     */
    @Column(name = "work_day")
    @ApiModelProperty(value = "调休日期")
    private String workDay;

    /**
     * 节假日
     */
    @ApiModelProperty(value = "节假日")
    private String holiday;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
}