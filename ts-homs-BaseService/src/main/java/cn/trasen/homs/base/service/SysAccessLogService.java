package cn.trasen.homs.base.service;


import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.model.CommLoginLogs;


public interface SysAccessLogService {
    Integer getLogins(String loginDate, String[] loginType,String queryDate);

    Integer getAccesses(String loginDate,String queryDate);

    List<CommLoginLogs> getLoginList(String loginDate, String[] loginType,String queryDate);

    List<CommLoginLogs> getAccessList(String loginDate,String queryDate);

	List<Map<String, String>> selectOrgInfo(List<String> userCodes);

	List<Map<String, Object>> getLoginsCount(String queryYear);

	List<Map<String, Object>> getLoginsNumbers(String queryYear, String source);

	void calculationLoginStackedLine();

	List<Map<String, Object>> getLoginsMonth(String queryYear,String source);

	Long selectTotalEmployee(String currentUserCorpCode);

	List<Map<String, Object>> sysUseAnalysisList(Map<String,Object> params);

	List<Map<String, Object>> sysUseAnalysisDetailList(Map<String, Object> params);
}
