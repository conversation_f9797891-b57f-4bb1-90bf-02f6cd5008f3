package cn.trasen.homs.base.bean;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsOrganizationResp {
	/**
	 * 主键ID
	 */

	private String organizationId;

	/**
	 * 组织机构编码
	 */
	private String code;

	/**
	 * 组织机构名称
	 */

	private String name;

	/**
	 * 树ID
	 */

	private String treeIds;

	/**
	 * 父类ID
	 */

	private String parentId;

	/**
	 * 是否启用: 1=是; 2=否;
	 */

	private String isEnable;

	/**
	 * 树结构中级别
	 */

	private Integer orgLevel;

	/**
	 * 排序
	 */

	private Integer seqNo;

	/**
	 * 组织机构类型
	 */

	private String orgFlag;

	/**
	 * 管理人员代码
	 */

	private String managerCode;

	/**
	 * 管理人员名称
	 */

	private String managerName;

	/**
	 * 分管领导
	 */

	private String assigneManagerCode;

	/**
	 * 分管领导名称
	 */

	private String assigneManagerName;

	/**
	 * 部门职责
	 */
	private String responsibility;

	/**
	 * 定编人数
	 */

	private Integer personnelAllocation;

	/**
	 * 报表排序
	 */

	private Integer reportSort;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业ID
	 */

	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */

	private String createUser;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 更新者ID
	 */

	private String updateUser;

	/**
	 * 删除标识: Y=是; N=否;
	 */

	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 上级机构名称
	 */

	private String parentName;
	
	/**
	 * 操作的组织机构列表
	 */

	private List<HrmsOrganizationResp> operateOrgs;
	
}