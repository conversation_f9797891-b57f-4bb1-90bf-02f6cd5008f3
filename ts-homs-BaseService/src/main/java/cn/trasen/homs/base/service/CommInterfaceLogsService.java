package cn.trasen.homs.base.service;

import cn.trasen.homs.base.model.CommInterfaceLogs;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommInterfaceLogsService
 * @Description TODO
 * @date 2024��8��22�� ����7:03:36
 * <AUTHOR>
 * @version 1.0
 */
public interface CommInterfaceLogsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	Integer save(CommInterfaceLogs record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	Integer update(CommInterfaceLogs record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommInterfaceLogs
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	CommInterfaceLogs selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommInterfaceLogs>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	DataSet<CommInterfaceLogs> getDataSetList(Page page, CommInterfaceLogs record);
}
