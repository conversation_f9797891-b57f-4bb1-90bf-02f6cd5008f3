/**
 * @Title: JsonSameUtil.java  
 * @Package: cn.trasen.homs.base.utils  
 * @Date: 2021年6月28日
 * @Author: jyq
 * @Description: TODO
 */
package cn.trasen.homs.base.utils;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeField;

/**
 * @ClassName: JsonSameUtil
 * @Author: 86189
 * @Date: 2021年6月28日
 */
public class JsonUtil {

	private static final Gson gson = new Gson();

	private static final JsonParser parser = new JsonParser();

	public static boolean same(Object a, Object b) {

		if (a == null) {
			return b == null;
		}
		return same(gson.toJson(a), gson.toJson(b));
	}

	/**
	 * 比较两个json字符串是否等价
	 */
	public static boolean same(String a, String b) {

		if (a == null) {
			return b == null;
		}
		if (a.equals(b)) {
			return true;
		}
		JsonElement aElement = parser.parse(a);
		JsonElement bElement = parser.parse(b);
		if (gson.toJson(aElement).equals(gson.toJson(bElement))) {
			return true;
		}
		return same(aElement, bElement);
	}

	private static boolean same(JsonElement a, JsonElement b) {
		if (a.isJsonObject() && b.isJsonObject()) {
			return same((JsonObject) a, (JsonObject) b);
		} else if (a.isJsonArray() && b.isJsonArray()) {
			return same((JsonArray) a, (JsonArray) b);
		} else if (a.isJsonPrimitive() && b.isJsonPrimitive()) {
			return same((JsonPrimitive) a, (JsonPrimitive) b);
		} else if (a.isJsonNull() && b.isJsonNull()) {
			return same((JsonNull) a, (JsonNull) b);
		} else {
			return Boolean.FALSE;
		}
	}

	private static boolean same(JsonObject a, JsonObject b) {

		Set<String> aSet = a.keySet();
		Set<String> bSet = b.keySet();
		if (!aSet.equals(bSet)) {
			return false;
		}
		for (String aKey : aSet) {
			if (!same(a.get(aKey), b.get(aKey))) {
				return false;
			}
		}
		return true;
	}

	private static boolean same(JsonArray a, JsonArray b) {
		if (a.size() != b.size()) {
			return false;
		}
		List<JsonElement> aList = toSortedList(a);
		List<JsonElement> bList = toSortedList(b);
		for (int i = 0; i < aList.size(); i++) {
			if (!same(aList.get(i), bList.get(i))) {
				return false;
			}
		}
		return true;
	}

	private static boolean same(JsonPrimitive a, JsonPrimitive b) {

		return a.equals(b);
	}

	private static boolean same(JsonNull a, JsonNull b) {
		return true;
	}

	private static List<JsonElement> toSortedList(JsonArray a) {
		List<JsonElement> aList = new ArrayList<>();
		a.forEach(aList::add);
		aList.sort(Comparator.comparing(gson::toJson));
		return aList;
	}

	public static boolean sameEmplpyeeMessage(String beforeDate, String afterDate) {

		boolean flag = true;
		// 字符串排序
		Object o1 = JSONObject.parse(beforeDate);

		Object o2 = JSONObject.parse(afterDate);

		JsonArray array1 = gson.fromJson(o1.toString(), JsonArray.class);

		JsonArray array2 = gson.fromJson(o2.toString(), JsonArray.class);

		List<String> strList1 = new ArrayList<>();

		List<String> strList2 = new ArrayList<>();

		for (JsonElement item : array1) {

			strList1.add(item.toString());
		}

		for (JsonElement item : array2) {

			strList2.add(item.toString());
		}

		for (String detail : strList1) {

			if (!strList2.contains(detail)) {

				flag = false;
			}
		}
		return flag;
	}

	public static String formatObject(Object o) {

		return gson.toJson(o);
	}

	public static List<List<CommEmployeeField>> formatJsonStrToBean(String str) {

		JsonArray array = gson.fromJson(str, JsonArray.class);

		List<List<CommEmployeeField>> returnList = new ArrayList<>();

		for (JsonElement element : array) {

			List<CommEmployeeField> fieldList = new ArrayList<>();

			String jsonStr = element.toString();

			Map<String, String> fieldMap = gson.fromJson(jsonStr, Map.class);

			if (null != fieldMap) {

				for (String key : fieldMap.keySet()) {

					CommEmployeeField field = new CommEmployeeField();

					field.setFieldName(key);

					field.setValue(fieldMap.get(key));

					fieldList.add(field);
				}

				returnList.add(fieldList);
			}
		}
		return returnList;
	}
	
	public static List<List<CustomEmployeeField>> formatJsonStrToBean2(String str) {

		JsonArray array = gson.fromJson(str, JsonArray.class);

		List<List<CustomEmployeeField>> returnList = new ArrayList<>();

		for (JsonElement element : array) {

			List<CustomEmployeeField> fieldList = new ArrayList<>();

			String jsonStr = element.toString();

			Map<String, String> fieldMap = gson.fromJson(jsonStr, Map.class);

			if (null != fieldMap) {

				for (String key : fieldMap.keySet()) {

					CustomEmployeeField field = new CustomEmployeeField();

					field.setFieldName(key);

					field.setValue(fieldMap.get(key));

					fieldList.add(field);
				}

				returnList.add(fieldList);
			}
		}
		return returnList;
	}
	
	public static <T> T fromJson(String json,Class<T> classOfT) {
		
		return gson.fromJson(json,(Type) classOfT);
		
	}
}
