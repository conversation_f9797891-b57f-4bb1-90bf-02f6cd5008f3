package cn.trasen.homs.base.mapper;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.RequestContent;
import cn.trasen.homs.base.bean.ResetJobNumberReq;
import cn.trasen.homs.base.bo.EmployeeDetails;
import cn.trasen.homs.base.bo.EmployeeListInBO;
import cn.trasen.homs.base.bo.EmployeeListOutBO;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface HrmsEmployeeMapper extends Mapper<HrmsEmployee> {

	
    /** 
    * @description: 根据员工工号集合查询员工信息集合
* @param: employeeNos
    * @return: java.util.List<cn.trasen.basicsbottom.model.HrmsEmployee>
    * @author: liyuan
    * @createTime: 2021/6/18 15:46
    */
    List<HrmsEmployeeResp> findByEmployeeNos(@Param("employeeNos") List<String> employeeNos,@Param("ssoOrgCode") String ssoOrgCode);

    /**
     * @description: 根据员工工号集合查询员工信息集合
     * @param: employeeNos
     * @return: java.util.List<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/6/18 15:46
     */
    List<HrmsEmployeeResp> findByEmployeeIds(@Param("employeeIds")List<String> employeeIds, @Param("ssoOrgCode") String ssoOrgCode);



    /** 
    * @description: 根据员工工号查询员工信息
* @param: employeeNo
    * @return: cn.trasen.basicsbottom.model.HrmsEmployee
    * @author: liyuan
    * @createTime: 2021/6/18 17:35
    */
    HrmsEmployeeResp findByEmployeeNo(@Param("employeeNo")String employeeNo,@Param("ssoOrgCode") String ssoOrgCode);

    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.model.HrmsEmployee
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    HrmsEmployeeResp findByEmployeePhoneNumber(@Param("phoneNumber")String phoneNumber,@Param("ssoOrgCode") String ssoOrgCode);


    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.model.HrmsEmployee
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    HrmsEmployeeResp findByEmployeeId(@Param("employeeId")String employeeId, @Param("ssoOrgCode")String ssoOrgCode);


/** 
* @description: 分页查询员工列表
* @param: page
* @param: entity
* @return: java.util.List<cn.trasen.basicsbottom.model.HrmsEmployee>
* @author: liyuan
* @createTime: 2021/6/18 18:08
*/
    List<HrmsEmployeeResp> getPageList(Page page, EmployeeListReq entity);
    List<HrmsEmployeeResp> getPageList(EmployeeListReq entity);
    
    
    /**
     * 
    * @Title: getEmployeeListByEqualPay  
    * @Description: 同工同酬选择人员列表
    * @Params: @param page
    * @Params: @param record
    * @Params: @return      
    * @Return: List<HrmsEmployeeResp>
    * <AUTHOR>
    * @date:2021年9月14日
    * @Throws
     */
    List<HrmsEmployeeResp> getEmployeeListByEqualPay(Page page,HrmsEmployee record);


    
    /**
    * 分页获取员工列表
    * @param page
    * @param req
    * @return java.util.List<cn.trasen.basicsbottom.bean.HrmsEmployeeResp>
    * <AUTHOR>
    * @date 2022/1/19 13:38
    */
    List<EmployeeListOutBO> list(Page page, @Param("req")  EmployeeListInBO req);
    
    
    List<HrmsEmployeeResp> getEmployeeByCodes(List<String> codes);

    void updateHrmsEmpNoByid(ResetJobNumberReq resetJobNumberReq);

    void updateToaEmpNoById(ResetJobNumberReq resetJobNumberReq);

    void updateThpsEmpNoByid(ResetJobNumberReq resetJobNumberReq);

    List<HrmsEmployeeResp> selectListByDeptCode(List<String> organizationIdList);

    List<EmployeeDetails> getLyzXuexi(@Param("employeeId") String employeeId, @Param("ssoOrgCode") String ssoOrgCode);

    List<EmployeeDetails> getLyzYuanwai(String employeeId);

    List<EmployeeDetails> getLyzYuannei(String employeeId);

    List<EmployeeDetails> getLyzZhicheng(String employeeId);

    List<EmployeeDetails> getLyzJinxiu(String employeeId);

    List<EmployeeDetails> getLyzGuipei(String employeeId);
    //吉首获取所有人员接口
    List<Map<String,String>> getEmpAll();

    List<Map<String, Object>> getKhjg(String employeeId);

    List<Map<String, Object>> getKylw(String employeeId);

    List<Map<String, Object>> getJcjl(String employeeId);

    List<Map<String, Object>> getJxjl(String employeeId);

    List<Map<String, Object>> getZyysgfhpx(String employeeId);

    void excelSql(@Param("excelSql")String excelSql);

    List<Map<String, Object>> getGpjl(String employeeId);

	void updateDisable(@Param("employeeId")String employeeId, @Param("disableStatus")String disableStatus);

	List<EmployeeListReq> getEmployeeListReqList(RequestContent requestContent);

    void updateHrmsEmpBecome(@Param("nowEmployeeNo")String nowEmployeeNo, @Param("employeeNo") String employeeNo);

	List<HrmsEmployee> getEmployeeBaseList(EmployeeListReq record);
}