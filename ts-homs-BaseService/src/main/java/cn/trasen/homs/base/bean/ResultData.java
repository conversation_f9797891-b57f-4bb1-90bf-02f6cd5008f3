package cn.trasen.homs.base.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * @Description: 结果信息返回帮助类
 * @Date: 2020/3/6 09:42
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
@Getter
@Setter
public class ResultData {
    private static final long serialVersionUID = 1L;

    private String status ;//是否成功

    private String msg;//返回信息

    private String timestamp;//时间戳

    private String data;//数据

    @Override
    public String toString() {
        return "{status:" + status + ", msg:" + msg + ", timestamp:" + timestamp +", data:" + data + "}";
    }
}
