package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeAuthorityField;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeAuthorityFieldMapper extends Mapper<CustomEmployeeAuthorityField> {

	List<CustomEmployeeAuthorityField> selectByAuthorityId(@Param("authorityId")String authorityId,@Param("groupId")String groupId);

	void updateFieldNameByGroupId(@Param("groupId") String groupId,@Param("groupName")String groupName);
}