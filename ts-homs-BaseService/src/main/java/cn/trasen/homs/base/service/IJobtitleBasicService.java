package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.JobtitleBasicListReq;
import cn.trasen.homs.base.bean.JobtitleBasicListResp;
import cn.trasen.homs.base.bean.JobtitleBasicSaveReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 10:30
 * @description
 */
public interface IJobtitleBasicService {
    List<TreeModel> getJobtitleTree();

    /**
    * 获取单个数据
    * @param jobtitleBasicListReq
    * @return cn.trasen.homs.base.bean.JobtitleBasicListResp
    * <AUTHOR>
    * @date 2021/11/1 17:27
    */
    JobtitleBasicListResp get(JobtitleBasicListReq jobtitleBasicListReq);

    /**
     * @description: 获取列表
     * @param: jobtitleBasicListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/7 11:32
     */
    List<JobtitleBasicListResp> getList(JobtitleBasicListReq jobtitleBasicListReq);

    /**
    * @description: 获取分页列表
* @param: jobtitleBasicListReq
* @param: page
    * @return: cn.trasen.BootComm.model.DataSet
    * @author: liyuan
    * @createTime: 2021/8/7 11:32
    */
    DataSet getPageList(JobtitleBasicListReq jobtitleBasicListReq, Page page);
    

    /**
    * @description: 新增
* @param: jobtitleBasicSaveReq
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/7 13:14
    */
    void add(JobtitleBasicSaveReq jobtitleBasicSaveReq);

    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 修改
     * @param: jobtitleBasicSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/7 13:14
     */
    void update(JobtitleBasicSaveReq jobtitleBasicSaveReq);

    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void updateSort(List<JobtitleBasicSaveReq> jobtitleBasicSaveReqList);

    /**
     * @description: 删除等级
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void delete(String id);

    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    void enable(String id, String enable);

    void verify(String id);
    
    /**
     * 
    * @Title: getCategoryPageList  
    * @Description: 获取职称类别
    * @Params: @param jobtitleBasicListReq
    * @Params: @param page
    * @Params: @return      
    * @Return: DataSet
    * <AUTHOR>
    * @date:2021年8月20日
    * @Throws
     */
    DataSet getCategoryPageList(JobtitleBasicListReq jobtitleBasicListReq, Page page);

	/**
	 * 获取职称类别   --级别
	 * @param jobtitleBasicListReq
	 * @param page
	 * @return
	 */
	DataSet getCategoryPageList2(JobtitleBasicListReq jobtitleBasicListReq, Page page);
}
