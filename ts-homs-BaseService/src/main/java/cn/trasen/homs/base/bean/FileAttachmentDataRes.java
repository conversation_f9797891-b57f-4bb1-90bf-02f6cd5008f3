package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021/6/17 13:25
 * @description
 */

@Setter
@Getter
public class FileAttachmentDataRes {
    @ApiModelProperty(value = "id")
    String id;

    @ApiModelProperty(value = "模块名字")
    String moduleName;

    @ApiModelProperty(value = "文件名称")

    String originalName;


    @ApiModelProperty(value = "文件扩展名")

    String fileExtension;
    @ApiModelProperty(value = "文件大小")

    Long fileSize;
    @ApiModelProperty(value = "预览地址")
    String realPath;


    @ApiModelProperty(value = "文件内容")
    String base64Body;
}