package cn.trasen.homs.base.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommSealManage;
import cn.trasen.homs.base.service.CommSealManageService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommSealManageController
 * @Description TODO
 * @date 2025��6��21�� ����5:44:39
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "印章管理")
public class CommSealManageController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommSealManageController.class);

	@Autowired
	private CommSealManageService commSealManageService;

	/**
	 * @Title saveCommSealManage
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/commSealManage/save")
	public PlatformResult<String> saveCommSealManage(@RequestBody CommSealManage record) {
		try {
			commSealManageService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommSealManage
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/commSealManage/update")
	public PlatformResult<String> updateCommSealManage(@RequestBody CommSealManage record) {
		try {
			commSealManageService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommSealManageById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommSealManage>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/commSealManage/{id}")
	public PlatformResult<CommSealManage> selectCommSealManageById(@PathVariable String id) {
		try {
			CommSealManage record = commSealManageService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommSealManageById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/commSealManage/delete/{id}")
	public PlatformResult<String> deleteCommSealManageById(@PathVariable String id) {
		try {
			commSealManageService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommSealManageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommSealManage>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/commSealManage/list")
	public DataSet<CommSealManage> selectCommSealManageList(Page page, CommSealManage record) {
		return commSealManageService.getDataSetList(page, record);
	}
	
	
	@ApiOperation(value = "查询当前登陆人印章数据", notes = "查询当前登陆人印章数据")
	@GetMapping("/api/commSealManage/getCommSealManageList")
	public PlatformResult<List<CommSealManage>> getCommSealManageList() {
		try {
			List<CommSealManage> record = commSealManageService.getCommSealManageList();
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
