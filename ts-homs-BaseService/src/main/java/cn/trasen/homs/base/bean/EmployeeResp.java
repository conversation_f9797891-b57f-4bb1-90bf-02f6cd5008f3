//package cn.trasen.homs.base.bean;
//
//import com.fasterxml.jackson.annotation.JsonFormat;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Getter;
//import lombok.Setter;
//import org.jeecgframework.poi.excel.annotation.Excel;
//import javax.persistence.Column;
//import javax.persistence.Id;
//import javax.persistence.Transient;
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//
//
//@Setter
//@Getter
//public class EmployeeResp {
//    /**
//     * 主键ID
//     */
//    @Id
//    @Column(name = "employee_id")
//    @ApiModelProperty(value = "主键ID")
//    private String employeeId;
//    
//    /**
//     * 员工工号
//     */
//    @Excel(name = "员工工号")
//    @Column(name = "employee_no")
//    @ApiModelProperty(value = "员工工号")
//    private String employeeNo;
//    
//    /**
//     * 发薪号
//     */
//    @Excel(name = "发薪号")
//    @Column(name = "emp_payroll")
//    @ApiModelProperty(value = "发薪号")
//    private String empPayroll;
//
//    /**
//     * 员工姓名
//     */
//    @Excel(name = "员工姓名")
//    @Column(name = "employee_name")
//    @ApiModelProperty(value = "员工姓名")
//    private String employeeName;
//
//    /**
//     * 组织机构ID
//     */
//    @Column(name = "org_id")
//    @ApiModelProperty(value = "组织机构ID")
//    private String orgId;
//
//    /**
//     * 曾用名
//     */
//    @Excel(name = "曾用名")
//    @Column(name = "used_name")
//    @ApiModelProperty(value = "曾用名")
//    private String usedName;
//
//    /**
//     * 性别
//     */
//    @Excel(name = "性别")
//    @ApiModelProperty(value = "性别")
//    private String gender;
//
//    /**
//     * 出生日期
//     */
//    @ApiModelProperty(value = "出生日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date birthday;
//
//    /**
//     * 身份证号
//     */
//    @Excel(name = "身份证号")
//    @Column(name = "identity_number")
//    @ApiModelProperty(value = "身份证号")
//    private String identityNumber;
//
//    /**
//     * 手机号码
//     */
//    @Excel(name = "手机号码")
//    @Column(name = "phone_number")
//    @ApiModelProperty(value = "手机号码")
//    private String phoneNumber;
//    
//    /**
//     * 
//     */
//    @Column(name = "EMP_PHONE_SECOND")
//    @ApiModelProperty(value = "员工电话2")
//    private String empPhoneSecond;
//
//    /**
//     * 移动短号
//     */
//    @Column(name = "EMP_BUSINESS_PHONE")
//    @ApiModelProperty(value = "移动短号")
//    private String empBusinessPhone;
//    
//    /**
//     * 电信短号
//     */
//    @Column(name = "EMP_TELECOM_BUSINESS_PHONE")
//    @ApiModelProperty(value = "电信短号")
//    private String empTelecomBusinessPhone;
//    
//    /**
//     * 联通短号
//     */
//    @Column(name = "EMP_UNICOM_BUSINESS_PHONE")
//    @ApiModelProperty(value = "联通短号")
//    private String empUnicomBusinessPhone;
//
//    /**
//     * 办公电话
//     */
//    @Column(name = "EMP_SHORT_PHONE")
//    @ApiModelProperty(value = "办公电话")
//    private String empShortPhone;
//
//
//    /**
//     * 座机号码
//     */
//    @Column(name = "landline_number")
//    @ApiModelProperty(value = "座机号码")
//    private String landlineNumber;
//
//    /**
//     * 员工类别
//     */
//    @Excel(name = "员工类别")
//    @Column(name = "employee_category")
//    @ApiModelProperty(value = "员工类别")
//    private String employeeCategory;
//
//    /**
//     * 员工状态
//     */
//    @Excel(name = "员工状态")
//    @Column(name = "employee_status")
//    @ApiModelProperty(value = "员工状态")
//    private String employeeStatus;
//
//    /**
//     * 编制类型
//     */
//    @Excel(name = "编制情况")
//    @Column(name = "establishment_type")
//    @ApiModelProperty(value = "编制类型")
//    private String establishmentType;
//
//    /**
//     * 籍贯
//     */
//    @Excel(name = "籍贯")
//    @ApiModelProperty(value = "籍贯")
//    private String birthplace;
//
//    /**
//     * 民族
//     */
//    @Excel(name = "民族")
//    @ApiModelProperty(value = "民族")
//    private String nationality;
//
//    /**
//     * 政治面貌
//     */
//    @Excel(name = "政治面貌")
//    @Column(name = "political_status")
//    @ApiModelProperty(value = "政治面貌")
//    private String politicalStatus;
//
//    /**
//     * 头像
//     */
//    @ApiModelProperty(value = "头像")
//    private String avatar;
//    
//    /**
//     * 签章图片
//     */
//    @Column(name = "SIGNATURE_IMG_NAME")
//    @ApiModelProperty(value = "签章图片")
//    private String signatureImgName;
//
//    /**
//     * 居住地址
//     */
//    @Excel(name = "居住地址")
//    @ApiModelProperty(value = "居住地址")
//    private String address;
//
//    /**
//     * 户籍地址
//     */
//    @Excel(name = "户籍地址")
//    @Column(name = "residence_address")
//    @ApiModelProperty(value = "户籍地址")
//    private String residenceAddress;
//
//    /**
//     * 邮编
//     */
//    @Excel(name = "邮编")
//    @ApiModelProperty(value = "邮编")
//    private String postcode;
//
//    /**
//     * 邮箱
//     */
//    @Excel(name = "邮箱")
//    @ApiModelProperty(value = "邮箱")
//    private String email;
//
//    /**
//     * 婚姻状况
//     */
//    @Column(name = "marriage_status")
//    @ApiModelProperty(value = "婚姻状况")
//    private String marriageStatus;
//
//    /**
//     * 健康状况
//     */
//    @Column(name = "health_status")
//    @ApiModelProperty(value = "健康状况")
//    private String healthStatus;
//
//    /**
//     * 血型
//     */
//    @Column(name = "blood_group")
//    @ApiModelProperty(value = "血型")
//    private String bloodGroup;
//
//    /**
//     * 姓名五笔码
//     */
//    @Column(name = "name_stroke")
//    @ApiModelProperty(value = "姓名五笔码")
//    private String nameStroke;
//
//    /**
//     * 姓名拼音码
//     */
//    @Column(name = "name_spell")
//    @ApiModelProperty(value = "姓名拼音码")
//    private String nameSpell;
//
//    /**
//     * 个人简介
//     */
//    @Column(name = "personal_profile")
//    @ApiModelProperty(value = "个人简介")
//    private String personalProfile;
//
//    /**
//     * 职务ID
//     */
//    @Column(name = "position_id")
//    @ApiModelProperty(value = "职务ID")
//    private String positionId;
//
//    /**
//     * 岗位ID
//     */
//    @Column(name = "post_id")
//    @ApiModelProperty(value = "岗位ID")
//    private String postId;
//
//
//    /**
//     * 岗位名称
//     */
//    @ApiModelProperty(value = "岗位名称")
//    private String postName;
//    /**
//     * 薪级ID
//     */
//    @Column(name = "salary_level_id")
//    @ApiModelProperty(value = "薪级ID")
//    private String salaryLevelId;
//
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value = "备注")
//    private String remark;
//
//    /**
//     * 企业ID
//     */
//    @Column(name = "enterprise_id")
//    @ApiModelProperty(value = "企业ID")
//    private String enterpriseId;
//
//    /**
//     * 创建时间
//     */
//    @Column(name = "create_date")
//    @ApiModelProperty(value = "创建时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date createDate;
//
//    /**
//     * 创建者ID
//     */
//    @Column(name = "create_user")
//    @ApiModelProperty(value = "创建者ID")
//    private String createUser;
//
//    /**
//     * 创建者姓名
//     */
//    @Column(name = "create_user_name")
//    @ApiModelProperty(value = "创建者姓名")
//    private String createUserName;
//
//    /**
//     * 更新时间
//     */
//    @ApiModelProperty(value = "更新时间")
//    private Date updateDate;
//
//    /**
//     * 更新人ID
//     */
//    @Column(name = "update_user")
//    @ApiModelProperty(value = "更新人ID")
//    private String updateUser;
//
//    /**
//     * 更新者姓名
//     */
//    @Column(name = "update_user_name")
//    @ApiModelProperty(value = "更新者姓名")
//    private String updateUserName;
//
//    /**
//     * 删除标识: Y=是; N=否;
//     */
//    @Column(name = "is_deleted")
//    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
//    private String isDeleted;
//
//    /**
//     * 是否启用: 1=是; 2=否;
//     */
//    @Column(name = "is_enable")
//    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
//    private String isEnable;
//    
//    
//    /**
//     * 是否接收短信提醒  否：0，是：1
//     */
//    @Column(name = "IS_SMS_REMINDER")
//    @ApiModelProperty(value = "是否接收短信提醒  否：0，是：1")
//    private Short isSmsReminder;
//
//    /**
//     * 是否接收语音提醒  否：0，是：1
//     */
//    @Column(name = "IS_VOICE_REMINDER")
//    @ApiModelProperty(value = "是否接收语音提醒  否：0，是：1")
//    private Short isVoiceReminder;
//
//    /**
//     * 是否接收微信消息推送  否：0，是：1
//     */
//    @Column(name = "IS_WX_REMINDER")
//    @ApiModelProperty(value = "是否接收微信消息推送  否：0，是：1")
//    private Short isWxReminder;
//
//    /**
//     * 是否显示个人手机号码  否：0，是：1
//     */
//    @Column(name = "IS_DISPLAY_PHONE_NO")
//    @ApiModelProperty(value = "是否显示个人手机号码  否：0，是：1")
//    private Short isDisplayPhoneNo;
//
//    /**
//     * 是否使用电子签章  否：0，是：1
//     */
//    @Column(name = "IS_USE_SIGNATURE")
//    @ApiModelProperty(value = "是否使用电子签章  否：0，是：1")
//    private Short isUseSignature;
//    
//    
//    /**
//     * 企业微信授权用户ID
//     */
//    @Column(name = "OPEN_ID")
//    @ApiModelProperty(value = "企业微信授权用户ID")
//    private String openId;
//    
//    /**
//     * 工龄
//     */
//    @Column(name = "YEAR_WORK")
//    @ApiModelProperty(value = "工龄")
//    private String yearWork;
//    
//    /**
//     * 年假天数
//     */
//    @Column(name = "YEAR_DAYS")
//    @ApiModelProperty(value = "年假天数")
//    private String yearDays;
//    
//    /**
//     * 已修年假天数
//     */
//    @Column(name = "YEAR_NUMBER")
//    @ApiModelProperty(value = "已修年假天数")
//    private String yearNumber;
//    
//    /**
//     * 允许上传附件大小（M）
//     */
//    @Column(name = "UPLOAD_FILE_SIZE")
//    @ApiModelProperty(value = "允许上传附件大小（M）")
//    private String uploadFileSize;
//    
//    /**
//     * 代理人ID
//     */
//    @Transient
//    @ApiModelProperty(value = "代理人ID")
//    private String agentId;
//    
//    
//    /**
//     * 代理人姓名
//     */
//    @Transient
//    @ApiModelProperty(value = "代理人姓名")
//    private String agentName;
//
//    
//    /**
//     * 是否启用流程代理   否：0，是：1
//     */
//    @Transient
//    @ApiModelProperty(value = "是否启用流程代理   否：0，是：1 ")
//    private Short isEnableProcessAgent;
//    
//
//    /**
//     * 代理开始时间
//     */
//    @Transient
//    @ApiModelProperty(value = "代理开始时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date agentStartTime;
//
//    /**
//     * 代理结束时间
//     */
//    @Transient
//    @ApiModelProperty(value = "代理结束时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date agentEndTime;
//    
//    
//
//    // ------- 扩展字段 ------- //
//    /**
//     * 主键ID(前端多选用)
//     */
//    @Transient
//    @ApiModelProperty(value = "主键ID(前端多选用)")
//    private String id;
//
//    /**
//     * 年龄
//     */
//    @Transient
//    @ApiModelProperty(value = "年龄")
//    private String age;
//
//    /**
//     * 机构名称
//     */
//    @Excel(name = "机构名称")
//    @Transient
//    @ApiModelProperty(value = "机构名称")
//    private String orgName;
//
//
//    /**
//     * 机构名称
//     */
//    @Excel(name = "机构Code")
//    @Transient
//    @ApiModelProperty(value = "机构Code")
//    private String orgCode;
//    /**
//     * 老系统员工工号
//     */
//    @Excel(name = "老系统工号")
//    @Transient
//    @ApiModelProperty(value = "老系统员工工号")
//    @Deprecated
//    private String oldEmployeeNo;
//
//    /**
//     * HIS系统员工工号
//     */
//    @Transient
//    @ApiModelProperty(value = "HIS系统员工工号")
//    @Deprecated
//    private String hisEmployeeNo;
//
//    /**
//     * 入职日期
//     */
//    @Excel(name = "入职日期")
//    @Transient
//    @ApiModelProperty(value = "入职日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date entryDate;
//
//    /**
//     * 退休日期
//     */
//    @Excel(name = "退休日期")
//    @Transient
//    @ApiModelProperty(value = "退休日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date retireDate;
//
//    /**
//     * 离职日期
//     */
//    @Excel(name = "离职日期")
//    @Transient
//    @ApiModelProperty(value = "离职日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date quitDate;
//
//    /**
//     * 转正日期
//     */
//    @Excel(name = "返聘日期")
//    @Transient
//    @ApiModelProperty(value = "返聘日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date reemploymentDate;
//
//    /**
//     * 入党日期
//     */
//    @Excel(name = "入党日期")
//    @Transient
//    @ApiModelProperty(value = "入党日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date partyDate;
//
//    /**
//     * 开始工作日期
//     */
//    @Excel(name = "开始工作日期")
//    @Transient
//    @ApiModelProperty(value = "开始工作日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date workStartDate;
//
//    /**
//     * 本单位开始工作日期
//     */
//    @Excel(name = "本单位工作日期")
//    @Transient
//    @ApiModelProperty(value = "本单位开始工作日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date unitStartDate;
//
//    /**
//     * 个人身份
//     */
//    @Excel(name = "个人身份")
//    @Transient
//    @ApiModelProperty(value = "个人身份")
//    private String personalIdentity;
//
//    /**
//     * 用工性质
//     */
//    @Excel(name = "用工性质")
//    @Transient
//    @ApiModelProperty(value = "用工性质")
//    private String workNature;
//
//    /**
//     * 擅长
//     */
//    @Transient
//    @ApiModelProperty(value = "擅长")
//    private String goodAt;
//
//    /**
//     * 考勤科室
//     */
//    @Excel(name = "考勤科室")
//    @Transient
//    @ApiModelProperty(value = "考勤科室")
//    private String checkWorkDepart;
//
//    /**
//     * 考勤科室ID
//     */
//    @Transient
//    @ApiModelProperty(value = "考勤科室ID")
//    private String checkWorkDepartId;
//
//    /**
//     * 审核科室
//     */
//    @Excel(name = "审核科室")
//    @Transient
//    @ApiModelProperty(value = "审核科室")
//    private String reviewDepart;
//
//    /**
//     * 审核科室ID
//     */
//    @Transient
//    @ApiModelProperty(value = "审核科室ID")
//    private String reviewDepartId;
//
//    /**
//     * 升级标识(平江用于年度统一调薪的判断依据)
//     */
//    @Transient
//    @ApiModelProperty(value = "升级标识(平江用于年度统一调薪的判断依据)")
//    private String upgradeFlag;
//
//    /**
//     * 提高10%标识(平江用于是否计算提高10%工资)
//     */
//    @Transient
//    @ApiModelProperty(value = "提高10%标识(平江用于是否计算提高10%工资)")
//    private String improveFlag;
//
//    /**
//     * 是否重复入职
//     */
//    @Transient
//    @ApiModelProperty(value = "是否重复入职")
//    private String isDuplicateEntry;
//
//    /**
//     * 紧急联系人
//     */
//    @Transient
//    @ApiModelProperty(value = "紧急联系人")
//    private String emergencyContact;
//
//    /**
//     * 紧急联系电话
//     */
//    @Transient
//    @ApiModelProperty(value = "紧急联系电话")
//    private String emergencyTel;
//
//    /**
//     * 试用期薪资
//     */
//    @Transient
//    @ApiModelProperty(value = "试用期薪资")
//    private BigDecimal probationSalary;
//
//    /**
//     * 转正薪资
//     */
//    @Transient
//    @ApiModelProperty(value = "转正薪资")
//    private BigDecimal regularSalary;
//
//    /**
//     * 社保购买日期
//     */
//    @Transient
//    @ApiModelProperty(value = "社保购买日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private String buySocialDate;
//
//    /**
//     * 公积金购买日期
//     */
//    @Transient
//    @ApiModelProperty(value = "公积金购买日期")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private String buyProvidentDate;
//
//    /**
//     * 薪资福利说明
//     */
//    @Transient
//    @ApiModelProperty(value = "薪资福利说明")
//    private String salaryRemark;
//
//    /**
//     * 性别文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "性别文本值")
//    private String genderText;
//
//    /**
//     * 员工状态文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "员工状态文本值")
//    private String employeeStatusText;
//
//    /**
//     * 编制情况文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "编制情况文本值")
//    private String establishmentTypeText;
//
//    /**
//     * 员工类别文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "员工类别文本值")
//    private String employeeCategoryText;
//
//    /**
//     * 民族名称
//     */
//    @Transient
//    @ApiModelProperty(value = "民族名称")
//    private String nationalityName;
//
//    /**
//     * 政治面貌文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "政治面貌文本值")
//    private String politicalStatusText;
//
//    /**
//     * 职务名称
//     */
//    @Excel(name = "职务")
//    @Transient
//    @ApiModelProperty(value = "职务名称")
//    private String positionName;
//
//    /**
//     * 岗位类别
//     */
//    @Excel(name = "岗位类别")
//    @Transient
//    @ApiModelProperty(value = "岗位类别")
//    private String postCategory;
//
//
//
//    /**
//     * 薪级类别
//     */
//    @Excel(name = "薪级类别")
//    @Transient
//    @ApiModelProperty(value = "薪级类别")
//    private String salaryLevelCategory;
//
//    /**
//     * 薪级名称
//     */
//    @Excel(name = "薪级名称")
//    @Transient
//    @ApiModelProperty(value = "薪级名称")
//    private String salaryLevelName;
//
//    /**
//     * 血型文本值
//     */
//    @Transient
//    @ApiModelProperty(value = "血型文本值")
//    private String bloodGroupText;
//
//    /**
//     * 组织机构ID集合-前台使用
//     */
//    @Transient
//    @ApiModelProperty(value = "组织机构ID集合-前台使用")
//    private String orgIds;
//
//    /**
//     * 组织机构ID集合-权限使用
//     */
//    @Transient
//    @ApiModelProperty(value = "组织机构ID集合-权限使用")
//    private List<String> orgIdList;
//
//    //组织机构ID集合-权限使用(后台授权使用)
//    @Transient
//    @ApiModelProperty(value = "组织机构ID集合-权限使用(后台授权的机构)")
//    private String htOrgIdList;
//
//
//    /**
//     * 用户Code-权限使用
//     */
//    @Transient
//    @ApiModelProperty(value = "用户Code-权限使用")
//    private String userCode;
//
//    @Column(name = "is_retire")
//    @ApiModelProperty(value = "是否退休过")
//    private String isRetire;  //是否退休过;(1：退休过)
//
//    ///////////////////// 导出 /////////////////////
//    /**
//     * 出生日期
//     */
//    @Transient
//    private String birthdayExport;
//
//    /**
//     * 入职日期
//     */
//    @Transient
//    private String entryDateExport;
//
//    /**
//     * 入党日期
//     */
//    @Transient
//    private String partyDateExport;
//
//    /**
//     * 状态(1: 启用 2:禁用)
//     */
//    @Transient
//    private String isEnableExport;
//
//    // ------- Excel导入用到的字段 ------- //
//    /**
//     * 出生日期(导入)
//     */
//    @Excel(name = "出生日期")
//    @Transient
//    private String birthdayImport;
//
//    /**
//     * 最高职称
//     */
//    @Transient
//    private String jobtitleName;
//
//
//    /**
//     * 最高学历
//     */
//    @Transient
//    private String educationType;
//
//    @Transient
//    private String multiEmployeeStatus;  //查询多个员工状态
//
//
//    @Transient
//    private String selectRetire;  //1查询退休的
//
//    @Transient
//    private String schoolName;  //毕业学校
//
//    @Transient
//    private String professional;  //所学专业
//
//    @Transient
//    private String degree;  //学位
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date endTime;  //学历结束日期
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTime;  //学历开始日期
//
//    @Transient
//    private String endTimeExport; //毕业时间字符串
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date assessmentDate;  //职称评定日期
//
//    @Transient
//    private String assessmentDateExport; //职称评定日期字符串
//
//    @Transient
//    private String workStartDateExport; //工作时间
//
//    @Transient
//    private Integer no;  //序号
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date inauguralDate;  //任职时间
//
//    @Transient
//    private String inauguralDateExport;
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date downwardDate;  //低一级聘任时间
//
//    @Transient
//    private String downwardDateExport;  //低一级聘任时间
//    @Transient
//    private String unitStartDateExport;  //
//    @Transient
//    private String retireDateExport;  //退休时间
//
//    //start 筛选条件
//    @Transient
//    private String birthdayStartTime;  //出生开始时间
//    @Transient
//    private String birthdayEndTime;  //出生结束时间
//
//    @Transient
//    private String workStartDateStart;  //参加工作
//    @Transient
//    private String workStartDateEnd;
//
//    @Transient
//    private String entryDateStart;  //来院工作开始时间
//    @Transient
//    private String entryDateEnd;  //
//
//    @Transient
//    private String educationDegree; //学历
//
//    @Transient
//    private String jobtitleBasicName;
//
//    //锦屏添加字段
//
//    //岗位描述
//    @Transient
//    private String jobDescriptionType;
//
//
//    //现职务任职时间
//    @Transient
//    private String jobDescriptionTypeTime;
//
//    //兼任职务
//    @Transient
//    private String concurrentPosition;
//
//    //兼任职务时间
//    @Transient
//    private String concurrentPositionTime;
//
//    //是否领导
//    @Transient
//    private String isLeader;
//
//    //岗位类型
//    @Transient
//    private String postType;
//
//    //医师执业资格证
//    @Transient
//    private String doctorQualificationCertificate;
//
//    //助产士
//    @Transient
//    private String midwife;
//
//    //起聘时间
//    @Transient
//    private String startEmployDate;
//
//    //终聘时间
//    @Transient
//    private String endEmployDate;
//
//    //是否退伍军人
//    @Transient
//    private String isVeteran;
//
//    //部队名称
//    @Transient
//    private String unitName;
//
//    //入职文件
//    @Transient
//    private String businessId;
//
//    @Transient
//    private String businessId2;
//
//    @Transient
//    private String businessId3;
//
//    //出生地址
//    @Transient
//    private String bornAddress;
//
//    //出生地址编码
//    @Transient
//    private String bornAddressName;
//
//
//    @Transient
//    private String firstEducationType;  //第一学历
//
//    @Transient
//    private String firstEducationTypeText;  //第一学历文本
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date acquisitionDate;  //任职时间
//
//    @Transient
//    private String authorizedOrg;  //编制所属机构
//
//    @Transient
//    private String employDuty;  //聘任职务
//
//    @Transient
//    private String employDutyDate;  //聘任日期
//
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @Transient
//    private Date employDutyEquallyDate;  //任同职级时间
//
//    @Transient
//    private String employDutyDuration;  //任职年限
//
//    @Transient
//    private String workYears;  //工龄
//
//    @Transient
//    private String complianceTraining;  //是否规培人员
//
//    //锦屏学历
//    //全日制
//    @Transient
//    private String educationTypeFull;  //学历
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTimeFull;  //学历开始日期
//
//    @Transient
//    private String startTimeFullExport;
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date endTimeFull;  //学历开始日期
//
//    @Transient
//    private String endTimeFullExport;
//
//    @Transient
//    private String schoolNameFull;  //学校
//
//    @Transient
//    private String professionalFull;  //专业
//
//    @Transient
//    private String degreeFull;  //学历
//
//    //第一学历
//
//    @Transient
//    private String educationTypeFirst;  //学历
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTimeFirst;  //学历开始日期
//
//    @Transient
//    private String startTimeFirstExport;
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date endTimeFirst;  //学历开始日期
//
//    @Transient
//    private String endTimeFirstExport;
//
//    @Transient
//    private String schoolNameFirst;  //学校
//
//    @Transient
//    private String professionalFirst;  //专业
//
//    @Transient
//    private String degreeFirst;  //学历
//
//    //第二学历
//
//    @Transient
//    private String educationTypeSecond;  //学历
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTimeSecond;  //学历开始日期
//
//    @Transient
//    private String startTimeSecondExport;
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date endTimeSecond;  //学历开始日期
//
//    @Transient
//    private String endTimeSecondExport;
//
//    @Transient
//    private String schoolNameSecond;  //学校
//
//    @Transient
//    private String professionalSecond;  //专业
//
//    @Transient
//    private String degreeSecond;  //学历
//
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date operationDate;    //首次执业时间
//
//    @Transient
//    private String operationOrg;  //注册机构
//
//    @Transient
//    private String operationScope;  //执业范围
//
//    @Transient
//    private String operationType;  //执业类别
//
//    @Transient
//    private String operationNumber;  //证书编号
//
//    //资格情况
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date certificationAcquisitionDate;    //首次执业时间
//    @Transient
//    private String certificationAssessmentAgency;  //审批机关
//    @Transient
//    private String certificationProfessionalName;  //专业
//    @Transient
//    private String certificationCertificateNumber;  //证书编号
//
//    //专业技术情况
//    //初级
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date primaryAcquisitionDate;    //首次执业时间
//
//    @Transient
//    private String primaryAcquisitionDateExport;
//
//    @Transient
//    private String primaryAssessmentAgency;  //审批机关
//    @Transient
//    private String primaryProfessionalName;  //专业
//    @Transient
//    private String primaryCertificateNumber;  //证书编号
//    //中级
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date middleAcquisitionDate;    //首次执业时间
//    @Transient
//    private String middleAcquisitionDateExport;
//
//    @Transient
//    private String middleAssessmentAgency;  //审批机关
//    @Transient
//    private String middleProfessionalName;  //专业
//    @Transient
//    private String middleCertificateNumber;  //证书编号
//
//    //副高	subSenior
//    @Transient
//    private Date subSeniorAcquisitionDate;    //首次执业时间
//    @Transient
//    private String subSeniorAcquisitionDateExport;
//    @Transient
//    private String subSeniorAssessmentAgency;  //审批机关
//    @Transient
//    private String subSeniorProfessionalName;  //专业
//    @Transient
//    private String subSeniorCertificateNumber;  //证书编号
//
//
//    //正高high
//    @Transient
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date highAcquisitionDate;    //首次执业时间
//    @Transient
//    private String highAcquisitionDateExport;
//    @Transient
//    private String highAssessmentAgency;  //审批机关
//    @Transient
//    private String highProfessionalName;  //专业
//    @Transient
//    private String highCertificateNumber;  //证书编号
//
//    @Transient
//    private String operationDateExport;
//    @Transient
//    private String certificationAcquisitionDateExport;
//    @Transient
//    private String employDutyEquallyDateExport;
//
//    @Transient
//    private String ygtwbdwCode;  //医共体外部单位code
//
//    @Transient
//    private String fileName;  //导出文件的名字
//
//    @Transient
//    private String postUpdate;  //修改聘任情况跟
//
//    @Transient
//    private String archiveAddress;    //存档地址
//
//
//    @ApiModelProperty(value = "人员去向")
//    private String workStatus;
//    @ApiModelProperty(value = "人员去向")
//    private String workStatusLable;
//
//}