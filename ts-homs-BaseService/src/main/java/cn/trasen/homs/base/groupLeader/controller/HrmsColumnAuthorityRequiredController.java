package cn.trasen.homs.base.groupLeader.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.groupLeader.model.HrmsColumnAuthorityRequired;
import cn.trasen.homs.base.groupLeader.service.HrmsColumnAuthorityRequiredService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsColumnAuthorityRequiredRequiredController
 * @Description TODO
 * @date 2023��12��25�� ����4:33:23
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsColumnAuthorityRequiredController")
public class HrmsColumnAuthorityRequiredController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsColumnAuthorityRequiredController.class);

	@Autowired
	private HrmsColumnAuthorityRequiredService HrmsColumnAuthorityRequiredService;

	/**
	 * @Title saveHrmsColumnAuthorityRequired
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/columnAuthorityRequired/save")
	public PlatformResult<String> save(@RequestBody HrmsColumnAuthorityRequired record) {
		try {
			HrmsColumnAuthorityRequiredService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	/**
	 *
	 * @Title selectHrmsColumnAuthorityRequiredById
	 * @Description 根据岗位查询
	 * @param id
	 * @return PlatformResult<HrmsColumnAuthorityRequired>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/columnAuthorityRequired/byPersonalIdentity/{id}")
	public PlatformResult<List<HrmsColumnAuthorityRequired>> byPersonalIdentity(@PathVariable String id) {
		try {
			List<HrmsColumnAuthorityRequired> list = HrmsColumnAuthorityRequiredService.selectById(id);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectHrmsColumnAuthorityRequiredById
	 * @Description 根据栏目查询
	 * @param id
	 * @return PlatformResult<HrmsColumnAuthorityRequired>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/columnAuthorityRequired/bycolumnId/{id}")
	public PlatformResult<List<HrmsColumnAuthorityRequired>> bycolumnId(@PathVariable String id) {
		try {
			List<HrmsColumnAuthorityRequired> list = HrmsColumnAuthorityRequiredService.bycolumnId(id);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
