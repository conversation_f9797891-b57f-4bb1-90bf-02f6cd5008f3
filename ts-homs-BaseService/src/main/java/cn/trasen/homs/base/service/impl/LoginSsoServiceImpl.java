package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.mapper.LoginSsoMapper;
import cn.trasen.homs.base.model.LoginSso;
import cn.trasen.homs.base.service.LoginSsoService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 外部系统 impl层
 * @Date: 2020/4/3 15:37
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class LoginSsoServiceImpl implements LoginSsoService {

    @Autowired
    private LoginSsoMapper loginSsoMapper;

    /**
     * @Author: Lizhihuo
     * @Description: 增加
     * @Date: 2020/4/1 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(LoginSso loginSso) {
        loginSso.setId(String.valueOf(IdWork.id.nextId()));
        loginSso.setIsDeleted(Contants.IS_DELETED_FALSE);
        loginSso.setCreateDate(new Date());
        loginSso.setCreateUser(UserInfoHolder.getCurrentUserCode());
        loginSso.setCreateUserName(UserInfoHolder.getCurrentUserName());
        loginSso.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        loginSso.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        loginSso.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
        loginSso.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        loginSso.setStatus("1");
        //dossierCategory.setOrgCode(UserInfoHolder.getCurrentUserInfo().getOrgRang());
        return loginSsoMapper.insertSelective(loginSso);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改
     * @Date: 2020/4/1 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(LoginSso loginSso) {
        loginSso.setUpdateDate(new Date());
        loginSso.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        loginSso.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        return loginSsoMapper.updateByPrimaryKeySelective(loginSso);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除
     * @Date: 2020/4/1 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(LoginSso loginSso) {
        LoginSso channel = loginSsoMapper.selectByPrimaryKey(loginSso.getId());
        channel.setIsDeleted(Contants.IS_DELETED_TURE);
        return loginSsoMapper.updateByPrimaryKeySelective(channel);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询
     * @Date: 2020/4/1 15:03
     * @Param:
     * @return: int
     **/
    @Override
    public List<LoginSso> getDataList(Page page, LoginSso loginSso) {
        Example example = new Example(LoginSso.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isNotBlank(loginSso.getTitle())) {
            example.and().andLike("title", "%" + loginSso.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(loginSso.getStatus())) {
            example.and().andLike("status", loginSso.getStatus());
        }
        boolean isAdmin = UserInfoHolder.getRight("ADMIN");
        if(isAdmin) {
            example.and().andNotEqualTo("title", "后台管理");
        }
        example.setOrderByClause("SORT ASC");
        return loginSsoMapper.selectByExampleAndRowBounds(example, page);
    }

    @Override
    @Transactional(readOnly = false)
    public void batchUpdate(List<LoginSso> record) {
        for (LoginSso loginSso : record) {
            loginSsoMapper.updateByPrimaryKeySelective(loginSso);
        }
    }

}
