package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.List;

import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.DictItemListReq;
import cn.trasen.homs.base.bean.PostCategoryListResp;
import cn.trasen.homs.base.bean.PostCategorySaveReq;
import cn.trasen.homs.base.bean.PostListReq;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IPostCategoryService;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.identifier.IdWork;

/**
 * @description: 岗位类别
 * @return:
 * @author: liyuan
 * @createTime: 2021/8/6 9:41
 */
@Service
public class PostCategoryService implements IPostCategoryService {


    @Autowired
    IDictItemService dictItemService;

    @Autowired
    IPostService postService;


    final static String postCategoryKey = "post_category";

    @Override
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    public List<PostCategoryListResp> getList() {
        List<DictItem> dictItemList = dictItemService.getPublicDictItemBydicTypeId(postCategoryKey);
        List<PostCategoryListResp> postCategoryListRespList = new ArrayList<>();
        for (DictItem dictItem : dictItemList) {
            PostCategoryListResp postCategoryListResp = new PostCategoryListResp();
            postCategoryListResp.setPostCategoryId(dictItem.getItemCode());
            postCategoryListResp.setPostCategoryName(dictItem.getItemName());
            if (StringUtils.isBlank(dictItem.getIsEnable())) {
                dictItem.setIsEnable(EnableEnum.Y.getKey());
            }
            postCategoryListResp.setDictTypeId(dictItem.getDicTypeId());
            postCategoryListResp.setId(dictItem.getId());
            postCategoryListResp.setIsEnable(dictItem.getIsEnable());
            postCategoryListResp.setIsEnableLable(EnableEnum.getValByKey(postCategoryListResp.getIsEnable()));
            postCategoryListRespList.add(postCategoryListResp);
        }
        return postCategoryListRespList;
    }


    @Override
    /**
     * @description: 新增
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(PostCategorySaveReq postCategorySaveReq) {
        DictItem entity = new DictItem();
        entity.setItemCode(String.valueOf(IdWork.id.nextId()));
        entity.setItemName(postCategorySaveReq.getPostCategoryName());
        entity.setDicTypeId(postCategoryKey);
        entity.setSysCode("HRMS");
        entity.setItemNameValue(entity.getItemCode());
        entity.setSsoOrgCode("*PUBLIC*");
        dictItemService.insert(entity);
    }


    @Override
    /**
     * @description: 修改
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(PostCategorySaveReq postCategorySaveReq) {
//        String dictItemId = "";
//        List<DictItem> dictItemList = dictItemService.getDictItemByTypeCode(postCategoryKey);
//        for (DictItem dictItem : dictItemList) {
//            if (dictItem.getItemCode().equals(postCategorySaveReq.getPostCategoryId())) {
//                dictItemId = dictItem.getId();
//                break;
//            }
//        }
        DictItem entity = new DictItem();
        entity.setItemName(postCategorySaveReq.getPostCategoryName());
        entity.setId(postCategorySaveReq.getId());
        dictItemService.update(entity);
    }


    @Override
    /**
     * @description: 删除类别
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String dictTypeId, String id) {
        verify(id);
        dictItemService.delete(dictTypeId, id);
    }


    @Override
    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String dictTypeId, String id, String enable) {
        if (enable.equals(EnableEnum.N.getKey())) {
            verify(id);
        }
        dictItemService.enable(dictTypeId, id, enable);
    }


    /**
     * @description: 验证
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 11:17
     */
    @Override
    public void verify(String id) {
        PostListReq postListReq = new PostListReq();
        postListReq.setPostCategory(id);
        postService.exists(postListReq);
        if (postService.exists(postListReq)) {
            throw new BusinessException("岗位类别已被使用，不能禁用和删除！");
        }
    }
}