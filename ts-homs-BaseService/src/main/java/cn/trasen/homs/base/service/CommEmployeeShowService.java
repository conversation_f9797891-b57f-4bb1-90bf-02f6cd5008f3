package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.BootComm.utils.JqGridBean;
import cn.trasen.homs.base.model.CommEmployeeShow;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/** 
* @ClassName: CommEmployeeShowService 
* @Description: 自定义列service 
* <AUTHOR>  
* @date 2022年12月5日 下午3:39:24 
*  
*/
public interface CommEmployeeShowService {

	/** 
	* @Title: getList 
	* @Description: 获取要展示的字段
	* @param @return    设定文件 
	* @return List<JqGridBean>    返回类型 
	* @throws 
	*/
	List<JqGridBean> getList();
	
	/** 
	* @Title: ref 
	* @Description: 同步数据
	* @param     设定文件 
	* @return void    返回类型 
	* @throws 
	*/
	void ref();

	/** 
	* @Title: getshowColumnList 
	* @Description: 自定义列设置
	* @param @param record
	* @param @return    设定文件 
	* @return List<CommEmployeeShow>    返回类型 
	* @throws 
	*/
	List<CommEmployeeShow> getshowColumnList(Page page,CommEmployeeShow record);

	/** 
	* @Title: update 
	* @Description: 修改子弹展示
	* @param @param record
	* @param @return    设定文件 
	* @return Integer    返回类型 
	* @throws 
	*/
	Integer update(CommEmployeeShow record);

	/** 
	* @Title: getColumnExport 
	* @Description: 获取导出需要的字段
	* @param @return    设定文件 
	* @return List<CommEmployeeShow>    返回类型 
	* @throws 
	*/
	List<CommEmployeeShow> getColumnExport();

	
	
}
