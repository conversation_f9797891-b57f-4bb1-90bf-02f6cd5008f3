package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.PersonalIdentityListReq;
import cn.trasen.homs.base.bean.PersonalIdentityListResp;
import cn.trasen.homs.base.bean.PersonalIdentitySaveReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:40
 * @description
 */
public interface IPersonalIdentityService {
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    List<PersonalIdentityListResp> getList();

    /**
    * @description: 分页获取
* @param: page
* @param: postListReq
    * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.homs.base.bean.PersonalIdentityListResp>
    * @author: liyuan
    * @createTime: 2021/8/16 15:09
    */
    DataSet<PersonalIdentityListResp> getPageList(Page page, PersonalIdentityListReq personalIdentityListReq);

    /**
     * @description: 新增
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void add(PersonalIdentitySaveReq personalIdentitySaveReq);

    /**
     * @description: 修改
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void update(PersonalIdentitySaveReq personalIdentitySaveReq);

    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void updateSort(List<PersonalIdentitySaveReq> personalIdentitySaveReqList);

    /**
     * @description: 删除类别
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void delete(String dictTypeId, String id);

    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    void enable(String dictTypeId, String id, String enable);

    void verify(String id);
}
