<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="cn.trasen.homs.base.groupLeader.mapper.HrmsRoleGroupLeaderMapper">
	<resultMap id="BaseResultMap"
		type="cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader">
		<!--
      WARNING - @mbg.generated
    -->
		<result column="id" jdbcType="VARCHAR" property="id" />
		<result column="usercode" jdbcType="VARCHAR" property="usercode" />
		<result column="username" jdbcType="VARCHAR" property="username" />
		<result column="deptcode" jdbcType="VARCHAR" property="deptcode" />
		<result column="deptname" jdbcType="VARCHAR" property="deptname" />
	</resultMap>

	<select id="getRolegroupleaderAllList"
		resultType="cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeaderBase">
		SELECT t3.usercode,
		t3.username,
		t3.deptcode,
		t4.deptname
		FROM ts_thps.thps_role t1
		LEFT JOIN ts_thps.thps_user_role t2 ON t1.ID=t2.ROLE_ID
		LEFT JOIN ts_thps.thps_user t3 ON t2.USER_ID=t3.id
		LEFT JOIN ts_thps.thps_dept t4 ON t3.deptcode = t4.deptcode
		WHERE t1.ROLE_CODE='GROUP_LEADER'

	</select>

	<select id="getDataSetList"
		resultType="cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader">
		SELECT id,usercode,username,
		GROUP_CONCAT(group_code) AS group_code,
		GROUP_CONCAT(group_code_name) AS group_code_name
		FROM hrms_role_group_leader WHERE is_deleted='N'
		GROUP BY usercode
	</select>
</mapper>