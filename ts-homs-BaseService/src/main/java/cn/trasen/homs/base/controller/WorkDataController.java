package cn.trasen.homs.base.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.bean.WeekDaysNumReq;
import cn.trasen.homs.base.service.IHolidayInfoService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @createTime 2021/9/6 13:37
 * @description
 */

@RestController
@Api(tags = "工作日假期")
@RequestMapping("/workData")

public class WorkDataController {


    @Autowired
    IHolidayInfoService holidayInfoService;

    @ApiOperation(value = "获取时间段的工作日数量", notes = "获取时间段的工作日数量")
    @PostMapping(value = "/getWeekDaysNum")
    public PlatformResult<Integer> getWeekDaysNum(@RequestBody WeekDaysNumReq weekDaysNumReq) {
        return PlatformResult.success(holidayInfoService.getWeekDaysNum(weekDaysNumReq));
    }
}