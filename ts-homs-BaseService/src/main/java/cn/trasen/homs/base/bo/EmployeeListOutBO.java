package cn.trasen.homs.base.bo;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/1/19 12:52
 */
@Setter
@Getter
public class EmployeeListOutBO {
    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 老系统员工工号
     */
    @Column(name = "old_employee_no")
    @ApiModelProperty(value = "老系统员工工号")
    private String oldEmployeeNo;

    /**
     * HIS系统员工工号
     */
    @Column(name = "his_employee_no")
    @ApiModelProperty(value = "HIS系统员工工号")
    private String hisEmployeeNo;

    /**
     * 工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 组织机构
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构")
    private String orgId;


    /**
     * 组织机构
     */
    @ApiModelProperty(value = "组织机构")
    private String orgName;

    /**
     * 组织机构
     */
    @ApiModelProperty(value = "组织机构编码")
    private String orgCode;
    /**
     * 曾用名
     */
    @Column(name = "used_name")
    @ApiModelProperty(value = "曾用名")
    private String usedName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "性别")
    private String genderText;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 身份证号
     */
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    /**
     * 手机号码
     */
    @Column(name = "phone_number")
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 座机号码
     */
    @Column(name = "landline_number")
    @ApiModelProperty(value = "座机号码")
    private String landlineNumber;

    /**
     * 员工类别
     */
    @Column(name = "employee_category")
    @ApiModelProperty(value = "员工类别")
    private String employeeCategory;

    @ApiModelProperty(value = "员工类别")
    private String employeeCategoryText;


    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentTypeText;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String birthplace;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nationality;
    @ApiModelProperty(value = "民族")
    private String nationalityName;

    /**
     * 政治面貌
     */
    @Column(name = "political_status")
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatusText;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatar;

    /**
     * 住址
     */
    @ApiModelProperty(value = "住址")
    private String address;

    /**
     * 户籍地址
     */
    @Column(name = "residence_address")
    @ApiModelProperty(value = "户籍地址")
    private String residenceAddress;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String postcode;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 婚姻状况
     */
    @Column(name = "marriage_status")
    @ApiModelProperty(value = "婚姻状况")
    private String marriageStatus;

    /**
     * 健康状况
     */
    @Column(name = "health_status")
    @ApiModelProperty(value = "健康状况")
    private String healthStatus;

    /**
     * 血型
     */
    @Column(name = "blood_group")
    @ApiModelProperty(value = "血型")
    private String bloodGroup;

    /**
     * 姓名五笔码
     */
    @Column(name = "name_stroke")
    @ApiModelProperty(value = "姓名五笔码")
    private String nameStroke;

    /**
     * 姓名拼音码
     */
    @Column(name = "name_spell")
    @ApiModelProperty(value = "姓名拼音码")
    private String nameSpell;

    /**
     * 个人简介
     */
    @Column(name = "personal_profile")
    @ApiModelProperty(value = "个人简介")
    private String personalProfile;

    /**
     * 职务
     */
    @Column(name = "position_id")
    @ApiModelProperty(value = "职务")
    private String positionId;

    @ApiModelProperty(value = "职务名称")
    private String positionName;


    /**
     * 岗位ID
     */
    @Column(name = "post_id")
    @ApiModelProperty(value = "岗位ID")
    private String postId;


    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String postName;
    /**
     * 薪级ID
     */
    @Column(name = "salary_level_id")
    @ApiModelProperty(value = "薪级ID")
    private String salaryLevelId;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String enableText;

    /**
     * 是否退休过，1退休过
     */
    @Column(name = "is_retire")
    @ApiModelProperty(value = "是否退休过，1退休过")
    private String isRetire;

    /**
     * 入院时间
     */
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入院时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 退休日期
     */
    @Column(name = "retire_date")
    @ApiModelProperty(value = "退休日期")
    private String retireDate;

    /**
     * 离职日期
     */
    @Column(name = "quit_date")
    @ApiModelProperty(value = "离职日期")
    private String quitDate;

    /**
     * 延聘日期
     */
    @Column(name = "reemployment_date")
    @ApiModelProperty(value = "延聘日期")
    private String reemploymentDate;

    /**
     * 入党日期
     */
    @Column(name = "party_date")
    @ApiModelProperty(value = "入党日期")
    private Date partyDate;

    /**
     * 参加工作日期
     */
    @Column(name = "work_start_date")
    @ApiModelProperty(value = "参加工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String workStartDate;

    /**
     * 没用
     */
    @Column(name = "unit_start_date")
    @ApiModelProperty(value = "没用")
    private String unitStartDate;

    /**
     * 岗位类别
     */
    @Column(name = "personal_identity")
    @ApiModelProperty(value = "岗位类别")
    private String personalIdentity;

    /**
     * 是否专业技术人员
     */
    @Column(name = "work_nature")
    @ApiModelProperty(value = "是否专业技术人员")
    private String workNature;

    /**
     * 擅长
     */
    @Column(name = "good_at")
    @ApiModelProperty(value = "擅长")
    private String goodAt;

    /**
     * 考勤科室
     */
    @Column(name = "check_work_depart")
    @ApiModelProperty(value = "考勤科室")
    private String checkWorkDepart;

    /**
     * 审核科室
     */
    @Column(name = "review_depart")
    @ApiModelProperty(value = "审核科室")
    private String reviewDepart;

    /**
     * 升级标识(平江用于年度统一调薪的判断依据)
     */
    @Column(name = "upgrade_flag")
    @ApiModelProperty(value = "升级标识(平江用于年度统一调薪的判断依据)")
    private String upgradeFlag;

    /**
     * 提高10%标识(平江用于是否计算提高10%工资)
     */
    @Column(name = "improve_flag")
    @ApiModelProperty(value = "提高10%标识(平江用于是否计算提高10%工资)")
    private String improveFlag;

    /**
     * 是否重复入职
     */
    @Column(name = "is_duplicate_entry")
    @ApiModelProperty(value = "是否重复入职")
    private String isDuplicateEntry;

    /**
     * 紧急联系人
     */
    @Column(name = "emergency_contact")
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    @Column(name = "emergency_tel")
    @ApiModelProperty(value = "紧急联系电话")
    private String emergencyTel;

    /**
     * 试用期薪资
     */
    @Column(name = "probation_salary")
    @ApiModelProperty(value = "试用期薪资")
    private String probationSalary;

    /**
     * 转正薪资
     */
    @Column(name = "regular_salary")
    @ApiModelProperty(value = "转正薪资")
    private String regularSalary;

    /**
     * 社保购买日期
     */
    @Column(name = "buy_social_date")
    @ApiModelProperty(value = "社保购买日期")
    private String buySocialDate;

    /**
     * 公积金购买日期
     */
    @Column(name = "buy_provident_date")
    @ApiModelProperty(value = "公积金购买日期")
    private String buyProvidentDate;

    /**
     * 薪资福利说明
     */
    @Column(name = "salary_remark")
    @ApiModelProperty(value = "薪资福利说明")
    private String salaryRemark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 最高学历
     */
    @Column(name = "first_education_type")
    @ApiModelProperty(value = "最高学历")
    private String firstEducationType;


    /**
     * 岗位描述
     */
    @Column(name = "job_deion_type")
    @ApiModelProperty(value = "岗位描述")
    private String jobDeionType;

    /**
     * 现职务任职时间
     */
    @Column(name = "job_deion_type_time")
    @ApiModelProperty(value = "现职务任职时间")
    private Date jobDeionTypeTime;

    /**
     * 兼任职务
     */
    @Column(name = "concurrent_position")
    @ApiModelProperty(value = "兼任职务")
    private String concurrentPosition;

    /**
     * 兼任职务时间
     */
    @Column(name = "concurrent_position_time")
    @ApiModelProperty(value = "兼任职务时间")
    private String concurrentPositionTime;

    /**
     * 是否领导
     */
    @Column(name = "is_leader")
    @ApiModelProperty(value = "是否领导")
    private String isLeader;

    /**
     * 岗位类型
     */
    @Column(name = "post_type")
    @ApiModelProperty(value = "岗位类型")
    private String postType;

    /**
     * 医师执业资格证
     */
    @Column(name = "doctor_qualification_certificate")
    @ApiModelProperty(value = "医师执业资格证")
    private String doctorQualificationCertificate;

    /**
     * 助产士
     */
    @ApiModelProperty(value = "助产士")
    private String midwife;

    /**
     * 起聘时间
     */
    @Column(name = "start_employ_date")
    @ApiModelProperty(value = "起聘时间")
    private Date startEmployDate;

    /**
     * 终聘时间
     */
    @Column(name = "end_employ_date")
    @ApiModelProperty(value = "终聘时间")
    private String endEmployDate;

    /**
     * 是否退伍军人
     */
    @Column(name = "is_veteran")
    @ApiModelProperty(value = "是否退伍军人")
    private String isVeteran;

    /**
     * 部队名称
     */
    @Column(name = "unit_name")
    @ApiModelProperty(value = "部队名称")
    private String unitName;

    /**
     * 入职文件
     */
    @Column(name = "business_id")
    @ApiModelProperty(value = "入职文件")
    private String businessId;

    /**
     * 出生地址
     */
    @Column(name = "born_address")
    @ApiModelProperty(value = "出生地址")
    private String bornAddress;

    /**
     * 出生地址编码
     */
    @Column(name = "born_address_name")
    @ApiModelProperty(value = "出生地址编码")
    private String bornAddressName;

    /**
     * 编制所属机构
     */
    @Column(name = "authorized_org")
    @ApiModelProperty(value = "编制所属机构")
    private String authorizedOrg;

    /**
     * 聘任职务
     */
    @Column(name = "employ_duty")
    @ApiModelProperty(value = "聘任职务")
    private String employDuty;

    /**
     * 聘任日期
     */
    @Column(name = "employ_duty_date")
    @ApiModelProperty(value = "聘任日期")
    private Date employDutyDate;

    /**
     * 任同职级时间
     */
    @Column(name = "employ_duty_equally_date")
    @ApiModelProperty(value = "任同职级时间")
    private Date employDutyEquallyDate;

    /**
     * 任现职年限
     */
    @Column(name = "employ_duty_duration")
    @ApiModelProperty(value = "任现职年限")
    private Integer employDutyDuration;

    /**
     * 是否规培人员
     */
    @Column(name = "compliance_training")
    @ApiModelProperty(value = "是否规培人员")
    private String complianceTraining;

    /**
     * 首次注册时间
     */
    @Column(name = "operation_date")
    @ApiModelProperty(value = "首次注册时间")
    private String operationDate;

    /**
     * 注册机构
     */
    @Column(name = "operation_org")
    @ApiModelProperty(value = "注册机构")
    private String operationOrg;

    /**
     * 执业范围
     */
    @Column(name = "operation_scope")
    @ApiModelProperty(value = "执业范围")
    private String operationScope;

    /**
     * 执业证书编号
     */
    @Column(name = "operation_number")
    @ApiModelProperty(value = "执业证书编号")
    private String operationNumber;

    /**
     * 执业类别
     */
    @Column(name = "operation_type")
    @ApiModelProperty(value = "执业类别")
    private String operationType;

    /**
     * 存档地址
     */
    @Column(name = "archive_address")
    @ApiModelProperty(value = "存档地址")
    private String archiveAddress;

    /**
     * 密码
     */
    @Column(name = "emp_password")
    @ApiModelProperty(value = "密码")
    private String empPassword;

    /**
     * 员工昵称
     */
    @Column(name = "emp_nick_name")
    @ApiModelProperty(value = "员工昵称")
    private String empNickName;

    /**
     * 头像，存放头像的路径
     */
    @Column(name = "emp_head_img")
    @ApiModelProperty(value = "头像，存放头像的路径")
    private String empHeadImg;

    /**
     * 签名图片
     */
    @Column(name = "emp_signimg")
    @ApiModelProperty(value = "签名图片")
    private String empSignimg;

    /**
     * 签章图片
     */
    @Column(name = "signature_img_name")
    @ApiModelProperty(value = "签章图片")
    private String signatureImgName;

    /**
     * 签章图片存储名
     */
    @Column(name = "signature_imgsave_name")
    @ApiModelProperty(value = "签章图片存储名")
    private String signatureImgsaveName;

    /**
     * 离职原因
     */
    @Column(name = "fire_reason")
    @ApiModelProperty(value = "离职原因")
    private String fireReason;

    /**
     * 员工年龄
     */
    @Column(name = "emp_age")
    @ApiModelProperty(value = "员工年龄")
    private String empAge;

    /**
     * 用户账号
     */
    @Column(name = "user_accounts")
    @ApiModelProperty(value = "用户账号")
    private String userAccounts;

    /**
     * 用户简码
     */
    @Column(name = "user_simple_name")
    @ApiModelProperty(value = "用户简码")
    private String userSimpleName;

    /**
     * 是否iKey验证 0 不需要验证 1
     */
    @Column(name = "key_validate")
    @ApiModelProperty(value = "是否iKey验证 0 不需要验证 1")
    private String keyValidate;

    /**
     * iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）
     */
    @Column(name = "key_serial")
    @ApiModelProperty(value = "iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）")
    private String keySerial;

    /**
     * 是否是域验证 否：0，是：1
     */
    @Column(name = "is_ad_check")
    @ApiModelProperty(value = "是否是域验证 否：0，是：1")
    private String isAdCheck;

    /**
     * 是否是休眠用户 否：0，是：1
     */
    @Column(name = "user_is_sleep")
    @ApiModelProperty(value = "是否是休眠用户 否：0，是：1")
    private String userIsSleep;

    /**
     * 是否接收短信提醒  否：0，是：1
     */
    @Column(name = "is_sms_reminder")
    @ApiModelProperty(value = "是否接收短信提醒  否：0，是：1")
    private String isSmsReminder;

    /**
     * 是否接收语音提醒  否：0，是：1
     */
    @Column(name = "is_voice_reminder")
    @ApiModelProperty(value = "是否接收语音提醒  否：0，是：1")
    private String isVoiceReminder;

    /**
     * 是否接收微信消息推送  否：0，是：1
     */
    @Column(name = "is_wx_reminder")
    @ApiModelProperty(value = "是否接收微信消息推送  否：0，是：1")
    private String isWxReminder;

    /**
     * 是否显示个人手机号码  否：0，是：1
     */
    @Column(name = "is_display_phone_no")
    @ApiModelProperty(value = "是否显示个人手机号码  否：0，是：1")
    private String isDisplayPhoneNo;

    /**
     * 是否使用电子签章  否：0，是：1
     */
    @Column(name = "is_use_signature")
    @ApiModelProperty(value = "是否使用电子签章  否：0，是：1")
    private String isUseSignature;

    /**
     * 是否活动用户  0 :不是  1：是
     */
    @Column(name = "user_isactive")
    @ApiModelProperty(value = "是否活动用户  0 :不是  1：是")
    private String userIsactive;

    /**
     * 是否正式用户  0:不是 1：是
     */
    @Column(name = "user_isformaluser")
    @ApiModelProperty(value = "是否正式用户  0:不是 1：是")
    private String userIsformaluser;

    /**
     * 是否特权用户  0：不是  1：是
     */
    @Column(name = "user_issuper")
    @ApiModelProperty(value = "是否特权用户  0：不是  1：是")
    private String userIssuper;

    /**
     * 特权开始时间
     */
    @Column(name = "user_super_begin")
    @ApiModelProperty(value = "特权开始时间")
    private String userSuperBegin;

    /**
     * 特权结束时间
     */
    @Column(name = "user_super_end")
    @ApiModelProperty(value = "特权结束时间")
    private String userSuperEnd;

    /**
     * 默认组织范围（本部、西院、肿瘤中心）
     */
    @ApiModelProperty(value = "默认组织范围（本部、西院、肿瘤中心）")
    private String browserange;

    /**
     * 默认组织范围名称（本部、西院、肿瘤中心）
     */
    @Column(name = "browserange_name")
    @ApiModelProperty(value = "默认组织范围名称（本部、西院、肿瘤中心）")
    private String browserangeName;

    /**
     * 院区编码
     */
    @Column(name = "hosp_code")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建部门编码")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 用户是否删除 N 未被删除 Y 已被删除
     */
    @Column(name = "user_is_deleted")
    @ApiModelProperty(value = "用户是否删除 N 未被删除 Y 已被删除")
    private String userIsDeleted;

    /**
     * 发薪号
     */
    @Column(name = "emp_payroll")
    @ApiModelProperty(value = "发薪号")
    private String empPayroll;

    /**
     * 企业微信授权用户ID
     */
    @Column(name = "open_id")
    @ApiModelProperty(value = "企业微信授权用户ID")
    private String openId;

    /**
     * 是否开启生日显示保护  否：0，是：1
     */
    @Column(name = "is_birthday_protect")
    @ApiModelProperty(value = "是否开启生日显示保护  否：0，是：1")
    private String isBirthdayProtect;

    /**
     * 员工职称ID
     */
    @Column(name = "emp_title_id")
    @ApiModelProperty(value = "员工职称ID")
    private String empTitleId;

    /**
     * 员工职称名称
     */
    @Column(name = "emp_title_name")
    @ApiModelProperty(value = "员工职称名称")
    private String empTitleName;

    /**
     * 工龄
     */
    @Column(name = "year_work")
    @ApiModelProperty(value = "工龄")
    private String yearWork;

    /**
     * 已休年假天数
     */
    @Column(name = "year_number")
    @ApiModelProperty(value = "已休年假天数")
    private String yearNumber;

    /**
     * 年假天数
     */
    @Column(name = "year_days")
    @ApiModelProperty(value = "年假天数")
    private String yearDays;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 员工电话2
     */
    @ApiModelProperty(value = "员工电话2")
    private String empPhoneSecond;


    /**
     * 移动短号
     */
    @ApiModelProperty(value = "移动短号")
    private String empBusinessPhone;

    /**
     * 电信短号
     */
    @ApiModelProperty(value = "电信短号")
    private String empTelecomBusinessPhone;

    /**
     * 联通短号
     */
    @ApiModelProperty(value = "联通短号")
    private String empUnicomBusinessPhone;


    /**
     * 允许上传附件大小（M）
     */
    @ApiModelProperty(value = "允许上传附件大小（M）")
    private Integer uploadFileSize;

    /**
     * 代理人ID
     */
    @ApiModelProperty(value = "代理人ID")
    private String agentIds;


    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人姓名")
    private String agentNames;


    /**
     * 是否启用流程代理   否：0，是：1
     */
    @ApiModelProperty(value = "是否启用流程代理   否：0，是：1")
    private Integer isEnableProcessAgent;


    /**
     * 代理开始时间
     */
    @ApiModelProperty(value = "代理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")

    private Date agentStartTime;


    /**
     * 代理开始时间
     */
    @ApiModelProperty(value = "代理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")

    private Date agentEndTime;

    @ApiModelProperty(value = "代理人明细")
    private List<HrmsEmployeeResp> agents;



    @ApiModelProperty(value = "虚拟机构名称")
    private String virtualOrgName;


    @ApiModelProperty(value = "虚拟机构id")
    private String virtualOrgId;

    @ApiModelProperty(value = "最高学历名称")
    private String educationTypeName;

    @ApiModelProperty(value = "最高职称名称")
    private String jobtitleCategoryName;

    @ApiModelProperty(value = "岗位类别名称")
    private String personalIdentityName;


    @ApiModelProperty(value = "人员去向")
    private String workStatus;
    @ApiModelProperty(value = "人员去向")
    private String workStatusLable;
}
