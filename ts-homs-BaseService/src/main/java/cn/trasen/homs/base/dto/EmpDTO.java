package cn.trasen.homs.base.dto;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: EmpDTO
 * @Description: 人员信息DTO
 * <AUTHOR>
 * @date 2022年12月15日 下午4:32:20
 * 
 */
@Getter
@Setter
public class EmpDTO {
	private String id;//人员id
	private String memberId;//人员id
	private String code; // 人员代码（工号）
	private String office;//归属科室id
	private String officeName; //归属科室名称
	private String onState; // 在职状态 1在编，2退休，3聘用，4临时（通用字典：zzzt0001）
	private String status; // 有效状态 必填 2无效 1有效
	private String sysCode;//系统编码
	private String sysName;//系统名称
	private String hospCode;//院区编码
	private String orgCode; // 医院编码 必填
	private String orgName; // 医院名称 必填
	private String createUserId;//创建人id
	private String createDate;//创建时间
	private String name; // 姓名 必填
	private String sex; // 性别 必填 GBT 2261.1-2003
	private String age; // 年龄
	private String birthday; // 出生日期
	private String idCard; // 身份证号 必填
	private String phone; // 手机号 必填
	private String email; // 电子邮件 
	private String nationCode; // 民族代码 用国家标准GBT3304-1991.00.001
	private String nationName; // 民族名称
	private String memberRole; // 人员类型 1 医生 2 护士 3 医技 4 药剂 5 行政 6 收费 9 其他
	private String doctorTitle; // 职称 1-主任医师 2-副主任医师 3-主治医师 4-住院医师
	private String nurseTitle; // 护士级别 1护士、2护师、3主管护师，4副主任护师、5主任护师
	private String entryDate; //入职日期
	private String education; //学历
	private String memberType;//人员类别
	private String pyCode;//拼音码
	private String wbCode;//五笔码
	private Integer sortNo;//排序号
	private String isDelete;//删除标识
	private String medicalInsuranceCode;//国家医保编码
	private String operatorId;//操作人
	
	
	
	private List<MemberDepts> memberDepts ; //人员科室集合
	
}
