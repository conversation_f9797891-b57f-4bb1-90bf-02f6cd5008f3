package cn.trasen.homs.base.mapping.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.mapping.model.CustEmpMapping;
import cn.trasen.homs.base.mapping.service.CustEmpMappingService;
import cn.trasen.homs.base.mapping.vo.CustEmpMappingReqVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CustomEmployeeMappingController
 * @Description TODO
 * @date 2025-04-28 14:54:06
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "人员映射表")
public class CustEmpMappingController {

	private transient static final Logger logger = LoggerFactory.getLogger(CustEmpMappingController.class);
	
	@Autowired
	private CustEmpMappingService custEmpMappingService;
	
	/**
	 * @Title initMapping
	 * @Description 初始化人员映射表
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-04-29 11:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "初始化人员映射表", notes = "初始化人员映射表")
    @PostMapping("/api/customEmployeeMapping/initMapping")
    public PlatformResult<String> initMapping(@RequestBody CustEmpMappingReqVo record) {
        try {
        	custEmpMappingService.initMapping(record);
            return PlatformResult.success("初始化成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
	}

	/**
	 * @Title syncHisUser
	 * @Description 同步his的人员
	 * @return PlatformResult<String>
	 * @date 2025-09-02 11:08:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "同步his的人员", notes = "同步his的人员")
	@GetMapping("/api/customEmployeeMapping/syncHisUser")
	public PlatformResult<String> syncHisUser() {
		try {
			custEmpMappingService.syncHisUser();
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title saveCustEmpMapping
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/customEmployeeMapping/save")
	public PlatformResult<String> saveCustEmpMapping(@RequestBody CustEmpMapping record) {
		try {
			custEmpMappingService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCustEmpMapping
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/customEmployeeMapping/update")
	public PlatformResult<String> updateCustEmpMapping(@RequestBody CustEmpMapping record) {
		try {
			custEmpMappingService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCustEmpMappingById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CustEmpMapping>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/customEmployeeMapping/{id}")
	public PlatformResult<CustEmpMapping> selectCustEmpMappingById(@PathVariable String id) {
		try {
			CustEmpMapping record = custEmpMappingService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title deleteCustEmpMappingById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/customEmployeeMapping/delete/{id}")
	public PlatformResult<String> deleteCustEmpMappingById(@PathVariable String id) {
		try {
			custEmpMappingService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCustEmpMappingList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CustEmpMapping>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@PostMapping("/api/customEmployeeMapping/list")
	public DataSet<CustEmpMapping> selectCustEmpMappingList(Page page, CustEmpMapping record) {
		return custEmpMappingService.getDataSetList(page, record);
	}
	
}
