package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_seal_manage")
@Setter
@Getter
public class CommSealManage {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 科室id
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "科室id")
    private String deptId;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 印章名称
     */
    @Column(name = "seal_name")
    @ApiModelProperty(value = "印章名称")
    private String sealName;

    /**
     * 印章管理员工号
     */
    @Column(name = "seal_user")
    @ApiModelProperty(value = "印章管理员工号")
    private String sealUser;

    /**
     * 印章管理员名称
     */
    @Column(name = "seal_user_name")
    @ApiModelProperty(value = "印章管理员名称")
    private String sealUserName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String tel;

    /**
     * 印章图片
     */
    @Column(name = "seal_image")
    @ApiModelProperty(value = "印章图片")
    private String sealImage;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}