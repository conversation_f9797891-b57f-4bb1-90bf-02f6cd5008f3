package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.contants.DictContants;
import cn.trasen.homs.base.mapper.CommLoginLogsMapper;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.HrmsPostWage;
import cn.trasen.homs.base.service.CommLoginLogsService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.utils.Utils;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommLoginLogsServiceImpl
 * @Description TODO
 * @date 2023��9��20�� ����10:30:27
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommLoginLogsServiceImpl implements CommLoginLogsService {

    @Autowired
    private CommLoginLogsMapper mapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private IDictItemService hrmsDictInfoService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(CommLoginLogs record,String orgCode) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        
        Example example = new Example(CommLoginLogs.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("userCode", record.getUserCode());
       
        CommLoginLogs commLoginLogs = mapper.selectOneByExample(example);

        if (null != commLoginLogs) {
            commLoginLogs.setPasswordType(record.getPasswordType());
            commLoginLogs.setRectification(record.getRectification());
            commLoginLogs.setLoginIp(record.getLoginIp());
            commLoginLogs.setLoginTime(record.getLoginTime());
            commLoginLogs.setLoginType(record.getLoginType());
            commLoginLogs.setUserName(record.getUserName());
            if (user != null) {
            	commLoginLogs.setCreateUser(user.getUsercode());
                commLoginLogs.setCreateUserName(user.getUsername());
                commLoginLogs.setUpdateUser(user.getUsercode());
                commLoginLogs.setUpdateUserName(user.getUsername());
                commLoginLogs.setSsoOrgCode(user.getCorpcode());
            }else {
            	if(StringUtils.isBlank(record.getSsoOrgCode())) {
            		commLoginLogs.setSsoOrgCode(orgCode);
            	}
            }
            return mapper.updateByPrimaryKeySelective(commLoginLogs);
        } else {
            record.setId(IdGeneraterUtils.nextId());
            record.setCreateDate(new Date());
            record.setUpdateDate(new Date());
            record.setIsDeleted("N");
            
            if (user != null) {
                record.setCreateUser(user.getUsercode());
                record.setCreateUserName(user.getUsername());
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
                record.setSsoOrgCode(user.getCorpcode());
            }else {
            	if(StringUtils.isBlank(record.getSsoOrgCode())) {
            		record.setSsoOrgCode(orgCode);
            	}
            }
            return mapper.insertSelective(record);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(CommLoginLogs record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        CommLoginLogs record = new CommLoginLogs();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CommLoginLogs selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<CommLoginLogs> getDataSetList(Page page, CommLoginLogs record) {
    	
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        
        if("2".equals(record.getDisableStatus())){
       	    Set<String> keys = redisTemplate.keys("user:login:failed:*");
            List<String> employeeIds = new ArrayList<>();
            employeeIds.add("null");
            for (String key : keys) {
                employeeIds.add(key.replace("user:login:failed:", ""));
            }
            
            record.setEmployeeIds(employeeIds);
        }
        
        List<CommLoginLogs> records = mapper.getDataSetList(page, record);
        for (CommLoginLogs commLoginLogs : records) {
      	  Object object = redisTemplate.opsForValue().get("user:login:failed:" + commLoginLogs.getEmployeeId());
      	  if(null != object){
      		  commLoginLogs.setDisableStatus("2");
      	  }
		}
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    @Transactional(readOnly = false)
    public void updateByUserCode(CommLoginLogs record) {
        Example example = new Example(CommLoginLogs.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("userCode", record.getUserCode());

        CommLoginLogs commLoginLogs = mapper.selectOneByExample(example);

        if (null != commLoginLogs) {
            mapper.updateByExampleSelective(record, example);
        } else {
            record.setId(IdGeneraterUtils.nextId());
            record.setCreateDate(new Date());
            record.setUpdateDate(new Date());
            record.setIsDeleted("N");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                record.setCreateUser(user.getUsercode());
                record.setCreateUserName(user.getUsername());
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
                record.setSsoOrgCode(user.getCorpcode());
            }
            mapper.insertSelective(record);
        }


    }

    @Override
    @Transactional(readOnly = false)
    public void updateDisable(String employeeId, String status) {
        mapper.updateDisable(employeeId, status);
    }


    /**
     * @param loginDate: 登录日期
     * @param loginType: 登录方式 空表示不区别
     * @return Integer
     * <AUTHOR>
     * @description 统计从当前时间起, 不同的登录方式的登录次数
     * @date 2023/11/16 14:16
     */
    @Override
    @Transactional(readOnly = false)
    public Integer getLogins(String loginDate, String loginType,String queryDate) {

        int r = 0;

        r = mapper.getLogins(loginDate, loginType,UserInfoHolder.getCurrentUserCorpCode(),queryDate);

        return r;
    }

    @Override
    public List<CommLoginLogs> getLoginList(String loginDate,String queryDate) {

        // 获取用户的组织机构代码 这个地方请求这个接口 用户必然是登陆状态
        String orgCode = UserInfoHolder.getCurrentUserCorpCode();

        return mapper.getLoginList(loginDate,orgCode,queryDate);
    }

    @Override
    public String selectOpenIdByUserCode(String usercode) {
        return mapper.selectOpenIdByUserCode(usercode);
    }


    @Override
    public String selectOrgCodeByUserCode(String usercode) {
        return mapper.selectOrgCodeByUserCode(usercode);
    }

    @Override
    public String getUserOnlineListCacheKey() {
        String orgCode = UserInfoHolder.getCurrentUserCorpCode();
        String cacheKey = "thps:oa:online:*";

        if (StringUtils.isNotBlank(orgCode)) {
            cacheKey = "thps:oa:online:" + orgCode + ":*";
        }
        return cacheKey;
    }

    @Override
    public List<Map<String, String>> getMultipleOrg(String orgCode,String userCode) {
    	/**
    	 * 根据 客户定制化参数 CUSTOM_CODE 的 MULTIP_ORG 的项目值为1表示 宣威多机构，否则为通用
    	 * <AUTHOR>
    	 * @date 2025-07-05
    	 */
    	boolean isXuanWeiMultip = false;
    	List<DictItem> dictItemList = hrmsDictInfoService.getDictItemByTypeCode(DictContants.CUSTOM_CODE); // 客户定制化参数
    	if (dictItemList != null && dictItemList.size() > 0) {
            for (DictItem item : dictItemList) {
            	if(item.getItemCode().equals("MULTIP_ORG") && !ObjectUtils.isEmpty(item.getItemNameValue()) && item.getItemNameValue().equals("1")){
            		isXuanWeiMultip = true;
            	}
            }
        }
    	//根据手机号匹配多机构用户（多个账号对应多个机构的情况-目前使用于宣威）
    	if(isXuanWeiMultip){
    		return mapper.getMultipleOrgByPhoneNumber(orgCode,userCode);
    	} else {
    		//根据工号匹配多机构用户（一个账号对应多个机构的情况）
    		return mapper.getMultipleOrgByUserCode(userCode);
    	}
    }

	@Override
	public String getUserCodeByOldCode(String usercode) {
		return mapper.getUserCodeByOldCode(usercode);
	}
    
    
}
