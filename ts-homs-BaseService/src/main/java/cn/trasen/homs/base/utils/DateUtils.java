package cn.trasen.homs.base.utils;

import lombok.SneakyThrows;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Title: DateHelper.java
 * @Package cn.trasen.hrms.utils
 * @Description: 日期处理工具类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月9日 上午9:51:43
 * @version V1.0
 */
public class DateUtils {



	public static String getLastMonth() {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1); // 设置为上一个月
		date = calendar.getTime();
		return format.format(date);
	}

	/**
	 * <p>
	 * @Title: getPresentTimeStr
	 * </p>
	 * <p>
	 * @Description: 获取当前时间yyyy-MM-dd HH:mm:ss
	 * </p>
	 * <p>
	 * @Param:
	 * </p>
	 * <p>
	 * @Return: String
	 * </p>
	 * <P>
	 * @Date: 2020年7月28日 下午3:43:18
	 * </p>
	 * <p>
	 * <AUTHOR>
	 * </p>
	 */
	public static String getPresentTimeStr() {
		Date d = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(d);
	}
	
	public static String getStringDateShort(Date date) {
		if(date != null) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			String dateString = formatter.format(date);
			return dateString;
		}
		return "";
	}
	
    /**
     * 获取当前日期 yy-MM-dd
     *
     * @return 2019-08-27
     */
    public static String getDateByString() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd");
        return sdf.format(date);
    }
	
	@SneakyThrows
	public static Date getStringToDate(String str) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");// 注意月份是MM
		return simpleDateFormat.parse(str);
	}
	
	public static Date getStringToDatePlain(String str) throws Exception {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");// 注意月份是MM
		return simpleDateFormat.parse(str);
	}

	/**
	 * 获取两个日期之间的日期(年/月/日），包括开始结束日期
	 * 
	 * @param start
	 *            开始日期
	 * @param end
	 *            结束日期
	 * @return 日期集合
	 */
	public static List<String> getDateSection(String start, String end) {
		try {
			Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse(start);
			Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse(end);

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			// 获取开始年份和开始月份
			int startYear = calendar.get(Calendar.YEAR);
			int startMonth = calendar.get(Calendar.MONTH);
			// 获取结束年份和结束月份
			calendar.setTime(endDate);
			int endYear = calendar.get(Calendar.YEAR);
			int endMonth = calendar.get(Calendar.MONTH);
			//
			List<String> list = new ArrayList<String>();
			for (int i = startYear; i <= endYear; i++) {
				String date = "";
				if (startYear == endYear) {
					for (int j = startMonth; j <= endMonth; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						list.add(date);
					}

				} else {
					if (i == startYear) {
						for (int j = startMonth; j < 12; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					} else if (i == endYear) {
						for (int j = 0; j <= endMonth; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					} else {
						for (int j = 0; j < 12; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					}
				}

			}
			return list;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 
	* @Title: getTwoMonthBefore  
	* @Description: 获取时间前后几个月的时间
	* @Params: @param date
	* @Params: @param month
	* @Params: @return      
	* @Return: String
	* <AUTHOR>
	* @date:2021年9月15日
	* @Throws
	 */
	public static String getMonthDifferDate(Date date,int month){
		 
        // 获取当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(date);//把当前时间赋给日历
        calendar.add(calendar.MONTH, month); //设置为前2月，可根据需求进行修改
        date = calendar.getTime();//获取2个月前的时间
 
        return dateFormat.format(date);
    }
	
	
	
	/**   
	 * @Title: dateDiff   
	 * @Description: 获取时间相差小时 
	 * @param: @param startTime  2016-05-01 12:00
	 * @param: @param endTime 2016-05-01 14:00
	 * @param: @return      
	 * @return: long      
	 * @throws   
	 */
	public static double dateDiff(String startTime, String endTime) {
		double diff = 0.0;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long startDate = simpleDateFormat.parse(startTime).getTime();
			long endDate = simpleDateFormat.parse(endTime).getTime();
		    diff = (endDate-startDate)/1000/(double)60;
	        return (double) diff;

		} catch (ParseException e) {
			e.printStackTrace();
		}
      return (double) diff;
	}
	
	
	/**   
	 * @Title: judgeSize   
	 * @Description: 判断结束时间大于开始时间   
	 * @param: @param startTime
	 * @param: @param endTime
	 * @param: @return      
	 * @return: boolean      
	 * @throws   
	 */
	public static boolean judgeSize(String startTime, String endTime) {
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long startDate = simpleDateFormat.parse(startTime).getTime();
			long endDate = simpleDateFormat.parse(endTime).getTime();
	        return endDate > startDate ;
		} catch (ParseException e) {
			e.printStackTrace();
		}
      return false;
	}
	
	
	
	/**   
	 * @Title: getMonthFullDay   
	 * @Description: 传入年月获取当月所有日期    
	 * @param: @param yyyy
	 * @param: @param mm
	 * @param: @return     2021-09-01 
	 * @return: List<String>      
	 * @throws   
	 */
	public static List<String> getMonthFullDay(String yyyy , String mm){
		int year = Integer.valueOf(yyyy);
		int month = Integer.valueOf(mm);
	    SimpleDateFormat dateFormatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
	    List<String> fullDayList = new ArrayList<>(32);
	    // 获得当前日期对象
	    Calendar cal = Calendar.getInstance();
	    cal.clear();// 清除信息
	    cal.set(Calendar.YEAR, year);
	    // 1月从0开始
	    cal.set(Calendar.MONTH, month-1 );
	    // 当月1号
	    cal.set(Calendar.DAY_OF_MONTH,1);
	    int count = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	    for (int j = 1; j <= count ; j++) {
	        fullDayList.add(dateFormatYYYYMMDD.format(cal.getTime()));
	        cal.add(Calendar.DAY_OF_MONTH,1);
	    }
	    return fullDayList;
	}
	
	public static String getLastDayOfMonth(String yearMonth) {
		int year = Integer.parseInt(yearMonth.split("-")[0]);  //年
		int month = Integer.parseInt(yearMonth.split("-")[1]); //月
		Calendar cal = Calendar.getInstance();
		// 设置年份
		cal.set(Calendar.YEAR, year);
		// 设置月份
		// cal.set(Calendar.MONTH, month - 1);
		cal.set(Calendar.MONTH, month); //设置当前月的上一个月
		// 获取某月最大天数
		//int lastDay = cal.getActualMaximum(Calendar.DATE);
		int lastDay = cal.getMinimum(Calendar.DATE); //获取月份中的最小值，即第一天
		// 设置日历中月份的最大天数
		//cal.set(Calendar.DAY_OF_MONTH, lastDay);
		cal.set(Calendar.DAY_OF_MONTH, lastDay - 1); //上月的第一天减去1就是当月的最后一天
		// 格式化日期
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(cal.getTime());
	}
	
	public static String getLastMonth(String dateStr, int addYear, int addMonth, int addDate)  {
		try {
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
			java.util.Date sourceDate = sdf.parse(dateStr);
			Calendar cal = Calendar.getInstance();
			cal.setTime(sourceDate);
			cal.add(Calendar.YEAR, addYear);
			cal.add(Calendar.MONTH, addMonth);
			cal.add(Calendar.DATE, addDate);
			java.text.SimpleDateFormat returnSdf = new java.text.SimpleDateFormat("yyyy-MM");
			String dateTmp = returnSdf.format(cal.getTime());
//			java.util.Date returnDate = returnSdf.parse(dateTmp);
			return dateTmp;
		} catch (Exception e) {
			e.printStackTrace();
			return "";		
		}

	}

	/**
	 * Date 转 LocalDate
	 * @param date
	 * @return
	 */
	public static LocalDate turnLocalDate(Date date) {
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}
}
