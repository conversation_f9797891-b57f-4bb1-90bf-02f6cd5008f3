package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;

public interface JdbcService {
    
	/**
	 * 
	* @Title: addFieldNew  
	* @Description: 新增字段
	* @Params: @param field
	* @Params: @param tableName
	* @Params: @return      
	* @Return: String
	* <AUTHOR>
	* @date:2021年6月16日
	* @Throws
	 */
    public String addFieldNew(CommEmployeeField field,String tableName); 
    
    /**
     * 
    * @Title: insert  
    * @Description: 新增人员档案
    * @Params: @param records
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月23日
    * @Throws
     */
    public String  insertEmployee(List<CommEmployeeField> records,String tableName,String id);
    
    /**
     * 
    * @Title: insertEmployeeDetail  
    * @Description: 新增员工明细信息
    * @Params: @param records
    * @Params: @param tableName
    * @Params: @param id
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    public String  insertEmployeeDetail(List<CommEmployeeField> records,String tableName,String employeeId);
    
    /**
     * 
    * @Title: updateEmployee  
    * @Description: 修改人员档案
    * @Params: @param records
    * @Params: @param tableName
    * @Params: @param id
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月23日
    * @Throws
     */
    public String  updateEmployee(List<CommEmployeeField> records,String tableName,String id);
    
    /**
     * 
    * @Title: deleteEmployeeDetailByEmployeeId  
    * @Description: 根据员工id删除明细表数据
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    public String  deleteEmployeeDetailByEmployeeId(String employeeId,String tableName,CommEmployeeFieldGroup commEmployeeFieldGroup);
    
    /**
     * 
    * @Title: createTable  
    * @Description: 创建表
    * @Params: @param group
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    String createTable(CommEmployeeFieldGroup group);
    
    /**
     * 
    * @Title: getEmployeeDetailInfoByShow  
    * @Description: 根据设置显示的字段,查询员工明细表业务数据
    * @Params: @param employeeId
    * @Params: @param fields
    * @Params: @param tableName
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月28日
    * @Throws
     */
    String getEmployeeDetailInfoByShow(String employeeId,List<CommEmployeeField> fields,String tableName);

	public String updateFieldLength(String tableName, String fieldName, Integer fieldLength,String showName);
}

