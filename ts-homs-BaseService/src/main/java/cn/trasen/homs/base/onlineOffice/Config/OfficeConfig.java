/************************************************
* @功能描述: TODO
* @Title: OfficeConfig.java
* @Package cn.trasen.homs.base.onlineOffice.DTO
* <AUTHOR>
* @date 2025年6月16日 下午12:08:00
* @version V1.0
*************************************************/
package cn.trasen.homs.base.onlineOffice.Config;

import cn.trasen.homs.base.model.FileAttachment;
import lombok.Data;

/**
* @ClassName: OfficeConfig
* @Description: TODO 
* <AUTHOR>
* @date 2025年6月16日 下午12:08:00
*
*/
@Data
public class OfficeConfig {
    private FileAttachment documentContext;
    private String documentType;
    private Document document;
    private EditorConfig editorConfig;
    
    @Data
    public static class Document {
        private String title;
        private String url;
        private String fileType;
        private String key;
    }
    
    @Data
    public static class EditorConfig {
        private String callbackUrl;
        private User user;
    }
    
    @Data
    public static class User {
        private String id;
        private String name;
        
        public User(String id, String name) {
            this.id = id;
            this.name = name;
        }
    }
}