package cn.trasen.homs.base.service.impl;



import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.dao.GlobalSettingMapper;
import cn.trasen.homs.base.service.EmployeeService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class GlobalSettingServiceImpl implements GlobalSettingsService {

    @Resource
    GlobalSettingMapper globalSettingMapper;
    
    @Resource
    HrmsEmployeeService hrmsEmployeeService;

    @Override
    public GlobalSetting getGlobalSetting(String isAll) {
        
    	return globalSettingMapper.getGlobalSetting(isAll,UserInfoHolder.getCurrentUserCorpCode());
    }

    @Override
    public GlobalSetting getGlobalSettingById(GlobalSetting globalSetting) {
        return globalSettingMapper.selectByPrimaryKey(globalSetting);
    }

    @Override
    @Transactional(readOnly = false)
    public void insert(GlobalSetting globalSetting) {
        if(StringUtils.isNotBlank(globalSetting.getId())) {
            globalSetting.setUpdateDate(new Date());
            globalSetting.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            globalSetting.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            
            setPasswordExpireDate(globalSetting);
            
            
            globalSettingMapper.updateByPrimaryKeySelective(globalSetting);
        }else {
            globalSetting.setId(String.valueOf(IdWork.id.nextId()));
            globalSetting.setCreateUser(UserInfoHolder.getCurrentUserCode());
            globalSetting.setCreateUserName(UserInfoHolder.getCurrentUserName());
            globalSetting.setCreateDate(new Date());
            globalSetting.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            
            setPasswordExpireDate(globalSetting);
            
            globalSettingMapper.insert(globalSetting);
        }
        
    }

    @Override
    @Transactional(readOnly = false)
    public void update(GlobalSetting globalSetting) {
    	
    	setPasswordExpireDate(globalSetting);
    	/**
    	 * 界面没有维护 平台登录类型-platformLoginType 这里和界面维护字段 platformType 保持一直
    	 * <AUTHOR>
    	 * @date 2025-07-05
    	 */
    	globalSetting.setPlatformLoginType(globalSetting.getPlatformType());
    	
        globalSettingMapper.updateByPrimaryKeySelective(globalSetting);
    }
    
	private void setPasswordExpireDate(GlobalSetting globalSetting) {
		
		if(null != globalSetting.getPasswordExpire() && 1 == globalSetting.getPasswordExpire()) {
			
			Integer passwordExpireDays = globalSetting.getPasswordExpireDays();
			
			DateTime passwordExpireDate = DateUtil.offsetDay(new Date(), passwordExpireDays);
			
			globalSettingMapper.updatePasswordExpireDate(passwordExpireDate,null);
		}
	}

    @Override
    @Transactional(readOnly = false)
    public void deleted(String id) {
        globalSettingMapper.deleteByPrimaryKey(id);
    }

    @Override
    public GlobalSetting getSafeGlobalSetting() {
         return globalSettingMapper.getGlobalSetting("N",UserInfoHolder.getCurrentUserCorpCode());
    }

	@Override
	@Transactional(readOnly = false)
	public void updatePasswordExpireDate(String employeeId) {
		
		GlobalSetting globalSetting = getGlobalSetting("Y");
		
		if(null != globalSetting.getPasswordExpire() && 1 == globalSetting.getPasswordExpire()) {
			
			HrmsEmployeeResp hrmsEmployeeResp = hrmsEmployeeService.findByEmployeeId(employeeId);
			
			Integer passwordExpireDays = globalSetting.getPasswordExpireDays();
			
			DateTime passwordExpireDate = DateUtil.offsetDay(hrmsEmployeeResp.getPasswordExpireDate(), passwordExpireDays);
			
			globalSettingMapper.updatePasswordExpireDate(passwordExpireDate,employeeId);
		}
	}
    
}
