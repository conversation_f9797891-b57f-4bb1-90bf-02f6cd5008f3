package cn.trasen.homs.base.service.impl;

import cn.trasen.BootComm.utils.JqGridBean;
import cn.trasen.homs.base.mapper.CommEmployeeShowServiceMapper;
import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeShow;
import cn.trasen.homs.base.service.CommEmployeeFieldService;
import cn.trasen.homs.base.service.CommEmployeeShowService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.workflow.WorkflowFeignService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tk.mybatis.mapper.util.StringUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/** 
* @ClassName: CommEmployeeShowServiceImpl 
* @Description: 自定义列展示导出实现类
* <AUTHOR>  
* @date 2022年12月5日 下午3:45:28 
*  
*/
@Service
public class CommEmployeeShowServiceImpl implements CommEmployeeShowService {
    
    
    @Autowired
    CommEmployeeFieldService commEmployeeFieldService;
    
    @Autowired
    CommEmployeeShowServiceMapper commEmployeeShowServiceMapper;
    @Autowired
    WorkflowFeignService feignService;

    @Override
    public List<JqGridBean> getList() {
        //查询表里面显示的字段
        // 查询本身数据 （多余的添加， 少了的停用）
        Example example = new Example(CommEmployeeShow.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("fieldShow", "1");  //显示的 
        createCriteria.andEqualTo("isDeleted", "N");
        example.setOrderByClause("field_sort asc");
        List<CommEmployeeShow> showList = commEmployeeShowServiceMapper.selectByExample(example); //列表

        List<JqGridBean> list = new ArrayList<>();
        if(showList != null && showList.size() > 0) {
            for (int i = 0; i < showList.size(); i++) {
                JqGridBean bean = new JqGridBean();
                if(i <= 2){
                    bean.setFrozen(true);
                }
                bean.setLabel(showList.get(i).getFieldLable());
                bean.setIndex(showList.get(i).getFieldIndex());
                bean.setName(showList.get(i).getFieldName());
                bean.setHidden(false);
                bean.setAlign(showList.get(i).getFieldAlign());
                Integer fieldSortable = showList.get(i).getFieldSortable();
                if(null != fieldSortable && 1 == fieldSortable) {
                    bean.setSortable(true);
                }else {
                    bean.setSortable(false);
                }
                String fieldWidth = showList.get(i).getFieldWidth();
                if(!StringUtil.isEmpty(fieldWidth)) {
                    bean.setWidth(Integer.valueOf(fieldWidth));
                }else {
                    bean.setWidth(80);
                }

                bean.setEditable(false);
                list.add(bean);
            }
        }
        return list;
    }
    
    //刷新数据
    @Override
    public void ref() {
        
        List<CommEmployeeShow> insertList = new ArrayList<CommEmployeeShow>();
        List<CommEmployeeShow> delList = new ArrayList<CommEmployeeShow>();
        //数据从字段表  添加到是否显示的表
        CommEmployeeField record = new CommEmployeeField();
        record.setGroupId("1");
        record.setIsHide(0);
        record.setIsDisabled(0);
        //查询主表数据
        List<CommEmployeeField> fielListByGroupid = commEmployeeFieldService.getFielListByGroupid(record);
        
        // 查询本身数据 （多余的添加， 少了的停用）
        Example example = new Example(CommEmployeeShow.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("fieldFixed", "0");  //不固定的
        createCriteria.andEqualTo("isDeleted", "N");
        List<CommEmployeeShow> showList = commEmployeeShowServiceMapper.selectByExample(example); //列表
        
        //对比要删除的数据
        if(showList != null && showList.size() > 0) {
            for (int f = 0; f < showList.size(); f++) {
                boolean del = false;
                for (int i = 0; i < fielListByGroupid.size(); i++) {
                    if(fielListByGroupid.get(i).getId().equals(showList.get(f).getFieldId())) {
                        del = true;
                    }
                }
                if(!del) {
                    delList.add(showList.get(f));
                }
            }
        }
        
        if(delList != null && delList.size() >0) {  //删除已停用的
            delList.forEach(item ->{
                commEmployeeShowServiceMapper.deleteByPrimaryKey(item.getId());
            });
        }
        
        //对比要插入的数据
        for (int i = 0; i < fielListByGroupid.size(); i++) {
            if (showList != null && showList.size() > 0) {
                boolean include = false;
                for (int f = 0; f < showList.size(); f++) {
                    if(fielListByGroupid.get(i).getId().equals(showList.get(f).getFieldId())) {
                        include = true;
                        continue; 
                    }
                }
                if(!include) {  //不存在就是要显示到列表的
                    CommEmployeeShow addBean = new CommEmployeeShow();
                    addBean.setId(String.valueOf(IdWork.id.nextId()));
                    addBean.setFieldId(fielListByGroupid.get(i).getId());
                    addBean.setFieldName(fielListByGroupid.get(i).getFieldName());
                    addBean.setFieldIndex(fielListByGroupid.get(i).getFieldName());
                    addBean.setFieldLable(fielListByGroupid.get(i).getShowName());
                    addBean.setFieldAlign("left");  //显示位置
                    addBean.setFieldFixed(0);
                    //创建人创建时间 。。。
                    addBean.setCreateDate(new Date());
                    addBean.setIsDeleted("N");
                    addBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    insertList.add(addBean);
                }
            }else { //没有数据就直接插入
                
                CommEmployeeShow addBean = new CommEmployeeShow();
                addBean.setId(String.valueOf(IdWork.id.nextId()));
                addBean.setFieldId(fielListByGroupid.get(i).getId());
                addBean.setFieldName(fielListByGroupid.get(i).getFieldName());
                addBean.setFieldIndex(fielListByGroupid.get(i).getFieldName());
                addBean.setFieldLable(fielListByGroupid.get(i).getShowName());
                addBean.setFieldAlign("left");  //显示位置
                addBean.setFieldFixed(0);
                //创建人创建时间 。。。
                addBean.setCreateDate(new Date());
                addBean.setIsDeleted("N");
                addBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                insertList.add(addBean);
            }
        }
    
        //插入 自定义列表 数据
        
        if(insertList!= null && insertList.size() > 0) {
            insertList.forEach(item -> commEmployeeShowServiceMapper.insertSelective(item) );
        }
    }
    
    //获取列表
    @Override
    public List<CommEmployeeShow> getshowColumnList(Page page,CommEmployeeShow record) {
        Example example = new Example(CommEmployeeShow.class);
        Criteria createCriteria = example.createCriteria();
        // createCriteria.andEqualTo("fieldShow", "1"); //显示的
        
        createCriteria.andEqualTo("isDeleted", "N");
        // 查询条件

        if (StringUtils.isNotBlank(record.getFieldLable())) {
            example.and().andLike("fieldLable", "%" + record.getFieldLable() + "%");
        }
        if (StringUtils.isNotBlank(record.getFieldName())) {
            example.and().andLike("fieldName", "%" + record.getFieldName() + "%");
        }
        if(StringUtils.isNotBlank(page.getSidx()) && StringUtils.isNotBlank(page.getSord())) {
            example.setOrderByClause(page.getSidx() + " is null , " + page.getSidx() + " " + page.getSord());
        }else {
            example.setOrderByClause(" field_sort asc");
        }
        List<CommEmployeeShow> showList = commEmployeeShowServiceMapper.selectByExample(example); // 列表
        return showList;
    }
    
    //修改数据
    @Override
    public Integer update(CommEmployeeShow record) {
        //处理空值
        if(null ==  record.getFieldShow()) {
            record.setFieldShow(0);
        }
        if(null ==  record.getFieldSortable()) {
            record.setFieldSortable(0);
        }


        if(null ==  record.getFieldExport()) {
            record.setFieldExport(0);
        }
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        record.setUpdateDate(new Date());
        return commEmployeeShowServiceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<CommEmployeeShow> getColumnExport() {
        Example example = new Example(CommEmployeeShow.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("fieldExport", "1");  //导出的
        createCriteria.andEqualTo("isDeleted", "N");
        example.setOrderByClause(" field_export_sort asc");  //按导出顺序排序
        List<CommEmployeeShow> showList = commEmployeeShowServiceMapper.selectByExample(example); //列表
        return showList;
    }

}
