package cn.trasen.homs.base.saasOrg.dao;

import java.util.List;

import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.vo.OrgData;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsOrgMapper extends Mapper<HrmsOrg> {
	
	/**
	 * 获取机构列表
	 * @param record
	 * @return
	 * @date 2025-07-12 11:30:00
	 * <AUTHOR>
	 */
	List<HrmsOrg> selectList(HrmsOrg record);

	/**
	 * 初始化新增机构相关数据
	 * @param orgData
	 * @return
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
	void initAddOrgData(OrgData orgData);
	
	/**
	 * 更新机构的其他信息
	 * @param orgData
	 * @return
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
	void updateOrgData(OrgData orgData);
	
	/**
	 * 删除的机构的其他信息
	 * @param orgData
	 * @return
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
	void deleteOrgData(OrgData orgData);
	
}
