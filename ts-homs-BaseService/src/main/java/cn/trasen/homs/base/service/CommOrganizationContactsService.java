package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Set;

import cn.trasen.homs.base.bean.OrganizationContactsImport;
import cn.trasen.homs.base.bean.OrganizationContactsImportResp;
import cn.trasen.homs.base.model.CommOrganizationContacts;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommOrganizationContactsService
 * @Description TODO
 * @date 2023年11月20日 上午11:43:39
 */
public interface CommOrganizationContactsService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    Integer save(CommOrganizationContacts record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    Integer update(CommOrganizationContacts record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return CommOrganizationContacts
     * @Title selectById
     * @Description 根据ID查询
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    CommOrganizationContacts selectById(String id);

    DataSet<CommOrganizationContacts> getDataSetList(Page page, Set<String> id,String keyword);

    Boolean checkExist(CommOrganizationContacts record);

    OrganizationContactsImportResp excelImport(List<OrganizationContactsImport> list);

    int batchInsert(List<CommOrganizationContacts> list);

    void syncOrganization2Contacts();

    void syncContacts2OrganizationSingleTime(String act, CommOrganizationContacts record);

}
