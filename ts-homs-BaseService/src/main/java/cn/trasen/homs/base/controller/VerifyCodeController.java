package cn.trasen.homs.base.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ramostear.captcha.HappyCaptcha;
import com.ramostear.captcha.support.CaptchaType;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "验证码")
@RestController
public class VerifyCodeController {
	
		@GetMapping("/verify/captcha")
		@ApiOperation(value = "生成验证码")
		public void happyCaptcha(HttpServletRequest request, HttpServletResponse response){
			    HappyCaptcha.require(request,response).type(CaptchaType.NUMBER).build().finish();
		}
		
		
		@PostMapping("/verify/verifyCode")
		@ApiOperation(value = "校验CODE")
		public String verify(String code,HttpServletRequest request){
		    boolean flag = HappyCaptcha.verification(request,code,true);
		    if(flag){
		    	 return "1";
		    }else{
		    	 return "0";
		    }
		}
		
		@GetMapping("/verify/remove/captcha")
		@ApiOperation(value = "验证码清理")
		public void removeCaptcha(HttpServletRequest request){
		    HappyCaptcha.remove(request);   
		}
}
