package cn.trasen.homs.base.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "comm_personal_filter")
@Setter
@Getter
public class CommPersonalFilter {
	@Id
    private String id;

    /**
     * 筛选器名称
     */
    @Column(name = "filter_name")
    @ApiModelProperty(value = "筛选器名称")
    private String filterName;

    /**
     * 菜单id
     */
    @Column(name = "menu_id")
    @ApiModelProperty(value = "菜单id")
    private String menuId;

    /**
     * 菜单名称
     */
    @Column(name = "menu_name")
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /**
     * 筛选器条件map
     */
    @Column(name = "condition_map")
    @ApiModelProperty(value = "筛选器条件map")
    private String conditionMap;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    private Date updateDate;

    /**
     * 筛选问题类型
     */
    @Column(name = "filter_type")
    @ApiModelProperty(value = "筛选问题类型")
    private Integer filterType;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}