package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.dao.PortalElementCustomMapper;
import cn.trasen.homs.base.model.PortalElementCustom;
import cn.trasen.homs.base.service.PortalElementCustomService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PortalElementCustomServiceImpl
 * @Description TODO
 * @date 2024��9��21�� ����10:52:59
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PortalElementCustomServiceImpl implements PortalElementCustomService {

	@Autowired
	private PortalElementCustomMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PortalElementCustom record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PortalElementCustom record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PortalElementCustom record = new PortalElementCustom();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PortalElementCustom selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PortalElementCustom> getDataSetList(Page page, PortalElementCustom record) {
		Example example = new Example(PortalElementCustom.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PortalElementCustom> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	

	@Override
	public List<PortalElementCustom> getElementCustomByUserCode(String themeId,String userCode) {
		Example example = new Example(PortalElementCustom.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("themeId", themeId);
		criteria.andEqualTo("createUser", userCode);
		return mapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public void savePortalElementCustom(List<PortalElementCustom> records) {
		
		if(CollectionUtils.isNotEmpty(records)) {
			
			//新增之前先删除
			PortalElementCustom del = new PortalElementCustom();
			del.setCreateUser(UserInfoHolder.getCurrentUserCode());
			del.setThemeId(records.get(0).getThemeId());
			mapper.delete(del);
			
			for (PortalElementCustom portalElementCustom : records) {
				save(portalElementCustom);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void restore(String themeId) {
		PortalElementCustom del = new PortalElementCustom();
		del.setCreateUser(UserInfoHolder.getCurrentUserCode());
		del.setThemeId(themeId);
		mapper.delete(del);
	}
	
	
}
