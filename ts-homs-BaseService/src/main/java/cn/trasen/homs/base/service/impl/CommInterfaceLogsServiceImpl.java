package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.dao.CommInterfaceLogsMapper;
import cn.trasen.homs.base.model.CommInterfaceLogs;
import cn.trasen.homs.base.service.CommInterfaceLogsService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommInterfaceLogsServiceImpl
 * @Description TODO
 * @date 2024��8��22�� ����7:03:36
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommInterfaceLogsServiceImpl implements CommInterfaceLogsService {

	@Autowired
	private CommInterfaceLogsMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommInterfaceLogs record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommInterfaceLogs record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CommInterfaceLogs record = new CommInterfaceLogs();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CommInterfaceLogs selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommInterfaceLogs> getDataSetList(Page page, CommInterfaceLogs record) {
		Example example = new Example(CommInterfaceLogs.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getInterfaceName())) {
			example.and().andLike("interfaceName", "%" + record.getInterfaceName() + "%");
		}
		
		if(StringUtils.isNotBlank(record.getStartDate()) && StringUtils.isNotBlank(record.getEndDate())) {
			example.and().andBetween("createDate", record.getStartDate() + " 00:00:00", record.getEndDate() + " 23:59:59");
		}
		
		example.orderBy("createDate").desc();
		
		List<CommInterfaceLogs> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
