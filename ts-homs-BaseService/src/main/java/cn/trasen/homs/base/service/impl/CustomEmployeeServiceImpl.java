/**
 * @Title: CustomEmployeeServiceImpl.java
 * @Package: cn.trasen.homs.base.service.impl
 * @Date: 2021年6月19日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.BootComm.excel.ExportFieldsAnnotation;
import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.base.bean.CustomEmployeeResp;
import cn.trasen.homs.base.bean.EmployeeStorageData;
import cn.trasen.homs.base.bean.HistoricalListResp;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeUpdateDetailMapper;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeUpdateOperationMapper;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateDetail;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateOperation;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeLeaderService;
import cn.trasen.homs.base.dao.CommCityMapper;
import cn.trasen.homs.base.dao.CommEmployeeUpdateDetailMapper;
import cn.trasen.homs.base.dao.CommEmployeeUpdateOperationMapper;
import cn.trasen.homs.base.enums.InsertOrUpdateEnum;
import cn.trasen.homs.base.groupLeader.service.HrmsRoleGroupLeaderService;
import cn.trasen.homs.base.mapper.CustomEmployeeMapper;
import cn.trasen.homs.base.mapper.HrmsJobtitleBasicMapper;
import cn.trasen.homs.base.mapper.HrmsPositionMapper;
import cn.trasen.homs.base.model.CommCity;
import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.model.CommEmployeeUpdateDetail;
import cn.trasen.homs.base.model.CommEmployeeUpdateOperation;
import cn.trasen.homs.base.model.CustomEmpModel;
import cn.trasen.homs.base.model.CustomEmployeeFieldModel;
import cn.trasen.homs.base.model.ExportEmployee;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.base.model.HrmsJobtitleBasic;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.model.HrmsPosition;
import cn.trasen.homs.base.model.Post;
import cn.trasen.homs.base.service.CommEmployeeFieldGroupService;
import cn.trasen.homs.base.service.CommEmployeeFieldService;
import cn.trasen.homs.base.service.CustomEmployeeService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.HrmsOrganizationService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.base.utils.JdbcTemplateUtil;
import cn.trasen.homs.base.utils.JsonUtil;
import cn.trasen.homs.bean.base.HrmsAdvancementIncidentReq;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.common.NoticeController;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.hrms.HrmsIncidentFeignService;
import cn.trasen.homs.feign.workflow.WorkflowFeignService;
import cn.trasen.homs.message.model.Notice;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

//import freemarker.template.utility.StringUtil;

//import freemarker.template.utility.StringUtil;

/**
 * @ClassName: CustomEmployeeServiceImpl
 * @Author: 86189
 * @Date: 2021年6月19日
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class CustomEmployeeServiceImpl implements CustomEmployeeService {
    private static final Logger logger = LoggerFactory.getLogger(CustomEmployeeServiceImpl.class);

    @Autowired
    private CustomEmployeeMapper customEmployeeMapper;

    @Autowired
    private HrmsEmployeeService hrmsEmployeeService;

    @Autowired
    private JdbcTemplateUtil jdbcTemplateUtil;

    @Autowired
    private CommEmployeeFieldGroupService commEmployeeFieldGroupService;

    @Autowired
    private CommEmployeeFieldService commEmployeeFieldService;

    @Autowired
    private CommEmployeeUpdateOperationMapper commEmployeeUpdateOperationMapper;

    @Autowired
    private WorkflowInstanceService workflowInstanceService;

    @Autowired
    private CommEmployeeUpdateDetailMapper commEmployeeUpdateDetailMapper;

    @Autowired
    private IDictItemService dictItemService;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Autowired
    private HrmsJobtitleBasicMapper hrmsJobtitleBasicMapper;

    @Autowired
    private CommCityMapper commCityMapper;

    @Autowired
    private HrmsPositionMapper hrmsPositionMapper;

    @Autowired
    private HrmsOrganizationService hrmsOrganizationService;

    @Autowired
    private IPostService iPostService;

    @Autowired
    GlobalSettingsService globalSettingsService;

    @Autowired
    HrmsRoleGroupLeaderService hrmsRoleGroupLeaderService;
    @Autowired
    WorkflowFeignService feignService;

    @Autowired
    HrmsIncidentFeignService incidentFeignService;
    
    @Autowired
    NoticeController noticeController;
    
    @Autowired
    AppConfigProperties appConfigProperties;
    
    @Autowired
    CustomEmployeeLeaderService customEmployeeLeaderService;
    
    @Autowired
    CustomEmployeeUpdateOperationMapper customEmployeeUpdateOperationMapper;
    
    @Autowired
    CustomEmployeeUpdateDetailMapper customEmployeeUpdateDetailMapper;

    /**
     * @Title: getEmployeePageListByCustom
     * @Description: 分页获取自定义人员档案列表
     * @Params: @param page
     * @Params: @param record
     * @Params: @return
     * @Return: List<Map < String, String>>
     * <AUTHOR>
     * @date:2021年6月19日
     * @Throws
     */
    @Override
    public List<Map<String, String>> getEmployeePageListByCustom(Page page, HrmsEmployee record) {

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        // 增加数据权限控制
        Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
        Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
        boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");

        if (!isAdmin && !isALL && !isInfoAdmin) {

            boolean isSub = UserInfoHolder.getRight("IS_SUB"); // 下属管理权限

            // 跟医德医风权限冲突,所有先暂时写死,去除医德医风
            boolean moralityMaster = UserInfoHolder.getRight("morality_master");

            if (isSub && !moralityMaster) {

                String orgRang = user.getOrgRang();
                List<String> orgCodeList = new ArrayList<>();
                if (StringUtils.isNotBlank(orgRang)) {
                    String[] orgList = (orgRang.substring(1, orgRang.length() - 1).replaceAll("'", "").split(","));
                    for (String orgCode : orgList) {
                        if (StringUtils.isNotBlank(orgCode)) {
                            orgCodeList.add(StringUtils.deleteWhitespace(orgCode));
                        }
                    }
                }
                // 本人 SELF 本部门 SELF_DEPT SELF_SUB_DEPT 本部门以及下属部门
                // 直接查询本部门 或者 下属部门权限
                List<String> hrmsOrganizationAndNextList = hrmsOrganizationService
                        .getHrmsOrganizationAndNextList(user.getDeptId());

                orgCodeList.addAll(hrmsOrganizationAndNextList);
                record.setOrgCodeList(orgCodeList);
            } else if (moralityMaster) { // 如果有医德医风的权限 、暂时只能看自己科室
                List<String> orgCodeList = new ArrayList<>();
                orgCodeList.add(user.getDeptcode());
                record.setOrgCodeList(orgCodeList);
            } else {
                if (user != null) {
                    record.setCreateUser(user.getUsercode());
                }

            }

        }
        GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
        String orgCode = globalSetting.getOrgCode();
        // 二福按左边机构排序
        if ("cssdeshfly".equals(orgCode)) {
            if (!StringUtils.isEmpty(page.getSidx())) {
                if ("create_date".equals(page.getSidx())) {
                    page.setSidx(" o.ksfb_no");
                    page.setSord("ASC");
                }
            }
        }

        // 医务科护理部办公室特殊要求处理
        List<String> roleByUserCode = hrmsRoleGroupLeaderService.getRoleByUserCode(user.getUsercode());
        record.setRoleByUserCode(roleByUserCode);

        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<Map<String, String>> list = customEmployeeMapper.getEmployeePageListByCustom(page, record);

        // 查询页面字段
        CommEmployeeField commEmployeeField = new CommEmployeeField();
        List<String> groupIds = new ArrayList<>();
        groupIds.add("1");
        groupIds.add("2");
        groupIds.add("3");
        commEmployeeField.setIsHide(0);
        commEmployeeField.setGroupIds(groupIds);
        List<CommEmployeeField> fieldList = commEmployeeFieldService.getFieldsListByCondition(commEmployeeField);

        setDictValue(list, fieldList);

        return list;
    }

    /**
     *
     * @Title: setDictValue
     * @Description: 设置字典值
     * @Params: @param list
     * @Params: @param fieldList
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月1日
     * @Throws
     */
    /**
     * @Title: setDictValue
     * @Description: 设置字典值
     * @Params: @param list
     * @Params: @param fieldList
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月1日
     * @Throws
     */
    private void setDictValue(List<Map<String, String>> list, List<CommEmployeeField> fieldList) {

        Map<String, Map<String, String>> dictMap = dictItemService.getDictMap();

        for (CommEmployeeField field : fieldList) {

            if (StringUtils.isNotBlank(field.getDictSource())) {

                String diceSource = field.getDictSource();

                Map<String, String> itemMap = dictMap.get(diceSource);

                for (Map<String, String> dataMap : list) {

                    for (String key : dataMap.keySet()) {

                        if (key.equals(field.getFieldName())) {

                            if (null != itemMap && null != dataMap.get(key)) {

                                dataMap.put(key, itemMap.get(dataMap.get(key)));
                            }
                        }
                    }
                }
            } else {

                if (field.getFieldName().equals("born_address")) {

                    for (Map<String, String> dataMap : list) {

                        for (String key : dataMap.keySet()) {

                            if (key.equals(field.getFieldName())) {

                                String value = dataMap.get(key);

                                if (StringUtils.isNoneBlank(value) && value.contains("-")) {

                                    String[] arrCodes = value.split("-");

                                    String cityNames = "";

                                    for (String cityCode : arrCodes) {

                                        CommCity commCity = commCityMapper.selectByPrimaryKey(cityCode);

                                        cityNames += commCity.getName() + "-";
                                    }
                                    if (StringUtils.isNotBlank(cityNames)) {

                                        dataMap.put(key, cityNames.substring(0, cityNames.length() - 1));
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
    }

    /**
     * @Title: findById
     * @Description: 根据id查询自定义人员档案
     * @Params: @param id
     * @Params: @return
     * @Return: Map<String, String>
     * <AUTHOR>
     * @date:2021年6月20日
     * @Throws
     */
    public Map<String, List<Map<String, String>>> findByIdAndDetails(String employeeId) {

        List<CommEmployeeFieldGroup> groupList = commEmployeeFieldGroupService.getDataList();

        Map<String, List<Map<String, String>>> map = new HashMap<String, List<Map<String, String>>>();

        CustomEmployeeFieldModel customEmployeeFieldModel = new CustomEmployeeFieldModel();

        customEmployeeFieldModel.setEmployeeId(employeeId);

        for (CommEmployeeFieldGroup group : groupList) {

            String tableName = group.getTableName();

            if (StringUtils.isNotBlank(tableName)) {

                customEmployeeFieldModel.setTableName(tableName);

                String defaultCondition = group.getDefaultCondition();

                String conditionSql = "";

                if (StringUtils.isNotBlank(defaultCondition)) {

                    String[] conditionArr = defaultCondition.split(";");

                    if (conditionArr.length > 1) {

                        for (int index = 0; index < conditionArr.length; index++) {

                            String conditionMap = conditionArr[index];

                            String[] conditionSunArr = conditionMap.split(":");

                            String condition = conditionSunArr[0];

                            String value = conditionSunArr[1];

                            if (value.contains(",")) {

                                String[] values = value.split(",");

                                conditionSql += condition + " in(";

                                for (int i = 0; i < values.length; i++) {

                                    if (i == values.length - 1) {

                                        conditionSql += "'" + values[i] + "'" + "";
                                    } else {
                                        conditionSql += "'" + values[i] + "'" + ",";
                                    }
                                }
                                if (index == conditionArr.length - 1) {

                                    conditionSql += ")";
                                } else {
                                    conditionSql += ") and ";
                                }

                            } else {

                                if (index == conditionArr.length - 1) {

                                    conditionSql += condition + "='" + value + "'";
                                } else {
                                    conditionSql += condition + "='" + value + "' and ";
                                }
                            }

                        }

                    } else {

                        String conditionMap = conditionArr[0];

                        String[] conditionSunArr = conditionMap.split(":");

                        String condition = conditionSunArr[0];

                        String value = conditionSunArr[1];

                        if (value.contains(",")) {

                            String[] values = value.split(",");

                            conditionSql = condition + " in(";

                            for (int i = 0; i < values.length; i++) {

                                if (i == values.length - 1) {

                                    conditionSql += "'" + values[i] + "'" + "";
                                } else {
                                    conditionSql += "'" + values[i] + "'" + ",";
                                }
                            }
                            conditionSql += ")";

                        } else {

                            conditionSql = condition + "='" + value + "'";
                        }
                    }

                }
                System.out.println(conditionSql + "============");
                customEmployeeFieldModel.setDefaultCondition(conditionSql);

                // 追加排序
                customEmployeeFieldModel.setSortField(group.getSortField());
                List<Map<String, String>> list = customEmployeeMapper.findByEmployeeInfoId(customEmployeeFieldModel);

                map.put(group.getId(), list);
            }
        }

        return map;
    }

    /*
     * public static void main(String [] args) { String defaultCondition =
     * "out_type:外出学习,外出会议"; String [] conditionArr = defaultCondition.split(":");
     * System.out.println(conditionArr[2]); }
     */

    /**
     * @Title: deleteById
     * @Description: 根据id删除人员档案
     * @Params: @param id
     * @Params: @return
     * @Return: int
     * <AUTHOR>
     * @date:2021年6月20日
     * @Throws
     */
    @Transactional(readOnly = false)
    public int deleteById(String id) {

        hrmsEmployeeService.userSync(id, InsertOrUpdateEnum.DELETE);
        return hrmsEmployeeService.deleteById(id);
    }

    /**
     * @Title: insert
     * @Description: 新增人员自定义档案
     * @Params: @param records
     * @Params: @return
     * @Return: String
     * <AUTHOR>
     * @date:2021年6月23日
     * @Throws
     */
    @Transactional(readOnly = false)
    @Override
    public String save(CustomEmpModel record) {
        String employeeId = "";

        if (StringUtils.isBlank(record.getEmployeeId())) { // 新增

            employeeId = jdbcTemplateUtil.insert(record.getCustomFileds());

            hrmsEmployeeService.userSync(employeeId, InsertOrUpdateEnum.INSERT);
        } else { // 修改

            employeeId = record.getEmployeeId();
            //待修改列表
            List<CommEmployeeUpdateDetail> emplist = checkNeedStartProcess(record);
            // 判断是否需要走流程
            if (CollectionUtils.isNotEmpty(emplist)) {
                SendBusiFlow(emplist, employeeId);
            }
            hrmsEmployeeService.userSync(record.getEmployeeId(), InsertOrUpdateEnum.UPDATE);
        }

        return employeeId;
    }

    /**
     * @Title: checkNeedStartProcess
     * @Description: 检查是否需要发起流程
     * @Params: @param record
     * @Return: void
     * <AUTHOR>
     * @date:2021年6月26日
     * @Throws
     */
    private List<CommEmployeeUpdateDetail> checkNeedStartProcess(CustomEmpModel record) {

        CustomEmployeeFieldModel customEmployeeFieldModel = new CustomEmployeeFieldModel();
        HrmsAdvancementIncidentReq entity = new HrmsAdvancementIncidentReq();

        String userCode = UserInfoHolder.getCurrentUserCode();

        customEmployeeFieldModel.setUserCode(userCode);

        String employeeId = record.getEmployeeId();

        customEmployeeFieldModel.setEmployeeId(employeeId);

        Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
        Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
        boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");// 档案管理员
        //流程防重复校验
        Map<String, String> empFlowMap = customEmployeeMapper.getEmpFlowStatus(employeeId);
        if (MapUtil.isNotEmpty(empFlowMap) && StrUtil.equals("1", MapUtil.getStr(empFlowMap, "status"))) {
            //不是人事系统管理员
            if (!isInfoAdmin) {
                throw new BusinessException("流程已发起,请勿重复提交! 发起时间:" + MapUtil.getStr(empFlowMap, "createDate"));
            }
        }
        // 修改的数据列表
        List<CommEmployeeUpdateDetail> operationList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(record.getCustomFileds())) {

            for (CustomEmployeeFieldModel item : record.getCustomFileds()) {

                String groupId = item.getGroupId();

                CommEmployeeFieldGroup commEmployeeFieldGroup = commEmployeeFieldGroupService.findById(groupId);

                String tableName = commEmployeeFieldGroup.getTableName();

                customEmployeeFieldModel.setTableName(tableName);

                customEmployeeFieldModel.setGroupId(groupId);

                customEmployeeFieldModel.setGroupName(commEmployeeFieldGroup.getGroupName());

//              if (null != commEmployeeFieldGroup && null != commEmployeeFieldGroup.getIsDetailed() && StringUtils.isBlank(commEmployeeFieldGroup.getDefaultCondition())) { // 判断是否明细表
                if (null != commEmployeeFieldGroup && (null != commEmployeeFieldGroup.getIsDetailed()
                        || "0".equals(commEmployeeFieldGroup.getIsDetailed())) && item.getDetailFields().size() != 0) { // 判断是否明细表

                    // 要修改的明细数据
                    List<List<CommEmployeeField>> detailFields = item.getDetailFields();

                    if (!isAdmin && !isALL && !isInfoAdmin) {// 不是管理员和档案管理员修改需要走流程

                        // 查询需要走权限的分组
                        List<CommEmployeeFieldGroup> jurisdictionFields = customEmployeeMapper
                                .getGroupJurisdictionListById(customEmployeeFieldModel);

                        if (CollectionUtils.isEmpty(jurisdictionFields)) { // 修改明细表,不需要走流程

                            jdbcTemplateUtil.updateEmpDetail(detailFields, tableName, employeeId,
                                    commEmployeeFieldGroup);

                        } else {

                            // 判断是否需要走流程,走流程前的保存修改前和修改后数据
                            List<Map<String, Object>> oldDetailList = getEmployeeDetailInfoByShow(employeeId, groupId,
                                    tableName);

                            checkDetailIsChange(oldDetailList, detailFields, customEmployeeFieldModel, operationList,
                                    commEmployeeFieldGroup);
                        }

                    } else {

                        jdbcTemplateUtil.updateEmpDetail(detailFields, tableName, employeeId, commEmployeeFieldGroup);
                    }

                } else {

                    // 个人信息
                    List<CommEmployeeField> fields = item.getFields();
                    // 增加数据权限控制

                    if (!isAdmin && !isALL && !isInfoAdmin) {

                        // 查询需要走权限的字段
                        List<CommEmployeeField> jurisdictionFields = customEmployeeMapper
                                .getEmployeeFieldJurisdictionList(customEmployeeFieldModel);

                        if (CollectionUtils.isNotEmpty(jurisdictionFields)) {

                            List<Map<String, Object>> oldDetailList = getEmployeeInfoByNeedStartProcess(employeeId,
                                    jurisdictionFields, tableName);

                            checkEmployeeInfoIsChange(oldDetailList, fields, customEmployeeFieldModel,
                                    jurisdictionFields, operationList);

                        } else {

                            jdbcTemplateUtil.updateEmployeeInfo(fields, tableName, employeeId);
                        }
                    } else {

                        jdbcTemplateUtil.updateEmployeeInfo(fields, tableName, employeeId);
                    }

                    HrmsEmployeeResp hrmsEmployeeResp = hrmsEmployeeService.findByEmployeeId(employeeId);
                    fields.forEach(commEmployeeField -> {
                        boolean b = (StringUtils.isNotEmpty(commEmployeeField.getValue())
                                && Objects.nonNull(hrmsEmployeeResp.getPlgw()));
                        //调整岗位信息的异动明细保存
                        //岗位类别
                        if (Objects.equals(commEmployeeField.getFieldName(), "plgw")
                                && b
                                && !Objects.equals(commEmployeeField.getValue(), hrmsEmployeeResp.getPlgw())) {
                            entity.setNewPlgw(commEmployeeField.getValue());
                            entity.setOldPlgw(hrmsEmployeeResp.getPlgw());
                            entity.setType(1);
                        }
                        //岗位等级
                        if (Objects.equals(commEmployeeField.getFieldName(), "gwdj")
                                && b
                                && !Objects.equals(commEmployeeField.getValue(), hrmsEmployeeResp.getGwdj())) {
                            entity.setNewGwdj(commEmployeeField.getValue());
                            entity.setOldGwdj(hrmsEmployeeResp.getGwdj());
                            entity.setType(1);
                        }
                        //薪级等级
                        if (Objects.equals(commEmployeeField.getFieldName(), "salary_level_id")
                                && b
                                && !Objects.equals(commEmployeeField.getValue(), hrmsEmployeeResp.getSalaryLevelId())) {
                            entity.setNewSalaryLevelId(commEmployeeField.getValue());
                            entity.setOldSalaryLevelId(hrmsEmployeeResp.getSalaryLevelId());
                            entity.setType(1);
                        }
                        //薪级类别
                        if (Objects.equals(commEmployeeField.getFieldName(), "salary_level_type")
                                && b
                                && !Objects.equals(commEmployeeField.getValue(), hrmsEmployeeResp.getSalaryLevelType())) {
                            entity.setNewSalaryLevelType(commEmployeeField.getValue());
                            entity.setOldSalaryLevelType(hrmsEmployeeResp.getSalaryLevelType());
                            entity.setType(1);
                        }
                    });
                    entity.setApprovalStatus(4);
                    entity.setEmployeeId(hrmsEmployeeResp.getEmployeeId());
                    entity.setEmployeeName(hrmsEmployeeResp.getEmployeeName());
                    entity.setEmployeeNo(hrmsEmployeeResp.getEmployeeNo());
                    entity.setIsDeleted(Contants.IS_DELETED_FALSE);
                    entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
                    entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
                    entity.setCreateDate(new Date());
                    entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                }
            }
            incidentFeignService.empSave(entity);
        }
        return operationList;
    }

    private void SendBusiFlow(List<CommEmployeeUpdateDetail> operationList, String employeeId) {
        if (CollectionUtils.isNotEmpty(operationList)) {
            // 发起流程
            String operationId = ApplicationUtils.GUID32();

            Map<String, Object> map = new HashMap<>();

            // 根据人员id查询岗位 中文名称
            String personalIdentityVal = getPersonalIdentityValBayEmployeeId(employeeId);
            map.put("L_BusinessId", operationId); // 业务id
            map.put("L_personalIdentity", personalIdentityVal); // 人员岗位名称 中文 审批流程判断使用的

            String workflowId = workflowInstanceService.doStartProcessInstance("L_00001", map);

            inserUpdateOperation(operationList, workflowId, operationId, employeeId);
        }
    }

    /**
     * @Title: checkDetailIsChange
     * @Description: 判断是否存在编辑
     * @Params: @param oldDetailMap
     * @Params: @param detailFields
     * @Params: @return
     * @Return: boolean
     * <AUTHOR>
     * @date:2021年6月28日
     * @Throws
     */
    private boolean checkDetailIsChange(List<Map<String, Object>> oldDetailList,
                                        List<List<CommEmployeeField>> detailFields, CustomEmployeeFieldModel customEmployeeFieldModel,
                                        List<CommEmployeeUpdateDetail> operationList, CommEmployeeFieldGroup commEmployeeFieldGroup) {

        boolean flag = false;

        List<Map<String, String>> newDetailList = new ArrayList<Map<String, String>>();

        for (List<CommEmployeeField> detailField : detailFields) {

            Map<String, String> newDetailMap = new HashMap<>();

            for (CommEmployeeField commEmployeeField : detailField) {

                if (StringUtils.isNotBlank(commEmployeeField.getValue())
                        && !"null".equals(commEmployeeField.getValue())) {

                    newDetailMap.put(commEmployeeField.getFieldName(), commEmployeeField.getValue());

                }
            }

            newDetailList.add(newDetailMap);

        }

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

        if (CollectionUtils.isNotEmpty(oldDetailList)) {

            for (Map<String, Object> dataMap : oldDetailList) {

                for (String key : dataMap.keySet()) {

                    Object object = dataMap.get(key);

                    boolean obgflag = ObjectUtils.allNotNull(object);

                    if (null != object && obgflag) {

                        if (object instanceof Date) {

                            dataMap.put(key, sf.format(object));

                        } else {

                            dataMap.put(key, object);
                        }
                    }
                }
            }

        }

        String beforeData = JsonUtil.formatObject(oldDetailList);

        String afterData = JsonUtil.formatObject(newDetailList);

        // 先判断长度,如果长度不对,就表示已经修改过了
        if (oldDetailList.size() == detailFields.size()) {

            flag = JsonUtil.sameEmplpyeeMessage(beforeData, afterData);

        }

        if (flag) {

            jdbcTemplateUtil.updateEmpDetail(detailFields, customEmployeeFieldModel.getTableName(),
                    customEmployeeFieldModel.getEmployeeId(), commEmployeeFieldGroup);

        } else {

            ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
            // 记录修改的操作 ,前后数据
            CommEmployeeUpdateDetail commEmployeeUpdateDetail = new CommEmployeeUpdateDetail();

            commEmployeeUpdateDetail.setAfterData(afterData);

            commEmployeeUpdateDetail.setBeforeData(beforeData);

            commEmployeeUpdateDetail.setEmployeeId(customEmployeeFieldModel.getEmployeeId());

            commEmployeeUpdateDetail.setGroupId(customEmployeeFieldModel.getGroupId());

            commEmployeeUpdateDetail.setFieldName(customEmployeeFieldModel.getGroupName());

            commEmployeeUpdateDetail.setShowName(customEmployeeFieldModel.getGroupName());

            commEmployeeUpdateDetail.setTableName(customEmployeeFieldModel.getTableName());

            commEmployeeUpdateDetail.setUpdateType(2);

            commEmployeeUpdateDetail.setSsoOrgCode(userInfo.getCorpcode());

            commEmployeeUpdateDetail.setSsoOrgName(userInfo.getOrgName());

            operationList.add(commEmployeeUpdateDetail);
        }

        return flag;
    }

    /**
     * @Title: checkEmployeeInfoIsChange
     * @Description: 检查修改人员基本信息是否需要走流程
     * @Params: @param oldDetailList
     * @Params: @param detailFields
     * @Params: @param tableName
     * @Params: @param employeeId
     * @Return: void
     * <AUTHOR>
     * @date:2021年6月30日
     * @Throws
     */
    private boolean checkEmployeeInfoIsChange(List<Map<String, Object>> oldDetailList, List<CommEmployeeField> fields,
                                              CustomEmployeeFieldModel customEmployeeFieldModel, List<CommEmployeeField> jurisdictionFields,
                                              List<CommEmployeeUpdateDetail> operationList) {

        boolean flag = false;

        Map<String, Object> oldEmployeeInfoMap = oldDetailList.get(0);

        List<CommEmployeeField> updateFields = new ArrayList<>();

        Map<String, String> fieldMap = new HashMap<>();

        for (CommEmployeeField item : jurisdictionFields) {

            fieldMap.put(item.getFieldName(), item.getShowName());
        }

        for (CommEmployeeField detailField : fields) {

            for (String fieldName : oldEmployeeInfoMap.keySet()) {

                String oldValue = oldEmployeeInfoMap.get(fieldName) != null
                        ? oldEmployeeInfoMap.get(fieldName).toString()
                        : "";

                if (fieldName.equals(detailField.getFieldName())) {

                    String newValue = detailField.getValue();

                    if (!oldValue.equals(newValue)) {
                        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();

                        CommEmployeeUpdateDetail commEmployeeUpdateDetail = new CommEmployeeUpdateDetail();

                        commEmployeeUpdateDetail.setAfterData(newValue);

                        commEmployeeUpdateDetail.setBeforeData(oldValue.toString());

                        commEmployeeUpdateDetail.setEmployeeId(customEmployeeFieldModel.getEmployeeId());

                        commEmployeeUpdateDetail.setGroupId(customEmployeeFieldModel.getGroupId());

                        commEmployeeUpdateDetail.setFieldName(fieldName);

                        commEmployeeUpdateDetail.setShowName(fieldMap.get(fieldName));

                        commEmployeeUpdateDetail.setUpdateType(1);

                        commEmployeeUpdateDetail.setSsoOrgCode(userInfo.getCorpcode());

                        commEmployeeUpdateDetail.setSsoOrgName(userInfo.getOrgName());

                        operationList.add(commEmployeeUpdateDetail);

                        updateFields.add(detailField);

                    }
                }
            }
        }

        fields.removeAll(updateFields);

        if (CollectionUtils.isNotEmpty(fields)) {

            jdbcTemplateUtil.updateEmployeeInfo(fields, customEmployeeFieldModel.getTableName(),
                    customEmployeeFieldModel.getEmployeeId());
        }

        return flag;
    }

    /**
     * @Title: getOldDetailMessageByEmployeeId
     * @Description: 根据设置显示的字段, 查询员工明细表业务数据
     * @Params: @param employeeId
     * @Params: @param groupId
     * @Params: @param tableName
     * @Params: @return
     * @Return: List<Map < String, Object>>
     * <AUTHOR>
     * @date:2021年6月28日
     * @Throws
     */
    private List<Map<String, Object>> getEmployeeDetailInfoByShow(String employeeId, String groupId, String tableName) {

        CommEmployeeField commEmployeeField = new CommEmployeeField();

        commEmployeeField.setGroupId(groupId);

        commEmployeeField.setIsHide(0);

        List<CommEmployeeField> fields = commEmployeeFieldService.getFieldsListByCondition(commEmployeeField);

        List<Map<String, Object>> list = jdbcTemplateUtil.getEmployeeDetailInfoByShow(employeeId, fields, tableName);

        return list;
    }

    /**
     * @Title: getEmployeeInfoByNeedStartProcess
     * @Description: 根据员工id, 查询需要走流程的字段数据
     * @Params: @param employeeId
     * @Params: @param jurisdictionFields
     * @Params: @return
     * @Return: List<Map < String, Object>>
     * <AUTHOR>
     * @date:2021年6月30日
     * @Throws
     */
    private List<Map<String, Object>> getEmployeeInfoByNeedStartProcess(String employeeId,
                                                                        List<CommEmployeeField> jurisdictionFields, String tabelName) {

        List<Map<String, Object>> list = jdbcTemplateUtil.getEmployeeDetailInfoByShow(employeeId, jurisdictionFields,
                tabelName);

        return list;
    }

    /**
     * @Title: inserUpdateOperation
     * @Description: 新增修改记录
     * @Params: @param beforeData
     * @Params: @param afterData
     * @Params: @param updateType
     * @Return: void
     * <AUTHOR>
     * @date:2021年6月30日
     * @Throws
     */
    private void inserUpdateOperation(List<CommEmployeeUpdateDetail> operationList, String workflowId,
                                      String operationId, String employeeId) {

        CommEmployeeUpdateOperation commEmployeeUpdateOperation = new CommEmployeeUpdateOperation();

        commEmployeeUpdateOperation.setWorkflowId(workflowId);

        commEmployeeUpdateOperation.setId(operationId);

        commEmployeeUpdateOperation.setAuditStatus(1);

        commEmployeeUpdateOperation.setEmployeeId(employeeId);

        commEmployeeUpdateOperation.setWorkflowId(workflowId);

        commEmployeeUpdateOperation.setCreateDate(new Date());

        commEmployeeUpdateOperation.setIsDeleted(Contants.IS_DELETED_FALSE);

        commEmployeeUpdateOperation.setCreateUserName(UserInfoHolder.getCurrentUserName());

        commEmployeeUpdateOperation.setCreateUser(UserInfoHolder.getCurrentUserCode());

        commEmployeeUpdateOperation.setCreateUserDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());

        commEmployeeUpdateOperation.setCreateUserDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());

        commEmployeeUpdateOperation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        commEmployeeUpdateOperationMapper.insert(commEmployeeUpdateOperation);

        for (CommEmployeeUpdateDetail record : operationList) {

            record.setId(ApplicationUtils.GUID32());

            record.setOperationId(operationId);

            record.setWorkflowId(workflowId);

            record.setCreateDate(new Date());

            record.setIsDeleted(Contants.IS_DELETED_FALSE);

            record.setCreateUserName(UserInfoHolder.getCurrentUserName());

            record.setCreateUser(UserInfoHolder.getCurrentUserCode());

            commEmployeeUpdateDetailMapper.insertSelective(record);
        }
    }

    public List<CustomEmployeeResp> getDataWorkflowList(Page page, CustomEmployeeResp record) {

        List<CustomEmployeeResp> listBean = new ArrayList<>();

        String userCode = UserInfoHolder.getCurrentUserInfo().getUsercode();

        record.setUserCode(userCode);
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isEmpty(record.getHandStatus()) || "1".equals(record.getHandStatus())) { // 待我办理

            listBean = customEmployeeMapper.getDataWorkflowByNoDoList(page, record);

        } else if ("2".equals(record.getHandStatus())) { // 我已办理

            listBean = customEmployeeMapper.getDataWorkflowIDoList(page, record);

        } else if ("3".equals(record.getHandStatus())) { // 由我发起

            listBean = customEmployeeMapper.getDataWorkflowIApplyList(page, record);
        }
        return listBean;
    }

    /**
     * @Title: auditDetails
     * @Description: 查询修改走流程详细信息
     * @Params: @param operationId
     * @Params: @return
     * @Return: List<CommEmployeeUpdateDetail>
     * <AUTHOR>
     * @date:2021年7月1日
     * @Throws
     */
    public List<CommEmployeeUpdateDetail> auditDetails(String operationId) {
        Example example = new Example(CommEmployeeUpdateDetail.class);

        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        example.and().andEqualTo("operationId", operationId);

        example.orderBy("groupId").asc();

        List<CommEmployeeUpdateDetail> list = commEmployeeUpdateDetailMapper.selectByExample(example);

        for (CommEmployeeUpdateDetail commEmployeeUpdateDetail : list) {

            CommEmployeeField commEmployeeField = new CommEmployeeField();

            commEmployeeField.setGroupId(commEmployeeUpdateDetail.getGroupId());

            commEmployeeField.setIsHide(0);
            commEmployeeField.setIsDisabled(0);

            List<CommEmployeeField> fieldList = commEmployeeFieldService.getFieldsListByCondition(commEmployeeField);

            if (null != commEmployeeUpdateDetail && commEmployeeUpdateDetail.getUpdateType() == 2) {

                String beforData = commEmployeeUpdateDetail.getBeforeData();

                String afterData = commEmployeeUpdateDetail.getAfterData();

                List<Map<String, String>> aftermapList = converFieldDict(fieldList, afterData);

                List<Map<String, String>> beformapList = converFieldDict(fieldList, beforData);

                commEmployeeUpdateDetail.setBeforeData(JsonUtil.formatObject(beformapList));

                commEmployeeUpdateDetail.setAfterData(JsonUtil.formatObject(aftermapList));

                commEmployeeUpdateDetail.setFields(fieldList);
            } else {

                converEmployeeBasicFieldDict(fieldList, commEmployeeUpdateDetail);
            }
        }

        return list;
    }

    /**
     * @Title: converField
     * @Description: 明细字典转换
     * @Params:
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月8日
     * @Throws
     */
    private List<Map<String, String>> converFieldDict(List<CommEmployeeField> fieldList, String json) {

        List<List<CommEmployeeField>> detailFields = new ArrayList<>();

        if (StringUtils.isNotBlank(json)) {

            detailFields = JsonUtil.formatJsonStrToBean(json);
        }

        Map<String, Map<String, String>> dictMap = dictItemService.getDictMap();

        List<Map<String, String>> mapList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(fieldList) && CollectionUtils.isNotEmpty(detailFields)) {

            for (List<CommEmployeeField> commEmployeeFieldList : detailFields) {

                Map<String, String> dataMap = new HashMap<>();

                for (CommEmployeeField commEmployeeField : commEmployeeFieldList) {

                    for (CommEmployeeField field : fieldList) {

                        String fieldName = field.getFieldName();

                        String fieldType = field.getFieldType();

                        String dictSource = field.getDictSource();

                        if (fieldName.equals(commEmployeeField.getFieldName())) {

                            if ("select".equals(fieldType) && StringUtils.isNotBlank(field.getDictSource())) {

                                Map<String, String> itemMap = dictMap.get(dictSource);

                                if (null != itemMap && StringUtils.isNotBlank(commEmployeeField.getValue())) {

                                    dataMap.put(commEmployeeField.getFieldName(),
                                            itemMap.get(commEmployeeField.getValue()));
                                }
                            } else if ("jobType_1".equals(fieldType) || "jobType_2".equals(fieldType)
                                    || "jobType_3".equals(fieldType)) {

                                HrmsJobtitleBasic hrmsJobtitleBasic = hrmsJobtitleBasicMapper
                                        .selectByPrimaryKey(commEmployeeField.getValue());

                                if (null != hrmsJobtitleBasic) {

                                    dataMap.put(commEmployeeField.getFieldName(),
                                            hrmsJobtitleBasic.getJobtitleBasicName());
                                }

                            } else {

                                dataMap.put(commEmployeeField.getFieldName(), commEmployeeField.getValue());
                            }
                        }

                    }
                }

                mapList.add(dataMap);
            }
        }

        return mapList;
    }

    /**
     * @Title: converEmployeeBasicFieldDict
     * @Description: 转换员工基本信息字典
     * @Params: @param fieldList
     * @Params: @param commEmployeeUpdateDetail
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月9日
     * @Throws
     */
    private void converEmployeeBasicFieldDict(List<CommEmployeeField> fieldList,
                                              CommEmployeeUpdateDetail commEmployeeUpdateDetail) {

        Map<String, Map<String, String>> dictMap = dictItemService.getDictMap();

        if (CollectionUtils.isNotEmpty(fieldList) && StringUtils.isNotBlank(commEmployeeUpdateDetail.getFieldName())) {

            for (CommEmployeeField field : fieldList) {

                String fieldName = field.getFieldName();

                String fieldType = field.getFieldType();

                String dictSource = field.getDictSource();

                if (fieldName.equals(commEmployeeUpdateDetail.getFieldName())) {
                    if ("file".equals(field.getFieldType())) {
                        commEmployeeUpdateDetail.setFileType(field.getFieldType());
                    }

                    if (("select".equals(fieldType) && StringUtils.isNotBlank(field.getDictSource()))
                            || "establishment_type".equals(fieldName) || "employee_category".equals(fieldName)) {

                        Map<String, String> itemMap = dictMap.get(dictSource);

                        if (null != itemMap) {

                            if (StringUtils.isNotBlank(commEmployeeUpdateDetail.getBeforeData())) {
                                commEmployeeUpdateDetail
                                        .setBeforeData(itemMap.get(commEmployeeUpdateDetail.getBeforeData()));
                            }
                            if (StringUtils.isNotBlank(commEmployeeUpdateDetail.getAfterData())) {
                                commEmployeeUpdateDetail
                                        .setAfterData(itemMap.get(commEmployeeUpdateDetail.getAfterData()));
                            }
                        }
                    }

                    // 人员档案审核时职务值回显,暂时根据字段名称写死
                    if ("position_id".equals(field.getFieldName())
                            || "concurrent_position".equals(field.getFieldName())) {

                        HrmsPosition hrmsPosition = hrmsPositionMapper
                                .selectByPrimaryKey(commEmployeeUpdateDetail.getBeforeData());

                        if (null != hrmsPosition) {

                            commEmployeeUpdateDetail.setBeforeData(hrmsPosition.getPositionName());
                        }

                        HrmsPosition hrmsPosition2 = hrmsPositionMapper
                                .selectByPrimaryKey(commEmployeeUpdateDetail.getAfterData());

                        if (null != hrmsPosition2) {

                            commEmployeeUpdateDetail.setAfterData(hrmsPosition2.getPositionName());
                        }

                    }
                    // 人员档案审核时出生地址,籍贯值回显,暂时根据字段名称写死
                    if ("born_address".equals(field.getFieldName()) || "birthplace".equalsIgnoreCase(field.getFieldName())) {

                        commEmployeeUpdateDetail
                                .setBeforeData(getAddressNameById(commEmployeeUpdateDetail.getBeforeData()));

                        commEmployeeUpdateDetail
                                .setAfterData(getAddressNameById(commEmployeeUpdateDetail.getAfterData()));
                    }

                    // 组织机构 暂时写死
                    if ("org_id".equals(field.getFieldName())) {

                        commEmployeeUpdateDetail.setBeforeData(getOrgName(commEmployeeUpdateDetail.getBeforeData()));

                        commEmployeeUpdateDetail.setAfterData(getOrgName(commEmployeeUpdateDetail.getAfterData()));
                    }

                    // 岗位等级 暂时写死
                    if ("gwdj".equals(field.getFieldName())) {

                        if (!StringUtil.isEmpty(commEmployeeUpdateDetail.getBeforeData())) {
                            Post findById = iPostService.findById(commEmployeeUpdateDetail.getBeforeData());
                            if (findById != null) {
                                commEmployeeUpdateDetail.setBeforeData(findById.getPostName());
                            }
                        }
                        if (!StringUtil.isEmpty(commEmployeeUpdateDetail.getAfterData())) {
                            Post findById = iPostService.findById(commEmployeeUpdateDetail.getAfterData());
                            if (findById != null) {
                                commEmployeeUpdateDetail.setAfterData(findById.getPostName());
                            }
                        }
                    }
                }
            }
        }
    }

    private String getOrgName(String orgId) {
        try {
            HrmsOrganization findById = hrmsOrganizationService.findById(orgId);
            if (findById != null) {
                return findById.getName();
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    private String getAddressNameById(String addressIds) {

        String addressNames = "";

        if (StringUtils.isNotBlank(addressIds)) {

            String[] addressArr = addressIds.split("-");

            for (int i = 0; i < addressArr.length; i++) {

                CommCity commCity = commCityMapper.selectByPrimaryKey(addressArr[i]);

                if (null != commCity) {

                    if (i == addressArr.length - 1) {
                        addressNames += commCity.getName();
                    } else {
                        addressNames += commCity.getName() + "-";
                    }
                }
            }
        }

        return addressNames;
    }

    /**
     * @Title: audit
     * @Description: 流程审核
     * @Params: @param record
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月2日
     * @Throws
     */
    @Transactional(readOnly = false)
    public void audit(CustomEmployeeResp record) {
        Map<String, Object> map = new HashMap<>();

        if (StringUtils.isNotBlank(record.getCopyToUsers())) {
            map.put("L_CopyToUsers", record.getCopyToUsers());
        }

        if (StringUtils.isNotBlank(record.getCopyToUserNames())) {
            map.put("L_CopyToUserNames", record.getCopyToUserNames());
        }

        if ("1".equals(record.getAuditStatus())) {

            workflowTaskService.completeTask(record.getTaskId(), map); // 流程审批

        } else { // 驳回 本地保存驳回备注
            // 多传一个参数
            CommEmployeeUpdateOperation ceo = new CommEmployeeUpdateOperation();
            ceo.setId(record.getOperationId());
            ceo.setRemark(record.getRemark());
            ceo.setAuditStatus(null);
            commEmployeeUpdateOperationMapper.updateByPrimaryKeySelective(ceo);

            map.put("handleMarkedWords", record.getRemark());
            workflowTaskService.doRejectTask(record.getTaskId(), map);
        }

        // commEmployeeUpdateOperationMapper.updateByPrimaryKey(record);
    }

    /**
     * @Title: auditFinish
     * @Description: 修改流程审批通过, 赋值
     * @Params: @param operationId
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月4日
     * @Throws
     */
    @Transactional(readOnly = false)
    public void auditFinish(String operationId) {

        CommEmployeeUpdateOperation updateOperation = commEmployeeUpdateOperationMapper.selectByPrimaryKey(operationId);

        if (null != updateOperation) {
            // 获取员工id
            String employeeId = updateOperation.getEmployeeId();
            Example example = new Example(CommEmployeeUpdateDetail.class);
            example.createCriteria().andEqualTo("operationId", operationId);
            example.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            example.and().andEqualTo("auditStatus", "1");// 只同步审核生效的数据
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            List<CommEmployeeUpdateDetail> details = commEmployeeUpdateDetailMapper.selectByExample(example);

            if (CollectionUtils.isNotEmpty(details)) {

                Map<Integer, List<CommEmployeeUpdateDetail>> empFieldMap = details.stream()
                        .collect(Collectors.groupingBy(CommEmployeeUpdateDetail::getUpdateType));

                for (Integer updateType : empFieldMap.keySet()) {

                    List<CommEmployeeUpdateDetail> empBasicsFields = empFieldMap.get(updateType);

                    if (1 == updateType) {
                        // 员工基础信息修改
                        if (CollectionUtils.isNotEmpty(empBasicsFields)) {

                            List<CommEmployeeField> fields = new ArrayList<>();

                            String tableName = "hrms_employee";

                            for (CommEmployeeUpdateDetail detail : empBasicsFields) {

                                CommEmployeeField commEmployeeField = new CommEmployeeField();

                                commEmployeeField.setFieldName(detail.getFieldName());

                                commEmployeeField.setValue(detail.getAfterData());

                                fields.add(commEmployeeField);
                            }

                            if (CollectionUtils.isNotEmpty(fields)) {

                                jdbcTemplateUtil.updateEmployeeInfo(fields, tableName, employeeId);
                            }

                        }
                    } else {// 员工明细表信息修改

//                      CommEmployeeUpdateDetail detailField = empBasicsFields.get(0);

                        if (CollectionUtils.isNotEmpty(empBasicsFields)) {

                            for (CommEmployeeUpdateDetail detail : empBasicsFields) {

                                CommEmployeeFieldGroup commEmployeeFieldGroup = commEmployeeFieldGroupService
                                        .findById(detail.getGroupId());

                                String tableName = detail.getTableName();

                                String afterData = detail.getAfterData();

                                List<List<CommEmployeeField>> detailFields = new ArrayList<>();

                                if (StringUtils.isNotBlank(afterData)) {

                                    detailFields = JsonUtil.formatJsonStrToBean(afterData);

                                }

                                if (CollectionUtils.isNotEmpty(detailFields)) {

                                    jdbcTemplateUtil.updateEmpDetail(detailFields, tableName, employeeId,
                                            commEmployeeFieldGroup);

                                } else {// 如果修改的数据为空，则表示全部删除
                                    jdbcTemplateUtil.deleteEmployeeDetailByEmployeeId(employeeId, tableName,
                                            commEmployeeFieldGroup);
                                }
                            }

                        }

//                      CommEmployeeFieldGroup commEmployeeFieldGroup = commEmployeeFieldGroupService.findById(detailField.getGroupId());
//
//                      String tableName = detailField.getTableName();
//
//                      String afterData = detailField.getAfterData();
//
//                      List<List<CommEmployeeField>> detailFields = new ArrayList<>();
//
//                      if (StringUtils.isNotBlank(afterData)) {
//
//                          detailFields = JsonUtil.formatJsonStrToBean(afterData);
//
//                      }
//
//                      if (CollectionUtils.isNotEmpty(detailFields)) {
//
//                          jdbcTemplateUtil.updateEmpDetail(detailFields, tableName, employeeId,commEmployeeFieldGroup);
//
//                      }else {//如果修改的数据为空，则表示全部删除
//                          jdbcTemplateUtil.deleteEmployeeDetailByEmployeeId(employeeId, tableName, commEmployeeFieldGroup);
//                      }
                    }
                }
            }
            
            Example example2 = new Example(CommEmployeeUpdateDetail.class);
            example2.createCriteria().andEqualTo("operationId", operationId);
            example2.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            example2.and().andEqualTo("auditStatus", "2");
            example2.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            List<CommEmployeeUpdateDetail> details2 = commEmployeeUpdateDetailMapper.selectByExample(example2);
            
            HrmsEmployee hrmsEmployee = hrmsEmployeeService.findById(employeeId);
            if(details2.size() > 0) {
            	Notice notice = 
                        Notice.builder()
                            .content("您的员工档案已审批完成，存在未合格项,请及时登录系统修改！")
                            .noticeType("3")
                            .receiver(hrmsEmployee.getEmployeeNo())
                            .sender(UserInfoHolder.getCurrentUserCode())
                            .senderName(UserInfoHolder.getCurrentUserName())
                            .subject("员工档案审批")
                            .toUrl("#")
                            .url(appConfigProperties.getLogin() + "ts-mobile-oa/pages/personalCenter/edit/edit?fromPage=my&index=0")
                            .ssoOrgCode(UserInfoHolder.getCurrentUserCorpCode())
                            .wxSendType("1")//卡片消息
                            .source("员工档案审批")
                            .build();
                
                noticeController.sendNotice(notice);
            }
        }
    }

    /**
     * @Title: UniqueCheck
     * @Description: 新增修改时姓名, 工号唯一校验
     * @Params: @param record
     * @Params: @return
     * @Return: boolean
     * <AUTHOR>
     * @date:2021年7月6日
     * @Throws
     */
    public String uniqueCheck(CustomEmpModel record) {

        return hrmsEmployeeService.uniqueCheck(record);

    }

    /**
     * @Title: synEmployeeMessage
     * @Description: 同工同酬审批完修改人员档案信息
     * @Params: @param birthday
     * @Params: @param entryDate
     * @Params: @param firstEducationType
     * @Params: @param firstEducationFile
     * @Params: @param zyfj
     * @Params: @param idCardFile
     * @Params: @param xuexinNetFile
     * @Return: void
     * <AUTHOR>
     * @date:2021年9月7日
     * @Throws
     */
    @Transactional(readOnly = false)
    @Deprecated
    public void synEmployeeMessage(String birthday, String entryDate, String firstEducationType,
                                   String firstEducationFile, String zyfj, String idCardFile, String xuexinNetFile, String userCode) {

        HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeCode(userCode);

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        // 修改员工档案休息
        if (null != hrmsEmployee) {

            hrmsEmployee.setIdCardFile(idCardFile);
            hrmsEmployee.setXuexinNetFile(xuexinNetFile);
            hrmsEmployee.setZyfj(zyfj);
            try {
                hrmsEmployee.setBirthday(sf.parse(birthday));
                hrmsEmployee.setEntryDate(sf.parse(entryDate));
            } catch (ParseException e) {
                // TODO Auto-generated catch block
                log.error(e.getMessage(), e);
            }
            hrmsEmployeeService.updateEmployeeInfoById(hrmsEmployee);

            // 修改学历信息
        }
    }

    /**
     * @Title: getEmployeeFields
     * @Description: 获取需要导出的字段
     * @Params: @return
     * @Return: List<Map < String, String>>
     * <AUTHOR>
     * @date:2021年11月12日
     * @Throws
     */
    public List<Map<String, String>> getEmployeeFields() {

        List<Map<String, String>> result = new ArrayList<>();
        try {

            Map<String, String> fieldMap = new LinkedHashMap<String, String>();

            Field[] fields = ExportEmployee.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ExportFieldsAnnotation.class)) {
                    ExportFieldsAnnotation api = field.getAnnotation(ExportFieldsAnnotation.class);
                    String key = field.getName();
                    String value = api.value();
                    fieldMap.put(key, value);
                }
            }

            for (String key : fieldMap.keySet()) {
                Map<String, String> m = new LinkedHashMap<>();
                m.put(key, fieldMap.get(key));
                result.add(m);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public List<Map<String, String>> getJklzyy() {
        return customEmployeeMapper.getJklzyy();
    }

    @Override
    public List<Map<String, String>> getJkTx() {
        // TODO Auto-generated method stub
        return customEmployeeMapper.getJkTx();
    }

    /**
     * 技术档案
     *
     * @return
     */
    @Override
    public List<Map<String, String>> getEmployeeJsda(String employeeId) {
        return customEmployeeMapper.getEmployeeJsda(employeeId);
    }

    private String getPersonalIdentityValBayEmployeeId(String employeeId) {
        //获取当前账号机构编码
        String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
        return customEmployeeMapper.getPersonalIdentityValBayEmployeeId(employeeId, ssoOrgCode);
    }

    /**
     * 根據id查询人员信息修改是否还有审批的流程
     *
     * @param employeeId
     * @return
     */
    @Override
    public Map<String, Object> authStatusByEmployeeId(String employeeId) {
        Map<String, String> map = customEmployeeMapper.getEmployeeNoByemployeeId(employeeId);
        if (map != null && !map.isEmpty()) {
            // 查询最后一次提交的数据
            String businessId = map.get("BUSINESS_ID");
            Example example = new Example(CustomEmployeeUpdateDetail.class);
            example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            example.and().andEqualTo("operationId", businessId);
            List<CustomEmployeeUpdateDetail> commEmployeeUpdateDetails = customEmployeeUpdateDetailMapper.selectByExample(example);
            
            CustomEmployeeUpdateOperation commEmployeeUpdateOperation = customEmployeeUpdateOperationMapper.selectByPrimaryKey(businessId); // 查询审批的备注

            Map<String, Object> returnMap = null;
            String status = String.valueOf(map.get("STATUS"));
            if (!StringUtil.isEmpty(status)) {
                returnMap = new HashMap();
                if ("1".equals(status)) {
                    returnMap.put("status", "1"); // 1 审核中
                    returnMap.put("data", commEmployeeUpdateDetails);
                } else if ("2".equals(status)) {
                    returnMap.put("status", "2"); // 2 通过
                    returnMap.put("data", commEmployeeUpdateDetails);
                } else {
                    returnMap.put("status", "3"); // 3 驳回
                    returnMap.put("data", commEmployeeUpdateDetails);
                }
            }
            if (commEmployeeUpdateOperation != null) {
                returnMap.put("remark", commEmployeeUpdateOperation.getRemark());
            } else {
                returnMap.put("remark", null);
            }

            return returnMap;
        }
        return null;
    }

    @Override
    @Transactional(readOnly = false)
    public Integer storage(String str, String employeeId) {
        // 先根据人员id删除 然后在添加
        if (!StringUtil.isEmpty(str)) {
            customEmployeeMapper.deltedStorage(employeeId);
            customEmployeeMapper.storage(str, employeeId, IdGeneraterUtils.nextId());
        } else {
            customEmployeeMapper.deltedStorage(employeeId);
        }
        return 1;
    }

    @Override
    @Transactional(readOnly = false)
    public EmployeeStorageData getstorage(String employeeId) {
        return customEmployeeMapper.getStorage(employeeId);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateDetailsStatus(List<CommEmployeeUpdateDetail> list) {

        if (list != null && list.size() > 0) {
            list.forEach(item -> {
                commEmployeeUpdateDetailMapper.updateByPrimaryKeySelective(item);
            });
        }
        return list.size();
    }

    @Override
    public Map<String, String> getEmployeeTask(String employeeNo) {
        Assert.hasText(employeeNo, "人员工号不能为空");
        Map<String, String> employeeTask = customEmployeeMapper.getEmployeeTask(employeeNo);

        Boolean sysArchivist = UserLoginService.getRight("SYS_ARCHIVIST");
        Boolean isadmin = UserLoginService.ISADMIN();

        if (null != employeeTask) {
            if (sysArchivist || isadmin) {
                employeeTask.put("isAdmin", "true");
            } else {
                employeeTask.put("isAdmin", "false");
            }
        } else {
            employeeTask = new HashMap<>();
            if (sysArchivist || isadmin) {
                employeeTask.put("isAdmin", "true");
            } else {
                employeeTask.put("isAdmin", "false");
            }
        }
        return employeeTask;
    }

    @Override
    public Map<String, Object> checkSave(CustomEmpModel record) {
        Map<String, Object> retMap = new HashMap<>();

        String employeeId = record.getEmployeeId();  //人员id
        if (CollectionUtils.isNotEmpty(record.getCustomFileds())) {
            // 修改的数据列表
            List<CommEmployeeUpdateDetail> operationList = new ArrayList<>();
            for (CustomEmployeeFieldModel item : record.getCustomFileds()) {
                String groupId = item.getGroupId();
                CommEmployeeFieldGroup commEmployeeFieldGroup = commEmployeeFieldGroupService.findById(groupId);  //拿到table表

                if (null != commEmployeeFieldGroup && (null == commEmployeeFieldGroup.getIsDetailed() || "0".equals(commEmployeeFieldGroup.getIsDetailed()))) { // 判断是否明细表
                    List<CommEmployeeField> fieldList = new ArrayList<>();
                    if (null != item.getFields()) {
                        item.getFields().forEach(field -> {
                            CommEmployeeField _fi = new CommEmployeeField();
                            _fi.setFieldName(field.getFieldName());
                            fieldList.add(_fi);
                        });
                    }
                    List<Map<String, Object>> list = jdbcTemplateUtil.getEmployeeDetailInfoByShow(employeeId, fieldList, commEmployeeFieldGroup.getTableName());
                    //比较数据有变化的
                    Map<String, Object> oldEmpMap = list.get(0);

                    Map<String, Object> retDetailsMap = new HashMap<>();
                    if (null != item.getFields()) {
                        item.getFields().forEach(field -> {
                            boolean isCheck = true;
                            String oldVal = oldEmpMap.get(field.getFieldName()) == null || "".equals(oldEmpMap.get(field.getFieldName())) ? null : oldEmpMap.get(field.getFieldName()).toString();
                            String value = field.getValue();
                            if (!check(oldVal, value)) {  // 不相同 返回 对应的key 和值
                                retDetailsMap.put(field.getFieldName(), field.getValue());
                            }
                        });
                    }
                    if (retDetailsMap != null && retDetailsMap.size() > 0) {
                        retMap.put(item.getGroupId(), retDetailsMap);
                    }

                }
            }
        }
        return retMap;
    }

    private boolean check(String oldVal, String value) {
        boolean isCheck = false;
        if ((oldVal == null && value == null) || (oldVal == null && value == "") || (oldVal == "" && value == null)) {
            isCheck = true;
        } else if (oldVal != null && value != null) {
            if (oldVal.equals(value)) {
                isCheck = true;
            } else {
                isCheck = false;
            }
        } else {
            isCheck = false;
        }
        return isCheck;
    }

    //人事档案修改流程发起
    @Transactional(readOnly = false)
    public void sendEmployeeFlow(List<CommEmployeeUpdateDetail> operationList, String employeeId) {
        if (CollectionUtils.isNotEmpty(operationList)) {
            // 发起流程
            String operationId = ApplicationUtils.GUID32();

            Map<String, Object> map = new HashMap<>();

            //根据人员id查询岗位 中文名称
            String personalIdentityVal = getPersonalIdentityValBayEmployeeId(employeeId);
            map.put("L_BusinessId", operationId); // 业务id
            map.put("L_personalIdentity", personalIdentityVal);  //人员岗位名称 中文  审批流程判断使用的

            Instant start = Instant.now();

            String workflowId = workflowInstanceService.doStartProcessInstance("L_00001", map);
            log.info("人事档案修改流程发起返回的workflowId:" + workflowId);
            Instant end = Instant.now();
            log.info("人事档案修改流程发起耗时时间:" + Duration.between(start, end).toMillis());

            inserUpdateOperation(operationList, workflowId, operationId, employeeId);

        }
    }

    @Override
    public List<HistoricalListResp> getHistoricalRecords(String employeeId) {
        List<HistoricalListResp> listResps = customEmployeeMapper.getHistoricalRecords(employeeId);
        if (CollectionUtils.isNotEmpty(listResps)) {
            HrmsEmployeeResp byEmployeeId = hrmsEmployeeService.findByEmployeeId(employeeId);
            listResps.forEach(historicalListResp -> {
                List<CustomEmployeeUpdateDetail> detailList = customEmployeeMapper.getAuditInfo(historicalListResp.getWfInstanceId(), employeeId);
                List<CustomEmployeeUpdateDetail> qualified = detailList.stream().filter(commEmployeeUpdateDetail -> Objects.equals(commEmployeeUpdateDetail.getAuditStatus(), 1)).collect(Collectors.toList());
                List<CustomEmployeeUpdateDetail> unQualified = detailList.stream().filter(commEmployeeUpdateDetail -> Objects.equals(commEmployeeUpdateDetail.getAuditStatus(), 2)).collect(Collectors.toList());
                //审批人查询
                Map<String, String> employeeTask = customEmployeeMapper.getEmployeeTask(Objects.nonNull(byEmployeeId) ? byEmployeeId.getEmployeeNo() : "");
                Map<String, Object> employeeMap = authStatusByEmployeeId(employeeId);
                String text = "合格：" + qualified.size();
                if (Objects.isNull(employeeMap)) {
                    historicalListResp.setAuditStatus("未发起");
                } else if (Objects.nonNull(employeeMap) && Objects.equals(employeeMap.get("status").toString(), "1")) {
                    String assigneeNames = Objects.nonNull(employeeTask) ? employeeTask.get("assigneeNames") : "";
                    historicalListResp.setAuditStatus("审批中" + "(" + assigneeNames + ")");
                } else if (Objects.nonNull(employeeMap) && Objects.equals(employeeMap.get("status").toString(), "2")) {
                    historicalListResp.setAuditStatus("审批通过");
                    text = text + "," + "不合格：" + unQualified.size();
                    historicalListResp.setAuditResult(text);
                }
                historicalListResp.setText(historicalListResp.getWorkflowName() + "-" + historicalListResp.getWfCurrentStepName() + "-" + historicalListResp.getWorkflowNumber());
            });

        } else {
            //没有流程查询
            return customEmployeeMapper.getUnFlowHistoricalRecords(employeeId);
        }

        return listResps;
    }

    private List<ExcelExportEntity> getReportList() {
        List<ExcelExportEntity> list = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("姓名", "employee_name");
        list.add(colEntity);
        ExcelExportEntity colEntity1 = new ExcelExportEntity("工号", "employee_no");
        list.add(colEntity1);
        ExcelExportEntity colEntity2 = new ExcelExportEntity("组织机构", "orgName");
        list.add(colEntity2);
        ExcelExportEntity colEntity3 = new ExcelExportEntity("发起时间", "initiationTime");
        list.add(colEntity3);
        ExcelExportEntity colEntity4 = new ExcelExportEntity("审核状态", "auditStatus");
        list.add(colEntity4);
        ExcelExportEntity colEntity5 = new ExcelExportEntity("审核结果", "auditResult");
        list.add(colEntity5);
        ExcelExportEntity colEntity6 = new ExcelExportEntity("必填结果", "requiredResult");
        list.add(colEntity6);
        ExcelExportEntity colEntity7 = new ExcelExportEntity("资料完成度", "archiveProgress");
        list.add(colEntity7);
        ExcelExportEntity colEntity8 = new ExcelExportEntity("不合格结果", "unQualifiedResult");
        list.add(colEntity8);
        ExcelExportEntity colEntity9 = new ExcelExportEntity("未完成项", "unFinish");
        list.add(colEntity9);
        return list;
    }

    @Override
    public void exportReport(Page page, HttpServletResponse response, HrmsEmployee record) {

        // 导出文件名称
        String filename = "档案报表.docx";
        // 模板位置
        Resource resource = new ClassPathResource("template/dabb.xlsx");
        // 导出数据列表
        page.setPageNo(1);
        page.setPageSize(Integer.MAX_VALUE);
        page.setSidx("s.create_date");
        page.setSord("desc");

        List<Map<String, String>> mapList = getEmployeePageListReport(page, record);
        try {
            XSSFWorkbook workbook = new XSSFWorkbook(resource.getInputStream());
            XSSFSheet sheet = workbook.getSheetAt(0);

            if (CollectionUtils.isNotEmpty(mapList)) {
                XSSFCellStyle colCellStyle = workbook.createCellStyle();
                colCellStyle.setWrapText(true);
                colCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                colCellStyle.setBorderLeft(BorderStyle.THIN);
                colCellStyle.setBorderTop(BorderStyle.THIN);
                colCellStyle.setBorderRight(BorderStyle.THIN);
                colCellStyle.setBorderBottom(BorderStyle.THIN);
                XSSFFont font = workbook.createFont();//生产一个字体
                font.setColor(HSSFColor.BLACK.index);    //字体颜色
                font.setFontHeightInPoints((short) 14);
                colCellStyle.setFont(font);

                try {
                    for (int i = 0; i < mapList.size(); i++) {
                        Row newRow = sheet.getRow(i + 2 );

                        newRow.getCell(0).setCellValue(i+1);
                        newRow.getCell(0).setCellStyle(colCellStyle);
                        newRow.getCell(1).setCellValue(mapList.get(i).get("employee_name"));
                        newRow.getCell(1).setCellStyle(colCellStyle);
                        newRow.getCell(2).setCellValue(mapList.get(i).get("employee_no"));
                        newRow.getCell(2).setCellStyle(colCellStyle);
                        newRow.getCell(3).setCellValue(mapList.get(i).get("orgName"));
                        newRow.getCell(3).setCellStyle(colCellStyle);
                        newRow.getCell(4).setCellValue(mapList.get(i).get("initiationTime"));
                        newRow.getCell(4).setCellStyle(colCellStyle);
                        newRow.getCell(5).setCellValue(mapList.get(i).get("auditStatus"));
                        newRow.getCell(5).setCellStyle(colCellStyle);
                        newRow.getCell(6).setCellValue(mapList.get(i).get("auditResult"));
                        newRow.getCell(6).setCellStyle(colCellStyle);
                        newRow.getCell(7).setCellValue(mapList.get(i).get("requiredResult"));
                        newRow.getCell(7).setCellStyle(colCellStyle);
                        newRow.getCell(8).setCellValue(mapList.get(i).get("archiveProgress"));
                        newRow.getCell(8).setCellStyle(colCellStyle);
                        newRow.getCell(9).setCellValue(mapList.get(i).get("unQualifiedResult"));
                        newRow.getCell(9).setCellStyle(colCellStyle);
                        newRow.getCell(10).setCellValue(mapList.get(i).get("unFinish"));
                        newRow.getCell(10).setCellStyle(colCellStyle);
                    }
                    response.setContentType("application/vnd.ms-excel");
                    response.setCharacterEncoding("UTF-8");
                    response.setHeader("Content-disposition", "attachment; filename="
                            + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                    OutputStream fos = response.getOutputStream();
                    workbook.write(fos);
                    fos.close();
                } catch (Exception e) {
                    logger.error("导出数据异常" + e.getMessage(), e);
                    PlatformResult.failure("导出数据异常" + e.getMessage());
                }

            }
        } catch (IOException e) {
            logger.error("获取导出模板错误" + e.getMessage(), e);
        }

    }

    @Override
    public List<Map<String, String>> getEmployeePageListReport(Page page, HrmsEmployee record) {
        // 机构排序
        page.setSidx(" o.tree_ids");
        page.setSord("ASC");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        // 增加数据权限控制
        Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
        Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
        boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");//人事管理员

        if (!isAdmin && !isALL && !isInfoAdmin) {
        	record.setOrgId(user.getDeptId());
        	//非管理员，只查看本科室数据，下面的 下属权限暂时没起作用
            boolean isSub = UserInfoHolder.getRight("IS_SUB"); // 下属管理权限

            if (isSub) {

                String orgRang = user.getOrgRang();
                List<String> orgCodeList = new ArrayList<>();
                if (StringUtils.isNotBlank(orgRang)) {
                    String[] orgList = (orgRang.substring(1, orgRang.length() - 1).replaceAll("'", "").split(","));
                    for (String orgCode : orgList) {
                        if (StringUtils.isNotBlank(orgCode)) {
                            orgCodeList.add(StringUtils.deleteWhitespace(orgCode));
                        }
                    }
                }
                // 本人 SELF 本部门 SELF_DEPT SELF_SUB_DEPT 本部门以及下属部门
                // 直接查询本部门 或者 下属部门权限
                List<String> hrmsOrganizationAndNextList = hrmsOrganizationService
                        .getHrmsOrganizationAndNextList(user.getDeptId());

                orgCodeList.addAll(hrmsOrganizationAndNextList);
                record.setOrgCodeList(orgCodeList);
            } else {
                if (user != null) {
                    record.setCreateUser(user.getUsercode());
                }

            }

        }

        //数据管理权限
		List<String> roleByUserCode = customEmployeeLeaderService.getIdentityCodeByUserCode(user.getUsercode());
      //  List<String> roleByUserCode = hrmsRoleGroupLeaderService.getRoleByUserCode(user.getUsercode());
        record.setRoleByUserCode(roleByUserCode);

        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<Map<String, String>> list = null;
        List<Map<String, String>> mapList = new ArrayList<>();
        //查询档案报表人数
        if (CollectionUtils.isNotEmpty(record.getGroupIds())) {
            list = customEmployeeMapper.getEmployeePageListGroup(page, record);
        } else {
            list = customEmployeeMapper.getEmployeePageListReport(page, record);
        }

        for (Map<String, String> dataMap : list) {
            Map<String, String> map = new HashMap<>();
            map.putAll(dataMap);

            //查询合格不合格的项
            List<CustomEmployeeUpdateDetail> detailList = customEmployeeMapper.getAuditInfo(Objects.nonNull(dataMap.get("wfInstanceId")) ? dataMap.get("wfInstanceId") : null, dataMap.get("employee_id"));
            //合格
            List<CustomEmployeeUpdateDetail> qualified = detailList.stream().filter(commEmployeeUpdateDetail -> Objects.equals(commEmployeeUpdateDetail.getAuditStatus(), 1)).collect(Collectors.toList());
            //不合格
            List<CustomEmployeeUpdateDetail> unQualified = detailList.stream().filter(commEmployeeUpdateDetail -> Objects.equals(commEmployeeUpdateDetail.getAuditStatus(), 2)).collect(Collectors.toList());
            //流程id
            Map<String, Object> employeeMap = authStatusByEmployeeId(dataMap.get("employee_id"));
            //查询分组
			List<CommEmployeeFieldGroup> groupList = customEmployeeMapper.getCompleteProgress();
			if (CollectionUtils.isNotEmpty(groupList)){
                StringBuilder sb = new StringBuilder();
                AtomicInteger i = new AtomicInteger();
                int j = 0;
                //查询分组中必填的分组表
                    List<String> fieldList;
                    List<CustomEmployeeGroup> fieldGroupList = commEmployeeFieldGroupService.getAllIsMustFeild(record.getGroupIds());
                    Map<String, List<CustomEmployeeGroup>> stringListMap = fieldGroupList.stream().collect(Collectors.groupingBy(b -> b.getId()));
                    for(String groupId :stringListMap.keySet()) {
                        List<CustomEmployeeGroup> employeeFieldGroupList = stringListMap.get(groupId);
                        //所有必填的列
                        
                        String tableName = employeeFieldGroupList.get(0).getTableName();
                        
                        if("cust_emp_info".equals(tableName)) {
                        	tableName = " cust_emp_base e left join cust_emp_info i on e.employee_id = i.info_id ";
                        }
                        
                        List<Map<String, String>> mustMapList = customEmployeeMapper.getEmployeeCloum(tableName,employeeFieldGroupList, dataMap.get("employee_id"));
                            if (CollectionUtils.isNotEmpty(mustMapList)){
                                Map<String, String> filteredMap = mustMapList.get(0).entrySet()
                                        .stream()
                                        .filter(entry -> !Objects.equals(entry.getValue(),"1"))
                                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                                //拼接未完成的数据
                                if (filteredMap.size() > 0){
                                    sb.append(employeeFieldGroupList.get(0).getGroupName());

                                    fieldList = new ArrayList<>(filteredMap.values());
                                    sb.append("(");
                                    sb.append(String.join(",", fieldList));
                                    sb.append(")");
                                    if (i.get() < groupList.size() - 1){
                                        sb.append("，");
                                    }
                                    j++;
                                }
                            } else {
                                //查询为空代表都未完成，拼接列
                                fieldList = employeeFieldGroupList.stream().map(CustomEmployeeGroup::getShowName).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(fieldList)){
                                    sb.append(employeeFieldGroupList.get(0).getGroupName());
                                    sb.append("(");
                                    sb.append(String.join(",", fieldList));
                                    sb.append(")");
                                    if (i.get() < stringListMap.keySet().size() - 1){
                                        sb.append("，");
                                    }
                                    j++;
                                }
                            }
                    i.incrementAndGet();
                }

                //必填结果 requiredResult
                map.put("requiredResult", "完成" + (groupList.size() - j) + "," + "未完成" + j);

                if (Objects.isNull(employeeMap)) {
                    map.put("auditStatus", "未发起");
                }
                double multiply = (double) (groupList.size() - j)/groupList.size();
                map.put("archiveProgress", (int) Math.round(multiply * 100) + "%");
                //未完成项 unFinish
                map.put("unFinish", sb.toString());

            String text = "合格：" + qualified.size();
            if (Objects.nonNull(dataMap.get("wfInstanceId"))) {
                //审批人查询
                Map<String, String> employeeTask = customEmployeeMapper.getEmployeeTask(dataMap.get("employee_no"));
                if (Objects.nonNull(employeeMap) && Objects.equals(employeeMap.get("status").toString(), "1")) {
                    String assigneeNames = Objects.nonNull(employeeTask) ? employeeTask.get("assigneeNames") : "";
                    map.put("auditStatus", "审批中" + "(" + assigneeNames + ")");
                } else if (Objects.nonNull(employeeMap) && Objects.equals(employeeMap.get("status").toString(), "2")) {
                    map.put("auditStatus", "审批通过");
                    //审核结果
                    text = text + "," + "不合格：" + unQualified.size();
                    map.put("auditResult", text);
                    //不合格结果 unQualifiedResult
                    List<String> unFinishResult = commEmployeeFieldGroupService.getUnfinishResult(dataMap.get("employee_id"), dataMap.get("wfInstanceId"), record.getGroupIds());
                    if (CollectionUtils.isNotEmpty(unFinishResult)) {
                        map.put("unQualifiedResult", String.join(",", unFinishResult));
                    }
                }
                //发起时间 initiationTime
                if (CollectionUtils.isNotEmpty(detailList)) {
                    map.put("initiationTime", DateUtil.format(detailList.get(0).getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
                }
              }
            }
            mapList.add(map);
        }

        return mapList;
    }

}
