package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/5 9:50
 * @description
 */
@Data
public class OrganizationAllocationConfigSaveReq {
    @ApiModelProperty(value = "操作符 =,>=,<=")
    String operator;
    @ApiModelProperty(value = "数量1")
    Float num1;
    @ApiModelProperty(value = "数量2")
    Float num2;
    @ApiModelProperty(value = "编码 人床比标准（rcb）医护比标准（yhb）医床比标准（ycb）护床比标准（hcb）")
    String code;
}
