package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrgChildrenEmplListReq;
import cn.trasen.homs.base.bean.OrgChildrenEmplListRes;
import cn.trasen.homs.base.bean.OrganizationChildrenListResp;
import cn.trasen.homs.base.bean.OrganizationFrameworkResp;
import cn.trasen.homs.base.bean.OrganizationImport;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.bean.OrganizationListResp;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.OrganizationMergeReq;
import cn.trasen.homs.base.bean.OrganizationMoveReq;
import cn.trasen.homs.base.bean.OrganizationReq;
import cn.trasen.homs.base.bean.OrganizationSaveReq;
import cn.trasen.homs.base.bean.OrganizationSplitReq;
import cn.trasen.homs.base.bo.OrgEmpInBO;
import cn.trasen.homs.base.bo.OrgEmpOutBO;
import cn.trasen.homs.base.dto.ReplaceLeader;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;

/**
 * <AUTHOR>
 * @createTime 2021/7/26 11:13
 * @description
 */
public interface IOrganizationService {

    /**
     * @description: 获取组织架构图
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationFrameworkResp>
     * @author: liyuan
     * @createTime: 2021/8/5 10:39
     */
    List<OrganizationFrameworkResp> getFramework(OrganizationListReq organizationListReq);

    /**
     * @description: 返回机构数列表
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationChildrenListResp>
     * @author: liyuan
     * @createTime: 2021/7/26 11:25
     */
    List<OrganizationChildrenListResp> getChildrenList(OrganizationListReq organizationListReq);


    List<Organization> getChildrenList(OrganizationReq organizationReq);


    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */
    List<Organization> getBaseList(OrganizationListReq organizationListReq);

    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    List<OrganizationListSimpleRes> getSimpleList(OrganizationListReq organizationListReq);

    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    DataSet<OrganizationListSimpleRes> pageSimple(Page page, OrganizationListReq organizationListReq);

    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    Organization getBase(OrganizationReq organizationReq);

    /**
     * 根据ID批量获取组织结构
     *
     * @param idList
     * @return java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * <AUTHOR>
     * @date 2021/10/25 19:26
     */
    List<OrganizationListResp> getListByIds(List<String> idList);

    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */
    DataSet<OrganizationListResp> getAllPageList(Page page, OrganizationListReq organizationListReq);

    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */
    OrganizationListResp getAll(String orgId);

    /**
     * @description: 获取下级所有组织机构
     * @param: orgId
     * @param: organizationList
     * @return: java.util.List<cn.trasen.basicsbottom.model.Organization>
     * @author: liyuan
     * @createTime: 2021/7/27 15:58
     */
    List<Organization> getNextOrg(String orgId);

    /**
     * @param organizationAddReq
     * @description: 新增
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    Organization add(OrganizationSaveReq organizationAddReq);

    /**
     * @description: 修改
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    void update(OrganizationSaveReq organizationAddReq);

    /**
     * @description: 删除
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    void delete(String orgId);


    /**
     * @description: 删除
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    @Transactional(rollbackFor = Exception.class)
    void delete(String orgId, boolean isVerify);

    /**
     * @description: 合并
     * @param: organizationMergeReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:06
     */
    @Transactional(rollbackFor = Exception.class)
    void merge(OrganizationMergeReq organizationMergeReq);

    /**
     * @description: 迁移
     * @param: organizationMoveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:15
     */
    @Transactional(rollbackFor = Exception.class)
    void move(OrganizationMoveReq organizationMoveReq);

    /**
     * @description: 拆分
     * @param: organizationSplitReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:42
     */
    @Transactional(rollbackFor = Exception.class)
    void split(OrganizationSplitReq organizationSplitReq);

    /**
     * @description: 禁用停用
     * @param: orgId
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 8:42
     */
    void enable(String orgId, String enable);

    /**
     * @description: 验证组织下有子级或人员
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 14:18
     */
    void verifyEnable(String orgId);

    /**
     * @description: 验证组织下有子级或人员
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 14:18
     */
    void verifyEnable(String orgId, String msg);

    /**
     * @description: 同步打sso
     * @param: organization
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:59
     */
    void synAllSystemDept();

    /**
     * @description: 导入数据
     * @param: organizationImports
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     * @author: liyuan
     * @createTime: 2021/7/29 14:44
     */
    @Transactional(readOnly = false)
    PlatformResult<String> excelImportOrganization(List<OrganizationImport> organizationImports, String type);

    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void updateSort(List<OrganizationSaveReq> organizationSaveReqs);

    /**
     * 获取子机构和员工信息
     *
     * @param orgEmplListReq
     * @return cn.trasen.basicsbottom.bean.OrgChildrenEmplListRes
     * <AUTHOR>
     * @date 2021/12/9 11:42
     */
    OrgChildrenEmplListRes getOrgChildrenEmplList(OrgChildrenEmplListReq orgEmplListReq);

    /**
     * 获取组织机构和员工列表
     *
     * @param orgEmpInBO
     * @return java.util.List<cn.trasen.basicsbottom.bo.OrgEmpOutBO>
     * <AUTHOR>
     * @date 2022/2/14 10:17
     */
    abstract OrgEmpOutBO getOrgEmp(OrgEmpInBO orgEmpInBO);

    /**
     * 获取所有组织机构
     *
     * @param page
     * @return
     */
    List<Organization> getAllOrgList(Page page, Organization organizatio);

    /**
     * 获取当前登录人所属科室
     *
     * @return
     */
    Organization getEmpBelongingHospital();

    /**
     * 获取所有医院
     *
     * @param record
     * @return
     */
    List<Organization> getAllHospital(OrganizationListResp record);


    /**
     * @param act:
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 把组织的数据同步到通讯录
     * @date 2023/12/4 17:55
     */

    void syncOrganization2ContactsSingleTime(String act, Organization record,Organization origin);

    Organization selectOneById(String id);

    /**
     * 组织机构导出排序
     * @param list
     * @param pid
     * @return
     */
    List<OrganizationListResp> getChildrenResp(List<OrganizationListResp> list,String pid);

	void bacthReplaceLeader(ReplaceLeader replaceLeader);
}