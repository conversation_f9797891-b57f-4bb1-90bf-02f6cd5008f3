package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

@Table(name = "comm_employee_field_group")
@Setter
@Getter
public class CommEmployeeFieldGroup {
    @Id
    private String id;

    /**
     * 分组名称
     */
    @Column(name = "group_name")
    @ApiModelProperty(value = "分组名称")
    private String groupName;
    
    @ApiModelProperty(value = "是否明细")
    @Column(name = "is_detailed")
    private Integer isDetailed;
    
    /**
     * 是否允许删除(0否 1 是)
     */
    @Column(name = "is_allow_deleted")
    @ApiModelProperty(value = "是否允许删除(0否 1 是)")
    private Integer isAllowDeleted;

    /**
     * 是否停用(0 否 1 是)
     */
    @Column(name = "is_disabled")
    @ApiModelProperty(value = "是否停用(0 否 1 是)")
    private Integer isDisabled;
    
    @ApiModelProperty(value = "排序")
    private Integer seq;
    
    @Column(name = "table_name")
    @ApiModelProperty(value = "关联表名")
    private String tableName;
    
    @Column(name = "default_condition")
    @ApiModelProperty(value = "默认查询条件")
    private String defaultCondition;
    
    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    @Transient
    private List<CommEmployeeField> fields;
    
    @Transient
    @ApiModelProperty(value = "是否只读")
    private Integer isEdit;
    
    @Transient
    @ApiModelProperty(value = "当前用户code,用于查询")
    private String userCode;


    @ApiModelProperty(value = "明细是否在履历中展示 0 不显示 1 显示")
    @Column(name = "show_pass_by")
    private Integer showPassBy;
    
    @ApiModelProperty(value = "明细是否在履历中展开 0 不展开 1 展开")
    @Column(name = "show_open_by")
    private Integer showOpenBy;

    @ApiModelProperty(value = "明细保留一组必填数据，删除按钮不显示 0 可删除 1不可删除")
    @Column(name = "show_delete")
    private Integer showDelete;

    @ApiModelProperty(value = "排序字段")
    @Column(name = "sort_field")
    private String sortField;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 显示名称
     */
    @Transient
    @ApiModelProperty(value = "显示名称")
    private String showName;

    /**
     * 字段名称
     */
    @Transient
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

}