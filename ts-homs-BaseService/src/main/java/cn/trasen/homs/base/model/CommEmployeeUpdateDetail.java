package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "comm_employee_update_detail")
@Setter
@Getter
public class CommEmployeeUpdateDetail {
    @Id
    private String id;

    /**
     * 修改表id
     */
    @Column(name = "operation_id")
    @ApiModelProperty(value = "修改记录表id")
    private String operationId;

    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 修改的分组
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "修改的分组")
    private String groupId;

    /**
     * 流程id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;
    
    @Column(name = "show_name")
    @ApiModelProperty(value = "显示名称")
    private String showName;
    
    /**
     * 修改的字段名称
     */
    @Column(name = "field_name")
    @ApiModelProperty(value = "修改的字段名称")
    private String fieldName;

    /**
     * 修改类型(1:单表 2:子表)
     */
    @Column(name = "update_type")
    @ApiModelProperty(value = "修改类型(1:单表 2:子表)")
    private Integer updateType;
    
    @Column(name = "table_name")
    @ApiModelProperty(value = "修改数据所在的表名称")
    private String tableName;

    /**
     * 审核状态
     */
    @Column(name = "audit_status")
    @ApiModelProperty(value = "审核状态 null 默认 1 合格 2 不合格")
    private Integer auditStatus;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    /**
     * 修改前数据
     */
    @Column(name = "before_data")
    @ApiModelProperty(value = "修改前数据")
    private String beforeData;

    @Column(name = "after_data")
    private String afterData;

    @Column(name = "remark")
    private String remark;


    
    @ApiModelProperty(value = "修改的明细表字段信息")
    private List<CommEmployeeField> fields;
    
    @Transient
    private String fileType;  //是否附件
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}