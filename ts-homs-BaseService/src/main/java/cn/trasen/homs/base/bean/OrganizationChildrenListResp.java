package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationChildrenListResp {

    @ApiModelProperty(value = "主键ID")
    private String organizationId;

    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private String code;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String name;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlagLable;
    /**
     * 员工人数
     */
    @ApiModelProperty(value = "员工人数")
    private Integer employeeNum;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer seqNo;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;




    @ApiModelProperty(value = "部门领导")
    private  List<OrganizationLeaderResp> leaders;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @ApiModelProperty(value = "是否启用: Y=是; N=否;")
    private String isEnable;


    /**
     * 是否启用: 1=是; 2=否;
     */
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnableLable;

    List<OrganizationChildrenListResp> children = null;

}