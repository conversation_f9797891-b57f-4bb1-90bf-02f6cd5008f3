package cn.trasen.homs.base.contants;

/**   
 * @Title: CommonContants.java 
 * @Package cn.trasen.hrms.contants 
 * @Description: 通用常量类 
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月22日 下午6:09:12 
 * @version V1.0   
 */
public class CommonContants {

	/** 启用状态: 启用 **/
	public static final String IS_ENABLE_TRUE = "1";
	/** 启用状态: 停用 **/
	public static final String IS_ENABLE_FALSE = "2";

	/** 职称最大父ID **/
	public static final String JOBTITLE_BASE_PID = "0";
	/** 组织机构顶级父级ID **/
	public static final String ORG_BASE_PID = "0";

	/** 薪酬项目计算公式符号 **/
	public static final String SALARYITEM_SYMBOL = "_";

	/** 最高职称: 是 **/
	public static final String HIGHESTLEVEL_TRUE = "1";

	/** 薪酬项目数据类别: 薪酬项目 **/
	public static final String DATA_CATEGORY_SALARY = "1";
	/** 薪酬项目数据类别: 考勤项目 **/
	public static final String DATA_CATEGORY_ATTENDANCE = "2";

	/** 合同期限: 有限 **/
	public static final String CONTRACT_ALLOTEDTIME_LIMITED = "1";
	/** 合同首次签订 **/
	public static final String CONTRACT_FIRST_SIGN = "首次";

	/** 简历状态: 未邀约 **/
	public static final String RESUME_STATUS_1 = "1";
	/** 简历状态: 已邀约 **/
	public static final String RESUME_STATUS_2 = "2";

	/** 生日礼物领取状态: 已领取 **/
	public static final String BIRTH_RECEIVE_STATUS_1 = "1";
	/** 生日礼物领取状态: 未领取 **/
	public static final String BIRTH_RECEIVE_STATUS_2 = "2";
	
	/** 机构操作类型: 合并 **/
	public static final String ORG_OPERATE_TYPE_1 = "1";
	/** 机构操作类型: 拆分 **/
	public static final String ORG_OPERATE_TYPE_2 = "2";

	/** 人事事件类别: 离职 **/
	public static final String INCIDENT_TYPE_RESIGNATION = "1";
	/** 人事事件类别: 退休 **/
	public static final String INCIDENT_TYPE_RETIREMENT = "2";
	/** 人事事件类别: 死亡 **/
	public static final String INCIDENT_TYPE_DEATH = "3";
	/** 人事事件类别: 延聘 **/
	public static final String INCIDENT_TYPE_POSTPONE = "4";
	/** 人事事件类别: 返聘 **/
	public static final String INCIDENT_TYPE_REENGAGE = "5";
	
	public static final String BEN_REN_EMP="BENREN";

}
