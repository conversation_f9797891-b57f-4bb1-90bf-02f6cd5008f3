package cn.trasen.homs.base.service.impl;

import cn.trasen.homs.base.service.InitializationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;

/**
  * @Description: saas化前历史单机构库里所有表机构编码设置初始值
  * <AUTHOR>
  * @Date    2025/7/18
**/
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
@Slf4j
public class InitializationServiceImpl implements InitializationService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询库里所有表以及表字段包含机构编码
     * @param ssoOrgCode
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String initialization(String ssoOrgCode){
        List<Map<String, Object>> tables = jdbcTemplate.queryForList("SHOW TABLES");
        tables.forEach(map -> map.forEach((key, value) -> {
            List<Map<String, Object>> mapList = jdbcTemplate.queryForList("SHOW COLUMNS FROM " + value);
            mapList.forEach(map1 -> {
                if (String.valueOf(map1.get("Field")).toLowerCase().equals("sso_org_code")) {
                    //检查是否有索引，如有则不更新
                    List<Map<String, Object>> isIndex = jdbcTemplate.queryForList("SHOW INDEXES FROM " + value);
                    long count = isIndex.stream().filter(is -> is.get("Column_name").equals("sso_org_code")).count();
                    String sql = "update " + value + " set sso_org_code = '" + ssoOrgCode + "'  where sso_org_code = null";
                    if (count > 0) {
                        log.info("含机构编号索引：" + sql);
                    }
                    jdbcTemplate.execute(sql);
                    log.info(sql);
                }
            });
        }));

        return "执行成功";
    }
}
