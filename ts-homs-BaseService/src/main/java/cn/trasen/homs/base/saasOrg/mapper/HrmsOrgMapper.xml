<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.saasOrg.dao.HrmsOrgMapper">
	 <select id="selectList" resultType="cn.trasen.homs.base.saasOrg.model.HrmsOrg" parameterType="cn.trasen.homs.base.saasOrg.model.HrmsOrg">
	  	SELECT
			t1.*,t2.org_id parentId
		FROM
			comm_org t1
		left join comm_org t2 on t2.org_code=t1.parent_code
		WHERE
			t1.is_deleted = 'N'
		<if test="keywords != null and keywords !=''">
       	 	AND ( t1.org_name like CONCAT('%', #{keywords}, '%') or t1.org_code like CONCAT('%', #{keywords}, '%') )
        </if>
		<if test="orgCode != null and orgCode !=''">
       	 	AND t1.org_code=#{orgCode}
        </if>
		<if test="orgId != null and orgId !=''">
       	 	AND t1.org_id=#{orgId}
        </if>
		<if test="status != null and status !=''">
       	 	AND t1.status=#{status}
        </if>
		order by t1.sort asc , t1.org_id asc
	 </select>
	 
    <update id="initAddOrgData" parameterType="cn.trasen.homs.base.saasOrg.vo.OrgData">
		
		<if test="sync == null or sync =='' or sync != 'Y'.toString">
			INSERT INTO `ts_thps`.`thps_org` (`org_id`,`org_code`,`org_name`,`STATUS`,`create_date`,`create_user`,`update_date`,`update_user`,`IS_DELETED`,
			`PARENT_CODE`,`CREATE_USER_NAME`,`UPDATE_USER_NAME`,`MAR_CATG_CODE`,`MAR_CATG_NAME`)
			VALUES(#{orgId},#{orgCode},#{orgName},'1','2024-01-16 02:54:22','admin','2024-01-16 02:54:22','admin','N',#{parentCode},'admin','admin','YLJG','医疗机构');
		
			INSERT INTO `ts_thps`.`thps_dept` (`id`,`corpcode`,`deptcode`,`deptname`,`level`,`deptlevel`,`STATUS`,`createuser`,`createtime`,
			`updateuser`,`updatetime`,`CREATE_USER_NAME`,`UPDATE_USER_NAME`,`IS_DELETED`,`IDTYPE`,`ORGFLAG` )VALUES
			(#{deptId},#{orgCode},#{deptId},#{orgName},'0','0','1','admin','2024-01-16 02:57:49','admin','2024-01-16 02:57:49','admin','admin','N','1','1');
			
			INSERT INTO `ts_thps`.thps_user (id,corpcode,usercode,username,PASSWORD,oldpassword,STATUS,apptime,updateuser,updatetime,EMPLOYEE_ID)
			VALUES(#{userId},#{orgCode},#{userCode},#{userName},'1000:7118e88aa62aa611886cc004293d3c4b:317836173e1e999a9222422d5da63367','PAPGvf0RpOsc3T+8XkFMvA==49.67',
					1,'2024-3-2 17:03:51','admin','2024-3-2 17:03:51',#{userId});		
			
			INSERT INTO `ts_thps`.THPS_EMPLOYEE (ID,NAME,NATIONALITY,BIRTHDAY,JOB_STATUS,JOB_STATUS_NAME,SEX,HEAD_IMAGE_URL,CARD_NO,CARD_TYPE,
			STATUS,CREATE_DATE,CREATE_USER,CREATE_USER_NAME,UPDATE_USER,UPDATE_USER_NAME,IS_DELETED,JOB_TYPE,JOB_TYPE_NAME ) 
			VALUES( #{userId},#{userName},'汉族','1111-11-11','在编','在编','1','','******************','01',
			1,'2024-3-2 17:03:50','admin','admin','admin','admin','N','01','医生');
			
			INSERT INTO `ts_thps`.thps_user_org_map (ID,IS_DELETED,CREATE_USER,CREATE_DATE,UPDATE_USER,UPDATE_DATE,UPDATE_USER_NAME,CREATE_USER_NAME,USER_ID,
			ORG_ID,DEAD_DATE,EFFECTIVE_DATE,DEFAULT_DEPT_CODE,DEFAULT_DEPT_ID,STATUS)
			VALUES(#{orgMapId},'N','admin','2024-3-2 17:03:51','admin','2024-3-2 17:03:51','admin','admin',#{userId},
			#{orgId},'2124-3-2 0:00:00','2024-3-2 0:00:00',#{deptId},#{deptId},1);
        </if>
		
		INSERT INTO `comm_organization` (`organization_id`,`code`,`name`,`parent_id`,`tree_ids`,`is_enable`,`org_level`,`seq_no`,`org_flag`,
		`personnel_allocation`,`create_date`,`create_user`,`update_date`,`update_user`,`is_deleted`,`sort`,`sso_org_code`)
		VALUES(#{deptId},#{deptId},#{orgName},'',#{deptId},'1','1','1','1','0','2023-01-12 17:27:19','admin','2024-01-20 17:07:38','admin','N','0',#{orgCode});

		INSERT INTO `cust_emp_base` (`employee_id`,`his_employee_no`,`employee_no`,`employee_name`,`org_id`,
		`employee_status`,is_deleted,is_enable,is_sms_reminder,is_voice_reminder,is_wx_reminder,is_display_phone_no,is_use_signature,sso_org_code,sso_org_name)
		VALUES(#{userId},#{userCode},#{userCode},#{userName},#{deptId},'1','N','1','1','1','1','1','1',#{orgCode},#{orgName});

		INSERT INTO `cust_emp_info` (`info_id`) VALUES(#{userId});

		INSERT INTO `toa_employee` (`ID`,`EMP_DEPT_ID`,`EMP_DEPT_NAME`,`EMP_DEPT_CODE`,`EMP_NAME`,`EMP_CODE`,`EMP_SEX`,
		EMP_STATUS,USER_ACCOUNTS,IS_SMS_REMINDER,IS_VOICE_REMINDER,IS_WX_REMINDER,IS_DISPLAY_PHONE_NO,IS_USE_SIGNATURE,IS_DELETED,sso_org_code)
		VALUES(#{userId},#{deptId},#{orgName},#{deptId},#{userName},#{userCode},1,1,#{userCode},1,1,1,1,1,'N',#{orgCode});

		INSERT INTO `cust_emp_org_map` (`id`,`employee_id`,`sso_org_code`,`sso_org_name`,`is_default`,`default_dept_code`,`default_dept_id`,`status`,`IS_DELETED`)
		VALUES(#{userId},#{userId},#{orgCode},#{orgName},'Y',#{deptId},#{deptId},1,'N');
		
		INSERT INTO `toa_sys_setting` (`ID`,`HOSPITAL_LOGO`,`TOP_LOGO`,`LOGIN_PAGE_BACKGROUND`,`LOGIN_TOP_LOGO`,
		`CREATE_USER`,`CREATE_USER_NAME`,`CREATE_DATE`,`UPDATE_USER`,`UPDATE_USER_NAME`,`UPDATE_DATE`,`IS_DELETED`,`ORG_CODE`,
		`watermark_list`,`watermark_text`,`password_preset`,`password_length`,`password_rule`,`remind_password`,`remind_signature`,
		`web_title`,`mobile_platform`,`salary_type`,`forget_pwd`,`verify_code`,`im_switch`,`worksheet_switch`,`account_login`,
		`lockscreen`,`remember_pwd`,`parttime_switch`,`anonymous_box`,`is_default`,`platform_type`,`platform_login_type`,`sso_org_code`,
		`sso_org_name`,`SMS_CODE`,`verify_code_type`)
		VALUES(#{settingId},'698090489444745216','699144934001696769','697379483491946496','698090542473330689','admin',
			'admin','2021-08-30 15:45:39','admin','管理员','2023-10-27 20:03:42','',#{orgCode},'','','123456','8','1,2,3,4','1',
			'0','协同办公平台','1','1,2','1','1','0','0','0','0','1','0','0','0','2','1',#{orgCode},#{orgName},'0','1');

		INSERT INTO `toa_portal_theme` (`id`,`title`,`is_default`,`create_user`,`create_user_name`,`create_time`,
		`is_deleted`,`all_reader`,`fixed_height`,`sso_org_code`,`sso_org_name`)
		VALUES(#{themeId},#{orgName},'1',#{userCode},#{userName},'2024-01-16 11:36:57','N','1','1',#{orgCode},#{orgName});

		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`,
		 `create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId1}, #{themeId}, '1', '2', '1', '', 'N', '0', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`, 
		`create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId2}, #{themeId}, '4', '2', '1', '', 'N', '1', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`, 
		`create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId3}, #{themeId}, '11', '2', '1', '', 'N', '2', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
	</update>
	 
    <update id="updateOrgData" parameterType="cn.trasen.homs.base.saasOrg.vo.OrgData">
    	<if test="sync == null or sync =='' or sync != 'Y'.toString">
    		UPDATE `ts_thps`.`thps_org` set `org_name`=#{orgName}, status=#{orgStatus} where `org_code`=#{orgCode};
    	</if>
    	UPDATE `comm_organization` set `sso_org_name`=#{orgName} where `sso_org_code`=#{orgCode};
    	
    	UPDATE `cust_emp_org_map` set `sso_org_name`=#{orgName} where `sso_org_code`=#{orgCode};
		
	</update>
	 
    <update id="deleteOrgData" parameterType="cn.trasen.homs.base.saasOrg.vo.OrgData">
    	
    	UPDATE `ts_thps`.`thps_org` set `IS_DELETED`='Y' where `org_code`=#{orgCode};
    	
    	UPDATE `comm_organization` set `IS_DELETED`='Y' where `sso_org_code`=#{orgCode};
    	
    	UPDATE `cust_emp_org_map` set `IS_DELETED`='Y' where `sso_org_code`=#{orgCode};
		
	</update>
</mapper>