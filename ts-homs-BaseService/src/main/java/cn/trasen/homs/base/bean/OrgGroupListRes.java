package cn.trasen.homs.base.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/** 
* @description:
* @return: 
* @author: liyuan
* @createTime: 2021/7/5 14:33
*/
@Setter
@Getter
public class OrgGroupListRes {
    /**
     * ID
     */
    private String groupId;

    /**
     * 群组名称
     */
    @ApiModelProperty(value = "群组名称")
    private String groupName;

    /**
     * 群组描述
     */
    @ApiModelProperty(value = "群组描述")
    private String groupDescription;

    /**
     * 域标识
     */
    @ApiModelProperty(value = "域标识")
    private String domainId;

    /**
     * 组类型 0:系统组  1:用户自定义组
     */
    @ApiModelProperty(value = "组类型 0:系统组  1:用户自定义组")
    private Short groupType;

    /**
     * 群组分类ID
     */
    @ApiModelProperty(value = "群组分类ID")
    private String groupClassId;

    /**
     * 群组分类名称
     */
    @ApiModelProperty(value = "群组分类名称")
    private String groupClassName;

    /**
     * 分组排序
     */
    @ApiModelProperty(value = "分组排序")
    private String groupOrder;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 简称
     */
    @ApiModelProperty(value = "简称")
    private String groupPinyin;

    /**
     * 组用户名称串
     */
    @ApiModelProperty(value = "组用户名称串")
    private String groupUserNames;

    /**
     * 组用户ID串
     */
    @ApiModelProperty(value = "组用户ID串")
    private String groupUserString;

    /**
     * 使用人范围名称
     */
    @ApiModelProperty(value = "使用人范围名称")
    private String rangeName;

    /**
     * 使用人ID
     */
    @ApiModelProperty(value = "使用人ID")
    private String rangeEmp;

    /**
     * 使用组织
     */
    @ApiModelProperty(value = "使用组织")
    private String rangeOrg;

    /**
     * 移动端 是否置顶（0：是、1：否）
     */
    @ApiModelProperty(value = "移动端 是否置顶（0：是、1：否）")
    private String isTop;

    @ApiModelProperty(value = "员工列表")
    List<EMPResp> employeeList;

    @ApiModelProperty(value = "使用范围")
    List<EMPResp> rangeEmployeeList;

}