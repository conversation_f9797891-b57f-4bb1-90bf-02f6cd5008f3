package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommPersonalFilter;


public interface CommPersonalFilterService {
	
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    新增筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午2:37:05
	 */
	int insert(CommPersonalFilter record);
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    修改筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午2:37:13
	 */
	int update(CommPersonalFilter record);
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    删除筛选器 
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午2:37:21
	 */
	int deleted(String id);
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    根据id查询筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午2:37:31
	 */
	CommPersonalFilter findById(String id);
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    复制筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午2:37:40
	 */
	int copy(CommPersonalFilter record);
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterService.java   
	 * @Package cn.trasen.sh.service   
	 * @Description:    获取当前用户的筛选器列表   
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:01:18
	 */
	public List<CommPersonalFilter> getList(CommPersonalFilter record);
}
