package cn.trasen.homs.base.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.trasen.BootComm.excel.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: <PERSON><PERSON>hu<PERSON>
 * @Description: 联系人
 * @Date: 2020/1/14 14:56
 * @Param:
 * @return:
 **/
@Table(name = "COMM_LINKMAN")
@Setter
@Getter
public class Linkman {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 联系人分类ID
     */
    @Column(name = "CLASS_ID")
    @ApiModelProperty(value = "联系人分类ID")
    private String classId;

    /**
     * 姓名
     */
    @Column(name = "LINKMAN_NAME")
    @ApiModelProperty(value = "姓名")
    @ExcelColumn(name = "员工姓名", index = 1, width = 20)
    private String linkmanName;

    /**
     * 英文姓名
     */
    @Column(name = "LINKMAN_ENNAME")
    @ApiModelProperty(value = "英文姓名")
    private String linkmanEnname;

    /**
     * 性别 1 男  2  女
     */
    @Column(name = "LINKMAN_SEX")
    @ApiModelProperty(value = "性别 1 男  2  女")
    private String linkmanSex;

    /**
     * 生日
     */
    @Column(name = "LINKMAN_BIRTH")
    @ApiModelProperty(value = "生日")
    private Date linkmanBirth;

    /**
     * 单位
     */
    @Column(name = "LINKMAN_UNIT")
    @ApiModelProperty(value = "单位")
    @ExcelColumn(name = "单位", index = 2, width = 20)
    private String linkmanUnit;

    /**
     * 部门
     */
    @Column(name = "LINKMAN_DEPART")
    @ApiModelProperty(value = "部门")
    @ExcelColumn(name = "部门", index = 7, width = 20)
    private String linkmanDepart;

    /**
     * 职业
     */
    @Column(name = "LINKMAN_PROFESSION")
    @ApiModelProperty(value = "职业")
    @ExcelColumn(name = "职业", index = 5, width = 20)
    private String linkmanProfession;

    /**
     * 职务
     */
    @Column(name = "LINKMAN_DUTY")
    @ApiModelProperty(value = "职务")
    @ExcelColumn(name = "职务", index = 6, width = 20)
    private String linkmanDuty;

    /**
     * 邮箱
     */
    @Column(name = "LINKMAN_EMAIL")
    @ApiModelProperty(value = "邮箱")
    @ExcelColumn(name = "电子邮件", index = 3, width = 30)
    private String linkmanEmail;

    /**
     * 网页地址
     */
    @Column(name = "LINKMAN_WEBURL")
    @ApiModelProperty(value = "网页地址")
    private String linkmanWeburl;

    /**
     * 电话
     */
    @Column(name = "BUSSINESS_PHONE")
    @ApiModelProperty(value = "电话")
    private String bussinessPhone;

    /**
     * 商务传真
     */
    @Column(name = "BUSSINESS_FAX")
    @ApiModelProperty(value = "商务传真")
    private String bussinessFax;

    /**
     * 固定电话
     */
    @Column(name = "FIXED_PHONE")
    @ApiModelProperty(value = "固定电话")
    private String fixedPhone;

    /**
     * 手机
     */
    @Column(name = "MOBILE_PHONE")
    @ApiModelProperty(value = "手机")
    @ExcelColumn(name = "手机", index = 4, width = 20)
    private String mobilePhone;

    /**
     * 国家
     */
    @Column(name = "LINKMAN_COUNTRY")
    @ApiModelProperty(value = "国家")
    private String linkmanCountry;

    /**
     * 省/州/区
     */
    @Column(name = "LINKMAN_STATE")
    @ApiModelProperty(value = "省/州/区")
    private String linkmanState;

    /**
     * 市/县
     */
    @Column(name = "LINKMAN_COUNTY")
    @ApiModelProperty(value = "市/县")
    private String linkmanCounty;

    /**
     * 地址
     */
    @Column(name = "LINKMAN_ADDRESS")
    @ApiModelProperty(value = "地址")
    private String linkmanAddress;

    /**
     * 邮政编码
     */
    @Column(name = "LINKMAN_POSTZIP")
    @ApiModelProperty(value = "邮政编码")
    private String linkmanPostzip;

    /**
     * 简介
     */
    @Column(name = "LINKMAN_DESCRIBE")
    @ApiModelProperty(value = "简介")
    private String linkmanDescribe;

    /**
     * 联系人类型
     */
    @Column(name = "LINKMAN_TYPE")
    @ApiModelProperty(value = "联系人类型")
    private Short linkmanType;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 查看权限
     */
    @Column(name = "VIEWSCOPE")
    @ApiModelProperty(value = "查看权限")
    private String viewscope;

    /**
     * 域标识
     */
    @Column(name = "DOMAIN_ID")
    @ApiModelProperty(value = "域标识")
    private String domainId;

    /**
     * 联系人分类名称
     */
    @Column(name = "CLASS_NAME")
    @ApiModelProperty(value = "联系人分类名称")
    @ExcelColumn(name = "联系人分类", index = 8, width = 20)
    private String className;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}