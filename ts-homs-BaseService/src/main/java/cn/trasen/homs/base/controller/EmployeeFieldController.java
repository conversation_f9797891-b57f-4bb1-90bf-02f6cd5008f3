/**
 * @Title: EmployeeFieldController.java  
 * @Package: cn.trasen.homs.base.controller  
 * @Date: 2021年6月16日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.service.CommEmployeeFieldService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.ApiOperation;

/**
* @ClassName: EmployeeFieldController  
 * @Author: 86189
 * @Date: 2021年6月16日
 */
@RestController
public class EmployeeFieldController {
	
	@Autowired
	private CommEmployeeFieldService commEmployeeFieldService;
	
	
	
	@ApiOperation(value = "根据id查询自定义人员档案字段", notes = "根据id查询自定义人员档案字段")
    @PostMapping("/employeeField/findById/{id}")
    public PlatformResult<CommEmployeeField> findById(@PathVariable String id) {
        try {
            return PlatformResult.success(commEmployeeFieldService.findById(id));
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "新增自定义人员档案字段", notes = "新增自定义人员档案字段")
    @PostMapping("/employeeField/insert")
    public PlatformResult<String> insert(@RequestBody CommEmployeeField record) {
        try {
        		
        	int count = commEmployeeFieldService.insert(record);
        	if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "修改自定义人员档案字段", notes = "修改自定义人员档案字段")
    @PostMapping("/employeeField/update")
    public PlatformResult<String> update(@RequestBody CommEmployeeField record) {
        try {
        	
        	int count = commEmployeeFieldService.update(record);
        	if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
        	
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据列表修改自定义人员档案字段", notes = "根据列表修改自定义人员档案字段")
    @PostMapping("/employeeField/updateList")
    public PlatformResult<CommEmployeeField> updateList(@RequestBody List<CommEmployeeField> records) {
        try {
        	commEmployeeFieldService.updateList(records);
            return PlatformResult.success();
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "获取人员档案列表头", notes = "获取人员档案列表头")
    @PostMapping("/employeeField/getEmployeeFieldTitel")
    public PlatformResult<List<CommEmployeeField>> getEmployeeFieldTitel() {
        try {
        	List<CommEmployeeField> list = commEmployeeFieldService.getEmployeeFieldTitel();
            return PlatformResult.success(list);
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "", notes = "")
    @GetMapping("/employeeField/create/{tableName}")
    public PlatformResult<String> create(@PathVariable String tableName) {
        try {
        	commEmployeeFieldService.create(tableName);
            return PlatformResult.success();
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
}
