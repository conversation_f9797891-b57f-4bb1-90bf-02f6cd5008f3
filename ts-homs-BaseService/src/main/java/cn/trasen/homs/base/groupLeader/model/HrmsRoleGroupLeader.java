package cn.trasen.homs.base.groupLeader.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


@Table(name = "hrms_role_group_leader")
@Setter
@Getter
public class HrmsRoleGroupLeader {

    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "ID")
    private String id;

    @Column(name = "usercode")
    @ApiModelProperty(value = "usercode")
    private String usercode;

    @Column(name = "username")
    @ApiModelProperty(value = "username")
    private String username;

    @Column(name = "group_code")
    @ApiModelProperty(value = "group_code")
    private String groupCode;

    @Column(name = "group_code_name")
    @ApiModelProperty(value = "group_code_name")
    private String groupCodeName;



    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;
}