package cn.trasen.homs.base.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.homs.base.bean.*;
import cn.trasen.homs.base.enums.JobtitleBasicGradeEnum;
import cn.trasen.homs.base.mapper.JobtitleBasicMapper;
import cn.trasen.homs.base.model.JobtitleBasic;
import cn.trasen.homs.base.model.JobtitleBasicUpgrade;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IJobtitleBasicService;
import cn.trasen.homs.base.service.IJobtitleBasicUpgradeService;
import cn.trasen.homs.base.service.IJobtitleInfoService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.UserInfoHolder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 10:30
 * @description
 */
@Service
public class JobtitleBasicService implements IJobtitleBasicService {

    @Autowired
    JobtitleBasicMapper jobtitleBasicMapper;


    @Autowired
    IJobtitleBasicUpgradeService jobtitleBasicUpgradeService;


    @Autowired
    IDictItemService dictItemService;


    @Autowired
    IJobtitleInfoService jobtitleInfoService;

    /**
     * @description: 获取职称信息树
     * @return: java.util.List<cn.trasen.BootComm.utils.TreeModel>
     * @author: liyuan
     * @createTime: 2021/8/7 10:32
     */
    @Override
    public List<TreeModel> getJobtitleTree() {


        Example example = new Example(JobtitleBasic.class);
        example.createCriteria()
            .andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            //根据当前登录账号机构编码过滤查询数据
//            .andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.orderBy("sortNo").asc();
        List<JobtitleBasic> jobtitleBasicList = jobtitleBasicMapper.selectByExample(example);
        List<TreeModel> trees = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(jobtitleBasicList)) {
            List<TreeModel> nodes = Lists.newLinkedList();
            jobtitleBasicList.forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getJobtitleBasicId());
                node.setName(item.getJobtitleBasicName());
                if (StringUtils.isBlank(item.getJobtitleBasicPid()) || "0".equals(item.getJobtitleBasicPid())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getJobtitleBasicPid());
                }
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }



    /**
    * 获取单个数据
    * @param jobtitleBasicListReq
    * @return cn.trasen.basicsbottom.bean.JobtitleBasicListResp
    * <AUTHOR>
    * @date 2021/11/1 17:27
    */
    @Override
    public JobtitleBasicListResp get(JobtitleBasicListReq jobtitleBasicListReq) {
        Example example = new Example(JobtitleBasic.class);
        Example.Criteria criteria = example.createCriteria();
        
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicName()) == false) {
            criteria.andEqualTo("jobtitleBasicName", jobtitleBasicListReq.getJobtitleBasicName());
        }

        if (StringUtils.isBlank(jobtitleBasicListReq.getEqJobtitleBasicPid()) == false) {
            criteria.andEqualTo("jobtitleBasicPid", jobtitleBasicListReq.getEqJobtitleBasicPid());
        }

        if (StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicId()) == false) {
            criteria.andEqualTo("jobtitleBasicId", jobtitleBasicListReq.getJobtitleBasicId());
        }

        example.setOrderByClause(" jobtitle_basic_id desc LIMIT 1 ");
        JobtitleBasic jobtitleBasic = jobtitleBasicMapper.selectOneByExample(example);
        JobtitleBasicListResp jobtitleBasicListResp = BeanUtil.copyProperties(jobtitleBasic, JobtitleBasicListResp.class);
        return jobtitleBasicListResp;
    }


    @Override
    /**
     * @description: 获取列表
     * @param: jobtitleBasicListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/7 11:32
     */
    public List<JobtitleBasicListResp> getList(JobtitleBasicListReq jobtitleBasicListReq) {

        Example example = new Example(JobtitleBasic.class);

        Example.Criteria criteria = example.createCriteria();

        example.setOrderByClause(" LENGTH(tree_ids) ASC ");
        example.orderBy("sortNo").asc().orderBy("createDate").desc();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
       // criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicName()) == false) {
            criteria.andLike("jobtitleBasicName", jobtitleBasicListReq.getJobtitleBasicName());
        }
        if (StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicPid()) == false) {
            // criteria.andLike("jobtitleBasicPid", jobtitleBasicListReq.getJobtitleBasicName());
            Example.Criteria criteriaParent = example.createCriteria();
            criteriaParent.orEqualTo("jobtitleBasicId", jobtitleBasicListReq.getJobtitleBasicPid()).orEqualTo("jobtitleBasicPid", jobtitleBasicListReq.getJobtitleBasicPid());
            example.and(criteriaParent);
        }
        if (StringUtils.isBlank(jobtitleBasicListReq.getEqJobtitleBasicPid()) == false) {
            criteria.andEqualTo("jobtitleBasicPid", jobtitleBasicListReq.getEqJobtitleBasicPid());
        }
        List<JobtitleBasic> jobtitleBasicList = jobtitleBasicMapper.selectByExample(example);


        List<JobtitleBasic> jobtitleBasicAllList = jobtitleBasicMapper.selectAll();


        List<JobtitleBasicListResp> jobtitleBasicListRespList = new ArrayList<>();
        for (JobtitleBasic jobtitleBasic : jobtitleBasicList) {
            JobtitleBasicListResp jobtitleBasicListResp = new JobtitleBasicListResp();
            BeanUtil.copyProperties(jobtitleBasic, jobtitleBasicListResp);
            jobtitleBasicListResp.setJobtitleBasicGradeLable(JobtitleBasicGradeEnum.getValByKey(jobtitleBasicListResp.getJobtitleBasicGrade()));
            jobtitleBasicListResp.setIsEnableLable(EnableEnum.getValByKey(jobtitleBasicListResp.getIsEnable()));
            fullData(jobtitleBasicListResp, jobtitleBasicAllList);
            jobtitleBasicListRespList.add(jobtitleBasicListResp);
        }
        return jobtitleBasicListRespList;
    }

    @Override
    /**
     * @description: 获取分页列表
     * @param: jobtitleBasicListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/7 11:32
     */
    public DataSet getPageList(JobtitleBasicListReq jobtitleBasicListReq, Page page) {

        Example example = new Example(JobtitleBasic.class);

        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause(" LENGTH(tree_ids) ASC ");
        example.orderBy("sortNo").asc().orderBy("createDate").desc();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
//        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (!StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicName())) {
            criteria.andLike("jobtitleBasicName", "%" + jobtitleBasicListReq.getJobtitleBasicName() + "%");
        }
        if (!StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicPid())) {
            // criteria.andLike("jobtitleBasicPid", jobtitleBasicListReq.getJobtitleBasicName());
            Example.Criteria criteriaParent = example.createCriteria();
            //根据当前登录账号机构编码过滤查询数据
//            criteriaParent.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            criteriaParent.orEqualTo("jobtitleBasicId", jobtitleBasicListReq.getJobtitleBasicPid()).orEqualTo("jobtitleBasicPid", jobtitleBasicListReq.getJobtitleBasicPid());
            example.and(criteriaParent);
        }

        List<JobtitleBasic> jobtitleBasicList = jobtitleBasicMapper.selectByExampleAndRowBounds(example, page);


        List<JobtitleBasic> jobtitleBasicAllList = jobtitleBasicMapper.selectAll();


        List<JobtitleBasicListResp> jobtitleBasicListRespList = new ArrayList<>();

        Map<String, String> educationTypeDict = dictItemService.convertDictMap("education_type");


        for (JobtitleBasic jobtitleBasic : jobtitleBasicList) {
            JobtitleBasicListResp jobtitleBasicListResp = new JobtitleBasicListResp();
            BeanUtil.copyProperties(jobtitleBasic, jobtitleBasicListResp);
            jobtitleBasicListResp.setJobtitleBasicGradeLable(JobtitleBasicGradeEnum.getValByKey(jobtitleBasicListResp.getJobtitleBasicGrade()));
            jobtitleBasicListResp.setIsEnableLable(EnableEnum.getValByKey(jobtitleBasicListResp.getIsEnable()));
            fullData(jobtitleBasicListResp, jobtitleBasicAllList);


            if (!jobtitleBasic.getJobtitleBasicGrade().equals(3)) {
                jobtitleBasicListResp.setJobtitleBasicName(null);

            }

            if (jobtitleBasicListResp.getJobtitleBasicGrade().equals(JobtitleBasicGradeEnum.NAME.getKey())) {

                List<JobtitleBasicUpgrade> jobtitleBasicUpgradeList = jobtitleBasicUpgradeService.getBaseList(jobtitleBasicListResp.getJobtitleBasicId());

                List<JobtitleBasicUpgradeListResp> jobtitleBasicUpgradeListRespList = new ArrayList<>();

                for (JobtitleBasicUpgrade jobtitleBasicUpgrade : jobtitleBasicUpgradeList) {

                    JobtitleBasicUpgradeListResp jobtitleBasicUpgradeListResp = new JobtitleBasicUpgradeListResp();

                    BeanUtil.copyProperties(jobtitleBasicUpgrade, jobtitleBasicUpgradeListResp);

                    for (JobtitleBasic j : jobtitleBasicAllList) {
                        if (j.getJobtitleBasicId().equals(jobtitleBasicUpgradeListResp.getUpgradeJobtitleBasicId())) {
                            jobtitleBasicUpgradeListResp.setUpgradeJobtitleBasicLable(j.getJobtitleBasicName());
                            break;
                        }
                    }
                    jobtitleBasicUpgradeListResp.setEducationTypeCodeLable(educationTypeDict.get(jobtitleBasicUpgradeListResp.getEducationTypeCode()));


                    jobtitleBasicUpgradeListRespList.add(jobtitleBasicUpgradeListResp);

                }

                jobtitleBasicListResp.setJobtitleBasicUpgradeListRespList(jobtitleBasicUpgradeListRespList);


            }
            jobtitleBasicListRespList.add(jobtitleBasicListResp);
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), jobtitleBasicListRespList);
    }


    private void fullData(JobtitleBasicListResp jobtitleBasicListResp, List<JobtitleBasic> jobtitleBasicList) {
        String[] treesArr = jobtitleBasicListResp.getTreeIds().split(",");

        for (JobtitleBasic jobtitleBasic : jobtitleBasicList) {
            if (treesArr.length > 0) {
                if (jobtitleBasic.getJobtitleBasicId().equals(treesArr[0])) {
                    jobtitleBasicListResp.setClassName(jobtitleBasic.getJobtitleBasicName());
                    jobtitleBasicListResp.setClassId(treesArr[0]);
                }
            }
            if (treesArr.length > 1) {
                if (jobtitleBasic.getJobtitleBasicId().equals(treesArr[1])) {
                    jobtitleBasicListResp.setLevelName(jobtitleBasic.getJobtitleBasicName());
                    jobtitleBasicListResp.setLevelId(treesArr[1]);

                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 新增
     * @param: jobtitleBasicSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/7 13:14
     */
    public void add(JobtitleBasicSaveReq jobtitleBasicSaveReq) {
        JobtitleBasic jobtitleBasic = new JobtitleBasic();
        jobtitleBasic.setCreateDate(new Date());
        jobtitleBasic.setUpdateDate(jobtitleBasic.getCreateDate());
        jobtitleBasic.setJobtitleBasicId(String.valueOf(IdWork.id.nextId()));
        jobtitleBasic.setCreateUser(UserInfoHolder.getCurrentUserCode());
        jobtitleBasic.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        jobtitleBasic.setIsDeleted(Contants.IS_DELETED_FALSE);
        jobtitleBasic.setIsEnable(EnableEnum.Y.getKey());
        jobtitleBasic.setJobtitleBasicName(jobtitleBasicSaveReq.getJobtitleBasicName());
        jobtitleBasic.setRemark(jobtitleBasicSaveReq.getRemark());
        jobtitleBasic.setSortNo(jobtitleBasicSaveReq.getSortNo());
        if (StringUtils.isBlank(jobtitleBasicSaveReq.getLevelId()) == false) {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.NAME.getKey());
            jobtitleBasic.setTreeIds(jobtitleBasicSaveReq.getClassId() + "," + jobtitleBasicSaveReq.getLevelId() + "," + jobtitleBasic.getJobtitleBasicId());
            jobtitleBasic.setJobtitleBasicPid(jobtitleBasicSaveReq.getLevelId());
            jobtitleBasicUpgradeService.save(jobtitleBasic.getJobtitleBasicId(), jobtitleBasicSaveReq.getJobtitleBasicUpgradeList());

        } else if (StringUtils.isBlank(jobtitleBasicSaveReq.getClassId()) == false) {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.LEVEL.getKey());
            jobtitleBasic.setTreeIds(jobtitleBasicSaveReq.getClassId() + "," + jobtitleBasic.getJobtitleBasicId());
            jobtitleBasic.setJobtitleBasicPid(jobtitleBasicSaveReq.getClassId());
        } else {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.CLASS.getKey());

            jobtitleBasic.setTreeIds(jobtitleBasic.getJobtitleBasicId());

            jobtitleBasic.setJobtitleBasicPid("0");

        }
        jobtitleBasic.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        jobtitleBasicMapper.insertSelective(jobtitleBasic);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 修改
     * @param: jobtitleBasicSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/7 13:14
     */
    public void update(JobtitleBasicSaveReq jobtitleBasicSaveReq) {
        JobtitleBasic jobtitleBasic = new JobtitleBasic();
        jobtitleBasic.setUpdateDate(jobtitleBasic.getCreateDate());
        jobtitleBasic.setJobtitleBasicId(jobtitleBasicSaveReq.getJobtitleBasicId());
        jobtitleBasic.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        jobtitleBasic.setJobtitleBasicName(jobtitleBasicSaveReq.getJobtitleBasicName());
        jobtitleBasic.setJobtitleBasicId(jobtitleBasicSaveReq.getJobtitleBasicId());
        jobtitleBasic.setRemark(jobtitleBasicSaveReq.getRemark());
        jobtitleBasic.setSortNo(jobtitleBasicSaveReq.getSortNo());
        if (StringUtils.isBlank(jobtitleBasicSaveReq.getLevelId()) == false) {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.NAME.getKey());
            jobtitleBasic.setTreeIds(jobtitleBasicSaveReq.getClassId() + "," + jobtitleBasicSaveReq.getLevelId() + "," + jobtitleBasic.getJobtitleBasicId());
            jobtitleBasic.setJobtitleBasicPid(jobtitleBasicSaveReq.getLevelId());
            jobtitleBasic.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            jobtitleBasicUpgradeService.save(jobtitleBasic.getJobtitleBasicId(), jobtitleBasicSaveReq.getJobtitleBasicUpgradeList());
        } else if (StringUtils.isBlank(jobtitleBasicSaveReq.getClassId()) == false) {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.LEVEL.getKey());
            jobtitleBasic.setTreeIds(jobtitleBasicSaveReq.getClassId() + "," + jobtitleBasic.getJobtitleBasicId());
            jobtitleBasic.setJobtitleBasicPid(jobtitleBasicSaveReq.getClassId());
        } else {
            jobtitleBasic.setJobtitleBasicGrade(JobtitleBasicGradeEnum.CLASS.getKey());

            jobtitleBasic.setTreeIds(jobtitleBasic.getJobtitleBasicId());

            jobtitleBasic.setJobtitleBasicPid("0");

        }
        jobtitleBasicMapper.updateByPrimaryKeySelective(jobtitleBasic);
    }


    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<JobtitleBasicSaveReq> jobtitleBasicSaveReqList) {
        for (JobtitleBasicSaveReq jobtitleBasicSaveReq : jobtitleBasicSaveReqList) {
            JobtitleBasic jobtitleBasicUpdate = new JobtitleBasic();
            jobtitleBasicUpdate.setJobtitleBasicId(jobtitleBasicSaveReq.getJobtitleBasicId());
            jobtitleBasicUpdate.setUpdateDate(new Date());
            jobtitleBasicUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            jobtitleBasicUpdate.setSortNo(jobtitleBasicSaveReq.getSortNo());
            jobtitleBasicMapper.updateByPrimaryKeySelective(jobtitleBasicUpdate);
        }
    }


    @Override
    /**
     * @description: 删除等级
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        verify(id);
        JobtitleBasic jobtitleBasicUpdate = new JobtitleBasic();
        jobtitleBasicUpdate.setJobtitleBasicId(id);
        jobtitleBasicUpdate.setIsDeleted(Contants.IS_DELETED_TURE);
        jobtitleBasicUpdate.setUpdateDate(new Date());
        jobtitleBasicUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        jobtitleBasicMapper.updateByPrimaryKeySelective(jobtitleBasicUpdate);
    }


    @Override
    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id, String enable) {

        if (enable.equals(EnableEnum.N.getKey())) {
            verify(id);
        }

        JobtitleBasic jobtitleBasicUpdate = new JobtitleBasic();
        jobtitleBasicUpdate.setJobtitleBasicId(id);
        jobtitleBasicUpdate.setIsEnable(enable);
        jobtitleBasicUpdate.setUpdateDate(new Date());
        jobtitleBasicUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        jobtitleBasicMapper.updateByPrimaryKeySelective(jobtitleBasicUpdate);
    }


    /**
     * @description: 验证
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 11:17
     */
    @Override
    public void verify(String id) {
        JobtitleBasic jobtitleBasic = jobtitleBasicMapper.selectByPrimaryKey(id);
        if (jobtitleBasic == null) {
            throw new BusinessException("数据不存在！");
        }

        if (jobtitleBasic.getJobtitleBasicGrade().equals(JobtitleBasicGradeEnum.NAME.getKey())) {
            JobtitleInfoReq jobtitleInfoReq = new JobtitleInfoReq();
            jobtitleInfoReq.setJobtitleName(id);
            if (jobtitleInfoService.exists(jobtitleInfoReq)) {
                throw new BusinessException("职称已被使用，不能禁用和删除！");
            }
        } else {

            Example example = new Example(JobtitleBasic.class);

            Example.Criteria criteria = example.createCriteria();

            criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            criteria.andEqualTo("jobtitleBasicPid", id);
            if (jobtitleBasicMapper.selectCountByExample(example) > 0) {
                throw new BusinessException("职称已被使用，不能禁用和删除！");

            }
        }
    }

    /**
     * @Title: getCategoryPageList
     * @Description: 获取职称类别
     * @Params: @param jobtitleBasicListReq
     * @Params: @param page
     * @Params: @return
     * @Return: DataSet
     * <AUTHOR>
     * @date:2021年8月20日
     * @Throws
     */
    public DataSet getCategoryPageList(JobtitleBasicListReq jobtitleBasicListReq, Page page) {

        Example example = new Example(JobtitleBasic.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
       // criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        if(!StringUtils.isEmpty(jobtitleBasicListReq.getJobtitleBasicPid())) {
            criteria.andEqualTo("jobtitleBasicPid",jobtitleBasicListReq.getJobtitleBasicPid());
        }
        
        if(!StringUtils.isEmpty(jobtitleBasicListReq.getJobtitleBasicGrade())) {
            criteria.andEqualTo("jobtitleBasicGrade",jobtitleBasicListReq.getJobtitleBasicGrade());
        }

        if (StringUtils.isBlank(jobtitleBasicListReq.getJobtitleBasicName()) == false) {
            criteria.andLike("jobtitleBasicName","%"+ jobtitleBasicListReq.getJobtitleBasicName()+"%");
        }


        List<JobtitleBasic> jobtitleBasicList = jobtitleBasicMapper.selectByExampleAndRowBounds(example, page);
        

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), jobtitleBasicList);
    }
    
    public DataSet getCategoryPageList2(JobtitleBasicListReq jobtitleBasicListReq, Page page) {

        //根据当前登录账号机构编码过滤查询数据
        jobtitleBasicListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        
        List<JobtitleBasic> jobtitleBasicList = jobtitleBasicMapper.getCategoryPageList(jobtitleBasicListReq,page);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), jobtitleBasicList);
  }
}