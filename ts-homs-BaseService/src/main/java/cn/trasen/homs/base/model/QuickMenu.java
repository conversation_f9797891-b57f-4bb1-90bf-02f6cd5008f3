package cn.trasen.homs.base.model;


import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_quick_menu")
@Setter
@Getter
public class QuickMenu {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 菜单地址
     */
    @Column(name = "menu_url")
    @ApiModelProperty(value = "菜单地址")
    private String menuUrl;

    /**
     * 菜单名称
     */
    @Column(name = "menu_name")
    @ApiModelProperty(value = "菜单名称")
    private String menuName;
    
    /**
     * 菜单图标
     */
    @Column(name = "menu_icon")
    @ApiModelProperty(value = "菜单图标")
    private String menuIcon;

    /**
     * 创建人名称
     */
    @Column(name = "create_name")
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建人编码
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人编码")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 排序序号
     */
    @ApiModelProperty(value = "排序序号")
    private Integer sord;
    
    
    @Column(name = "is_default")
    @ApiModelProperty(value = "是否默认")
    private String isDefault;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}