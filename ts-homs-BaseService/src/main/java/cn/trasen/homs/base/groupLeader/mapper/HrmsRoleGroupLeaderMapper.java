package cn.trasen.homs.base.groupLeader.mapper;

import java.util.List;

import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader;
import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeaderBase;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsRoleGroupLeaderMapper extends Mapper<HrmsRoleGroupLeader> {

    List<HrmsRoleGroupLeaderBase> getRolegroupleaderAllList();

    List<HrmsRoleGroupLeader> getDataSetList(HrmsRoleGroupLeader record, Page page);
}