package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @createTime 2021/8/19 9:12
 * @description
 */
@Data
public class PositionSaveReq {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String positionId;


    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer serialNumber;
}
