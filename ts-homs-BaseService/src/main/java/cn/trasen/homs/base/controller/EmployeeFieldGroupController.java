/**
 * @Title: EmployeeFieldGroupController.java  
 * @Package: cn.trasen.homs.base.controller  
 * @Date: 2021��6��15��
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.controller;

import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.service.CommEmployeeFieldGroupService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* @ClassName: EmployeeFieldGroupController  
 * @Author: 86189
 * @Date: 2021��6��15��
 */
@Slf4j
@RestController
public class EmployeeFieldGroupController {
	
	@Autowired
	private CommEmployeeFieldGroupService commEmployeeFieldGroupService;
	
	@ApiOperation(value = "根据id查询自定义人员档案分组", notes = "根据id查询自定义人员档案分组")
    @PostMapping("/employeeFieldGroup/findById/{id}")
    public PlatformResult<CommEmployeeFieldGroup> findById(@PathVariable String id) {
        try {
            return PlatformResult.success(commEmployeeFieldGroupService.findById(id));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        	return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "新增自定义人员档案分组", notes = "新增自定义人员档案分组")
    @PostMapping("/employeeFieldGroup/insert")
    public PlatformResult<String> insert(@RequestBody CommEmployeeFieldGroup record) {
        try {
        	
        	boolean flag = commEmployeeFieldGroupService.checkName(record);
        	
        	if(flag) {
        		
        		int count = commEmployeeFieldGroupService.insert(record);
            	if(count>0) {
            		return PlatformResult.success();
            	}else {
            		return PlatformResult.failure();	
            	}
        	}else {
        		return PlatformResult.failure("分组名称已存在,请修改");	
        	}
        	
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "修改自定义人员档案分组", notes = "修改自定义人员档案分组")
    @PostMapping("/employeeFieldGroup/update")
    public PlatformResult<String> update(@RequestBody CommEmployeeFieldGroup record) {
        try {
        	
        	int count = commEmployeeFieldGroupService.update(record);
        	if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
        	
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据列表修改自定义人员档案分组", notes = "根据列表修改自定义人员档案分组")
    @PostMapping("/employeeFieldGroup/updateList")
    public PlatformResult<CommEmployeeFieldGroup> updateList(@RequestBody List<CommEmployeeFieldGroup> records) {
        try {
        	commEmployeeFieldGroupService.updateList(records);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "获取字段列表分组及分组明细", notes = "获取字段分组列表及分组明细")
    @PostMapping("/employeeField/getList")
    public PlatformResult<List<CommEmployeeFieldGroup>> getList() {
        try {
        	List<CommEmployeeFieldGroup> list = commEmployeeFieldGroupService.getFieldAndJurisdictionListByGroupid();
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据id删除自定义人员档案分组", notes = "根据id删除自定义人员档案分组")
    @PostMapping("/employeeFieldGroup/deleteById/{id}")
    public PlatformResult<CommEmployeeFieldGroup> deleteById(@PathVariable String id) {
        try {
        	commEmployeeFieldGroupService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    
    @ApiOperation(value = "获取字段列表分组及分组明细", notes = "获取字段分组列表及分组明细")
    @PostMapping("/employeeField/getDataList")
    public PlatformResult<List<CommEmployeeFieldGroup>> getDataList() {
        try {
        	List<CommEmployeeFieldGroup> list = commEmployeeFieldGroupService.getDataList();
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        	return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取字段分组必填字段不为空列表", notes = "获取字段分组必填字段不为空列表")
    @PostMapping("/employeeField/getGroupDataList")
    public PlatformResult<List<CommEmployeeFieldGroup>> getGroupDataList() {
        try {
            List<CommEmployeeFieldGroup> list = commEmployeeFieldGroupService.getGroupDataList();
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
