package cn.trasen.homs.base.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.OrgGroupClass;
import cn.trasen.homs.base.service.OrgGroupClassService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 自定义群组类型Controller层
 * @Date: 2020/1/13 18:32
 * @Author: Liz<PERSON>huo
 * @Company: 湖南创星
 */
@Api(tags = "自定义群组类型Controller")
@RestController
public class OrgGroupClassController {

    private static final Logger logger = LoggerFactory.getLogger(OrgGroupClassController.class);

    @Autowired
    private OrgGroupClassService orgGroupClassService;

    /**
     * @Author: Lizhihuo
     * @Description: 查询自定义群组类型列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    @ApiOperation(value = "自定义群组类型列表", notes = "自定义群组类型列表")
    @PostMapping("/employee/orgGroupClass/list")
    public DataSet<OrgGroupClass> getDataList(Page page, OrgGroupClass record) {
	    try {
    		List<OrgGroupClass> list = orgGroupClassService.getDataList(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增自定义群组类型
     * @Date: 2020/1/13 8:41
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "新增自自定义群组类型", notes = "新增自定义群组类型")
    @PostMapping("/employee/orgGroupClass/save")
    public PlatformResult<String> insert(@RequestBody OrgGroupClass record) {
        try {
            orgGroupClassService.insert(record);
            return PlatformResult.success();
        } catch (RuntimeException e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }

    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组类型
     * @Date: 2020/1/13 9:23
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "修改自定义群组类型", notes = "修改自定义群组类型")
    @PostMapping("/employee/orgGroupClass/update")
    public PlatformResult<String> update(@RequestBody OrgGroupClass record) {
        try {
            orgGroupClassService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    
    @ApiOperation(value = "修改自定义群组类型", notes = "修改自定义群组类型")
    @PostMapping("/employee/orgGroupClass/updateSort")
    public PlatformResult<String> updateSort(@RequestBody List<OrgGroupClass> record) {
        try {
            orgGroupClassService.updateSort(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组
     * @Date: 2020/1/13 10:22
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "删除自定义群组类型", notes = "删除自定义群组类型")
    @PostMapping("/employee/orgGroupClass/deletedById")
    public PlatformResult<String> deleteById(@RequestBody OrgGroupClass record) {
        try {
            orgGroupClassService.deleted(record.getId());
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

}
