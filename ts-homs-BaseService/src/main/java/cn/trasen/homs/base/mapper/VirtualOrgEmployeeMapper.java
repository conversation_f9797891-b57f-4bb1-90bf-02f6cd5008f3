package cn.trasen.homs.base.mapper;

import java.util.List;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeListReq;
import cn.trasen.homs.base.model.VirtualOrgEmployee;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface VirtualOrgEmployeeMapper extends Mapper<VirtualOrgEmployee> {


    List<HrmsEmployeeResp> getPageList(Page page, VirtualOrgEmployeeListReq entity);

    List<HrmsEmployeeResp> getPageList(VirtualOrgEmployeeListReq entity);

}