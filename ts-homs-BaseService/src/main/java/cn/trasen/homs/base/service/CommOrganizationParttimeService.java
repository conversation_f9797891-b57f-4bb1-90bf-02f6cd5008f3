package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommOrganizationParttime;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommOrganizationParttimeService
 * @Description TODO
 * @date 2023��8��26�� ����11:41:09
 * <AUTHOR>
 * @version 1.0
 */
public interface CommOrganizationParttimeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	void save(CommOrganizationParttime record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	Integer update(CommOrganizationParttime record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommOrganizationParttime
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	CommOrganizationParttime selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommOrganizationParttime>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	DataSet<CommOrganizationParttime> getDataSetList(Page page, CommOrganizationParttime record);

	/**
	 * 
	 * @MethodName: deleteByEmployeeId
	 * @Description: TODO
	 * <AUTHOR>
	 * @param employeeId void
	 * @date 2023-08-26 02:07:02
	 */
	void deleteByEmployeeId(String employeeId);

	/**
	 * 
	 * @MethodName: getList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<CommOrganizationParttime>
	 * @date 2023-08-26 02:18:00
	 */
	List<CommOrganizationParttime> getList(CommOrganizationParttime record);

	/**
	 * 
	 * @MethodName: chooseOrganizationParttime
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-08-26 02:57:49
	 */
	void chooseOrganizationParttime(CommOrganizationParttime record);

}
