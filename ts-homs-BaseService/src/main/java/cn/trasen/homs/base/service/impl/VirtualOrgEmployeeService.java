package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeImport;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeListReq;
import cn.trasen.homs.base.mapper.VirtualOrgEmployeeMapper;
import cn.trasen.homs.base.model.VirtualOrgEmployee;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IVirtualOrgEmployeeService;
import cn.trasen.homs.base.service.IVirtualOrgService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/7/27 17:11
 * @description
 */

@Service
public class VirtualOrgEmployeeService implements IVirtualOrgEmployeeService {


    @Autowired
    VirtualOrgEmployeeMapper virtualOrgEmployeeMapper;

    @Autowired
    HrmsEmployeeService hrmsEmployeeService;

    @Autowired
    IVirtualOrgService virtualOrgService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    /**
     * @description: 保存员工信息  后期优化批量写入
     * @param: orgId
     * @param: leaders
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 16:59
     */
    public void saveEmployee(String orgId, List<String> employeeIds) {



        if (employeeIds != null) {
            Example example = new Example(VirtualOrgEmployee.class);
            example.createCriteria().andEqualTo("virtualOrgId", orgId);
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            virtualOrgEmployeeMapper.deleteByExample(example);
            for (String id : employeeIds) {
                VirtualOrgEmployee bean = BeanUtils.InitBean(VirtualOrgEmployee.class);
                bean.setVirtualOrgId(orgId);
                bean.setEmployeeId(id);
                bean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                virtualOrgEmployeeMapper.insertSelective(bean);
            }
        }
    }

    @Override
    /** 
    * @description: 删除
* @param: orgId
* @param: employeeId
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/4 16:24
    */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEmployee(String orgId,String employeeId) {

        VirtualOrgEmployee virtualOrgEmployeeUpdate=new VirtualOrgEmployee();
        virtualOrgEmployeeUpdate.setIsDeleted(Contants.IS_DELETED_TURE);
        Example example = new Example(VirtualOrgEmployee.class);
        example.createCriteria()
                .andEqualTo("virtualOrgId", orgId)
                .andEqualTo("employeeId", employeeId);


        virtualOrgEmployeeMapper.updateByExampleSelective(virtualOrgEmployeeUpdate,example);

    }


    @Override
    /**
     * @description: 获取列表
     * @param: orgId
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationLeaderResp>
     * @author: liyuan
     * @createTime: 2021/7/28 17:05
     */
    public DataSet<HrmsEmployeeResp> getPageList(Page page, VirtualOrgEmployeeListReq virtualOrgEmployeeListReq) {

        if (virtualOrgEmployeeListReq != null) {
            if (virtualOrgEmployeeListReq.getVirtualId() != null) {
                virtualOrgEmployeeListReq.setVirtualIdList(virtualOrgService.getNodeIds(virtualOrgEmployeeListReq.getVirtualId()));
            }
        }

        virtualOrgEmployeeListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsEmployeeResp> getPageList = virtualOrgEmployeeMapper.getPageList(page, virtualOrgEmployeeListReq);
        for (HrmsEmployeeResp hrmsEmployeeResp : getPageList) {
            hrmsEmployeeService.fullEmployeeData(hrmsEmployeeResp);
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), getPageList);
    }


    @Override
    /**
     * @description: 获取列表
     * @param: orgId
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationLeaderResp>
     * @author: liyuan
     * @createTime: 2021/7/28 17:05
     */
    public List<HrmsEmployeeResp> getList(VirtualOrgEmployeeListReq virtualOrgEmployeeListReq) {
        virtualOrgEmployeeListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsEmployeeResp> getPageList = virtualOrgEmployeeMapper.getPageList(virtualOrgEmployeeListReq);
        for (HrmsEmployeeResp hrmsEmployeeResp : getPageList) {
            hrmsEmployeeService.fullEmployeeData(hrmsEmployeeResp);
        }
        return getPageList;
    }


    @Override
    /** 
    * @description: 导入
* @param: virtualOrgEmployeeImportList
* @param: orgId
* @param: type
    * @return: cn.trasen.BootComm.utils.PlatformResult
    * @author: liyuan
    * @createTime: 2021/8/3 16:39
    */
    @Transactional(rollbackFor = Exception.class)
    public PlatformResult excelImportEmployee(List<VirtualOrgEmployeeImport> virtualOrgEmployeeImportList, String orgId, String type) {
        List<String> errorList = new ArrayList<>();
        List<String> employeeIds = new ArrayList<>();
      //  Set<String> employeeNos = new HashSet<>();
        Integer i = 0;
        for (VirtualOrgEmployeeImport virtualOrgEmployeeImport : virtualOrgEmployeeImportList) {
            i++;
            if (StringUtils.isBlank(virtualOrgEmployeeImport.getEmployeeNo())) {
                errorList.add("错误信息：第" + i + "行工号不能为空！");

                continue;
            }

          //  employeeNos.add(virtualOrgEmployeeImport.getEmployeeNo());
        }
         i = 0;
        for (VirtualOrgEmployeeImport virtualOrgEmployeeImport : virtualOrgEmployeeImportList) {
            employeeIds.add(virtualOrgEmployeeImport.getEmployeeNo());
            i++;
            EmployeeListReq record = new EmployeeListReq();
            record.setEmployeeNo(virtualOrgEmployeeImport.getEmployeeNo());
            if (hrmsEmployeeService.getEmployeeBaseList(record).size() < 1) {
                errorList.add("错误信息：第" + i + "行工号不存在");
                continue;
            }

            if (type.equals("2")==false) {
                Example example = new Example(VirtualOrgEmployee.class);
                example.createCriteria()
                        .andEqualTo("employeeId", virtualOrgEmployeeImport.getEmployeeNo())
                        .andEqualTo("virtualOrgId", orgId)
                ;
                if (virtualOrgEmployeeMapper.selectByExample(example).size() > 0) {
                    errorList.add("错误信息：第" + i + "行工号重复");
                    continue;
                }
            }
        }
        if (errorList.size() < 1) {
            if (type.equals("2")) {
                saveEmployee(orgId, employeeIds);
            }
            else
            {
                for (String id : employeeIds) {
                    VirtualOrgEmployee bean = BeanUtils.InitBean(VirtualOrgEmployee.class);
                    bean.setVirtualOrgId(orgId);
                    bean.setEmployeeId(id);
                    bean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    virtualOrgEmployeeMapper.insertSelective(bean);
                }
            }
            return PlatformResult.success("", "导入数量：" + virtualOrgEmployeeImportList.size());

        } else {


            String error="";
            for (String s:errorList)
            {
                error=error+s+"</br>";
            }
            return PlatformResult.failure(error);

        }
      //  return PlatformResult.success(virtualOrgEmployeeImportList.size());
    }
}