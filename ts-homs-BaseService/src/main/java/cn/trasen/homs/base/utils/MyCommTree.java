package cn.trasen.homs.base.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import cn.trasen.homs.core.model.MyTreeModel;



/**
 *  Tree模型  
 * <AUTHOR>
 *
 */
public class MyCommTree {
	
	 public List<MyTreeModel> commObject;

	    public List<MyTreeModel> treelist = new ArrayList<MyTreeModel>();

	    public List<MyTreeModel> CommTreeList(List<MyTreeModel> objs) {
	        this.commObject = objs;
	        MyTreeModel treeNode = null;
	        for (MyTreeModel x : objs) {

	            treeNode = new MyTreeModel();
	            if (("".equals(x.getPid())) || ("0".equals(x.getPid())) || ("root".equals(x.getPid().toLowerCase()))
	                    || ("/".equals(x.getPid().toLowerCase()))) {
	                treeNode.setId(x.getId());
	                treeNode.setPid(x.getPid());
	                treeNode.setName(x.getName());
	                treeNode.setOpen(true);
	                treeNode.setParent(true);
	                treeNode.setIcon(x.getIcon());
	                treeNode.setChildren(getChildTree(x.getId()));
	                treeNode.setAliasname(x.getAliasname());
	                treeNode.setCode(x.getCode());
	                treeNode.setUserData(x.getUserData());
	                treeNode.setSort(x.getSort());
	                treeNode.setIsEnable(x.getIsEnable());
	                treeNode.setpNumber(x.getpNumber());
	                treeNode.setSsoOrgCode(x.getSsoOrgCode());
	                treelist.add(treeNode);
	            }
	        }
	        return treelist;
	    }

	    public List<MyTreeModel> CommTreeList(List<MyTreeModel> objs, String Root) {
	        this.commObject = objs;
	        MyTreeModel treeNode = null;
	        for (MyTreeModel x : objs) {
	            treeNode = new MyTreeModel();
	            if (("".equals(x.getPid())) || ("0".equals(x.getPid())) || ("root".equals(x.getPid().toLowerCase()))
	                    || ("/".equals(x.getPid().toLowerCase())) || Root.toLowerCase().equals(x.getPid().toLowerCase())) {
	                treeNode.setId(x.getId());
	                treeNode.setPid(x.getPid());
	                treeNode.setName(x.getName());
	                treeNode.setOpen(true);
	                treeNode.setParent(true);
	                treeNode.setIcon(x.getIcon());
	                treeNode.setChildren(getChildTree(x.getId()));
	                treeNode.setAliasname(x.getAliasname());
	                treeNode.setCode(x.getCode());
	                treeNode.setUserData(x.getUserData());
	                treeNode.setSort(x.getSort());
	                treeNode.setpNumber(x.getpNumber());
	                treeNode.setSsoOrgCode(x.getSsoOrgCode());
	                treeNode.setIsEnable(x.getIsEnable());

	                treelist.add(treeNode);
	            }
	        }
	        return treelist;
	    }

	    private List<MyTreeModel> getChildTree(String pid) {
	        List<MyTreeModel> subList = new ArrayList<MyTreeModel>();
	        MyTreeModel childNode = null;
	        for (MyTreeModel a : commObject) {
	            childNode = new MyTreeModel();
	            if (pid != null && pid.equals(a.getPid())) {
	                childNode.setId(a.getId());
	                childNode.setPid(a.getPid());
	                childNode.setName(a.getName());
	                childNode.setParent(false);
	                childNode.setIcon(a.getIcon());
	                childNode.setChildren(getChildTree(a.getId()));
	                //System.out.println("瀛楄妭鐐癸細a.id:==="+a.getId()+" a.Pid="+a.getPid());
	                childNode.setAliasname(a.getAliasname());
	                childNode.setCode(a.getCode());
	                childNode.setUserData(a.getUserData());
	                childNode.setSort(a.getSort());
	                childNode.setpNumber(a.getpNumber());
	                childNode.setSsoOrgCode(a.getSsoOrgCode());
	                childNode.setIsEnable(a.getIsEnable());
	                subList.add(childNode);
	            }
	        }
	        // 濡傛灉涓虹┖鍒欒繑鍥瀗ull
	        if (CollectionUtils.isNotEmpty(subList)) {
	            return subList;
	        }
	        return null;
	    }
}
