package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.mapper.LinkmanclassMapper;
import cn.trasen.homs.base.model.Linkmanclass;
import cn.trasen.homs.base.service.LinkmanclassService;
import cn.trasen.homs.base.utils.Utils;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 联系人类型Service实现层
 * @Date: 2020/1/13 18:23
 * @Author: Liz<PERSON>huo
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class LinkmanclassServiceImpl implements LinkmanclassService {

    @Autowired
    private LinkmanclassMapper linkmanclassMapper;

    /**
     * @Author: Lizhihuo
     * @Description: 查询联系人类型列表
     * @Date: 2020/1/13 20:09
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.Linkmanclass>
     **/
    @Override
    public List<Linkmanclass> getDataList(Page page, Linkmanclass linkmanclass) {
        Example example = new Example(Linkmanclass.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isNoneBlank(linkmanclass.getClassName())) {
            example.and().andLike("className", "%" + linkmanclass.getClassName() + "%");
        }
        return linkmanclassMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增联系人类型
     * @Date: 2020/1/13 20:09
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(Linkmanclass linkmanclass) {


        Example example = new Example(Linkmanclass.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("className", linkmanclass.getClassName());

        int count = linkmanclassMapper.selectCountByExample(example);
        if (count>0) {
            throw new RuntimeException("名字不能相同！");
        }


        linkmanclass.setClassId(String.valueOf(IdWork.id.nextId()));
        linkmanclass.setEmpId(UserInfoHolder.getCurrentUserCode());//创建人ID
        linkmanclass.setIsDeleted(Contants.IS_DELETED_FALSE);
        linkmanclass.setClassType(Utils.STATUS_ENABLED);//个人联系人
        linkmanclass.setDomainId(String.valueOf(Utils.STATUS_ENABLED));
        linkmanclass.setCreateDate(new Date());
        linkmanclass.setCreateUser(UserInfoHolder.getCurrentUserCode());
        linkmanclass.setCreateUserName(UserInfoHolder.getCurrentUserName());
        //部门\部门名称
        linkmanclass.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        linkmanclass.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        linkmanclass.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
        linkmanclass.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return linkmanclassMapper.insertSelective(linkmanclass);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改联系人类型
     * @Date: 2020/1/13 20:10
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(Linkmanclass linkmanclass) {
        linkmanclass.setUpdateDate(new Date());
        linkmanclass.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        linkmanclass.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        return linkmanclassMapper.updateByPrimaryKeySelective(linkmanclass);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除联系人类型
     * @Date: 2020/1/13 20:10
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        Linkmanclass linkmanclass = linkmanclassMapper.selectByPrimaryKey(id);
        linkmanclass.setIsDeleted(Contants.IS_DELETED_TURE);//是否删除
        return linkmanclassMapper.updateByPrimaryKeySelective(linkmanclass);
    }
}
