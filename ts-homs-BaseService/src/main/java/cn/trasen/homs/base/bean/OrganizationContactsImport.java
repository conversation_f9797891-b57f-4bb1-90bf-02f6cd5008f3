package cn.trasen.homs.base.bean;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @projectName: xtbg
 * @package: package cn.trasen.homs.baseservice.bean;
 * @className: OrganizationContactsImport
 * @author: chenbin
 * @description: TODO
 * @date: 2023/11/21 14:07
 * @version: 1.0
 */
@Data
public class OrganizationContactsImport {

    private String hash;

    private String orgId;

    @Excel(name = "科室名称")
    private String orgName;


    @Excel(name = "联系部门（人）")
    private String name;


    @Excel(name = "电话")
    private String tel;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}
