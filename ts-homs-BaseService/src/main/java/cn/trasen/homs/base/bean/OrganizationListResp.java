package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationListResp {

    @ApiModelProperty(value = "主键ID")
    private String organizationId;

    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private String code;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String name;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlagLable;
    /**
     * 员工人数
     */
    @ApiModelProperty(value = "员工人数")
    private Integer employeeNum;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer seqNo;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;


    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类名称")
    private String parentName;


    @ApiModelProperty(value = "部门领导")
    private  List<OrganizationLeaderResp> leaders;


    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnable;



    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnableLable;


    @ApiModelProperty(value = "是否有子集")
    private Boolean haveChildren;


    @ApiModelProperty(value = "电话")
    private String tel;
    
	/**
	 * 三甲医生标准人数
	 */
	@ApiModelProperty(value = "三甲医生标准人数")
	private String doctorSta;
	
	/**
	 * 三甲护士标准人数
	 */
	@ApiModelProperty(value = "三甲护士标准人数")
	private String nurseSta;
	
	/**
	 * 三甲医床比
	 */
	@ApiModelProperty(value = "三甲医床比")
	private String doctorThree;
	
	/**
	 * 三甲护床比
	 */
	@ApiModelProperty(value = "三甲护床比")
	private String nurseThree;
	
	private String orgType;

    @ApiModelProperty(value = " 设备管理部门自定义编码")
    @Column(name = "custom_code")
    private String customCode;
    
	private Integer sort;
    
}