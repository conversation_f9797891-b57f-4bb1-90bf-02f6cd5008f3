package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_modes_uses")
@Setter
@Getter
public class CommModesUses {
    /**
     * 唯一标识
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "唯一标识")
    private String id;
    
    @Column(name = "model_id")
    @ApiModelProperty(value = "模块id")
    private String modelId;

    /**
     * 模块名称
     */
    @Column(name = "model_name")
    @ApiModelProperty(value = "模块名称")
    private String modelName;

    /**
     * 模块url地址
     */
    @Column(name = "model_urls")
    @ApiModelProperty(value = "模块url地址")
    private String modelUrls;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 科室id
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "科室id")
    private String deptId;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 访问时间
     */
    @Column(name = "access_time")
    @ApiModelProperty(value = "访问时间")
    private Date accessTime;

    /**
     * 终端类型，1为PC端，2为移动端
     */
    @Column(name = "teram_type")
    @ApiModelProperty(value = "终端类型，1为PC端，2为移动端")
    private Integer teramType;
}