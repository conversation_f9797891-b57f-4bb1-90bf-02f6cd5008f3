package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.model.OrgGroup;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface OrgGroupMapper extends Mapper<OrgGroup> {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description:  移动端-查询自定义群组信息列表
     * @Date: 2020/4/27 10:33
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.OrgGroup>
     **/
    List<OrgGroup> getOrgGroupList(Page page, OrgGroup orgGroup);


    List<OrgGroup> getOrgGroupData(String groupId,Short groupType);
    
    
    List<OrgGroup> getOrgGroupTree(Map<String,Object> params);


    List<Map<String,String>> selectOrgGroupList(@Param("groupName")String groupName,@Param("ssoOrgCode")String ssoOrgCode);


    List<String> selectGroupIdByUserCode(@Param("userCode")String userCode,@Param("ssoOrgCode")String ssoOrgCode);


}