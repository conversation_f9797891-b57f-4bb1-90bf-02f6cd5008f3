package cn.trasen.homs.base.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 公共库字段引入至机构私有
 * @date 2025/7/28
 **/
@Data
@Accessors(chain = true)
@Validated
public class IntroductionPublicBO {

    @ApiModelProperty("机构编号")
    @NotNull
    private String ssoOrgCode;

    @ApiModelProperty("字典ID")
    @NotNull
    private List<String> ids;
}
