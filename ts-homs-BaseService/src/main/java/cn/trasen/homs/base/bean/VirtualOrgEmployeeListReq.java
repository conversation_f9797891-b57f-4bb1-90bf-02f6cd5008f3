package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class VirtualOrgEmployeeListReq {
    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;
//    /**
//     * 父类ID
//     */
//    @ApiModelProperty(value = "父类ID")
//    private String parentId;

    @ApiModelProperty(value = "虚拟组织ID列表")
    private List<String> virtualIdList;

    @ApiModelProperty(value = "虚拟组织ID查询该节点以及字节点")
    private String virtualId;


    @ApiModelProperty(value = "虚拟组织ID查询该节点的")
    private String eqVirtualId;
    
    private String ssoOrgCode;
}