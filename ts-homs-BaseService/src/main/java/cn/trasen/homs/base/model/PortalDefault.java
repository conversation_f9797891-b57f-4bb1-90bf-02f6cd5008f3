package cn.trasen.homs.base.model;


import io.swagger.annotations.*;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_portal_default")
@Setter
@Getter
public class PortalDefault {
    @Id
    private String id;

    @Column(name = "user_code")
    private String userCode;

    @Column(name = "theme_id")
    private String themeId;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}