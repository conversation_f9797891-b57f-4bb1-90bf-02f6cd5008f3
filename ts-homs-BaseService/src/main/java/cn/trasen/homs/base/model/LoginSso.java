package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 外部系统
 * @Date: 2020/4/3 15:31
 * @Param:
 * @return:
 **/
@Table(name = "COMM_LOGIN_SSO")
@Setter
@Getter
public class LoginSso {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 标题
     */
    @Column(name = "TITLE")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 图标
     */
    @Column(name = "ICON")
    @ApiModelProperty(value = "图标")
    private String icon;
    
    /**
     * 排序
     */
    @Column(name = "SORT")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 内网地址
     */
    @Column(name = "INTRANET_ADDRESS")
    @ApiModelProperty(value = "内网地址")
    private String intranetAddress;

    /**
     * 外网地址
     */
    @Column(name = "OUTER_NET_ADDRESS")
    @ApiModelProperty(value = "外网地址")
    private String outerNetAddress;

    /**
     * 参数名字1
     */
    @Column(name = "PARAMETER_NAME1")
    @ApiModelProperty(value = "参数名字1")
    private String parameterName1;

    /**
     * 参数名字2
     */
    @Column(name = "PARAMETER_NAME2")
    @ApiModelProperty(value = "参数名字2")
    private String parameterName2;

    /**
     * 参数值1
     */
    @Column(name = "PARAMETER_VALUE1")
    @ApiModelProperty(value = "参数值1")
    private String parameterValue1;

    /**
     * 参数值2
     */
    @Column(name = "PARAMETER_VALUE2")
    @ApiModelProperty(value = "参数值2")
    private String parameterValue2;

    /**
     * 客户名称
     */
    @Column(name = "HOSPITAL_NAME")
    @ApiModelProperty(value = "客户名称")
    private String hospitalName;

    /**
     * 描述
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "描述")
    private String remark;

    /**
     * 开启状态(正常、关闭)
     */
    @Column(name = "STATUS")
    @ApiModelProperty(value = "开启状态(正常、关闭)")
    private String status;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}