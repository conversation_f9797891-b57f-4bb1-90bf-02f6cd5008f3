package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrganizationAllocationSaveNumReq;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.model.OrganizationAllocation;
import cn.trasen.homs.core.utils.PlatformResult;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 10:15
 * @description
 */
public interface IOrganizationAllocationService {
        /**
        * @description: 保存编制
    * @param: organizationAllocationSaveNumReq
        * @return: void
        * @author: liyuan
        * @createTime: 2021/8/4 13:34
        */
    void saveNum(OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq);

    /**
    * @description: 导入数据
* @param: importData
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/4 16:52
    */
    @Transactional(rollbackFor = Exception.class)
    PlatformResult excelImportNum(List<Map<String, Object>> importData, String type);

    List<OrganizationAllocation> getBaseList();

    Map<String,Object> getList(OrganizationListReq organizationListReq);
}
