package cn.trasen.homs.base.webSocket;

import javax.servlet.http.HttpServlet;

import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JettyWebsocketConfig {
	@Bean
	public ServletRegistrationBean<HttpServlet> jettyWebsocket(){
		ServletRegistrationBean<HttpServlet> registrationBean = new ServletRegistrationBean<>();
		registrationBean.setServlet(new JettyWebsocketServlet());
		registrationBean.addUrlMappings("/messagewebsocket/*");
		return registrationBean;
	}
	
	@Bean
    public JettyWebsocket jettyWebsocketBean() {
        return new JettyWebsocket();
    }
}
