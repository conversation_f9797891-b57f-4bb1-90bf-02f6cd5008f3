package cn.trasen.homs.base.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cn.trasen.homs.base.contants.ContantsRole;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.UserInfoHolder;

/**
 * @ClassName: DictManager
 * @Description: 字典管理
 * <AUTHOR>
 * @date 2018年6月19日 下午2:50:16
 *
 */
public class UserPermissionManager {

	private static final Object LOCK = new Object();

	/**
	 * 管理人员
	 */
	public static final String SYS_ROLE_SUPERVISE ="SYS_ROLE_SUPERVISE";

	private volatile static UserPermissionManager userPermissionManager;

	private UserPermissionManager() {
	}

	/**
	 * @Title: getInstance
	 * @Description: 获取单列
	 * @return
	 * @date 2018年7月2日 下午12:46:09
	 * <AUTHOR>
	 */
	public static UserPermissionManager getInstance() {
		if (userPermissionManager == null) {
			synchronized (LOCK) {
				if (userPermissionManager == null) {
					userPermissionManager = new UserPermissionManager();
				}
			}
		}
		return userPermissionManager;
	}
	
	/**
	 * @Title: getUserDataPermission  
	 * @Description: 获取数据权限
	 * @return    参数  
	 * UserDataPermissionVo    返回类型  
	 * @throws
	 */
	public UserDataPermissionVo getUserDataPermission() {
		// 数据权限范围
		// 第一个情况全部，第二个情况下属，第三个情况查看自己
		// 管理员
		boolean isAdmin = UserInfoHolder.ISADMIN();
		// 所有
		// 是否所有权限
		boolean isALl = UserInfoHolder.ISALL();
		UserDataPermissionVo vo = new UserDataPermissionVo();
		// 管理员不做数据权限
		if (isAdmin || isALl) {
			return vo;
		}
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		// 数据权限控制
		String roleCode = user.getSysRoleCode();
		if (StringUtils.isNotBlank(roleCode)) {
			// 人事管理员 + 科室管理员
			if (roleCode.contains(ContantsRole.SYS_ROLE_PERSONNEL_STAFF)
					|| roleCode.contains(ContantsRole.SYS_ROLE_DEPARTMENT_MANAGER)) {
				String org = user.getOrgRang();
				List<String> orgCodeList = new ArrayList<>();
				if (StringUtils.isNotBlank(org)) {
					String[] orgList = (org.substring(1, org.length() - 1).replaceAll("'", "").split(","));
					for (String orgCode : orgList) {
						if (StringUtils.isNotBlank(orgCode)) {
							orgCodeList.add(StringUtils.deleteWhitespace(orgCode));
						}
					}
				}
				// 自己所在部门
				if (!orgCodeList.contains(user.getDeptcode())) {
					orgCodeList.add(user.getDeptcode());
				}
				vo.setOrgCodeList(orgCodeList);
			}else {
				vo.setUserCode(user.getUsercode());
			}
		} else {
			vo.setUserCode(user.getUsercode());
		}
		return vo;
	}
	
	/**
	 * 
	* @Title: getUserIsALLDatePermission  
	* @Description: 判断当前用户是否所有权限
	* @Params: @return      
	* @Return: boolean
	* <AUTHOR>
	* @date:2021年6月28日
	* @Throws
	 */
	public boolean getUserIsALLDatePermission() {
		// 管理员
		boolean isAdmin = UserInfoHolder.ISADMIN();
		// 是否所有权限
		boolean isALl = UserInfoHolder.ISALL();
		
		if(isAdmin || isALl) {
			
			return true;
		}
		
		return false;
	}
}
