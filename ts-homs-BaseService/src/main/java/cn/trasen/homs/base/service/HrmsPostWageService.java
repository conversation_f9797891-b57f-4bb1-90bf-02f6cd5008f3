package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.model.HrmsPostWage;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**   
 * @Title: HrmsPostWageWageService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 岗位工资 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月1日 下午10:00:28 
 * @version V1.0   
 */
public interface HrmsPostWageService {

	/**
	 * @Title: insert
	 * @Description: 新增岗位工资
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int insert(HrmsPostWage entity);
	
	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月8日 下午3:17:03
	 */
	int batchInsert(List<HrmsPostWage> list);

	/**
	 * @Title: update
	 * @Description: 更新岗位工资
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int update(HrmsPostWage entity);

	/**
	 * @Title: deleted
	 * @Description: 删除岗位工资
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	int deleted(String id);

	/**
	 * @Title: getDataList
	 * @Description: 获取岗位工资列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsPostWage>
	 * <AUTHOR>
	 */
	List<HrmsPostWage> getDataList(Page page, HrmsPostWage entity);

	/**
	 * 查询岗位工资设置列表
	 * @param entity
	 * @return
	 */
	List<HrmsPostWage> getPostWagesSettingList(HrmsPostWage entity);

	/**
	 * 批量保存岗位工资数据
	 * @param entity
	 */
	void postWagesSettingBatchSave(HrmsPostWage entity);

	HrmsPostWage getDataByPostWageId(HrmsPostWage hrmsPostWage);

	/**
	 * 岗位工资批量启用/禁用
	 * @param postWageIds
	 * @param enable
	 */
    void batchEnable(List<String> postWageIds, String enable);

	/**
	 *
	 * @Title importData
	 * @Description 导入
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Map<String,Object> importData(List<HrmsPostWage> list);
}
