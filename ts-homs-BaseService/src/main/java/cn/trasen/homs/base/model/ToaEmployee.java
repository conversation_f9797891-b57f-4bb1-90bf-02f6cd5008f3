package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_employee")
@Setter
@Getter
public class ToaEmployee {
    /**
     * ID
     */
	@Id
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 部门ID
     */
    @Column(name = "EMP_DEPT_ID")
    @ApiModelProperty(value = "部门ID")
    private String empDeptId;

    /**
     * 部门名称
     */
    @Column(name = "EMP_DEPT_NAME")
    @ApiModelProperty(value = "部门名称")
    private String empDeptName;

    /**
     * 部门Code
     */
    @Column(name = "EMP_DEPT_CODE")
    @ApiModelProperty(value = "部门Code")
    private String empDeptCode;

    /**
     * 员工姓名
     */
    @Column(name = "EMP_NAME")
    @ApiModelProperty(value = "员工姓名")
    private String empName;

    /**
     * 员工工号
     */
    @Column(name = "EMP_CODE")
    @ApiModelProperty(value = "员工工号")
    private String empCode;

    /**
     * 密码
     */
    @Column(name = "EMP_PASSWORD")
    @ApiModelProperty(value = "密码")
    private String empPassword;

    /**
     * 员工昵称
     */
    @Column(name = "EMP_NICK_NAME")
    @ApiModelProperty(value = "员工昵称")
    private String empNickName;

    /**
     * 性别  0：男  1：女
     */
    @Column(name = "EMP_SEX")
    @ApiModelProperty(value = "性别  0：男  1：女")
    private Integer empSex;

    /**
     * 员工电话
     */
    @Column(name = "EMP_PHONE")
    @ApiModelProperty(value = "员工电话")
    private String empPhone;

    /**
     * 员工短号
     */
    @Column(name = "EMP_BUSINESS_PHONE")
    @ApiModelProperty(value = "员工短号")
    private String empBusinessPhone;

    /**
     * 头像,存放头像文件的路径
     */
    @Column(name = "EMP_HEAD_IMG")
    @ApiModelProperty(value = "头像,存放头像文件的路径")
    private String empHeadImg;

    /**
     * 签名图片
     */
    @Column(name = "EMP_SIGNIMG")
    @ApiModelProperty(value = "签名图片")
    private String empSignimg;

    /**
     * 签章图片
     */
    @Column(name = "SIGNATURE_IMG_NAME")
    @ApiModelProperty(value = "签章图片")
    private String signatureImgName;

    /**
     * 签章图片存储名
     */
    @Column(name = "SIGNATURE_IMGSAVE_NAME")
    @ApiModelProperty(value = "签章图片存储名")
    private String signatureImgsaveName;

    /**
     * 入司（职）日期
     */
    @Column(name = "EMP_HIREDATE")
    @ApiModelProperty(value = "入司（职）日期")
    private Date empHiredate;

    /**
     * 离职日期
     */
    @Column(name = "EMP_FIREDATE")
    @ApiModelProperty(value = "离职日期")
    private Date empFiredate;

    /**
     * 离职原因
     */
    @Column(name = "FIRE_REASON")
    @ApiModelProperty(value = "离职原因")
    private String fireReason;

    /**
     * 身份证号
     */
    @Column(name = "EMP_IDCARD")
    @ApiModelProperty(value = "身份证号")
    private String empIdcard;

    /**
     * 出生年月
     */
    @Column(name = "EMP_BIRTH")
    @ApiModelProperty(value = "出生年月")
    private Date empBirth;

    /**
     * 员工年龄
     */
    @Column(name = "EMP_AGE")
    @ApiModelProperty(value = "员工年龄")
    private Integer empAge;

    /**
     * 员工状态  1：正常  2：停用  3：病假  4：事假  5：离职  6：年假  7：退休  8：调休   9：婚丧假  10：产假护理假
     */
    @Column(name = "EMP_STATUS")
    @ApiModelProperty(value = "员工状态  1：正常  2：停用  3：病假  4：事假  5：离职  6：年假  7：退休  8：调休   9：婚丧假  10：产假护理假")
    private Integer empStatus;

    /**
     * 照片
     */
    @Column(name = "EMP_PHOTO")
    @ApiModelProperty(value = "照片")
    private String empPhoto;

    /**
     * 民族
     */
    @Column(name = "EMP_NATION")
    @ApiModelProperty(value = "民族")
    private String empNation;

    /**
     * 籍贯
     */
    @Column(name = "EMP_NATIVEPLACE")
    @ApiModelProperty(value = "籍贯")
    private String empNativeplace;

    /**
     * 婚姻状况  1: 已婚 2 未婚 3 保密
     */
    @Column(name = "EMP_IS_MARRIAGE")
    @ApiModelProperty(value = "婚姻状况  1: 已婚 2 未婚 3 保密")
    private Integer empIsMarriage;

    /**
     * 政治面貌
     */
    @Column(name = "EMP_POLITY")
    @ApiModelProperty(value = "政治面貌")
    private String empPolity;

    /**
     * 现居住地址
     */
    @Column(name = "EMP_ADDRESS")
    @ApiModelProperty(value = "现居住地址")
    private String empAddress;

    /**
     * 身份证地址
     */
    @Column(name = "EMP_CARD_ADDRESS")
    @ApiModelProperty(value = "身份证地址")
    private String empCardAddress;

    /**
     * 电子邮箱
     */
    @Column(name = "EMP_EMAIL")
    @ApiModelProperty(value = "电子邮箱")
    private String empEmail;

    /**
     * 职务ID
     */
    @Column(name = "EMP_DUTY_ID")
    @ApiModelProperty(value = "职务ID")
    private String empDutyId;

    /**
     * 职务名称
     */
    @Column(name = "EMP_DUTY_NAME")
    @ApiModelProperty(value = "职务名称")
    private String empDutyName;

    /**
     * 员工类型（非编制、正式、在编、合同）
     */
    @Column(name = "EMP_TYPE")
    @ApiModelProperty(value = "员工类型（非编制、正式、在编、合同）")
    private String empType;

    /**
     * 用户帐号
     */
    @Column(name = "USER_ACCOUNTS")
    @ApiModelProperty(value = "用户帐号")
    private String userAccounts;

    /**
     * 用户简码
     */
    @Column(name = "USER_SIMPLE_NAME")
    @ApiModelProperty(value = "用户简码")
    private String userSimpleName;

    /**
     * 是否iKey验证 0 不需要验证 1 需要
     */
    @Column(name = "KEY_VALIDATE")
    @ApiModelProperty(value = "是否iKey验证 0 不需要验证 1 需要")
    private Short keyValidate;

    /**
     * iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）
     */
    @Column(name = "KEY_SERIAL")
    @ApiModelProperty(value = "iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）")
    private String keySerial;

    /**
     * 是否是域验证 否：0，是：1
     */
    @Column(name = "IS_AD_CHECK")
    @ApiModelProperty(value = "是否是域验证 否：0，是：1")
    private String isAdCheck;

    /**
     * 是否是休眠用户 否：0，是：1
     */
    @Column(name = "USER_IS_SLEEP")
    @ApiModelProperty(value = "是否是休眠用户 否：0，是：1")
    private String userIsSleep;

    /**
     * 是否接收短信提醒  否：0，是：1
     */
    @Column(name = "IS_SMS_REMINDER")
    @ApiModelProperty(value = "是否接收短信提醒  否：0，是：1")
    private Short isSmsReminder;

    /**
     * 是否接收语音提醒  否：0，是：1
     */
    @Column(name = "IS_VOICE_REMINDER")
    @ApiModelProperty(value = "是否接收语音提醒  否：0，是：1")
    private Short isVoiceReminder;

    /**
     * 是否接收微信消息推送  否：0，是：1
     */
    @Column(name = "IS_WX_REMINDER")
    @ApiModelProperty(value = "是否接收微信消息推送  否：0，是：1")
    private Short isWxReminder;

    /**
     * 是否显示个人手机号码  否：0，是：1
     */
    @Column(name = "IS_DISPLAY_PHONE_NO")
    @ApiModelProperty(value = "是否显示个人手机号码  否：0，是：1")
    private Short isDisplayPhoneNo;

    /**
     * 是否使用电子签章  否：0，是：1
     */
    @Column(name = "IS_USE_SIGNATURE")
    @ApiModelProperty(value = "是否使用电子签章  否：0，是：1")
    private Short isUseSignature;

    /**
     * 是否活动用户  0 :不是  1：是
     */
    @Column(name = "USER_ISACTIVE")
    @ApiModelProperty(value = "是否活动用户  0 :不是  1：是")
    private Short userIsactive;

    /**
     * 是否正式用户  0:不是 1：是
     */
    @Column(name = "USER_ISFORMALUSER")
    @ApiModelProperty(value = "是否正式用户  0:不是 1：是")
    private Short userIsformaluser;

    /**
     * 是否特权用户  0：不是  1：是
     */
    @Column(name = "USER_ISSUPER")
    @ApiModelProperty(value = "是否特权用户  0：不是  1：是")
    private Short userIssuper;

    /**
     * 特权开始时间
     */
    @Column(name = "USER_SUPER_BEGIN")
    @ApiModelProperty(value = "特权开始时间")
    private Date userSuperBegin;

    /**
     * 特权结束时间
     */
    @Column(name = "USER_SUPER_END")
    @ApiModelProperty(value = "特权结束时间")
    private Date userSuperEnd;

    /**
     * 默认组织范围（本部、西院、肿瘤中心）
     */
    @Column(name = "BROWSERANGE")
    @ApiModelProperty(value = "默认组织范围（本部、西院、肿瘤中心）")
    private String browserange;

    /**
     * 默认组织范围名称（本部、西院、肿瘤中心）
     */
    @Column(name = "BROWSERANGE_NAME")
    @ApiModelProperty(value = "默认组织范围名称（本部、西院、肿瘤中心）")
    private String browserangeName;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人编码
     */
    @Column(name = "CREATE_USER_CODE")
    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 用户是否删除 N 未被删除 Y 已被删除
     */
    @Column(name = "USER_IS_DELETED")
    @ApiModelProperty(value = "用户是否删除 N 未被删除 Y 已被删除")
    private String userIsDeleted;

    /**
     * 用户拼音
     */
    @Column(name = "EMP_NAME_PINYIN")
    @ApiModelProperty(value = "用户拼音")
    private String empNamePinyin;

    /**
     * 发薪号
     */
    @Column(name = "EMP_PAYROLL")
    @ApiModelProperty(value = "发薪号")
    private String empPayroll;

    /**
     * 办公电话
     */
    @Column(name = "EMP_SHORT_PHONE")
    @ApiModelProperty(value = "办公电话")
    private String empShortPhone;

    /**
     * 企业微信授权用户ID
     */
    @Column(name = "OPEN_ID")
    @ApiModelProperty(value = "企业微信授权用户ID")
    private String openId;

    /**
     * 是否开启生日显示保护  否：0，是：1
     */
    @Column(name = "IS_BIRTHDAY_PROTECT")
    @ApiModelProperty(value = "是否开启生日显示保护  否：0，是：1")
    private Short isBirthdayProtect;

    /**
     * 员工职称ID
     */
    @Column(name = "EMP_TITLE_ID")
    @ApiModelProperty(value = "员工职称ID")
    private String empTitleId;

    /**
     * 员工职称名称
     */
    @Column(name = "EMP_TITLE_NAME")
    @ApiModelProperty(value = "员工职称名称")
    private String empTitleName;

    /**
     * 工龄
     */
    @Column(name = "YEAR_WORK")
    @ApiModelProperty(value = "工龄")
    private String yearWork;

    /**
     * 已修年假天数
     */
    @Column(name = "YEAR_NUMBER")
    @ApiModelProperty(value = "已修年假天数")
    private String yearNumber;

    /**
     * 年假天数
     */
    @Column(name = "YEAR_DAYS")
    @ApiModelProperty(value = "年假天数")
    private String yearDays;

    /**
     * 手机号码2
     */
    @Column(name = "EMP_PHONE_SECOND")
    @ApiModelProperty(value = "手机号码2")
    private String empPhoneSecond;

    /**
     * 电信短号
     */
    @Column(name = "EMP_TELECOM_BUSINESS_PHONE")
    @ApiModelProperty(value = "电信短号")
    private String empTelecomBusinessPhone;

    /**
     * 联通短号
     */
    @Column(name = "EMP_UNICOM_BUSINESS_PHONE")
    @ApiModelProperty(value = "联通短号")
    private String empUnicomBusinessPhone;

    /**
     * 允许上传附件大小（M）
     */
    @Column(name = "UPLOAD_FILE_SIZE")
    @ApiModelProperty(value = "允许上传附件大小（M）")
    private String uploadFileSize;

    @Column(name = "agent_id")
    private String agentId;

    @Column(name = "agent_name")
    private String agentName;

    @Column(name = "is_enable_process_agent")
    private Long isEnableProcessAgent;

    @Column(name = "agent_start_time")
    private Date agentStartTime;

    @Column(name = "agent_end_time")
    private Date agentEndTime;
}