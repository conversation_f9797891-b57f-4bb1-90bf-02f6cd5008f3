package cn.trasen.homs.base.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 岗位等级
 *
 */
@Table(name = "comm_post")
@Data
public class Post {
    /**
     * 主键ID
     */
    @Id
    private String postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位类别
     */
    private String postCategory;

    /**
     * 是否启用: 1=是; 2=否;
     */
    private String isEnable;
    
    
    private Integer upgradeTime;  //升级时间
  
    private String upgradeSystem;   //是否在升级体系内
    
    
    private Integer upgradeNo;   //升级排序号
    
    

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者ID
     */
    private String createUser;

    /**
     * 创建者姓名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者ID
     */
    private String updateUser;

    /**
     * 更新者姓名
     */
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    private String isDeleted;


    /**
     * 升级至的岗位ID
     */
    private String upgradePostId;


    private  Integer sortNo;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}