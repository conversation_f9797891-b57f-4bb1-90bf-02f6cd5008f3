package cn.trasen.homs.base.model;


import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_portal_element")
@Setter
@Getter
public class PortalElement {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 关联主表id
     */
    @Column(name = "theme_id")
    @ApiModelProperty(value = "关联主表id")
    private String themeId;

    /**
     * 栏目类型：1 、流程待办  2、公文 3、常用入口 4、日程 5、常用流程 6、外部系统 7、信息管理
     */
    @Column(name = "element_type")
    @ApiModelProperty(value = "栏目类型：1、流程待办 2、信息管理  3、公文  4、日程   5、常用流程6、常用入口 7、应用导航  8、个人看板 ")
    private String elementType;

    /**
     * 栏目数量  1、单个栏目  2、多栏目切换
     */
    @Column(name = "element_column")
    @ApiModelProperty(value = "栏目数量  1、单个栏目  2、多栏目切换")
    private String elementColumn;
    
    @Column(name = "width_type")
    @ApiModelProperty(value = "宽度类型")
    private String widthType;
    
    @Column(name = "height_type")
    @ApiModelProperty(value = "高度类型")
    private String heightType;

    /**
     * 显示模式 1、列表模式  2、图文模式  3、轮播模式
     */
    @Column(name = "element_show")
    @ApiModelProperty(value = "显示模式 1、列表模式  2、图文模式  3、轮播模式")
    private String elementShow;
    
    @Column(name = "element_medical_business")
    @ApiModelProperty(value = "院长查询数据内容")
    private String elementMedicalBusiness;

    /**
     * 关联信息栏目id
     */
    @Column(name = "element_channel")
    @ApiModelProperty(value = "关联信息栏目id")
    private String elementChannel;
    
    @Column(name = "element_name")
    @ApiModelProperty(value = "自定义栏目名称")
    private String elementName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 排序
     */
    @Column(name = "sord")
    @ApiModelProperty(value = "排序")
    private Integer sord;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "height")
    private Integer height; //高度
    
    @Column(name = "width")
    private Integer width; //宽度
    
    @Column(name = "min_height")
    private Integer minHeight; //最小高度
    
    @Column(name = "min_width")
    private Integer minWidth; //最小宽度
    
    @Column(name = "max_height")
    private Integer maxHeight; //最大高度
    
    @Column(name = "max_width")
    private Integer maxWidth; //最大宽度
    
    @Column(name = "xpos")
    private Integer xpos; //X坐标
    
    @Column(name = "ypos")
    private Integer ypos; //Y坐标
    
    @Column(name = "is_draggable")
    private Integer isDraggable; //元素是否拖拽  0否  1是
    
    @Column(name = "is_resizable")
    private Integer isResizable; //元素是否缩放  0否 1是
}