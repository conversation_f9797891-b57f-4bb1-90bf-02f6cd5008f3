package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * @Author: Liz<PERSON>huo
 * @Description: 字典项目
 * @Date: 2020/4/29 10:31
 * @Param:
 * @return:
 **/
@Table(name = "COMM_DICT_ITEM")
@Setter
@Getter
public class DictItem {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 字典类型ID
     */
    @Column(name = "DIC_TYPE_ID")
    @ApiModelProperty(value = "字典类型ID")
    private String dicTypeId;

    /**
     * 字典项目编码
     */
    @Column(name = "ITEM_CODE")
    @ApiModelProperty(value = "字典项目编码")
    private String itemCode;

    /**
     * 字典项目名称
     */
    @Column(name = "ITEM_NAME")
    @ApiModelProperty(value = "字典项目字段名称")
    private String itemName;

    /**
     * 字典项目字段值
     */
    @Column(name = "ITEM_NAME_VALUE")
    @ApiModelProperty(value = "字典项目字段值")
    private String itemNameValue;


    /**
     * 字典项目图片
     */
    @Column(name = "ITEM_IMG")
    @ApiModelProperty(value = "字典项目图片")
    private String itemImg;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @ApiModelProperty(value = "排序")
    private Short sort;

    /**
     * 系统编码
     */
    @Column(name = "SYS_CODE")
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 项目中是否有图片（0：否，1：是）
     */
    @Column(name = "IS_ITEM_IMG")
    @ApiModelProperty(value = "项目中是否有图片（0：否，1：是）")
    private Short isItemImg;

    /**
     * 字典类型名称
     */
    @Transient
    @ApiModelProperty(value = "字典类型名称")
    private String dictTypeName;

    /**
     * 类型编码
     */
    @Transient
    @ApiModelProperty(value = "类型编码")
    private String dictTypeCode;


    //是否启用: 1=是; 0=否;
    private  String isEnable;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 模糊筛选
     */
    @Transient
    private String fuzzyScreening;
}