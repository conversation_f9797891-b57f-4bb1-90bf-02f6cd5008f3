package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface CommLoginLogsMapper extends Mapper<CommLoginLogs> {

    List<CommLoginLogs> getDataSetList(Page page, CommLoginLogs record);

    void updateDisable(@Param("employeeId") String employeeId, @Param("status") String status);

    Integer getLogins(@Param("loginDate") String loginDate, @Param("loginType") String loginType, @Param("orgCode") String orgCode,@Param("queryDate")String queryDate);

    List<CommLoginLogs> getLoginList(@Param("loginDate") String loginDate, @Param("orgCode") String orgCode,@Param("queryDate")String queryDate);

    String selectOpenIdByUserCode(@Param("usercode") String usercode);

    String selectOrgCodeByUserCode(String usercode);

    /**
     * 根据手机号匹配多机构用户（多个账号对应多个机构的情况-目前使用于宣威）
     * @param orgCode
     * @param userCode
     * @return
     * <AUTHOR>
     */
    List<Map<String, String>> getMultipleOrgByPhoneNumber(@Param("orgCode") String orgCode,@Param("userCode") String userCode);
    
    /**
     * 根据工号匹配多机构用户（一个账号对应多个机构的情况）
     * @param userCode
     * @return
     * <AUTHOR>
     */
    List<Map<String, String>> getMultipleOrgByUserCode(@Param("userCode") String userCode);

	String getUserCodeByOldCode(@Param("usercode")String usercode);
}