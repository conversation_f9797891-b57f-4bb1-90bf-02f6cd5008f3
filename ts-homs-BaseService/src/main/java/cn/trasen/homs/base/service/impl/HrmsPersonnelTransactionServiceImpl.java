package cn.trasen.homs.base.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.lang.UUID;
import cn.trasen.homs.base.mapper.HrmsPersonnelTransactionMapper;
import cn.trasen.homs.base.model.HrmsPersonnelTransaction;
import cn.trasen.homs.base.service.HrmsPersonnelTransactionService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.util.StringUtil;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPersonnelTransactionServiceImpl implements HrmsPersonnelTransactionService {
    
    @Autowired
    private HrmsPersonnelTransactionMapper hrmsPersonnelTransactionMapper;
    
    @Override
    public int batchInsert(List<HrmsPersonnelTransaction> list) {
        
        return hrmsPersonnelTransactionMapper.batchInsert(list);
    }

    @Override
    public int insert(HrmsPersonnelTransaction bean) {
//    	if (bean.getId() == null) {
//    		bean.setId((int)IdWork.id.nextId());
//    	}
        return hrmsPersonnelTransactionMapper.insert(bean);
    }

    @Override
    public List<HrmsPersonnelTransaction> getDataList(Page page, HrmsPersonnelTransaction entity) {
        
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if(!UserInfoHolder.ISADMIN()) { // 是否管理员   
            if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
                 entity.setHtOrgIdList(orgRang);
            }
        }
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsPersonnelTransactionMapper.getDataList(page,entity);
    }
    
    @Override
    public List<HrmsPersonnelTransaction> getList(HrmsPersonnelTransaction entity) {
        return hrmsPersonnelTransactionMapper.getList(entity);
    }

    @Override
    public List<HrmsPersonnelTransaction> getCycleList(HrmsPersonnelTransaction personnelTransaction) {
        return hrmsPersonnelTransactionMapper.getCycleList(personnelTransaction);
    }


    //  获取通知单编号
    @Override
    public String getBatchNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        String ym = formatter.format(new Date());
        String newYm = hrmsPersonnelTransactionMapper.getBatchNumber(ym);
        if(StringUtil.isEmpty(newYm)) {
            newYm = ym + "01";
        }else {
            String substring = newYm.substring(6);
            Integer re = Integer.valueOf(substring);
            if(re < 10) {
                newYm = ym + "0" + (re +1); 
            }else {
                newYm  = ym + (re + 1);
            }
        }
        return newYm;
    }

    @Override
    public List<String> loadChangeSelect() {
        
        return hrmsPersonnelTransactionMapper.loadChangeSelect();
    }

}
