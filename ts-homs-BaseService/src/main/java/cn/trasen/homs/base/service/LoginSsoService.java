package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.LoginSso;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 外部系统 Service层
 * @Date: 2020/4/3 15:35
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface LoginSsoService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月3日  下午1:49:22 </p>
     */
    int insert(LoginSso loginSso);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月3日  下午1:49:22 </p>
     */
    int update(LoginSso loginSso);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月3日  下午1:49:22 </p>
     */
    int deleted(LoginSso loginSso);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<documentChannel></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月3日  下午1:49:22 </p>
     */
    List<LoginSso> getDataList(Page page, LoginSso loginSso);

	void batchUpdate(List<LoginSso> record);

}
