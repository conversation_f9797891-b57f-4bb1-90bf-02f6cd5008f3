package cn.trasen.homs.base.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.bean.DictItemListReq;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface DictItemMapper extends Mapper<DictItem> {
    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 通过ID查询字典项目
     * @Date: 2020/4/29 16:24
     * @Param:
     * @return: cn.trasen.system.model.DictItem
     **/
    DictItem getDictItemById(DictItem entity);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 通过dicTypeId查询字典项目
     * @Date: 2020/5/6 11:03
     * @Param:
     * @return: java.util.List<cn.trasen.system.model.DictItem>
     **/
    List<DictItem> getDictItemByDicTypeId(@Param("dicTypeId") String dicTypeId);

    /**
     * @throws
     * @Title: getDictItemByTypeCode
     * @Description: TODO(描述这个方法的作用)
     * @param: @param typeCode
     * @param: @return
     * @return: List<DictItem>
     * @author: YueC
     * @date: 2020年7月30日 下午2:08:47
     */
    List<DictItem> getDictItemByTypeCode(@Param("req") DictItemListReq dictItemListReq);


    /**
     * @description: 分页
     * @param: page
     * @param: typeCode
     * @return: java.util.List<cn.trasen.basicsbottom.model.DictItem>
     * @author: liyuan
     * @createTime: 2021/8/16 14:54
     */
    List<DictItem> getDictItemByTypeCode(Page page, @Param("req") DictItemListReq dictItemListReq);


    List<DictItem> getDictItemByTypeCodeShow(@Param("typeCode") List<String> typeCode, @Param("ssoOrgCode") String ssoOrgCode);

    DictItem getDictItemByDictTypeIdAndItemNameValue(@Param("dictTypeId") String dictTypeId, @Param("itemNameValue") String itemNameValue, @Param("ssoOrgCode") String ssoOrgCode);
}