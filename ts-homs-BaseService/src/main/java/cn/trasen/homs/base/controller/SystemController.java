package cn.trasen.homs.base.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.base.bean.LoginLogStatResp;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.service.CommLoginLogsService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.SysAccessLogService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.SystemMenusClient;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/12/14 10:42
 */

@Controller
@Api(tags = "system对接系统")
@RestController
@RequestMapping("/system")
@Slf4j
public class SystemController {

    @Autowired
    SystemMenusClient systemMenusClient;

    @Autowired
    SystemUserFeignService systemUserFeignService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CommLoginLogsService commLoginLogsService;

    @Autowired
    private SysAccessLogService sysAccessLogService;
    
    @Autowired
    private GlobalSettingsService globalSettingsService;


    @ApiOperation(value = "获取菜单", notes = "获取菜单")
    @RequestMapping(value = "/getfmenus", method = {RequestMethod.POST, RequestMethod.GET})
    public Result getfmenus(String syscode) {
        return systemMenusClient.getfmenus(syscode);
    }


    @ApiOperation(value = "获取在线菜单", notes = "获取在线菜单")
    @RequestMapping(value = "/onlineCount", method = {RequestMethod.POST, RequestMethod.GET})
    public Result onlineCount(String fromType) {

        String cacheKey = commLoginLogsService.getUserOnlineListCacheKey();

        Set<String> keys = redisTemplate.keys(cacheKey);

        List<CommLoginLogs> onlineList = new ArrayList<>();

        for (String key : keys) {
            // 获取key对应值
            String value = (String) redisTemplate.opsForValue().get(key);

            CommLoginLogs commLoginLogs = JSON.parseObject(value, CommLoginLogs.class);

            onlineList.add(commLoginLogs);
//                         if(null != commLoginLogs.getSsoOrgCode() && (commLoginLogs.getSsoOrgCode().equals(UserInfoHolder.getCurrentUserCorpCode()))){
//            	 onlineList.add(commLoginLogs);
//             }
        }

        //去重
        onlineList = onlineList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CommLoginLogs::getUserCode))),
                        ArrayList::new));


        Result result = new Result();

        if (CollectionUtils.isNotEmpty(onlineList)) {
            result.setMessage("");
            result.setObject(onlineList.size());
            result.setSuccess(true);
            result.setStatusCode(200);
            return result;
        } else {
            result.setMessage("");
            result.setObject(0);
            result.setSuccess(true);
            result.setStatusCode(200);
            return result;
        }
    }


    @ApiOperation(value = "我的个人信息", notes = "我的个人信息")
    @RequestMapping(value = "/userInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public Result userInfo() {
        return systemUserFeignService.userInfo();
    }

    @ApiOperation(value = "我的系统资源", notes = "我的系统资源")
    @RequestMapping(value = "/resourceGetSys/{userCode}", method = {RequestMethod.POST, RequestMethod.GET})
    public Result resourceGetSys(@PathVariable("userCode") String userCode) {
        return systemUserFeignService.resourceGetSys(userCode);
    }

    @ApiOperation(value = "在线人数", notes = "在线人数")
    @RequestMapping(value = "/onlineList", method = {RequestMethod.POST, RequestMethod.GET})
    public Result onlineList(String fromType) {

        String cacheKey = commLoginLogsService.getUserOnlineListCacheKey();

        Set<String> keys = redisTemplate.keys(cacheKey);

        List<CommLoginLogs> onlineList = new ArrayList<>();

        for (String key : keys) {
            // 获取key对应值
            String value = (String) redisTemplate.opsForValue().get(key);

            CommLoginLogs commLoginLogs = JSON.parseObject(value, CommLoginLogs.class);

            onlineList.add(commLoginLogs);
//            if (null != commLoginLogs.getSsoOrgCode() && (commLoginLogs.getSsoOrgCode().equals(UserInfoHolder.getCurrentUserCorpCode()))) {
//                onlineList.add(commLoginLogs);
//            }

        }

        //去重
        onlineList = onlineList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CommLoginLogs::getUserCode))),
                        ArrayList::new));


        Result result = new Result();

        result.setMessage("");
        result.setObject(onlineList);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;

    }


    @ApiOperation(value = "登录日志统计接口", notes = "登录日志统计接口")
    @RequestMapping(value = "/login/stat", method = {RequestMethod.GET})
    public Result loginLogStat(String queryDate) {

        LoginLogStatResp res = new LoginLogStatResp();

        String cacheKey = commLoginLogsService.getUserOnlineListCacheKey();

        // 查询在线数据
        Set<String> keys = redisTemplate.keys(cacheKey);

        List<CommLoginLogs> onlineList = new ArrayList<>();

        for (String key : keys) {
            // 获取key对应值
            String value = (String) redisTemplate.opsForValue().get(key);

            CommLoginLogs commLoginLogs = JSON.parseObject(value, CommLoginLogs.class);

            onlineList.add(commLoginLogs);
        }

        //去重
        onlineList = onlineList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CommLoginLogs::getUserCode))),
                        ArrayList::new));
        // 填充在线人数
        res.setOnlines(onlineList.size());

        // 今天最早时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        String todayDate = sdf.format(new Date());

        // 填充今日累积访问人数
        res.setLogins(commLoginLogsService.getLogins(todayDate, "",queryDate));

        String[] pc = {"PC", "PCSMS"};
        // 填充今日PC登录次数
        res.setLoginsByPc(sysAccessLogService.getLogins(todayDate, pc,queryDate));
        String[] wx = {"WX", "DD"};
        // 填充今日移动端登录次数
        res.setLoginsByWx(sysAccessLogService.getLogins(todayDate, wx,queryDate));
        // 填充今日累积访问次数
        res.setAccesses(sysAccessLogService.getAccesses(todayDate,queryDate));
        Result result = new Result();
        result.setMessage("");
        result.setObject(res);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }

    @ApiOperation(value = "登录日志列表接口", notes = "登录日志列表接口")
    @RequestMapping(value = "/login/log", method = {RequestMethod.GET})
    public Result loginLogList(@ApiParam(name = "type", value = "记录类型 0 在线记录 1 累积访问记录 2 PC访问记录 3移动访问记录 4累积访问次数", required = true, defaultValue = "0") String type,String queryDate) {

        List<CommLoginLogs> list = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        String todayDate = sdf.format(new Date());

        switch (type) {
            case "0":
                String cacheKey = commLoginLogsService.getUserOnlineListCacheKey();
                Set<String> keys = redisTemplate.keys(cacheKey);
                list = keys.stream()
                        .map(key -> {
                            String value = (String) redisTemplate.opsForValue().get(key);
                            return JSON.parseObject(value, CommLoginLogs.class);
                        })
                        .collect(Collectors.toMap(
                                CommLoginLogs::getUserCode,
                                e -> e,
                                (e1, e2) -> e1.getLoginTime().compareTo(e2.getLoginTime()) > 0 ? e1 : e2
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(CommLoginLogs::getLoginTime).reversed())
                        .collect(Collectors.toList());
                break;
            case "1":
                // 累积访问记录
                list = commLoginLogsService.getLoginList(todayDate,queryDate);
                break;
            case "2":
                // PC
                String[] pc = {"PC", "PCSMS"};
                list = sysAccessLogService.getLoginList(todayDate, pc,queryDate);
                break;
            case "3":
                // 移动
                String[] wx = {"WX", "DD"};
                list = sysAccessLogService.getLoginList(todayDate, wx,queryDate);
                break;
            case "4":
                // 累积访问记录
                list = sysAccessLogService.getAccessList(todayDate,queryDate);
                break;

        }

        //需要查询部门信息
        if(CollectionUtils.isNotEmpty(list)) {
        	List<String> userCodes = new ArrayList<>();
        	for (CommLoginLogs commLoginLogs : list) {
        		userCodes.add(commLoginLogs.getUserCode());
			}
        	
        	List<Map<String,String>> orgList = sysAccessLogService.selectOrgInfo(userCodes);
        	if(CollectionUtils.isNotEmpty(orgList)) {
        		for (CommLoginLogs commLoginLogs : list) {
        			for (Map<String, String> map : orgList) {
						if(map.get("employee_no").equals(commLoginLogs.getUserCode())) {
							commLoginLogs.setOrgName(map.get("name"));
						}
					}
				}
        	}
        }
        
        Result result = new Result();
        result.setMessage("");
        result.setObject(list);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }
    
    @ApiOperation(value = "登录日志折线图", notes = "登录日志折线图")
    @RequestMapping(value = "/login/stackedLine", method = {RequestMethod.GET})
    public Result stackedLine(String queryYear) {

        Map<String,Object> resultData = new HashMap<>();
        
        if(StringUtils.isBlank(queryYear)) {
        	queryYear = String.valueOf(DateUtil.year(new Date()));
        }

        GlobalSetting globalSetting = globalSettingsService.getSafeGlobalSetting();
        
        String moth = DateUtil.format(new Date(), "yyyy-MM");
        // 获取当前月份的第一天
        Date firstDayOfMonth = DateUtil.beginOfMonth(new Date());
        // 计算两个日期之间的天数差异 获取当月已经过去多少天
        long daysPassed = DateUtil.between(firstDayOfMonth, new Date(), DateUnit.DAY);
        if(0 == daysPassed) {
        	daysPassed = 1;
        }else {
        	daysPassed = daysPassed + 1;
        }
        
        //累计访问人数
        if(1 == globalSetting.getPlatformType()) {
        	
        	 List<Map<String,Object>> loginsCount = sysAccessLogService.getLoginsMonth(queryYear,null);
        	 for (Map<String, Object> map : loginsCount) {
				calAvgNumbers(moth, map, daysPassed);
			 }
             resultData.put("loginsCount", loginsCount);
             
             List<Map<String,Object>> loginsPCCount = sysAccessLogService.getLoginsMonth(queryYear,"PC");
             for (Map<String, Object> map : loginsPCCount) {
 				calAvgNumbers(moth, map, daysPassed);
 			 }
             resultData.put("loginsPcNumbers", loginsPCCount);
             
             
             List<Map<String,Object>> loginsWXCount = sysAccessLogService.getLoginsMonth(queryYear,"WX");
             for (Map<String, Object> map : loginsWXCount) {
  				calAvgNumbers(moth, map, daysPassed);
  			 }
             resultData.put("loginsWxNumbers", loginsWXCount);
             
        }else {
        	 List<Map<String,Object>> loginsCount = sysAccessLogService.getLoginsCount(queryYear);
        	 for (Map<String, Object> map : loginsCount) {
 				calAvgNumbers(moth, map, daysPassed);
 			 }
             resultData.put("loginsCount", loginsCount);
             
             //PC端访问次数
             List<Map<String,Object>> loginsPcNumbers = sysAccessLogService.getLoginsNumbers(queryYear,"PC");
             for (Map<String, Object> map : loginsPcNumbers) {
  				calAvgNumbers(moth, map, daysPassed);
  			 }
             resultData.put("loginsPcNumbers", loginsPcNumbers);
             
             //移动端访问次数
             List<Map<String,Object>> loginsWxNumbers = sysAccessLogService.getLoginsNumbers(queryYear,"WX");
             for (Map<String, Object> map : loginsWxNumbers) {
   				calAvgNumbers(moth, map, daysPassed);
   			 }
             resultData.put("loginsWxNumbers", loginsWxNumbers);
        }
        
        //在院总人数
        Long totalEmp = sysAccessLogService.selectTotalEmployee(UserInfoHolder.getCurrentUserCorpCode());
        resultData.put("totalEmp", totalEmp);
        
        Result result = new Result();
        result.setMessage("查询成功");
        result.setObject(resultData);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }


	private void calAvgNumbers(String moth, Map<String, Object> map,long daysPassed) {
		if(map.get("loginMonth").equals(moth)) {
			long loginNumbersValue = (long) map.get("loginNumbers");
			if(0 == loginNumbersValue) {
				map.put("loginNumbers", 0);
			}else {
				Double loginNumbers = (double) (loginNumbersValue / daysPassed);
				map.put("loginNumbers", (int)Math.ceil(loginNumbers));
			}
		}else {
			long loginNumbersValue = (long) map.get("loginNumbers");
			if(0 == loginNumbersValue) {
				map.put("loginNumbers", 0);
			}else {
				Double loginNumbers = (double) (loginNumbersValue / 30);
				map.put("loginNumbers", (int)Math.ceil(loginNumbers));
			}
		}
	}
	
	@ApiOperation(value = "系统使用情况分析", notes = "系统使用情况分析")
    @RequestMapping(value = "/login/sysUseAnalysisList", method = {RequestMethod.GET})
    public Result sysUseAnalysisList(String startDate,String endDate,String sidx,String sord) {

        Map<String,Object> params = new HashMap<>();
        if(StringUtils.isNotBlank(startDate)) {
        	params.put("startDate", startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)) {
        	params.put("endDate", endDate + " 23:59:59");
        }
        if(StringUtils.isBlank(sidx)) {
        	params.put("sidx", "t3.sort,t3.org_level,t3.seq_no");
        	params.put("sord", "asc");
        }else {
        	params.put("sidx", sidx);
       	 	params.put("sord", sord);
        }
       
        params.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        List<Map<String,Object>> sysUseAnalysisList = sysAccessLogService.sysUseAnalysisList(params);
        
        Result result = new Result();
        result.setMessage("查询成功");
        result.setObject(sysUseAnalysisList);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }
	
	@ApiOperation(value = "系统使用情况分析(详情)", notes = "系统使用情况分析(详情)")
    @RequestMapping(value = "/login/sysUseAnalysisDetailList", method = {RequestMethod.GET})
    public Result sysUseAnalysisDetailList(String startDate,String endDate,String organizationId,String useStatus) {

        Map<String,Object> params = new HashMap<>();
        if(StringUtils.isNotBlank(startDate)) {
        	params.put("startDate", startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)) {
        	params.put("endDate", endDate + " 23:59:59");
        }
        params.put("organizationId", organizationId);
        params.put("useStatus", useStatus);
        params.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        List<Map<String,Object>> sysUseAnalysisList = sysAccessLogService.sysUseAnalysisDetailList(params);
        
        Result result = new Result();
        result.setMessage("查询成功");
        result.setObject(sysUseAnalysisList);
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }

}

