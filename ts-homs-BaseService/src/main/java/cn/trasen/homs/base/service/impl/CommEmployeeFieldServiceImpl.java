/**
 * @Title: CommEmployeeFieldServiceImpl.java  
 * @Package: cn.trasen.homs.base.service.impl  
 * @Date: 2021年6月16日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.base.dao.CommEmployeeFieldMapper;
import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.service.CommEmployeeFieldGroupService;
import cn.trasen.homs.base.service.CommEmployeeFieldService;
import cn.trasen.homs.base.utils.JdbcTemplateUtil;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
* @ClassName: CommEmployeeFieldServiceImpl  
 * @Author: 86189
 * @Date: 2021年6月16日
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class CommEmployeeFieldServiceImpl implements CommEmployeeFieldService{

    @Autowired
    private CommEmployeeFieldMapper commEmployeeFieldMapper;
    
    @Autowired
    private CommEmployeeFieldGroupService commEmployeeFieldGroupService;
    
    @Autowired
    private JdbcTemplateUtil jdbcTemplateUtil;
    
    /**
     * 
    * @Title: insert  
    * @Description: 新增人员档案字段
    * @Params: @param record
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int insert(CommEmployeeField record) {
        record.setCreateDate(new Date());
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        record.setCreateUserName(UserInfoHolder.getCurrentUserName());
        record.setId(ApplicationUtils.GUID32());
        record.setIsDeleted(Contants.IS_DELETED_FALSE);
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        
        if(StringUtils.isBlank(record.getFieldName())) {
            
            Example example = new Example(record.getClass());
            
            example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE).andEqualTo("groupId",record.getGroupId());
            
            String fieldName = PinYinUtil.converterToFirstSpell(record.getFieldName());
            
            int count = commEmployeeFieldMapper.selectCountByExample(example);
            
            fieldName = fieldName+count;
            
            record.setFieldName(fieldName);
            
        }
        
        //同步表结构
        CommEmployeeFieldGroup group = commEmployeeFieldGroupService.findById(record.getGroupId());
        
        jdbcTemplateUtil.addFieldNew(record, group.getTableName());
        
        return commEmployeeFieldMapper.insertSelective(record);
    }
    
    /**
     * 
    * @Title: update  
    * @Description: 修改人员档案
    * @Params: @param record
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int update(CommEmployeeField record) {
        
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        
        return commEmployeeFieldMapper.updateByPrimaryKeySelective(record);
    }
    
    /**
     * 
    * @Title: deleted  
    * @Description: 删除人员档案字段
    * @Params: @param id
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int deleted(String id) {
        
        CommEmployeeField record = new CommEmployeeField();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setIsDeleted(Contants.IS_DELETED_TURE);
        return commEmployeeFieldMapper.updateByPrimaryKeySelective(record);
    }
    
    /**
     * 
    * @Title: getList  
    * @Description: 获取人员档案字段列表
    * @Params: @param page
    * @Params: @param record
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    public List<CommEmployeeField> getList(Page page,CommEmployeeField record) {
        
        Example example = new Example(CommEmployeeField.class);
        
        example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        
        if(StringUtils.isNotBlank(record.getGroupId())) {
            
            example.and().andEqualTo("groupId",record.getGroupId());
        }
        if(null!=record.getIsHide()) {
            example.and().andEqualTo("isHide",record.getIsHide());
        }
        
        example.orderBy("seq").asc();
        
        return commEmployeeFieldMapper.selectByExample(example);
    }
    
    /**
     * 
    * @Title: getListByGroupid  
    * @Description: 根据分组id查询字段列表
    * @Params: @param groupId
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    public List<CommEmployeeField> getFieldsListByCondition(CommEmployeeField record) {
        return commEmployeeFieldMapper.getFieldsListByCondition(record);
    }
    
    /**
     * 
    * @Title: getFieldAndJurisdictionListByGroupid  
    * @Description: 根据分组id获取字段明细以及当前用户字段编辑权限
    * @Params: @param groupId
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    public List<CommEmployeeField> getFieldAndJurisdictionListByGroupid(String groupId) {
        
        CommEmployeeField record = new CommEmployeeField();
        
        record.setGroupId(groupId);
    
        String userCode = UserInfoHolder.getCurrentUserCode(); 
        
        record.setUserCode(userCode);
        
        record.setIsHide(0);
        
        record.setUserId(UserInfoHolder.getCurrentUserInfo().getId());
        
        return commEmployeeFieldMapper.getFieldAndJurisdictionListByGroupid(record);
    }
    
    /**
     * 
    * @Title: findById  
    * @Description: 根据id查询人员档案字段
    * @Params: @param id
    * @Params: @return      
    * @Return: CommEmployeeField
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    public CommEmployeeField findById(String id) {
        
        return commEmployeeFieldMapper.selectByPrimaryKey(id);
    }
    
    /**
     * 获取员工明细分组
     */
    @Override
    public List<CommEmployeeField> getFielListByGroupid(CommEmployeeField record) {
        
        String userCode = UserInfoHolder.getCurrentUserCode(); 
        
        record.setUserCode(userCode);
        
        return commEmployeeFieldMapper.getFielListByGroupid(record);
    }

    /**
     * 
    * @Title: updateList  
    * @Description: 批量修改人员档案字段
    * @Params: @param records
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public String updateList(List<CommEmployeeField> records) {
        
        if(CollectionUtils.isNotEmpty(records)) {
            
            for(CommEmployeeField record : records) {
                
                record.setUpdateDate(new Date());
                record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
                commEmployeeFieldMapper.updateByPrimaryKeySelective(record);
            }
        }
        
        return  "";
    }
    /**
     * 
    * @Title: getEmployeeFieldTitel  
    * @Description: 获取人员档案列表头
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    public List<CommEmployeeField> getEmployeeFieldTitel() {
        
        Example example = new Example(CommEmployeeField.class);
        
        example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        
        example.and().andEqualTo("isTableShow","1");
        
        example.and().andEqualTo("groupId",1);
        
        return commEmployeeFieldMapper.selectByExample(example);
    }
    
    @Transactional(readOnly = false)
    public void create(String tableName) {
        
         Example example = new Example(CommEmployeeField.class);
        
         List<CommEmployeeField>  list =commEmployeeFieldMapper.selectByExample(example);
         
         for(CommEmployeeField field : list) {
             
             field.setFieldName(field.getFieldName().toLowerCase());
             
             commEmployeeFieldMapper.updateByPrimaryKeySelective(field);
         }
    }
    

}
