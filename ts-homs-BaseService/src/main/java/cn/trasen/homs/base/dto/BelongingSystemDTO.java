package cn.trasen.homs.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 所属系统查询
 * @date 2025/7/28
 **/
@Data
@Accessors(chain = true)
@Validated
public class BelongingSystemDTO {

    @ApiModelProperty("机构编号")
    @NotNull(message = "机构编号必填")
    @Valid
    private String orgCode;

    @ApiModelProperty("系统编码")
    private String sysCode;
}
