//package cn.trasen.homs.base.webSocket;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.stream.Collectors;
//
//import org.eclipse.jetty.websocket.api.annotations.WebSocket;
//import org.springframework.util.StringUtils;
//import org.springframework.web.socket.CloseStatus;
//import org.springframework.web.socket.TextMessage;
//import org.springframework.web.socket.WebSocketHandler;
//import org.springframework.web.socket.WebSocketMessage;
//import org.springframework.web.socket.WebSocketSession;
//
//import com.google.common.base.Objects;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//@WebSocket
//public class MessageWebSocketHandler implements WebSocketHandler {
//
//	// 在线用户列表
//	private static ConcurrentHashMap<String, WebSocketSession> webSocketSet = new ConcurrentHashMap<>();
//
//	public ConcurrentHashMap<String, WebSocketSession> getMessageWebSocket() {
//		return webSocketSet;
//	}
//
//	//新的在线用户列表，支持同一帐户可以在不同的终端通讯
//	private static ConcurrentHashMap<WebSocketSession, String> userList = new ConcurrentHashMap<>();
//	
//	
//	@Override
//	public void afterConnectionEstablished(WebSocketSession session) throws Exception {
//		log.debug("建立链接：" + session.getId());
//		String requestUri = session.getUri().toString();
//		String[] requestUriArr = requestUri.split("/");
//		String userCode = requestUriArr[requestUriArr.length - 1];
//		synchronized (MessageWebSocketHandler.class) {
//			//如果当前session不存在，则放入在线列表中
//			if (!userList.containsKey(session)) {
//				if (!StringUtils.isEmpty(userCode)) {
//					userList.put(session, userCode);
//				}
//			}
//		}
//		System.out.println("有新连接加入！当前在线人数为" + userList.size() + ",用户userCode为=======" + userCode);
////		try {
////			sendMessage(session, "链接成功");
////		} catch (IOException e) {
////			System.out.println("IO异常");
////		}
//
//	}
//
//	//接收webSocket消息
//	@Override
//	public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
//		//如果在接收到特定消息后，可以对特定消息进行特殊的处理
//		//需要定义特殊的应用场景
//	}
//	//发送消息给指定人员
//	public boolean sendMessageToUser(String userCode, TextMessage message) {
//		//找到当前userCode对应的WebSocketSession，并发送消息
//		Set<WebSocketSession> sessions =  findSessionsByValue(userList, userCode);
//		for (WebSocketSession session: sessions) {
//			if (!session.isOpen()) { //无效websocketsession，直接进行移除
//				userList.remove(session);
//				return false;
//			}else {
//				try {
//					session.sendMessage(message);
//				}catch(IOException e) {
//					e.printStackTrace();
//					return false;
//				}
//			}
//		}
//		return true;
//	}
//	//消息广播
//	public boolean sendMessageToAllUsers(TextMessage message) {
//		boolean allSendSuccess = true;
//		for(Map.Entry<WebSocketSession, String> entry: userList.entrySet()) {
//			WebSocketSession session = entry.getKey();
//			if (!session.isOpen()) {
//				userList.remove(session);
//			}else {
//				try {
//					session.sendMessage(message);
//				} catch (IOException e) {
//					e.printStackTrace();
//					allSendSuccess = false;
//				}
//			}
//		}
//		return allSendSuccess;
//	}
//	
//	
//	@Override
//	public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
//		if (session.isOpen()) {
//			session.close();
//		}
//		log.info("连接出错...");
//		userList.remove(session);
//	}
//
//	@Override
//	public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
//		log.info("连接已经关闭："+ closeStatus);
//		userList.remove(session);
//	}
//
//	@Override
//	public boolean supportsPartialMessages() {
//		return false;
//	}
//
//	public Set<WebSocketSession> findSessionsByValue(ConcurrentHashMap<WebSocketSession,String> map, String value){
//		return map.entrySet().stream()
//				.filter(entry->Objects.equal(value, entry.getValue()))
//				.map(Map.Entry::getKey)
//				.collect(Collectors.toSet());
//	}
//}
