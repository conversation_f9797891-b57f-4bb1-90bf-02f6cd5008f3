package cn.trasen.homs.base.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.VersionAccessory;
import cn.trasen.homs.base.service.VersionAccessoryService;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 版本管理附件信息附件信息Controller层
 * @Date: 2020/4/27 16:37
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Api(tags = "版本管理附件信息附件信息Controller")
@RestController
public class VersionAccessoryController {

    private static final Logger logger = LoggerFactory.getLogger(VersionAccessoryController.class);

    @Autowired
    private VersionAccessoryService versionAccessoryService;

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增版本管理附件信息</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日 10:38 </p>
     */
    @ApiOperation(value = "新增版本管理附件信息", notes = "新增版本管理附件信息")
    @PostMapping("/versionAccessory/save")
    @NoRepeatSubmit(lockTime = 3)
    public PlatformResult<String> insert(@RequestBody VersionAccessory entity) {
        try {
            versionAccessoryService.insert(entity);
            return PlatformResult.success();

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改版本管理附件信息</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午4:48:58 </p>
     */
    @ApiOperation(value = "修改版本管理附件信息", notes = "修改版本管理附件信息")
    @PostMapping("/versionAccessory/update")
    @NoRepeatSubmit(lockTime = 3)
    public PlatformResult<String> update(@RequestBody VersionAccessory entity) {
        try {
            versionAccessoryService.update(entity);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: deleteById</p>
     * <p> @Description: 删除版本管理附件信息</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午4:49:12 </p>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/versionAccessory/deletedById")
    public PlatformResult<String> deleteById(@RequestBody VersionAccessory entity) {
        try {
            versionAccessoryService.deleted(entity);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 获取版本管理附件信息列表</p>
     * <p> @Return: DataSet<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午4:49:35 </p>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/versionAccessory/list")
    public DataSet<VersionAccessory> getDataList(Page page, VersionAccessory entity) {
    	try {
	        List<VersionAccessory> list = versionAccessoryService.getDataList(page, entity);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }
    
}
