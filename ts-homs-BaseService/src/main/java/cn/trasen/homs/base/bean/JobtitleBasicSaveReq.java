package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 11:12
 * @description
 */

@Data
public class JobtitleBasicSaveReq {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "id")
    private String jobtitleBasicId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    //@Length(max = 20, message = "名称不能超过20个字符")
    private String jobtitleBasicName;

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID")
    private String classId;

    /**
     * 级别ID
     */
    @ApiModelProperty(value = "级别ID")
    private String levelId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "升级")
    List<JobtitleBasicUpgradeSaveReq> jobtitleBasicUpgradeList;


    /**
     * 排序小到大
     */
    @ApiModelProperty(value = "排序小到大")

    private  int sortNo;
}