package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 15:53
 * @description
 */

@Data
public class JobtitleBasicUpgradeSaveReq {

    @ApiModelProperty(value = "职称ID")
    String jobtitleBasicId;

    @ApiModelProperty(value = "升级时间")
    Integer upgradeTime;

    @ApiModelProperty(value = "升级后的职称ID")
    String upgradeJobtitleBasicId;


    @ApiModelProperty(value = "学历编码")
    String educationTypeCode;

}
