package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 9:43
 * @description
 */

@Data
public class PostListResp {

    @ApiModelProperty(value = "级别ID")
    private String postId;

    @ApiModelProperty(value = "岗位类别")
    private String postCategory;

    @ApiModelProperty(value = "岗位类别名称")
    private String postCategoryName;
    @ApiModelProperty(value = "级别名称")
    private String postName;

    @ApiModelProperty(value = "岗位职责")
    private String remark;

    @ApiModelProperty(value = "晋升条件时间年")
    private Integer upgradeTime;  //升级时间


    @ApiModelProperty(value = "升级至的岗位ID")
    private String upgradePostId;


    @ApiModelProperty(value = "升级至的岗位名称")
    private String upgradePostName;

    @ApiModelProperty(value = "是否启用")
    private String isEnable;


    @ApiModelProperty(value = "是否启用")
    private String isEnableLable;


    @ApiModelProperty(value = "排序")
    private Integer sortNo;
}