/**
 * @Title: CustomEmployeeService.java  
 * @Package: cn.trasen.homs.base.service  
 * @Date: 2021年6月19日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service;

import cn.trasen.homs.base.bean.CustomEmployeeResp;
import cn.trasen.homs.base.bean.EmployeeStorageData;
import cn.trasen.homs.base.bean.HistoricalListResp;
import cn.trasen.homs.base.model.CommEmployeeUpdateDetail;
import cn.trasen.homs.base.model.CustomEmpModel;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
* @ClassName: CustomEmployeeService  
 * @Author: 86189
 * @Date: 2021年6月19日
 */
public interface CustomEmployeeService {
	
	/**
	 * 
	* @Title: getEmployeePageListByCustom  
	* @Description: 分页获取自定义人员档案列表
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<Map<String,String>>
	* <AUTHOR>
	* @date:2021年6月19日
	* @Throws
	 */
	List<Map<String,String>> getEmployeePageListByCustom(Page page,HrmsEmployee record);
	
	/**
	 * 
	* @Title: findById  
	* @Description: 根据id查询自定义人员档案
	* @Params: @param id
	* @Params: @return      
	* @Return: Map<String,String>
	* <AUTHOR>
	* @date:2021年6月20日
	* @Throws
	 */
	Map<String,List<Map<String,String>>> findByIdAndDetails(String id);
	
	/**
	 * 
	* @Title: deleteById  
	* @Description: 根据id删除人员档案
	* @Params: @param id
	* @Params: @return      
	* @Return: int
	* <AUTHOR>
	* @date:2021年6月20日
	* @Throws
	 */
	int deleteById(String id);
	
	/**
	 * 
	* @Title: insert  
	* @Description: 新增或修改人员自定义档案
	* @Params: @param records
	* @Params: @return      
	* @Return: String
	* <AUTHOR>
	* @date:2021年6月23日
	* @Throws
	 */
	String save(CustomEmpModel record);
	
	/**
	 * 
	* @Title: UniqueCheck  
	* @Description: 新增修改时姓名,工号唯一校验
	* @Params: @param record
	* @Params: @return      
	* @Return: boolean
	* <AUTHOR>
	* @date:2021年7月6日
	* @Throws
	 */
	String uniqueCheck(CustomEmpModel record);
	
	/**
	 * 
	* @Title: getDataWorkflowList  
	* @Description: 获取审批列表
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<CustomEmployeeResp>
	* <AUTHOR>
	* @date:2021年7月6日
	* @Throws
	 */
	List<CustomEmployeeResp> getDataWorkflowList(Page page,CustomEmployeeResp record);
	
	/**
	 * 
	* @Title: auditDetails  
	* @Description: 查询修改走流程详细信息
	* @Params: @param operationId
	* @Params: @return      
	* @Return: List<CommEmployeeUpdateDetail>
	* <AUTHOR>
	* @date:2021年7月1日
	* @Throws
	 */
	List<CommEmployeeUpdateDetail> auditDetails(String operationId);
	
	
	/**
	 * 
	* @Title: audit  
	* @Description: 流程审核
	* @Params: @param record      
	* @Return: void
	* <AUTHOR>
	* @date:2021年7月2日
	* @Throws
	 */
	void audit(CustomEmployeeResp record);
	
	/**
	 * 
	* @Title: auditFinish  
	* @Description: 修改流程审批通过,赋值
	* @Params: @param operationId      
	* @Return: void
	* <AUTHOR>
	* @date:2021年7月4日
	* @Throws
	 */
	public void auditFinish(String operationId);
	
	/**
	 * 
	* @Title: synEmployeeMessage  
	* @Description: 同工同酬审批完修改人员档案信息
	* @Params: @param birthday
	* @Params: @param entryDate
	* @Params: @param firstEducationType
	* @Params: @param firstEducationFile
	* @Params: @param zyfj
	* @Params: @param idCardFile
	* @Params: @param xuexinNetFile      
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月7日
	* @Throws
	 */
	void synEmployeeMessage(String birthday,String entryDate,String firstEducationType,String firstEducationFile,String zyfj,String idCardFile,String xuexinNetFile,String userCode);
	
	
	/**
	 * 
	* @Title: getEmployeeFields  
	* @Description: 获取需要导出的字段
	* @Params: @return      
	* @Return: List<Map<String,String>>
	* <AUTHOR>
	* @date:2021年11月12日
	* @Throws
	 */
	List<Map<String, String>> getEmployeeFields();

	/**
	 * 经开离职原因
	 * @return
	 */
	List<Map<String, String>> getJklzyy();
	
	//退休时间
	List<Map<String, String>> getJkTx();

    List<Map<String, String>> getEmployeeJsda(String employeeId);

	Map<String, Object> authStatusByEmployeeId(String employeeId);

	Integer storage(String str, String employeeId);

	EmployeeStorageData getstorage(String employeeId);

	Integer updateDetailsStatus(List<CommEmployeeUpdateDetail> list);

	Map<String,String>  getEmployeeTask(String employeeNo);
	
	Map<String,Object> checkSave(CustomEmpModel record);

	void sendEmployeeFlow(List<CommEmployeeUpdateDetail> list, String employeeId);

	List<HistoricalListResp> getHistoricalRecords(String employeeId);

    void exportReport(Page page, HttpServletResponse response, HrmsEmployee record);

	List<Map<String, String>> getEmployeePageListReport(Page page, HrmsEmployee record);
}
