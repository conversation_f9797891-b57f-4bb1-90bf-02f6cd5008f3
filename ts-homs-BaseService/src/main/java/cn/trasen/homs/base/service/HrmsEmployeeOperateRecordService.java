package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.HrmsEmployeeOperateRecord;

/**   
 * @Title: HrmsEmployeeOperateRecordService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 员工操作记录 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月29日 下午5:08:56 
 * @version V1.0   
 */
public interface HrmsEmployeeOperateRecordService {

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月29日 下午5:10:07
	 */
	int batchInsert(List<HrmsEmployeeOperateRecord> list);


}
