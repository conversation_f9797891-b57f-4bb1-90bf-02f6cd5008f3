package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.trasen.homs.base.bo.IntroductionPublicBO;
import cn.trasen.homs.base.dto.BelongingSystemDTO;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.vo.BelongingSystemVO;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.trasen.BootComm.utils.RedisUtil;
import cn.trasen.homs.base.mapper.DictItemMapper;
import cn.trasen.homs.base.mapper.DictTypeMapper;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.DictType;
import cn.trasen.homs.base.service.IDictTypeService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * @Description: 字典类型Impl层
 * @Date: 2020/4/29 10:44
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
@Slf4j
public class DictTypeServiceImpl implements IDictTypeService {

    @Autowired
    private DictTypeMapper dictTypeMapper;

    @Autowired
    private DictItemMapper dictItemMapper;

    @Autowired
    private IDictItemService dictItemService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * @Author: Lizhihuo
     * @Description: 增加
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(DictType entity) {
        int count;
        //可多机构
        if (StringUtils.isEmpty(entity.getSsoOrgCode())){
            entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        }
        //判断是否重复
        Example example = new Example(DictType.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isEmpty(entity.getIsDeleted())) {
            criteria.andEqualTo("isDeleted", Contants.IS_DELETED_TURE);
        }
        criteria.andEqualTo("typeCode", entity.getTypeCode());
        //多机构
        criteria.andEqualTo("ssoOrgCode", entity.getSsoOrgCode());
        List<DictType> dictTypeList = dictTypeMapper.selectByExample(example);
        if (dictTypeList.size() == 0) {
            String versionId = String.valueOf(IdWork.id.nextId());
            entity.setId(versionId);
            if (StringUtils.isEmpty(entity.getIsDeleted())) {
                entity.setIsDeleted(Contants.IS_DELETED_FALSE);
            }
            entity.setCreateDate(new Date());
            entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
            entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
            entity.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
            entity.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
            entity.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
            //dossierCategory.setOrgCode(UserInfoHolder.getCurrentUserInfo().getOrgRang());
            count = dictTypeMapper.insertSelective(entity);
        } else {
            count = 0;
        }
        return count;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(DictType entity) {
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        return dictTypeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(DictType entity) {
        DictType dictType = dictTypeMapper.selectByPrimaryKey(entity.getId());
        dictType.setIsDeleted(Contants.IS_DELETED_TURE);

        //先删除redis缓存
        redisUtil.del(dictType.getTypeCode() + dictType.getSsoOrgCode());

        /*Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("dicTypeId", entity.getId());
        dictItemMapper.deleteByExample(example);*/
        return dictTypeMapper.updateByPrimaryKeySelective(dictType);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 启用
     * @Date: 2020/5/16 11:14
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int dictTypeEnable(DictType entity) {
        //校验是否有相同类型编码已启用,已有开启则不允许开启相同类型编码字典
        DictType dictType1 = dictTypeMapper.selectByPrimaryKey(entity.getId());
        Example example1 = new Example(DictType.class);
        Example.Criteria criteria = example1.createCriteria();
        criteria.andEqualTo("typeCode", dictType1.getTypeCode())
                .andEqualTo("ssoOrgCode", dictType1.getSsoOrgCode())
                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        if (dictTypeMapper.selectCountByExample(example1) > 0){
            throw new BusinessException("存在已启用相同类型编码字典");
        }
        DictType dictType = dictTypeMapper.selectByPrimaryKey(entity.getId());
        dictType.setIsDeleted(Contants.IS_DELETED_FALSE);//启用
        int count = dictTypeMapper.updateByPrimaryKeySelective(dictType);
        if (count > 0) {
            Example example = new Example(DictItem.class);
            example.createCriteria().andEqualTo("dicTypeId", entity.getId());
            List<DictItem> dictItemList = dictItemMapper.selectByExample(example);
            //先删除redis缓存
            redisUtil.del(dictType.getTypeCode()+dictType.getSsoOrgCode());

            //放入缓存
            redisUtil.set(dictType.getTypeCode()+dictType.getSsoOrgCode(), JSON.toJSON(dictItemList).toString());
        }
        return count;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    public List<DictType> getDataList(Page page, DictType entity) {
        //多机构字典公共库引用去除已引用
        if(StringUtils.isNotEmpty(entity.getBelonging())){
            Example example = new Example(DictType.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("ssoOrgCode", entity.getBelonging());
            //查出当前已选机构已引用字典类型编码
            List<String> typeCodeList = dictTypeMapper.selectByExample(example).stream().map(DictType::getTypeCode).collect(Collectors.toList());
            entity.setTypeCodeList(typeCodeList);
        }
        //jqgrid传过来是驼峰命名  导致数据库字段无法识别 做一个转换操作
        if ("createDate".equals(page.getSidx())) {
            page.setSidx("CREATE_DATE");
        }
        //如果传了机构编号则按指定机构编号过滤
        if (StringUtils.isEmpty(entity.getSsoOrgCode())){
            entity.setSsoOrgCodeUser(UserInfoHolder.getCurrentUserCorpCode());
        }
        List<DictType> dictTypeList = dictTypeMapper.selectByExamplePage(page, entity);
        for (DictType dictType : dictTypeList) {
            //先删除redis缓存
            redisUtil.del(dictType.getTypeCode()+dictType.getSsoOrgCode());
            Example example1 = new Example(DictItem.class);
            example1.createCriteria().andEqualTo("dicTypeId", dictType.getId());
            List<DictItem> dictItemList = dictItemMapper.selectByExample(example1);

            //放入缓存
            redisUtil.set(dictType.getTypeCode()+dictType.getSsoOrgCode(), JSON.toJSON(dictItemList).toString());
        }
        return dictTypeList;
    }


    /**
     * 根据条件获取所属系统下拉
     *
     * @return
     */
    @Override
    public List<BelongingSystemVO> getBelongingSystem(BelongingSystemDTO dto) {
        List<BelongingSystemVO> list = Lists.newArrayList();
        list.add(new BelongingSystemVO().setName("全部").setKey("ALL"));
        //根据条件过滤
        Example example = new Example(DictType.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ssoOrgCode", dto.getOrgCode());
        if (StringUtils.isNotEmpty(dto.getSysCode())) {
            criteria.andLike("sysCode", "%" + dto.getSysCode() + "%");
        }
        example.orderBy("updateDate").desc();
        dictTypeMapper.selectByExample(example).stream().map(DictType::getSysCode).distinct().forEach(d -> {
            list.add(new BelongingSystemVO().setName(d).setKey(d));
        });
        return list;
    }

    @Override
    @Transactional(readOnly = false)
    public String addIntroductionPublic(IntroductionPublicBO bo) {
        Example example = new Example(DictType.class);
        example.createCriteria().andIn("id", bo.getIds());
        dictTypeMapper.selectByExample(example).forEach(d ->{
            //根据COMM_DICT_TYPE历史id查出COMM_DICT_ITEM数据复制一份
            Example dictItemExample = new Example(DictItem.class);
            dictItemExample.createCriteria().andEqualTo("dicTypeId", d.getId());
            List<DictItem> dictItems = dictItemMapper.selectByExample(dictItemExample);
            //赋值新机构编号
            d.setSsoOrgCode(bo.getSsoOrgCode());
            //复制一份COMM_DICT_TYPE至指定机构
            int insert = insert(d);
            if (insert > 0) {
                //生成
                dictItems.forEach(i -> {
                    //绑定新COMM_DICT_TYPE.id
                    i.setDicTypeId(d.getId());
                    //赋值新机构编号
                    i.setSsoOrgCode(bo.getSsoOrgCode());
                    dictItemService.insert(i);
                });
            }
        });
        return "true";
    }

    @Override
    @Transactional(readOnly = false)
    public Boolean dictTypeData(String ssoOrgCode){
        Example example = new Example(DictType.class);
        example.createCriteria().andEqualTo("ssoOrgCode", "*PUBLIC*");
        //查出公共库主键
        List<String> ids = dictTypeMapper.selectByExample(example).stream().map(DictType::getId).collect(Collectors.toList());
        IntroductionPublicBO bo = new IntroductionPublicBO().setIds(ids).setSsoOrgCode(ssoOrgCode);
        //插入数据
        String s = addIntroductionPublic(bo);
        if (!s.equals("true")){
            log.error("新增机构字典数据初始化失败");
            return false;
        }
        return true;
    }

    @Override
    @Transactional(readOnly = false)
    public DictType getInfoByTypeCode(String typeCode) {
        // 参数校验
        if (StringUtil.isEmpty(typeCode)) {
            throw new RuntimeException("字典类型编码不能为空");
        }

        // 获取当前用户信息
        String currentOrgCode = UserInfoHolder.getCurrentUserInfo().getCorpcode();

        // 1. 优先查询本机构的字典主数据
        Example selfExample = new Example(DictType.class);
        selfExample.createCriteria()
                .andEqualTo("typeCode", typeCode)
                .andEqualTo("ssoOrgCode", currentOrgCode)
                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        List<DictType> selfDictTypeList = dictTypeMapper.selectByExample(selfExample);
        if (!selfDictTypeList.isEmpty()) {
            return selfDictTypeList.get(0);
        }

        // 2. 查询其他机构的字典主数据
        Example otherExample = new Example(DictType.class);
        otherExample.createCriteria()
                .andEqualTo("typeCode", typeCode)
                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        List<DictType> otherDictTypeList = dictTypeMapper.selectByExample(otherExample);
        if (!otherDictTypeList.isEmpty()) {
            DictType source = otherDictTypeList.get(0);
            DictType target = new DictType();
            org.springframework.beans.BeanUtils.copyProperties(source, target, "id", "createUser", "createUserName", "createDate", "updateUser", "updateUserName", "updateDate", "isDeleted","orgCode","hospCode","createDept","createDeptName","ssoOrgCode","ssoOrgName");
            target.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            target.setSsoOrgName(UserInfoHolder.getCurrentUserName());
            insert(target);
            return target;
        }
        return null;
    }

}