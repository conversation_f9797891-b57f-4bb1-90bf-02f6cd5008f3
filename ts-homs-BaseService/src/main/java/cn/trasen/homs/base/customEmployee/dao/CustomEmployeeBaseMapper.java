package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.customEmployee.model.CommContact;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeDetailsModel;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeBaseMapper extends Mapper<CustomEmployeeBase> {

	List<Map<String, String>> getDataSetList(Page page, CustomEmployeeInfo record);

	List<Map<String, String>> findByEmployeeId(CustomEmployeeDetailsModel customEmployeeModel);

//	Map<String, Object> getEmpFlowStatus(@Param("employeeId")String employeeId);
	Map<String, Object> getEmpFlowStatus(@Param("employeeId")String employeeId, @Param("archivesType")String archivesType, @Param("ssoOrgCode")String ssoOrgCode);

	List<CustomEmployeeBase> getEmployeeBasePageList(Page page, CustomEmployeeBase record);

	void calculationEmployeeMessage(CustomEmployeeInfo employee);

	List<CustomEmployeeInfo> getCalculationEmployeeMessageInfo();

	Map<String, String> getEmployeeTask(String employeeNo);

	String selectTableNameByFieldName(String fieldName);

	void saveContact(CommContact commContact);

	List<Map<String, Object>> getCommContact(Page page,CommContact commContact);
	
	int updateAgentTimeById(CustomEmployeeBase record);

	/**
	 * 科主任可以查看自己管理的科室
	 * @param currentUserCode
	 * @param ssoOrgCode
	 * @return
	 */
	List<String> selectManageDept(@Param("currentUserCode") String currentUserCode, @Param("ssoOrgCode") String ssoOrgCode);

	List<CustomEmployeeBase> getEmployeeBaseList(CustomEmployeeBase record);
}