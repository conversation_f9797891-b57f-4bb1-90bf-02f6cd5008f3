package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.base.bean.RolePageRes;
import cn.trasen.homs.base.mapper.RolePageMapper;
import cn.trasen.homs.base.model.RolePage;
import cn.trasen.homs.base.service.RolePageService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/11/25 13:39
 */

@Service
public class RolePageServiceImpl implements RolePageService {
    @Autowired
    RolePageMapper rolePageMapper;


    @Override
    /**
     * 获取角色页面
     * @return cn.trasen.basicsbottom.bean.RolePageRes
     * <AUTHOR>
     * @date 2021/11/25 13:47
     */
    public RolePageRes getRolePage() {

        Example example = new Example(RolePage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.setOrderByClause("sort_no ASC");


      //  System.out.println(UserInfoHolder.getCurrentUserInfo().getSysRoleCode() + "------");

        List<RolePage> rolePageList = rolePageMapper.selectByExample(example);

        String toPage = "";

        int i = 0;
        for (RolePage rolePage : rolePageList) {
            if (i == 0) {
                toPage = rolePage.getPageUrl();
            }
            if (null != UserInfoHolder.getCurrentUserInfo() && (","+UserInfoHolder.getCurrentUserInfo().getSysRoleCode()+",").indexOf(","+rolePage.getRoleCode()+",")>=0) {
                toPage = rolePage.getPageUrl();
                break;
            }
            i++;
        }
        RolePageRes rolePageRes = new RolePageRes();
        rolePageRes.setPageUrl(toPage);
        return rolePageRes;
    }
}