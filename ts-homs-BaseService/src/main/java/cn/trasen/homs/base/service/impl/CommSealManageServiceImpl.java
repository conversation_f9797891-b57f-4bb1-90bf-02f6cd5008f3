package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.base.dao.CommSealManageMapper;
import cn.trasen.homs.base.model.CommSealManage;
import cn.trasen.homs.base.service.CommSealManageService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommSealManageServiceImpl
 * @Description TODO
 * @date 2025��6��21�� ����5:44:39
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommSealManageServiceImpl implements CommSealManageService {

	@Autowired
	private CommSealManageMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommSealManage record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommSealManage record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CommSealManage record = new CommSealManage();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CommSealManage selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommSealManage> getDataSetList(Page page, CommSealManage record) {
		Example example = new Example(CommSealManage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		if(StrUtil.isNotBlank(record.getDeptName())) {
			criteria.andLike("deptName", "%" + record.getDeptName() + "%");
		}
		
		if(StrUtil.isNotBlank(record.getSealName())) {
			criteria.andLike("sealName", "%" + record.getSealName() + "%");
		}
		
		if(StrUtil.isNotBlank(record.getSealUser())) {
			criteria.andCondition(" find_in_set('" + record.getSealUser() + "',seal_user)");
		}
			
		List<CommSealManage> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<CommSealManage> getCommSealManageList() {
		Example example = new Example(CommSealManage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition(" find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',seal_user)");
		
		return mapper.selectByExample(example);
	}
	
	
}
