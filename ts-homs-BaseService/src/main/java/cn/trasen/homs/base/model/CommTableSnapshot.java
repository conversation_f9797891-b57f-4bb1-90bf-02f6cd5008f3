package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_table_snapshot")
@Setter
@Getter
public class CommTableSnapshot {
    @Id
    private String id;

    /**
     * 表名称
     */
    @Column(name = "table_name")
    @ApiModelProperty(value = "表名称")
    private String tableName;

    /**
     * 行主键值
     */
    @Column(name = "row_pk_value")
    @ApiModelProperty(value = "行主键值")
    private String rowPkValue;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 行记录旧
     */
    @Column(name = "row_json_old")
    @ApiModelProperty(value = "行记录旧")
    private String rowJsonOld;

    /**
     * 行记录新
     */
    @Column(name = "row_json_new")
    @ApiModelProperty(value = "行记录新")
    private String rowJsonNew;
}