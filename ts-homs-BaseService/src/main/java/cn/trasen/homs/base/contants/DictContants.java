package cn.trasen.homs.base.contants;

/**   
 * @Title: DictContants.java 
 * @Package cn.trasen.hrms.contants 
 * @Description: 数据字典常量类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月26日 下午3:47:13 
 * @version V1.0   
 */
public class DictContants {

	/** 员工状态 **/
	public static final String EMPLOYEE_STATUS = "employee_status";
	/** 员工类别 **/
	public static final String EMPLOYEE_CATEGORY = "employee_category";
	/** 岗位类别 **/
	public static final String POST_CATEGORY = "post_category";
	/** 薪级类别 **/
	public static final String SALARY_LEVEL_CATEGORY = "salary_level_category";
	/** 民族名称 **/
	public static final String NATIONALITY_NAME = "nationality_name";
	/** 政治面貌 **/
	public static final String POLITICAL_STATUS = "political_status";
	/** 婚姻状况 **/
	public static final String MARRIAGE_STATUS = "marriage_status";
	/** 用工性质 **/
	public static final String EMPLOYMENT_NATURE = "employment_nature";
	/** 个人身份 **/
	public static final String PERSONAL_IDENTITY = "personal_identity";
	/** 健康状况 **/
	public static final String HEALTH_STATUS = "health_status";
	/** 编制类型 **/
	public static final String ESTABLISHMENT_TYPE = "establishment_type";
	/** 学历类型 **/
	public static final String EDUCATION_TYPE = "education_type";
	/** 学习方式 **/
	public static final String LEARN_WAY = "learn_way";
	/** 学制 **/
	public static final String SCHOOLSYSTEM_YEARS = "schoolsystem_years";
	/** 证书类型 **/
	public static final String CERTIFICATE_TYPE = "certificate_type";
	/** 证书获得方式 **/
	public static final String CERTIFICATE_OBTAIN_TYPE = "certificate_obtain_type";
	/** 人员关系 **/
	public static final String PERSONNEL_RELATIONSHIP = "personnel_relationship";
	/** 单位类别 **/
	public static final String UNIT_CATEGORY = "unit_category";
	/** 奖惩类别 **/
	public static final String REWARDS_PUNISHMENTS_TYPE = "rewards_punishments_type";
	/** 血型 **/
	public static final String BLOOD_GROUP = "blood_group";
	/** 奖惩类型 **/
	public static final String REWARD_PENALTY_TYPE = "reward_penalty_type";
	/** 劳动合同类型 **/
	public static final String LABOR_CONTRACT_TYPE = "labor_contract_type";
	/** 简历库来源渠道 **/
	public static final String RESUME_SOURCE_CHANNEL = "resume_source_channel";
	/** 考核结果 **/
	public static final String EVALUATION_RESULT = "evaluation_result";
	/** 讲师类型 **/
	public static final String LECTURER_TYPE = "lecturer_type";
	/** 审核科室 **/
	public static final String REVIEW_DEPART = "review_depart";
	/** 学位类型**/
	public static final String EDUCATION_DEGREE = "education_degree";
	/**第一学历**/
	public static final String FIRST_EDUCATION_TYPE = "first_education_type";
	/** 岗位描述 **/
	public static final String JOB_DESCRIPTION_TYPE = "job_deion_type";
	
	public static final String POST_TYPE = "post_type";
	
	public static final String AUTHORIZED_ORG = "authorized_org";  //编制所属机构
	
	public static final String OPERATION_TYPE = "operation_type";  //执业类别	
	
	public static final String HOSP_AREA = "hosp_area";  //院区
	
	public static final String CUSTOM_CODE = "CUSTOM_CODE";  //客户定制化参数

}
