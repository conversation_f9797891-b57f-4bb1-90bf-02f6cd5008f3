package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.bean.ResetJobNumberReq;
import cn.trasen.homs.base.bo.EmployeeDetails;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeInfoMapper extends Mapper<CustomEmployeeInfo> {

	CustomEmployeeInfo findByEmployeeId(@Param("employeeId")String employeeId, @Param("ssoOrgCode") String ssoOrgCode);

	CustomEmployeeInfo findByEmployeeNo(@Param("employeeNo")String employeeNo);

	void updateHrmsEmpNoByid(ResetJobNumberReq resetJobNumberReq);

	void updateThpsEmpNoByid(ResetJobNumberReq resetJobNumberReq);

	void updateHrmsEmpBecome(@Param("nowEmployeeNo")String nowEmployeeNo, @Param("employeeNo")String employeeNo);
	
	List<EmployeeDetails> getLyzXuexi(@Param("employeeId") String employeeId, @Param("ssoOrgCode") String ssoOrgCode);

    List<EmployeeDetails> getLyzYuanwai(String employeeId);

    List<EmployeeDetails> getLyzYuannei(String employeeId);

    List<EmployeeDetails> getLyzZhicheng(String employeeId);

    List<EmployeeDetails> getLyzJinxiu(String employeeId);

    List<EmployeeDetails> getLyzGuipei(String employeeId);

	List<Map<String, Object>> getKhjg(String employeeId);

    List<Map<String, Object>> getKylw(String employeeId);

    List<Map<String, Object>> getJcjl(String employeeId);

    List<Map<String, Object>> getJxjl(String employeeId);

    List<Map<String, Object>> getZyysgfhpx(String employeeId);

    List<Map<String, Object>> getGpjl(String employeeId);

	List<Map<String, String>> getEmployeeJsda(String employeeId);
}