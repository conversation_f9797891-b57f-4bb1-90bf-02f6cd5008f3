package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "comm_org_operate_record")
@Setter
@Getter
public class HrmsOrgOperateRecord {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "operate_record_id")
    @ApiModelProperty(value = "主键ID")
    private String operateRecordId;

    /**
     * 原机构ID
     */
    @Column(name = "old_org_id")
    @ApiModelProperty(value = "原机构ID")
    private String oldOrgId;

    /**
     * 原机构名称
     */
    @Column(name = "old_org_name")
    @ApiModelProperty(value = "原机构名称")
    private String oldOrgName;

    /**
     * 新机构ID
     */
    @Column(name = "new_org_id")
    @ApiModelProperty(value = "新机构ID")
    private String newOrgId;

    /**
     * 新机构名称
     */
    @Column(name = "new_org_name")
    @ApiModelProperty(value = "新机构名称")
    private String newOrgName;

    /**
     * 操作类型: 1=合并; 2=拆分;
     */
    @Column(name = "operate_type")
    @ApiModelProperty(value = "操作类型: 1=合并; 2=拆分;")
    private String operateType;

    /**
     * 批次号
     */
    @Column(name = "batch_number")
    @ApiModelProperty(value = "批次号")
    private String batchNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
}