package cn.trasen.homs.base.controller;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.PortalTheme;
import cn.trasen.homs.base.service.PortalDefaultService;
import cn.trasen.homs.base.service.PortalThemeService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


@Api(tags = "首页内容管理")
@RestController
@Slf4j
public class PortalThemeController {

	@Resource
	PortalThemeService portalThemeService;
	
	@Resource
	PortalDefaultService portalDefaultService;
	
	/**
	 * 
	 * @Title: save   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param portalTheme
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年6月16日 下午5:53:38       
	 * @throws
	 */
	@ApiOperation(value = "新增首页内容管理", notes = "新增首页内容管理")
    @PostMapping("/portalTheme/save")
    public PlatformResult<String> save(@RequestBody PortalTheme portalTheme) {
    	try {
    		portalThemeService.insert(portalTheme);
	        return PlatformResult.success("新增首页内容管理成功");
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("新增首页内容管理失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: update   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param portalTheme
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年6月17日 下午2:07:00       
	 * @throws
	 */
	@ApiOperation(value = "更新页内容管理", notes = "更新页内容管理")
    @PostMapping("/portalTheme/update")
    public PlatformResult<String> update(@RequestBody PortalTheme portalTheme) {
    	try {
    		portalThemeService.update(portalTheme);
	        return PlatformResult.success("更新首页内容管理成功");
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("更新首页内容管理失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: delete   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param id
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年6月18日 上午9:43:46       
	 * @throws
	 */
	@ApiOperation(value = "删除内容管理", notes = "删除内容管理")
    @PostMapping("/portalTheme/delete")
    public PlatformResult<String> delete(@RequestParam("id")String id) {
    	try {
    		portalThemeService.delete(id);
	        return PlatformResult.success("删除内容管理成功");
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("删除内容管理失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: selectById   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param id
	 * @param: @return      
	 * @return: PlatformResult<PortalTheme>  
	 * @author: Yuec    
	 * @date:   2021年6月17日 下午2:31:51       
	 * @throws
	 */
	@ApiOperation(value = "查询首页内容管理", notes = "查询首页内容管理")
    @PostMapping("/portalTheme/selectById")
    public PlatformResult<PortalTheme> selectById(@RequestParam("id")String id) {
    	try {
    		PortalTheme portalTheme = portalThemeService.selectById(id);
	        return PlatformResult.success(portalTheme);
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("更新首页内容管理失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: selectDefaultPortalTheme   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年6月17日 下午2:28:13       
	 * @throws
	 */
	@ApiOperation(value = "查询首页内容管理(默认)", notes = "查询首页内容管理(默认)")
    @PostMapping("/portalTheme/selectDefaultPortalTheme")
    public PlatformResult<PortalTheme> selectDefaultPortalTheme(String themeId) {
    	try {
    		PortalTheme portalTheme = portalThemeService.selectDefaultPortalTheme(themeId);
	        return PlatformResult.success(portalTheme);
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("查询首页内容管理(默认)失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: selectPortalThemeList   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param portalTheme
	 * @param: @return      
	 * @return: PlatformResult<List<PortalTheme>>  
	 * @author: Yuec    
	 * @date:   2021年6月17日 下午5:12:48       
	 * @throws
	 */
	@ApiOperation(value = "查询首页内容列表", notes = "查询首页内容列表")
    @PostMapping("/portalTheme/selectPortalThemeList")
    public PlatformResult<List<PortalTheme>> selectPortalThemeList(@RequestBody PortalTheme portalTheme) {
    	try {
    		List<PortalTheme> list = portalThemeService.selectPortalThemeList(portalTheme);
	        return PlatformResult.success(list);
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("查询首页内容管理失败，失败原因：" +e.getMessage());
		}
    }
	
	/**
	 * 
	 * @Title: setDefaultTheme   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param themeId
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年6月18日 上午10:33:42       
	 * @throws
	 */
	@ApiOperation(value = "设置默认门户", notes = "设置默认门户")
    @PostMapping("/portalTheme/setDefaultTheme")
    public PlatformResult<String> setDefaultTheme(@RequestParam("themeId")String themeId) {
    	try {
    		portalDefaultService.setDefaultTheme(themeId);
	        return PlatformResult.success("设置默认门户成功");
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure("设置默认门户失败，失败原因：" +e.getMessage());
		}
    }
}
