package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.HrmsJobtitleBasic;
import cn.trasen.homs.base.service.HrmsJobtitleBasicService;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Title: HrmsJobtitleBasicController.java
 * @Package cn.trasen.hrms.controller
 * @Description: 职称基础数据Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年4月23日 上午11:15:56
 * @version V1.0
 */
@Slf4j
@Api(tags = "职称基础数据Controller")
@RestController
public class HrmsJobtitleBasicController {

	@Autowired
	HrmsJobtitleBasicService hrmsJobtitleBasicService;

//	/**
//	 * @Title: insert
//	 * @Description: 新增职称基础数据
//	 * @Param: entity
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "新增职称基础数据", notes = "新增职称基础数据")
//	@PostMapping(value = "/jobtitleBasic/save")
//	public PlatformResult<String> save(@RequestBody HrmsJobtitleBasic entity) {
//		try {
//			if (hrmsJobtitleBasicService.insert(entity) > 0) {
//				return PlatformResult.success();
//			}
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}
//
//	/**
//	 * @Title: update
//	 * @Description: 修改职称基础数据
//	 * @Param: entity
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "修改职称基础数据", notes = "修改职称基础数据")
//	@PostMapping(value = "/jobtitleBasic/update")
//	public PlatformResult<String> update(@RequestBody HrmsJobtitleBasic entity) {
//		try {
//			if (hrmsJobtitleBasicService.update(entity) > 0) {
//				return PlatformResult.success();
//			}
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}
//
//	/**
//	 * @Title: deleteById
//	 * @Description: 删除职称基础数据
//	 * @Param: accountId 职称基础数据主键ID
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "删除职称基础数据", notes = "删除职称基础数据")
//	@PostMapping(value = "/jobtitleBasic/deletedById/{jobtitleBasicId}")
//	@ResponseBody
//	public PlatformResult<String> deleteById(@PathVariable String jobtitleBasicId) {
//		try {
//			hrmsJobtitleBasicService.deleted(jobtitleBasicId);
//			return PlatformResult.success();
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//			return PlatformResult.failure();
//		}
//	}

//	/**
//	 * @Title: getDataList
//	 * @Description: 查询职称信息列表(分页)
//	 * @param page
//	 * @param entity
//	 * @Return DataSet<HrmsJobtitleBasic>
//	 * <AUTHOR>
//	 * @date 2020年6月12日 下午2:27:18
//	 */
//	@ApiOperation(value = "查询职称信息列表(分页)", notes = "查询职称信息列表(分页)")
//	@PostMapping(value = "/jobtitleBasic/getDataList")
//	public DataSet<HrmsJobtitleBasic> getDataList(Page page, HrmsJobtitleBasic entity) {
//		List<HrmsJobtitleBasic> list = hrmsJobtitleBasicService.getDataList(page, entity);
//		return new DataSet<HrmsJobtitleBasic>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
//	}

	/**
	 * @Title: getJobTitleCategoryList
	 * @Description: 查询职称类别列表(不分页)
	 * @param entity
	 * @Return PlatformResult<List<HrmsJobtitleBasic>>
	 * <AUTHOR>
	 * @date 2020年4月23日 上午11:44:36
	 */
	@ApiOperation(value = "查询职称类别列表(不分页)", notes = "查询职称类别列表(不分页)")
	@PostMapping(value = "/jobtitleBasic/getJobTitleCategoryList")
	public PlatformResult<List<HrmsJobtitleBasic>> getJobTitleCategoryList(@RequestBody HrmsJobtitleBasic entity) {
		try {
			return PlatformResult.success(hrmsJobtitleBasicService.getJobtitleCategory(entity));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	/**
	 * 
	 * @Title: getJobTitleLevelList
	 * @Description: 根据Pid查询职称基础数据列表
	 * @param entity
	 * @Return PlatformResult<List<HrmsJobtitleBasic>>
	 * <AUTHOR>
	 * @date 2020年4月23日 上午11:50:40
	 */
	@ApiOperation(value = "根据Pid查询职称基础数据列表", notes = "根据Pid查询职称基础数据列表")
	@PostMapping(value = "/jobtitleBasic/getListByPid")
	public PlatformResult<List<HrmsJobtitleBasic>> getListByPid(@RequestBody HrmsJobtitleBasic entity) {
		try {
			return PlatformResult.success(hrmsJobtitleBasicService.getListByPid(entity));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	/**
	 * @Title: getJobtitleTree
	 * @Description: 获取职称树
	 * @Return PlatformResult<LinkedList<TreeModel>>
	 * <AUTHOR>
	 * @date 2020年6月12日 上午11:12:10
	 */
//	@ApiOperation(value = "获取职称树", notes = "获取职称树")
//	@PostMapping(value = "/jobtitleBasic/getJobtitleTree")
//	public PlatformResult<List<TreeModel>> getJobtitleTree() {
//		try {
//			hrmsJobtitleBasicService.getJobtitleTree();
//			return PlatformResult.success();
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}


	/**
	 * @Title: getJobtitleTree
	 * @Description: 获取职称树
	 * @Return PlatformResult<LinkedList<TreeModel>>
	 * <AUTHOR>
	 * @date 2020年6月12日 上午11:12:10
	 */
	@ApiOperation(value = "获取职称列表", notes = "获取职称列表")
	@PostMapping(value = "/jobtitleBasic/getJobtitleBasicList")
	public PlatformResult<List<HrmsJobtitleBasic>> getJobtitleBasicList(@RequestBody HrmsJobtitleBasic entity) {
		try {
			return PlatformResult.success(hrmsJobtitleBasicService.getJobtitleBasicList(entity));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

}
