package cn.trasen.homs.base.bean;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/7/27 17:16
 * @description
 */
@Data
public class OrganizationLeaderResp {

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "员工")
    private List<HrmsEmployeeResp> employeeIdList;

    @ApiModelProperty(value = "同步下级部门1同步0不同步")
    private Integer synNextOrg;

    @ApiModelProperty(value = "是否显示组织架构图 1显示0不显示")
    private Integer showFramework;


    @ApiModelProperty(value = "排序 小到大")
    private Integer sortNo;


}