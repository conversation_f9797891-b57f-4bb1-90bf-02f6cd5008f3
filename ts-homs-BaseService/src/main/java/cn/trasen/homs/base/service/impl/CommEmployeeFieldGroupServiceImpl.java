/**
 * @Title: CommEmployeeFieldGroupServiceImpl.java  
 * @Package: cn.trasen.homs.base.service.impl  
 * @Date: 2021��6��15��
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service.impl;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.dao.CommCityMapper;
import cn.trasen.homs.base.dao.CommEmployeeFieldGroupMapper;
import cn.trasen.homs.base.model.*;
import cn.trasen.homs.base.service.*;
import cn.trasen.homs.base.utils.JdbcTemplateUtil;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName: CommEmployeeFieldGroupServiceImpl
 * @Author: 86189
 * @Date: 2021��6��15��
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class CommEmployeeFieldGroupServiceImpl implements CommEmployeeFieldGroupService {

    @Autowired
    private CommEmployeeFieldGroupMapper commEmployeeFieldGroupMapper;

    @Autowired
    private CommEmployeeFieldService commEmployeeFieldService;

    @Autowired
    private JdbcTemplateUtil jdbcTemplateUtil;

    @Autowired
    private IDictItemService dictItemService;
    
    @Autowired
    private CommCityMapper commCityMapper;
    
    @Autowired
    private HrmsOrganizationService   hrmsOrganizationService;
    
    @Autowired
    private GlobalSettingsService globalSettingsService;
    
    public static final String regEx = "[\\s~·`!！@#￥$%^……&*（()）\\-——\\-_=+【\\[\\]】｛{}｝\\|、\\\\；;：:‘'“”\"，,《<。.》>、/？?]";

    /**
     * 
     * @Title: insert
     * @Description: 新增自定义人员档案分组
     * @param @param
     *            record
     * @param @return
     * @return int
     * @throws <AUTHOR>             jyq#trasen.cn
     * @date 2019��7��23�� ����5:33:54
     */
    @Transactional(readOnly = false)
    @Override
    public int insert(CommEmployeeFieldGroup record) {

        record.setCreateDate(new Date());
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        record.setCreateUserName(UserInfoHolder.getCurrentUserName());
        record.setId(ApplicationUtils.GUID32());
        record.setIsDeleted(Contants.IS_DELETED_FALSE);
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isBlank(record.getTableName())) {

            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");

            String firstName = PinYinUtil.converterToFirstSpell(record.getGroupName());

            if (firstName.contains(",")) {
                firstName = firstName.split(",")[0];
            }

            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(firstName);
            firstName = m.replaceAll("");

            String tableName = "emp_" + firstName + sf.format(new Date());

            record.setTableName(tableName);

            // 创建表
            jdbcTemplateUtil.createTable(record);

        }

        return commEmployeeFieldGroupMapper.insertSelective(record);
    }
    
    public boolean checkName(CommEmployeeFieldGroup record) {
        
        Example example = new Example(record.getClass());
        
        example.createCriteria().andEqualTo("groupName",record.getGroupName());
        
        if(StringUtils.isNotBlank(record.getId())) {
            
            example.and().andNotEqualTo("id", record.getId());
        }
        
        List< CommEmployeeFieldGroup> list = commEmployeeFieldGroupMapper.selectByExample(example);
        
        if(CollectionUtils.isNotEmpty(list)){
            return false;
        }else {
            return true;
        }
    }
    

    /**
     * 
     * @Title: update
     * @Description: 修改自定义人员档案分组
     * @param @param
     *            record
     * @param @return
     * @return int
     * @throws <AUTHOR>             jyq#trasen.cn
     * @date 2019��7��23�� ����5:34:01
     */
    @Override
    @Transactional(readOnly = false)
    public int update(CommEmployeeFieldGroup record) {

        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());

        return commEmployeeFieldGroupMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 
     * @Title: deleted
     * @Description:删除自定义人员档案分组
     * @param @param
     *            id
     * @param @return
     * @return int
     * @throws <AUTHOR>             jyq#trasen.cn
     * @date 2019��7��23�� ����5:34:08
     */
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {

        CommEmployeeFieldGroup record = new CommEmployeeFieldGroup();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setIsDeleted(Contants.IS_DELETED_TURE);
        return commEmployeeFieldGroupMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 
     * @Title: getList
     * @Description: 获取自定义人员档案分组列表
     * @param @param
     *            record
     * @param @param
     *            page
     * @param @return
     * @return List<CommEmployeeFieldGroup>
     * @throws <AUTHOR>             jyq#trasen.cn
     * @date 2019��7��23�� ����5:34:26
     */
    @Override
    public List<CommEmployeeFieldGroup> getFieldAndJurisdictionListByGroupid() {

        Example example = new Example(CommEmployeeFieldGroup.class);

        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

        example.orderBy("seq").asc();

        CommEmployeeFieldGroup record = new CommEmployeeFieldGroup();

        record.setUserCode(UserInfoHolder.getCurrentUserCode());
        
        
        Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
        Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
        boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");
        boolean isRemoveCheck = UserInfoHolder.getRight("ARCHIVES_REMOVE_CHECK");//去除必填校验
        
        List<CommEmployeeFieldGroup> list = new ArrayList<CommEmployeeFieldGroup>();
        
        if(isAdmin || isALL || isInfoAdmin) {
            
            list = commEmployeeFieldGroupMapper.getGroupListById(record);
            
            
        }else {
            
            list = commEmployeeFieldGroupMapper.getGroupAndJurisdictionListById(record);
        }
        
/*      GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
        String orgCode = globalSetting.getOrgCode();
        String code = "jssrmyy,jsxzyyy,cssdeshfly,xdyy";
        if (!code.contains(orgCode)) {
            for (CommEmployeeFieldGroup group : list) {
                // 进修,规培,学习和会议先写死不让编辑,由流程那边同步
                if (StringUtils.isNotBlank(group.getDefaultCondition())) {
                    group.setIsEdit(0);
                }
            }
        }*/

        // 根据字段分组id查询字段
        for (CommEmployeeFieldGroup group : list) {

            List<CommEmployeeField> fields = new ArrayList<>();
            
            if(isAdmin || isALL || isInfoAdmin || isRemoveCheck) {
                CommEmployeeField commEmployeeField =new CommEmployeeField();
                commEmployeeField.setIsHide(0);
                commEmployeeField.setGroupId(group.getId());
                
                fields = commEmployeeFieldService.getFielListByGroupid(commEmployeeField);

                //平江去掉必填
                GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
                String orgCode = globalSetting.getOrgCode();
                if("pjxdyrmyy".equals(orgCode)){
                    fields.forEach(item ->{     //去掉所有必填
                        item.setIsMust(null);
                    });
                }else {
                	if(isAdmin || isALL || isRemoveCheck) {
                		 fields.forEach(item ->{     //去掉所有必填
                             item.setIsMust(null);
                         });
                	}
                }
                
            }else {
                fields = commEmployeeFieldService.getFieldAndJurisdictionListByGroupid(group.getId());

                if("1".equals(group.getIsDetailed())){ //不是明细
                    if(null == group.getIsEdit()){  //没有栏目权限
                        fields.forEach(item ->{     //不能编辑
                            item.setIsMust(null);
                        });
                    }
                }
            }
            group.setFields(fields);
        }

        return list;
    }

    /**
     * 
     * @Title: findById
     * @Description:根据id查询自定义人员档案分组
     * @param @param
     *            id
     * @param @return
     *            ����
     * @return CommEmployeeFieldGroup
     * @throws <AUTHOR>             jyq#trasen.cn
     * @date 2019��7��23�� ����5:41:42UpdateOperationMapper.updateByPrimaryKeySelective
     */
    @Override
    public CommEmployeeFieldGroup findById(String id) {

        return commEmployeeFieldGroupMapper.selectByPrimaryKey(id);
    }

    /**
     * 
     * @Title: updateList
     * @Description: 根据列表修改自定义人员档案分组
     * @Params: @param records
     * @Params: @return
     * @Return: String
     * <AUTHOR>
     * @date:2021年6月15日
     * @Throws
     */
    @Override
    @Transactional(readOnly = false)
    public String updateList(List<CommEmployeeFieldGroup> records) {

        if (CollectionUtils.isNotEmpty(records)) {

            for (CommEmployeeFieldGroup record : records) {

                record.setUpdateDate(new Date());
                record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
                commEmployeeFieldGroupMapper.updateByPrimaryKeySelective(record);
            }
        }

        return "";
    }

    /**
     * 
     * @Title: deleteById
     * @Description: 根据id删除
     * @Params: @param id
     * @Params: @return
     * @Return: int
     * <AUTHOR>
     * @date:2021年6月17日
     * @Throws
     */
    @Override
    @Transactional(readOnly = false)
    public int deleteById(String id) {

        CommEmployeeFieldGroup record = commEmployeeFieldGroupMapper.selectByPrimaryKey(id);
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        record.setIsDeleted(Contants.IS_DELETED_TURE);

        return commEmployeeFieldGroupMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 
     * @Title: getList
     * @Description: 获取所有分组
     * @Params: @return
     * @Return: List<CommEmployeeFieldGroup>
     * <AUTHOR>
     * @date:2021年6月25日
     * @Throws
     */
    public List<CommEmployeeFieldGroup> getDataList() {

        Example example = new Example(CommEmployeeFieldGroup.class);

        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

        example.orderBy("seq").asc();

        List<CommEmployeeFieldGroup> list = commEmployeeFieldGroupMapper.selectByExample(example);
        
        CommEmployeeField record =new CommEmployeeField();
    
        record.setIsHide(0);
        
        for (CommEmployeeFieldGroup group : list) {
            
            record.setGroupId(group.getId());

            List<CommEmployeeField> fields = commEmployeeFieldService.getFielListByGroupid(record);
            
            group.setFields(fields);
        }
        
        return list;
    }

    @Override
    public List<Map<String, String>> getGroupListTrends(HrmsEmployee record) {
        // TODO Auto-generated method stub
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<Map<String, String>> list = commEmployeeFieldGroupMapper.getGroupListTrends(record);
        
        // 查询页面字段
                CommEmployeeField commEmployeeField = new CommEmployeeField();
                commEmployeeField.setIsHide(0);
                commEmployeeField.setGroupId(record.getFieldGroupId());
                List<CommEmployeeField>  fieldList  =   commEmployeeFieldService.getFielListByGroupid(commEmployeeField);
                setDictValue(list, fieldList,record.getTableName());
        return list;
    }

    @Override
    public List<CommEmployeeFieldGroup> findByIdList(List<String> ids) {
        Example example = new Example(CommEmployeeFieldGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if (CollectionUtils.isNotEmpty(ids)){
            criteria.andIn("id",ids);
        }
        return commEmployeeFieldGroupMapper.selectByExample(example);
    }

    @Override
    public List<String> getUnfinishResult(String employeeId,String wfInstanceId,List<String> groupIds) {
        return commEmployeeFieldGroupMapper.getUnfinishResult(employeeId,wfInstanceId,groupIds);
    }

    @Override
    public List<CustomEmployeeGroup> getAllIsMustFeild(List<String> groupIds) {
        return commEmployeeFieldGroupMapper.getAllIsMustFeild(groupIds);
    }

    @Override
    public List<CommEmployeeFieldGroup> getGroupDataList() {
        Example example = new Example(CommEmployeeFieldGroup.class);

        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

        example.orderBy("seq").asc();

        List<CommEmployeeFieldGroup> list = commEmployeeFieldGroupMapper.selectByExample(example);

        CommEmployeeField record =new CommEmployeeField();

        record.setIsHide(0);

        List<CommEmployeeFieldGroup> fieldGroupList = new ArrayList<>();

        for (CommEmployeeFieldGroup group : list) {

            record.setGroupId(group.getId());
            //查询必填分组下是否有必填字段
            Integer isMust=commEmployeeFieldGroupMapper.getMustGroupList(group.getId());
            if (isMust > 0){
                List<CommEmployeeField> fields = commEmployeeFieldService.getFielListByGroupid(record);
                group.setFields(fields);
                fieldGroupList.add(group);
            }

        }
        return fieldGroupList;
    }


    /**
     * 
     * @Title: setDictValue
     * @Description: 设置字典值
     * @Params: @param list
     * @Params: @param fieldList
     * @Return: void
     * <AUTHOR>
     * @date:2021年7月1日
     * @Throws
     */
    private void setDictValue(List<Map<String, String>> list, List<CommEmployeeField> fieldList,String tableName) {

        Map<String, Map<String, String>> dictMap = dictItemService.getDictMap();
        Map<String, String> baseJobMap = new HashMap<>();
        if("hrms_jobtitle_info".equals(tableName)){
            // 查询职称 转为字典
            List<Map<String,String>> jobMap = commEmployeeFieldGroupMapper.getJobMap();
            baseJobMap = jobMap.stream().collect(Collectors.toMap(item -> item.get("jobtitle_basic_id"), item -> item.get("jobtitle_basic_name")));

        }

        for (CommEmployeeField field : fieldList) {


            if (StringUtils.isNotBlank(field.getDictSource())) {//处理字典值

                String diceSource = field.getDictSource();

                Map<String, String> itemMap = dictMap.get(diceSource);

                for (Map<String, String> dataMap : list) {

                    for (String key : dataMap.keySet()) {

                        if (key.equals(field.getFieldName())) {

                            if (null != itemMap && null != dataMap.get(key)) {

                                dataMap.put(key, itemMap.get(dataMap.get(key)));
                            }
                        }
                    }
                }
            }else {
                
                if(field.getFieldName().equals("born_address")) {
                    
                    for (Map<String, String> dataMap : list) {

                        for (String key : dataMap.keySet()) {

                            if (key.equals(field.getFieldName())) {

                                String value = dataMap.get(key);
                                
                                if(StringUtils.isNoneBlank(value) && value.contains("-")) {
                                    
                                    String [] arrCodes = value.split("-");
                                    
                                    String cityNames = "";
                                    
                                    for(String cityCode : arrCodes) {
                                        
                                        CommCity commCity = commCityMapper.selectByPrimaryKey(cityCode);
                                        
                                        cityNames +=commCity.getName()+"-";
                                    }
                                    if(StringUtils.isNotBlank(cityNames)) {
                                        
                                        dataMap.put(key, cityNames.substring(0,cityNames.length()-1));
                                    }
                                }
                            }
                        }
                    }
                }


                if(field.getFieldName().equals("jobtitle_category") ||
                            field.getFieldName().equals("jobtitle_level") ||
                            field.getFieldName().equals("jobtitle_name")) {
                        if("hrms_jobtitle_info".equals(tableName)){
                            for (Map<String, String> dataMap : list) {
                                for (String key : dataMap.keySet()) {
                                    if (key.equals(field.getFieldName())) {
                                        dataMap.put(key, baseJobMap.get(dataMap.get(key)));
                                    }
                                }
                            }
                        }
                }
                
                if("deptChose".equals(field.getFieldType())){//处理科室类型字段值
                    for (Map<String, String> dataMap : list) {
                        for (String key : dataMap.keySet()) {
                            if (key.equals(field.getFieldName())) {
                                String value = dataMap.get(key);
                                if(StringUtils.isNoneBlank(value)) {
                                    HrmsOrganization hrmsOrganization = hrmsOrganizationService.findById(value);
                                    if (key.equals(field.getFieldName())) {
                                        if (null != hrmsOrganization && null != dataMap.get(key)) {
                                            dataMap.put(key, hrmsOrganization.getName());
                                        }
                                    }
                                }
                            
                            }
                        }
                    }
                }

                
                
            }
        }
    }

    
}

