package cn.trasen.homs.base.bean;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsWorkRecord {
	
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    @ApiModelProperty(value = "起止时间")
    private Date startTime;
    
    @ApiModelProperty(value = "起止时间")
    private Date endTime;
    
    @ApiModelProperty(value = "工作单位")
    private String workUnit;

    @ApiModelProperty(value = "所在部门")
    private String deptName;

    @ApiModelProperty(value = "岗位")
    private String post;

    @ApiModelProperty(value = "证明人")
    private String witness;
    
    @ApiModelProperty(value = "证明人电话")
    private String witnessPhone;

    @ApiModelProperty(value = "附件")
    private String gzjlfj;
    
}