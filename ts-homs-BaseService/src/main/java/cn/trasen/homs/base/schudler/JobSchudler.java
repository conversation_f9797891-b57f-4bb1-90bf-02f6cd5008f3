package cn.trasen.homs.base.schudler;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.cloud.commons.lang.StringUtils;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.base.bean.HrmsEmployeeSaveReq;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.mapper.CustomEmployeeMapper;
import cn.trasen.homs.base.mapper.HrmsEmployeeMapper;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.SysAccessLogService;
import cn.trasen.homs.base.utils.HisUserBean;
import cn.trasen.homs.base.utils.JDBCUtils;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;

@Service
public class JobSchudler {

	private Logger logger = LoggerFactory.getLogger(JobSchudler.class);
	
	@Autowired
	private CustomEmployeeInfoService hrmsEmployeeService;
	
	@Autowired
	private CustomEmployeeBaseService customEmployeeBaseService;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;
	
	@Autowired
    private SysAccessLogService sysAccessLogService;
	
	@Value("${syncHis2Switch}")
	private String syncHis2Switch;
	
	@Value("${his2username}")
	private String his2username;
	
	@Value("${his2pwd}")
	private String his2pwd;
	
	@Value("${his2url}")
	private String his2url;
	
	@Value("${his2driver}")
	private String his2driver;
	
	@Value("${hospitalId}")
	private String hospitalId;

	/**
	 * 
	 * @Title: calculationEmployeeMessage
	 * @Description: 计算员工信息:生日,工龄,年龄
	 * @Params:
	 * @Return: void
	 * <AUTHOR>
	 * @date:2021年7月22日
	 * @Throws
	 */
	@Scheduled(cron = "0 10 02 * * ?")
	public void calculationEmployeeMessage() {

		List<CustomEmployeeInfo> list = customEmployeeBaseService.getCalculationEmployeeMessageInfo();

		SimpleDateFormat sf = new SimpleDateFormat("yyyy");
		SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");

		String year = sf.format(new Date());

		for (CustomEmployeeInfo employee : list) {

			try {

				if (StringUtils.isNotBlank(employee.getWorkStartDate())) {// 计算连续工龄

					String workStartDate = sf.format(sf2.parse(employee.getWorkStartDate()));
					
					Integer bdwlxgl = Integer.valueOf(year) - Integer.valueOf(workStartDate);
					
					if(null != bdwlxgl) {
						employee.setBdwlxgl(String.valueOf(bdwlxgl));
					}
				}

				if (null != employee.getEntryDate()) {// 计算在职工龄

					String entryDate = sf.format(DateUtil.parse(employee.getEntryDate(), "yyyy-MM-dd"));
					
					Integer yearWork = Integer.valueOf(year) - Integer.valueOf(entryDate);
					
					employee.setYearWork(yearWork.toString());
				}

				if (null != employee.getBirthday()) {  //计算年龄

					Integer empAge = getAge(employee.getBirthday());
					employee.setEmpAge(empAge.toString());

				}
				
				if(StringUtils.isNotBlank(employee.getEmployeeName()) && StringUtils.isBlank(employee.getNameSpell())) { //简拼
					
					String jianpin = PinYinUtil.converterToFirstSpell(employee.getEmployeeName());
					
					employee.setNameSpell(jianpin);
				}

			} catch (Exception e) {
				e.printStackTrace();
			}

			customEmployeeBaseService.calculationEmployeeMessage(employee);
		}

	}

	public static int getAge(Date birthDay) throws Exception {
		
		Calendar cal = Calendar.getInstance();

		if (cal.before(birthDay)) {
			throw new IllegalArgumentException(
					"The birthDay is before Now.It's unbelievable!");
		}

		int yearNow = cal.get(Calendar.YEAR);
		int monthNow = cal.get(Calendar.MONTH) + 1;
		int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

		cal.setTime(birthDay);
		int yearBirth = cal.get(Calendar.YEAR);
		int monthBirth = cal.get(Calendar.MONTH) + 1;
		int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

		int age = yearNow - yearBirth;

		if (monthNow <= monthBirth) {
			if (monthNow == monthBirth) {
				if (dayOfMonthNow < dayOfMonthBirth) {
					age--;
				}
			} else {
				age--;
			}
		}
		return age;
	}
	
	/**
	 * 
	* @Title: syncHis2UserData
	* @Description: 同步his2.0人员数据  每5分钟执行一次
	* @param     参数
	* @return void    返回类型
	* <AUTHOR>
	* @date 2022年6月25日
	* @throws
	 */
	@Scheduled(cron = "0 0/5 * * * ? ")
	public void syncHis2UserData() {
		
		if("1".equals(syncHis2Switch)) {
			
			 UserLoginService.loginContext("admin");
			
			logger.info("=================开始执行定时任务:同步his2.0人员数据==================");
			StringBuilder sb = new StringBuilder();
			sb.append("use trasen SELECT b.code,a.name,a.sex,a.sfzh,a.phone,a.DELETE_BIT deletebit,convert(varchar(50),c.DEPT_ID) deptcode,dbo.fun_getDeptname(c.DEPT_ID) deptname,b.password ");
			sb.append("FROM JC_EMPLOYEE_PROPERTY a ");
			sb.append("LEFT JOIN  Pub_User b ON a.EMPLOYEE_ID =b.Employee_Id ");
			sb.append("LEFT JOIN JC_EMP_DEPT_ROLE c ON a.EMPLOYEE_ID =c.EMPLOYEE_ID AND c.[DEFAULT]=1");
			sb.append(" where b.code is not null and c.DEPT_ID is not null");
			
			Map<String,String> dataMap = new HashMap<>();
			dataMap.put("his2username", his2username);
			dataMap.put("his2pwd", his2pwd);
			dataMap.put("his2url", his2url);
			dataMap.put("his2driver", his2driver);
			    
			logger.info("==获取人员数据his2url==" + his2url);
			
			List<HisUserBean> result = JDBCUtils.query(sb.toString(),dataMap, HisUserBean.class);
			
			if(!CollectionUtils.isEmpty(result)) {
				
				logger.info("==获取人员数据size==" + result.size());
				
				for (HisUserBean hisUserBean : result) {
					
					CustomEmployeeInfo hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(hisUserBean.getCode());
					
					if(0 == hisUserBean.getDeletebit()) {//0 正常  1删除
						if(null == hrmsEmployee) {
							CustomEmployeeInfo hrmsEmployeeSaveReq = new CustomEmployeeInfo();
							hrmsEmployeeSaveReq.setEmployeeNo(hisUserBean.getCode());
							hrmsEmployeeSaveReq.setEmployeeName(hisUserBean.getName());
							if(1 == hisUserBean.getSex()) {
								hrmsEmployeeSaveReq.setGender("0");
							}else {
								hrmsEmployeeSaveReq.setGender("1");
							}
							hrmsEmployeeSaveReq.setIdentityNumber(hisUserBean.getSfzh());
							hrmsEmployeeSaveReq.setPhoneNumber(hisUserBean.getPhone());
							if(StringUtils.isNotBlank(hospitalId)) {
								hrmsEmployeeSaveReq.setOrgId(hospitalId);
							}else {
								hrmsEmployeeSaveReq.setOrgId(hisUserBean.getDeptcode());
							}
							
							customEmployeeBaseService.add(hrmsEmployeeSaveReq);
						}
					}else {
						if(null != hrmsEmployee) {
							
							 ThpsUserReq user = new ThpsUserReq();
							 user.setId(hrmsEmployee.getEmployeeId());
							 user.setStatus(0);
							 systemUserFeignService.newDisable(user);
							 
							 hrmsEmployeeService.disable(hrmsEmployee.getEmployeeId(),"0");
						}
					}
					
				}
			}
			logger.info("=================同步his2.0人员数据任务执行结束==================");
		}
	}
	
	/**
	 * 计算登录折线图数据
	 */
	@Scheduled(cron = "0 0/30 * * * ? ")
	public void calculationLoginStackedLine() {
		sysAccessLogService.calculationLoginStackedLine();
	}
}
