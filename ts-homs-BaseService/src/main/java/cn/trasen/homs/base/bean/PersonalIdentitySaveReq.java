package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:41
 * @description
 */

@Data
public class PersonalIdentitySaveReq {
    @ApiModelProperty(value = "ID")
    String personalIdentityId;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 20, message = "名称不能超过20个字符")
    String personalIdentityName;

    @ApiModelProperty(value = "备注")
    String remark;

    @ApiModelProperty(value = "排序")
    Short sort;

    @ApiModelProperty(value = "字典类型ID")
    private String dictTypeId;

    @ApiModelProperty(value = "主键")
    private String id;
}
