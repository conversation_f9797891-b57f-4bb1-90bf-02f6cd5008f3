package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.model.CommLoginLogs;

/**
 * @projectName: xtbg
 * @package: cn.trasen.homs.base.mapper
 * @className: SysAccessLogMapper
 * @author: chenbin
 * @description: TODO
 * @date: 2023/11/16 16:15
 * @version: 1.0
 */

public interface SysAccessLogMapper extends Mapper {
    Integer getLogins(@Param("loginDate") String loginDate, @Param("loginType") String[] loginType,@Param("orgCode") String orgCode,@Param("queryDate")String queryDate);

    List<CommLoginLogs> getLoginList(@Param("loginDate") String loginDate, @Param("loginType") String[] loginType, @Param("orgCode") String orgCode,@Param("queryDate")String queryDate);

    Integer getAccesses(@Param("loginDate") String loginDate,@Param("orgCode")String orgCode,@Param("queryDate")String queryDate);

    List<CommLoginLogs> getAccessList(@Param("loginDate") String loginDate,@Param("orgCode")String orgCode,@Param("queryDate")String queryDate);

	List<Map<String, String>> selectOrgInfo(@Param("userCodes")List<String> userCodes);

	List<Map<String, Object>> getLoginsCount(@Param("queryYear")String queryYear, @Param("ssoOrgCode")String ssoOrgCode);

	List<Map<String, Object>> getLoginsNumbers(@Param("queryYear")String queryYear, @Param("ssoOrgCode")String ssoOrgCode,@Param("source")String source);

	Map<String, Object> selectAccessMonth(@Param("loginMonth")String loginMonth);

	void updateAccessMonth(@Param("loginMonth")String loginMonth, @Param("loginNumbers")Long loginNumbers, @Param("loginPCNumbers")Long loginPCNumbers, @Param("loginWXNumbers")Long loginWXNumbers);

	void insertAccessMonth(@Param("id")String id,@Param("loginMonth")String loginMonth, @Param("loginNumbers")Long loginNumbers);

	List<Map<String, Object>> getLoginsMonth(@Param("queryYear")String queryYear,@Param("source")String source);

	Long selectTotalEmployee(@Param("ssoOrgCode") String ssoOrgCode);

	List<Map<String, Object>> sysUseAnalysisList(Map<String, Object> params);

	List<Map<String, Object>> sysUseAnalysisDetailList(Map<String, Object> params);

}
