package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.bean.PositionSaveReq;
import cn.trasen.homs.base.model.HrmsPosition;
import cn.trasen.homs.base.service.HrmsPositionService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**   
 * @Title: HrmsPositionController.java 
 * @Package cn.trasen.hrms.controller 
 * @Description: 职务Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月15日 下午4:48:38 
 * @version V1.0   
 */
@Slf4j
@Api(tags = "职务Controller")
@RequestMapping("/position")
@RestController
public class HrmsPositionController {

	@Autowired
	HrmsPositionService hrmsPositionService;

	/**
	 * @Title: insert
	 * @Description: 新增职务
	 * @Param: entity
	 * @Return: PlatformResult<String>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增职务", notes = "新增职务")
	@PostMapping(value = "/save")
	public PlatformResult<String> insert(@RequestBody HrmsPosition entity) {
		try {
			PlatformResult<String> result = hrmsPositionService.validate(entity);
			if (!result.isSuccess()) {
				return result;
			}
			if (hrmsPositionService.insert(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	/**
	 * @Title: update
	 * @Description: 修改职务
	 * @Param: entity
	 * @Return: PlatformResult<String>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "修改职务", notes = "修改职务")
	@PostMapping(value = "/update")
	public PlatformResult<String> update(@RequestBody HrmsPosition entity) {
		try {
			PlatformResult<String> result = hrmsPositionService.validate(entity);
			if (!result.isSuccess()) {
				return result;
			}
			
			hrmsPositionService.update(entity);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title: deleteById
	 * @Description: 删除职务
	 * @Param: accountId 职务主键ID
	 * @Return: PlatformResult<String>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除职务", notes = "删除职务")
	@PostMapping(value = "/deletedById/{positionId}")
	@ResponseBody
	public PlatformResult<String> deleteById(@PathVariable String positionId) {
		try {
			hrmsPositionService.deleted(positionId);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @param entity
	 * @Title: getDataList
	 * @Description: 分页查询查询职务列表
	 * @Param: page
	 * @Return: DataSet<HrmsPosition>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页查询职务列表", notes = "分页查询职务列表")
	@PostMapping(value = "/list")
	public DataSet<HrmsPosition> getDataList(Page page, HrmsPosition entity) {
		List<HrmsPosition> list = hrmsPositionService.getDataList(page, entity);
		return new DataSet<HrmsPosition>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	/**
	 * @param entity
	 * @Title: getList
	 * @Description: 查询职务列表(不分页)
	 * @Return PlatformResult<List < HrmsPosition>>
	 * <AUTHOR>
	 * @date 2020年4月16日 下午3:01:08
	 */
	@ApiOperation(value = "查询职务列表(不分页)", notes = "查询职务列表(不分页)")
	@PostMapping(value = "/getList")
	public PlatformResult<List<HrmsPosition>> getList(@RequestBody HrmsPosition entity) {
		try {
			return PlatformResult.success(hrmsPositionService.getList(entity));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}


	@ApiOperation(value = "验证删除", notes = "验证删除")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "positionId", value = "positionId", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyDel", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyDel(@RequestParam("positionId") String positionId) {
		try {
			hrmsPositionService.verify(positionId);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
		
		
	}

	@ApiOperation(value = "验证禁用", notes = "验证禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "positionId", value = "positionId", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyEnable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyEnable(@RequestParam("positionId") String positionId) {
		try{
			hrmsPositionService.verify(positionId);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "启用禁用", notes = "启用禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "positionId", value = "positionId", required = true, dataType = "String")
			, @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
	})
	@RequestMapping(value = "/enable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult enable(@RequestParam("positionId") String positionId, @RequestParam("enable") String enable) {
		try{
			hrmsPositionService.enable(positionId, enable);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "修改排序", notes = "修改排序")
	@PostMapping(value = "/updateSort")
	public PlatformResult updateSort(@RequestBody List<PositionSaveReq> positionSaveReqs) {
		hrmsPositionService.updateSort(positionSaveReqs);
		return PlatformResult.success();
	}
}
