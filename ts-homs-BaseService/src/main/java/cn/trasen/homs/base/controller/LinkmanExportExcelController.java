package cn.trasen.homs.base.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.LinkmanResp;
import cn.trasen.homs.base.model.Linkman;
import cn.trasen.homs.base.service.LinkmanService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: Excel导出Controller层
 * @Date: 2020/2/26 09:24
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Api(tags = "Excel导出Controller")
@RestController
public class LinkmanExportExcelController {

    private static String slave = "oa";

    private static final Logger logger = LoggerFactory.getLogger(LinkmanExportExcelController.class);

//    @Autowired
//    private EmployeeService employeeService;

    @Autowired
    private LinkmanService linkmanService;

//    /**
//     * @Author: Lizhihuo
//     * @Description: 员工信息导出(第一种)
//     * @Date: 2020/2/25 11:12
//     * @Param:
//     * @return: PlatformResult<byte [ ]>
//     **/
//    @GetMapping(value = "/employee/excel/expotEmployee")
//    @ApiOperation(value = "员工信息导出", notes = "员工信息导出")
//    public void expotEmployee(String empName, String empDeptCode, String empCode, String empDutyId, String empSex, String empHiredate, HttpServletResponse response, HttpServletRequest request) throws Exception {
//        Employee entity = new Employee();
//        if (StringUtils.isNoneBlank(empName)) {
//            entity.setEmpName(empName);
//        }
//        if (StringUtils.isNoneBlank(empDeptCode)) {
//            entity.setEmpDeptCode(empDeptCode);
//        }
//        if (StringUtils.isNoneBlank(empCode)) {
//            entity.setEmpCode(empCode);
//        }
//        if (StringUtils.isNoneBlank(empDutyId)) {
//            entity.setEmpDutyId(empDutyId);
//        }
//        if (StringUtils.isNoneBlank(empSex)) {
//            entity.setEmpSex(Short.valueOf(empSex));
//        }
//        if (StringUtils.isNoneBlank(empHiredate)) {
//            Date hiredate = DateUtil.stringToDate(empHiredate);
//            entity.setEmpHiredate(hiredate);
//        }
//        List<Employee> list = employeeService.findExpotEmployee(entity);
//        //表头标题
//        String name = "员工信息.xls";
//        // 模板位置
//        String templateUrl = "template/employee.xls";
//        cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, list, name, templateUrl);
//
//        //表头标题
//        /*String title = "员工信息";
//
//        //表头列名称
//        List<String> headList = new ArrayList<String>();
//        headList.add("发薪号");
//        headList.add("所属部门");
//        headList.add("姓名");
//        headList.add("性别");
//        headList.add("兼职科室");
//        headList.add("身份证");
//        headList.add("出生日期");
//        headList.add("入职时间");
//        headList.add("手机号码");
//        headList.add("职务");
//        headList.add("状态");
//        //headList.add("科室主任");
//        //标题所对应的字段
//        List<String> fieldList = new ArrayList<String>();
//        fieldList.add("EMP_CODE");
//        fieldList.add("EMP_DEPT_NAME");
//        fieldList.add("EMP_NAME");
//        fieldList.add("EMPSEX");
//        fieldList.add("DEPTNAMELIST");
//        fieldList.add("EMP_IDCARD");
//        fieldList.add("EMP_BIRTH");
//        fieldList.add("EMP_HIREDATE");
//        fieldList.add("EMP_PHOTO");
//        fieldList.add("EMP_DUTY_NAME");
//        fieldList.add("EMP_STATUS");
//        //fieldList.add("deptDirector");
//        ExportUtil.createExcel(title, headList, fieldList, list, response, request);*/
//    }


    /**
     * @Author: Lizhihuo
     * @Description: 员工信息导出(第二种)
     * @Date: 2019/8/30 11:32
     * @Param:
     * @return: ixe.cloud.common.bean.PlatformResult<byte [ ]>
     **/
    /*@GetMapping(value = "/employee/excel/expotEmployeeTwo")
    @ResponseBody
    public PlatformResult<byte[]> findPatientRegistration(String empName, String empDeptName, String empCode, String empDutyId, String empSex, String empHiredate, HttpServletResponse response) {
        EmployeePo entity = new EmployeePo();
        if (StringUtils.isNoneBlank(empName)) {
            entity.setEmpName(empName);
        }
        if (StringUtils.isNoneBlank(empDeptName)) {
            entity.setEmpDeptName(empDeptName);
        }
        if (StringUtils.isNoneBlank(empCode)) {
            entity.setEmpCode(empCode);
        }
        if (StringUtils.isNoneBlank(empDutyId)) {
            entity.setEmpDutyId(empDutyId);
        }
        if (StringUtils.isNoneBlank(empSex)) {
            entity.setEmpSex(empSex);
        }

        if (StringUtils.isNoneBlank(empHiredate)) {
            Date hiredate = DateUtil.stringToDate(empHiredate);
            entity.setHiredate(hiredate);
        }
        PlatformResult<byte[]> data = new PlatformResult<byte[]>();
        try {
            List<Map<String, Object>> list = employeeService.findExpotEmployee(entity);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(slave, list);
            byte[] dataBytes = ExportBeanExcelUtil.exportExcell(map, null, EmployeePo.class, "员工信息");
            buildExcelDocument("员工信息.xls", dataBytes, response);
        } catch (Exception e) {
            logger.error("===查询员工信息异常===", e);
            data.setSuccess(false);
            data.setMessage("导出异常");
        }
        return data;
    }*/

    /**
     * @Author: Lizhihuo
     * @Description: 个人联系人导出
     * @Date: 2020/3/5 15:25
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<byte [ ]>
     **/
    @GetMapping(value = "/employee/excel/expotLinkMan")
    @ApiOperation(value = "个人联系人导出", notes = "个人联系人导出")
    public PlatformResult<byte[]> expotLinkMan(String linkmanName, String linkmanUnit, String linkmanDepart, HttpServletResponse response, HttpServletRequest request) {
        Linkman entity = new Linkman();
        if(StringUtils.isBlank(linkmanDepart)==false)
        {
            linkmanDepart=linkmanDepart.replace("undefined","");
        }
        entity.setLinkmanUnit(linkmanUnit);
        entity.setLinkmanName(linkmanName);
        entity.setLinkmanDepart(linkmanDepart);
        PlatformResult<byte[]> data = new PlatformResult<byte[]>();





        try {
            List<Linkman> list = linkmanService.findExpotLinkman(entity);
            for (Linkman linkman : list) {
            	if("0".equals(linkman.getLinkmanSex())) {
            		linkman.setLinkmanSex("男");
            	}else {
            		linkman.setLinkmanSex("女");
            	}
            	
			}
            //表头标题
            String name = "个人联系人.xls";
            // 模板位置
            String templateUrl = "template/linkman.xls";
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, list, name, templateUrl);
        } catch (Exception e) {
            logger.error("===查询个人联系人信息异常===", e);
            data.setSuccess(false);
            data.setMessage("导出异常");
        }
        return data;
    }

  /** 
  * @description: 内部联系人导出
* @param: empName
* @param: empPhone
* @param: empSex
* @param: response
* @param: request
  * @return: cn.trasen.BootComm.utils.PlatformResult<byte[]>
  * @author: liyuan
  * @createTime: 2021/6/21 15:37
  */
    @GetMapping(value = "/employee/excel/expotInnerLinkMan")
    @ApiOperation(value = "内部联系人导出", notes = "内部联系人导出")
    public PlatformResult<byte[]> expotInnerLinkMan(String employeeName, String phoneNumber,String empSex, HttpServletResponse response, HttpServletRequest request) {

        PlatformResult<byte[]> data = new PlatformResult<byte[]>();
        try {
            EmployeeListReq employeeReq=new EmployeeListReq();
            employeeReq.setEmployeeName(employeeName);
            employeeReq.setPhoneNumber(phoneNumber);
            List<LinkmanResp> list = linkmanService.selectExpotInnerLinkMan(employeeReq);
            //表头标题
            String name = "内部联系人.xls";
            // 模板位置
            String templateUrl = "template/insideContacts.xls";
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, list, name, templateUrl);
        } catch (Exception e) {
            logger.error("===查询内部联系人信息异常===", e);
            data.setSuccess(false);
            data.setMessage("导出异常");
        }
        return data;
    }


    /**
     * <p> @Title: buildExcelDocument</p>
     * <p> @Description: 浏览器下载excel</p>
     * <p> @Param: </p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     */
    protected void buildExcelDocument(String fileName, byte[] bytes, HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            response.flushBuffer();
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
        } catch (Exception e) {
            logger.error("=========浏览器下载异常=========", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("=========关闭流异常=========", e);
                }
            }
        }
    }

}
