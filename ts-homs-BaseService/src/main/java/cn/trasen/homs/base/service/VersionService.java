package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.model.Version;
import cn.trasen.homs.base.model.VersionAccessory;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 版本管理Service层
 * @Date: 2020/4/27 16:28
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface VersionService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int insert(Version entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int update(Version entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int deleted(Version entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    List<Version> getDataList(Page page, Version entity);

    /**
     * @Author: Lizhihuo
     * @Description: 初始化查询附件信息
     * @Date: 2020/4/28 9:54
     * @Param: 
     * @return: java.util.List<cn.trasen.system.model.VersionAccessory>
     **/
    List<VersionAccessory> selectVersionAccessoryByVersionId(String versionId);

    /**
     * @Author: Lizhihuo
     * @Description: 移动端获取版本信息详情
     * @Date: 2020/6/3 15:54
     * @Param: 
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String,Object> getWxVersionDetailsByVersionId(String versionId);
}
