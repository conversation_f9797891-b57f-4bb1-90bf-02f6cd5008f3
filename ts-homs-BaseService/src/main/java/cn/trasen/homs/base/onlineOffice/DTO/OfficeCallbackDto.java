/************************************************
* @功能描述: TODO
* @Title: OfficeCallBackDto.java
* @Package cn.trasen.homs.base.onlineOffice.DTO
* <AUTHOR>
* @date 2025年6月16日 下午12:29:50
* @version V1.0
*************************************************/
package cn.trasen.homs.base.onlineOffice.DTO;

import lombok.Data;

/**
* @ClassName: OfficeCallBackDto
* @Description: TODO 
* <AUTHOR>
* @date 2025年6月16日 下午12:29:50
*
*/
@Data
public class OfficeCallbackDto {
    // 文档状态：1-正在编辑，2-准备保存，3-保存出错，4-关闭无修改，6-正在保存
    private Integer status;
    
    // 文档唯一标识
    private String key;
    
    // 文档最新版本下载URL
    private String url;
    
    // 文档变更历史URL
    private String changesurl;
    
    // 文档版本历史信息
    private Object history;
    
    // 用户ID
    private String userid;
    
    // 文档修改后的文件名
    private String filename;
    
    // 文档类型
    private String filetype;
    
    // 文档大小（字节）
    private Long filesize;
    
    // 文档版本号
    private Integer version;
    
    // 文档修改时间
    private Long modified;
    
    // 文档修改者
    private String modifiedBy;
    
    // 文档修改者头像URL
    private String userAvatar;
    
    // 文档修改者姓名
    private String userName;
    
    // 错误信息（status=3时有效）
    private String error;
    
}