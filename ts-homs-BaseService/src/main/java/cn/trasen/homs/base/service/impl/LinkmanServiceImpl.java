package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.LinkmanResp;
import cn.trasen.homs.base.mapper.LinkmanMapper;
import cn.trasen.homs.base.model.Linkman;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.LinkmanService;
import cn.trasen.homs.base.utils.StringConvertPinyin;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

/**
 * @Description: 联系人ServiceImpl层
 * @Date: 2020/1/13 18:17
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class LinkmanServiceImpl implements LinkmanService {

    @Autowired
    private LinkmanMapper linkmanMapper;

    @Autowired
    private HrmsEmployeeService hrmsEmployeeService;

    /**
     * @Author: Lizhihuo
     * @Description: 查询个人联系人列表
     * @Date: 2020/1/13 19:19
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.Linkman>
     **/
    @Override
    public List<Linkman> getDataList(Page page, Linkman linkman) {
        Example example = new Example(Linkman.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isNotBlank(linkman.getLinkmanName())) {//姓名
            example.and().andLike("linkmanName", "%" + linkman.getLinkmanName() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getLinkmanDepart())) {//部门
            example.and().andEqualTo("linkmanDepart", linkman.getLinkmanDepart());
        }
        if (StringUtils.isNotBlank(linkman.getLinkmanUnit())) {//单位
            example.and().andLike("linkmanUnit", "%" + linkman.getLinkmanUnit() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getMobilePhone())) {
            example.and().andLike("mobilePhone", "%" + linkman.getMobilePhone() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getLinkmanEmail())) {
            example.and().andLike("linkmanEmail", "%" + linkman.getLinkmanEmail() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getClassName())) {
            example.and().andLike("className", "%" + linkman.getClassName() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getBussinessPhone())) {
            example.and().andLike("bussinessPhone", "%" + linkman.getBussinessPhone() + "%");
        }
        if (StringUtils.isNotBlank(linkman.getLinkmanSex())) {
            example.and().andLike("linkmanSex", "%" + linkman.getLinkmanSex() + "%");
        }
        return linkmanMapper.selectByExampleAndRowBounds(example, page);
    }


    /** 
    * @description: 内部联系人列表
* @param: page
* @param: employee
    * @return: java.util.List<Employee>
    * @author: liyuan
    * @createTime: 2021/6/21 14:27
    */
   @Override
    public List<LinkmanResp> innerLinkManlist(Page page, EmployeeListReq employeeReq) {
       //搜索条件

       List<LinkmanResp> linkmens = new ArrayList<>();

//       page.setSidx(" convert(e.employee_name using gbk)   ");
//       page.setSord(" asc ");
       page.setSidx(" o.sort,e.emp_sort  ");
       page.setSord(" asc ");
       //根据当前账号机构编码过滤
       employeeReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
       List<HrmsEmployeeResp> employeeList = hrmsEmployeeService.getEmployeePageList(page, employeeReq);
       for (HrmsEmployeeResp employeeResp : employeeList) {
           if (null == employeeResp.getIsDisplayPhoneNo()
                   || employeeResp.getIsDisplayPhoneNo().equals("1")) {
               String empPhone = employeeResp.getPhoneNumber();
               String empPhoneSecond = employeeResp.getEmpPhoneSecond();
               if (StringUtils.isNoneBlank(empPhone)) {
                   employeeResp.setPhoneNumber(empPhone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));

               }
               if (StringUtils.isNoneBlank(empPhoneSecond)) {
                   employeeResp.setEmpPhoneSecond(empPhoneSecond.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
               }
           }

           LinkmanResp linkman = new LinkmanResp();
         
           if(!StringUtil.isEmpty(employeeResp.getEmployeeName())) {
               linkman.setEmpName(employeeResp.getEmployeeName());
               linkman.setAvatar(employeeResp.getAvatar());
               linkman.setEmpBusinessPhone(employeeResp.getEmpBusinessPhone());
               linkman.setEmpSex(employeeResp.getGender());
               linkman.setEmpSexName(employeeResp.getGenderText());
               linkman.setEmpDeptName(employeeResp.getOrgName());
               linkman.setEmpEmail(employeeResp.getEmail());
               linkman.setEmpShortPhone(employeeResp.getLandlineNumber());
               linkman.setLandlineNumber(employeeResp.getLandlineNumber());
               linkman.setEmpPhone(employeeResp.getPhoneNumber());
               linkman.setEmpPhoneSecond(employeeResp.getEmpPhoneSecond());
               linkman.setEmpTelecomBusinessPhone(employeeResp.getEmpTelecomBusinessPhone());
               linkman.setEmpUnicomBusinessPhone(employeeResp.getEmpUnicomBusinessPhone());
               linkman.setPositionId(employeeResp.getPositionId());
               linkman.setPositionName(employeeResp.getPositionName());
               linkman.setPersonalIdentityName(employeeResp.getPersonalIdentityName());
               linkman.setPersonalIdentity(employeeResp.getPersonalIdentity());
               linkman.setEmployeeStatusName(employeeResp.getEmployeeStatusName());
               linkman.setEmployeeId(employeeResp.getEmployeeId());
               linkmens.add(linkman);
           }
       }
       return linkmens;
   }

    /**
     * @Author: Lizhihuo
     * @Description: 内部联系人列表导出
     * @Date: 2020/5/14 10:12
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.Employee>
     **/
    @Override
    public List<LinkmanResp> selectExpotInnerLinkMan(EmployeeListReq employeeReq) {
        /*boolean isAdmin = UserInfoHolder.getRight("ADMIN");
        if (!isAdmin) {
            employee.setEmpDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }*/
        Page page=new Page();
        page.setPageSize(Integer.MAX_VALUE);
        page.setPageNo(1);

        return innerLinkManlist(page,employeeReq);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询个人联系人列表(导出)
     * @Date: 2020/3/5 15:28
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.Linkman>
     **/
    @Override
    public List<Linkman> findExpotLinkman(Linkman linkman) {
        linkman.setCreateUser(UserInfoHolder.getCurrentUserCode());
        linkman.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return linkmanMapper.findExpotLinkman(linkman);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增个人联系人
     * @Date: 2020/1/13 19:19
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(Linkman linkman) {
        StringConvertPinyin cte = new StringConvertPinyin();
        linkman.setId(String.valueOf(IdWork.id.nextId()));
        linkman.setIsDeleted(Contants.IS_DELETED_FALSE);
        linkman.setCreateDate(new Date());
        linkman.setCreateUser(UserInfoHolder.getCurrentUserCode());
        linkman.setCreateUserName(UserInfoHolder.getCurrentUserName());
        //部门\部门名称
        linkman.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        linkman.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        //院区编码
        linkman.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
        linkman.setLinkmanEnname(cte.getAllFirstLetter(linkman.getLinkmanName()));//拼音
        linkman.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return linkmanMapper.insertSelective(linkman);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改个人联系人
     * @Date: 2020/1/13 19:20
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(Linkman linkman) {
        linkman.setUpdateDate(new Date());
        linkman.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        linkman.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        return linkmanMapper.updateByPrimaryKeySelective(linkman);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除个人联系人
     * @Date: 2020/1/13 19:20
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        Linkman linkman = linkmanMapper.selectByPrimaryKey(id);
        linkman.setIsDeleted(Contants.IS_DELETED_TURE);//是否删除
        return linkmanMapper.updateByPrimaryKeySelective(linkman);
    }
}
