package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/9 11:34
 */


@Setter
@Getter
public class OrgChildrenEmplListRes {

    @ApiModelProperty(value = "orgID")
    String orgId;


    @ApiModelProperty(value = "机构列表")
    List<Org> orgList;



    @ApiModelProperty(value = "员工列表")
    List<EMP> empList;


    @Setter
    @Getter
    public static  class Org
    {
        String orgId;
        String orgName;
        String orgCode;
    }

    @Setter
    @Getter
    public static  class EMP
    {
        String empName;
        String empCode;
        String empId;

        /**
         * 头像
         */
        @ApiModelProperty(value = "头像")
        private String avatar;
    }

}
