package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.List;

import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.base.bean.DictItemListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeReq;
import cn.trasen.homs.base.bean.PersonalIdentityListReq;
import cn.trasen.homs.base.bean.PersonalIdentityListResp;
import cn.trasen.homs.base.bean.PersonalIdentitySaveReq;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IPersonalIdentityService;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;

/** 
* @description: 岗位类别
* @return: 
* @author: liyuan
* @createTime: 2021/8/6 9:41
*/
@Service
public class PersonalIdentityService implements IPersonalIdentityService {

    @Autowired
    private CustomEmployeeInfoService customEmployeeInfoService;

    @Autowired
    IDictItemService dictItemService;

    @Autowired
    IPostService postService;


    final static String personalIdentity = "personal_identity";

    @Override
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    public List<PersonalIdentityListResp> getList() {
        List<DictItem> dictItemList = dictItemService.getDictItemByTypeCodeAll(personalIdentity);
        List<PersonalIdentityListResp> personalIdentityListRespList = new ArrayList<>();
        for (DictItem dictItem : dictItemList) {
            if (dictItem.getIsDeleted().equals(Contants.IS_DELETED_TURE)) {
                continue;
            }
            PersonalIdentityListResp personalIdentityListResp = new PersonalIdentityListResp();
            personalIdentityListResp.setPersonalIdentityId(dictItem.getItemCode());
            personalIdentityListResp.setPersonalIdentityName(dictItem.getItemName());
            personalIdentityListResp.setRemark(dictItem.getRemark());
            if (StringUtils.isBlank(dictItem.getIsEnable())) {
                dictItem.setIsEnable(EnableEnum.Y.getKey());
            }
            personalIdentityListResp.setDictTypeId(dictItem.getDicTypeId());
            personalIdentityListResp.setSort(dictItem.getSort());
            personalIdentityListResp.setIsEnable(dictItem.getIsEnable());
            personalIdentityListResp.setIsEnableLable(EnableEnum.getValByKey(personalIdentityListResp.getIsEnable()));
            personalIdentityListRespList.add(personalIdentityListResp);
        }
        return personalIdentityListRespList;
    }


    @Override
    /**
    * @description: 分页获取
* @param: page
* @param: postListReq
    * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.basicsbottom.bean.PersonalIdentityListResp>
    * @author: liyuan
    * @createTime: 2021/8/16 15:09
    */
    public DataSet<PersonalIdentityListResp> getPageList( Page page,PersonalIdentityListReq personalIdentityListReq) {
        DictItemListReq dictItemListReq = new DictItemListReq();
        dictItemListReq.setTypeCode(personalIdentity);
        dictItemListReq.setItemName(personalIdentityListReq.getPersonalIdentityName());
        //根据当前登录账号机构编码过滤查询数据
        dictItemListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        //非管理维护界面默认返回启用数据
        if(ObjectUtils.isEmpty(personalIdentityListReq.getIsManagerPage()) || !"1".equals(personalIdentityListReq.getIsManagerPage())){
        	dictItemListReq.setIsEnable("1");
        }
        DataSet<DictItem> dataSet = dictItemService.getDictItemByTypeCode(page, dictItemListReq);
        List<PersonalIdentityListResp> personalIdentityListRespList = new ArrayList<>();
        for (DictItem dictItem : dataSet.getRows()) {
//            if (dictItem.getIsDeleted().equals(Contants.IS_DELETED_TURE)) {
//                continue;
//            }
            PersonalIdentityListResp personalIdentityListResp = new PersonalIdentityListResp();
            personalIdentityListResp.setPersonalIdentityId(dictItem.getItemCode());
            personalIdentityListResp.setPersonalIdentityName(dictItem.getItemName());
            personalIdentityListResp.setRemark(dictItem.getRemark());
//            if (StringUtils.isBlank(dictItem.getIsEnable())) {
//                dictItem.setIsEnable(EnableEnum.Y.getKey());
//            }
            personalIdentityListResp.setDictTypeId(dictItem.getDicTypeId());
            personalIdentityListResp.setId(dictItem.getId());
            personalIdentityListResp.setSort(dictItem.getSort());
            personalIdentityListResp.setIsEnable(dictItem.getIsEnable());
            personalIdentityListResp.setIsEnableLable(EnableEnum.getValByKey(personalIdentityListResp.getIsEnable()));
            personalIdentityListRespList.add(personalIdentityListResp);
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), personalIdentityListRespList);
    }




    @Override
    /**
     * @description: 新增
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(PersonalIdentitySaveReq personalIdentitySaveReq) {
        DictItem entity = new DictItem();
        entity.setItemCode(String.valueOf(IdWork.id.nextId()));
        entity.setItemName(personalIdentitySaveReq.getPersonalIdentityName());
        entity.setRemark(personalIdentitySaveReq.getRemark());
        entity.setDicTypeId(personalIdentity);
        entity.setSysCode("HRMS");
        entity.setItemNameValue(entity.getItemCode());
        entity.setSort(personalIdentitySaveReq.getSort());
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        dictItemService.insert(entity);
    }


    @Override
    /**
     * @description: 修改
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(PersonalIdentitySaveReq personalIdentitySaveReq) {
        DictItem entity = new DictItem();
        entity.setItemName(personalIdentitySaveReq.getPersonalIdentityName());
        entity.setRemark(personalIdentitySaveReq.getRemark());
        entity.setItemCode(personalIdentitySaveReq.getPersonalIdentityId());
        entity.setSort(personalIdentitySaveReq.getSort());
        entity.setId(personalIdentitySaveReq.getId());
        dictItemService.update(entity);
    }


    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<PersonalIdentitySaveReq> personalIdentitySaveReqList) {
        for (PersonalIdentitySaveReq personalIdentitySaveReq : personalIdentitySaveReqList) {
            String dictItemId = "";
            List<DictItem> dictItemList = dictItemService.getDictItemByTypeCode(personalIdentity);
            for (DictItem dictItem : dictItemList) {
                if (dictItem.getItemCode().equals(personalIdentitySaveReq.getPersonalIdentityId())) {
                    dictItemId = dictItem.getId();
                    break;
                }
            }
            DictItem entity = new DictItem();
            entity.setId(dictItemId);
            entity.setSort(personalIdentitySaveReq.getSort());
            dictItemService.update(entity);
        }
    }

    @Override
    /**
     * @description: 删除类别
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String dictTypeId, String id) {
        verify(id);
        dictItemService.delete(dictTypeId, id);
    }


    @Override
    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String dictTypeId, String id, String enable) {
        if (enable.equals(EnableEnum.N.getKey())) {
            verify(id);
        }
        dictItemService.enable(dictTypeId, id, enable);
    }


    /**
     * @description: 验证
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 11:17
     */
    @Override
    public void verify(String id) {

    	CustomEmployeeInfo employeeReq = new CustomEmployeeInfo();
        employeeReq.setPersonalIdentity(id);
        if(customEmployeeInfoService.exists(employeeReq))
        {
            throw new BusinessException("岗位已被使用，不能禁用和删除！");
        }
    }
}