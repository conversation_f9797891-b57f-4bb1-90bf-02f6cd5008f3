package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.PortalElementCustom;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName PortalElementCustomService
 * @Description TODO
 * @date 2024��9��21�� ����10:52:59
 * <AUTHOR>
 * @version 1.0
 */
public interface PortalElementCustomService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	Integer save(PortalElementCustom record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	Integer update(PortalElementCustom record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PortalElementCustom
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	PortalElementCustom selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PortalElementCustom>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	DataSet<PortalElementCustom> getDataSetList(Page page, PortalElementCustom record);

	void savePortalElementCustom(List<PortalElementCustom> records);

	List<PortalElementCustom> getElementCustomByUserCode(String themeId,String userCode);

	void restore(String themeId);
}
