package cn.trasen.homs.base.service.impl;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.dao.QuickMenuMapper;
import cn.trasen.homs.base.model.QuickMenu;
import cn.trasen.homs.base.service.QuickMenuService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.SystemMenusClient;
import tk.mybatis.mapper.entity.Example;


@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class QuickMenuServiceImpl implements QuickMenuService{
    
    @Resource
    private QuickMenuMapper quickMenuMapper;
    
    @Resource
    private SystemMenusClient systemMenusClient;

    @Override
    @Transactional(readOnly = false)
    public void insert(List<QuickMenu> quickMenuList) {
        
        QuickMenu record = new QuickMenu();
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        quickMenuMapper.delete(record);
        
        for (QuickMenu quickMenu : quickMenuList) {
            
            quickMenu.setId(String.valueOf(IdWork.id.nextId()));
            quickMenu.setCreateUser(UserInfoHolder.getCurrentUserCode());
            quickMenu.setCreateName(UserInfoHolder.getCurrentUserName());
            quickMenu.setCreateTime(new Date());
            quickMenu.setIsDeleted(Contants.IS_DELETED_FALSE);
            quickMenu.setIsDefault("0");
            quickMenu.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            quickMenuMapper.insertSelective(quickMenu);
        }
    }

    @Override
    public void delete(String id) {
        quickMenuMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<QuickMenu> selectQuickMenuList() {
        Example example = new Example(QuickMenu.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("createUser", UserInfoHolder.getCurrentUserCode())
        .orEqualTo("isDefault", "1");
        example.and().andIsNotNull("menuUrl");
        List<QuickMenu> elementList = quickMenuMapper.selectByExample(example);
        
        if(CollectionUtils.isNotEmpty(elementList)) {
            //去重
            elementList = elementList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(QuickMenu::getMenuUrl))), ArrayList::new));
            //排序
            Collections.sort(elementList, Comparator.comparing(QuickMenu::getSord));
        }
        
        List<QuickMenu> quickMenuList = new ArrayList<>();
        
        //过滤已经删除的菜单
        Result result = systemMenusClient.getfmenus("ts-platform");
        List<Object> list = (List<Object>) result.getObject();
        String menus = list.get(0).toString();
        for (QuickMenu quickMenu : elementList) {
            if(menus.contains(quickMenu.getMenuName()) && menus.contains(quickMenu.getMenuUrl())) {
                quickMenuList.add(quickMenu);
            }
        }
        return quickMenuList;
    }

    
}
