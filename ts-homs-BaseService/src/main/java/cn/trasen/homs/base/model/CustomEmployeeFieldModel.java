/**
 * @Title: CustomEmployee.java  
 * @Package: cn.trasen.homs.baseservice.model  
 * @Date: 2021年6月19日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.model;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
* @ClassName: 自定义字段接手model  
 * @Author: 86189
 * @Date: 2021年6月19日
 */
@Setter
@Getter
public class CustomEmployeeFieldModel {
	
	@ApiModelProperty(value = "分组id")
	private String groupId;
	
	@ApiModelProperty(value = "单表保存数据字段josn集合")
	private List<CommEmployeeField> fields;
	
	@ApiModelProperty(value = "明细表保存数据字段json集合")
	private List<List<CommEmployeeField>> detailFields;
	
	@ApiModelProperty(value = "员工id,用于查询")
	private String employeeId;
	
	@ApiModelProperty(value = "表名称,用于查询")
	private String tableName;
	
	@ApiModelProperty(value = "员工Code,用于查询")
	private String userCode;
	
	@ApiModelProperty(value = "分组名称")
	private String groupName;
	
	@ApiModelProperty(value = "默认查询条件")
	private String defaultCondition;

	@ApiModelProperty(value = "排序字段")
	private String sortField;
}
