/**
````````````````````````` * @Title: CommEmployeeFieldService.java  
 * @Package: cn.trasen.homs.base.service  
 * @Date: 2021年6月16日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
* @ClassName: CommEmployeeFieldService  
 * @Author: 86189
 * @Date: 2021年6月16日
 */
public interface CommEmployeeFieldService {
	
	/**
	 * 
	* @Title: insert  
	* @Description: 新增人员档案字段
	* @Params: @param record
	* @Params: @return      
	* @Return: int
	* <AUTHOR>
	* @date:2021年6月16日
	* @Throws
	 */
	int insert(CommEmployeeField record);
    
	/**
	 * 
	* @Title: update  
	* @Description: 修改人员档案
	* @Params: @param record
	* @Params: @return      
	* @Return: int
	* <AUTHOR>
	* @date:2021年6月16日
	* @Throws
	 */
    int update(CommEmployeeField record);
    
    /**
     * 
    * @Title: deleted  
    * @Description: 删除人员档案字段
    * @Params: @param id
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    int deleted(String id);
    
    /**
     * 
    * @Title: getList  
    * @Description: 获取人员档案字段列表
    * @Params: @param page
    * @Params: @param record
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    List<CommEmployeeField> getList(Page page,CommEmployeeField record);
    
    /**
     * 
    * @Title: findById  
    * @Description: 根据id查询人员档案字段
    * @Params: @param id
    * @Params: @return      
    * @Return: CommEmployeeField
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    CommEmployeeField findById(String id);
    
    /**
     * 
    * @Title: updateList  
    * @Description: 批量修改人员档案字段
    * @Params: @param records
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    String updateList(List<CommEmployeeField> records);
    
    /**
     * 
    * @Title: getListByGroupid  
    * @Description: 根据条件查询字段列表
    * @Params: @param groupId
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    List<CommEmployeeField> getFieldsListByCondition(CommEmployeeField record);
    
    /**
     * 
    * @Title: getFieldAndJurisdictionListByGroupid  
    * @Description: 根据分组id获取字段明细以及当前用户字段编辑权限
    * @Params: @param groupId
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    List<CommEmployeeField> getFieldAndJurisdictionListByGroupid(String groupId);
    
    /**
     * 
    * @Title: getFielListByGroupid  
    * @Description: 获取所有明细分组字段 无编辑权限控制
    * @Params: @param groupId
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月28日
    * @Throws
     */
    List<CommEmployeeField> getFielListByGroupid(CommEmployeeField record);
    
    
    
    /**
     * 
    * @Title: getEmployeeFieldTitel  
    * @Description: 获取人员档案列表头
    * @Params: @return      
    * @Return: List<CommEmployeeField>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    List<CommEmployeeField> getEmployeeFieldTitel();
    
    void create(String tableName);
    
    
}
