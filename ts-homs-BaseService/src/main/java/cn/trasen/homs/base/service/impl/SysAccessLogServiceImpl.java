package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.base.mapper.SysAccessLogMapper;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.service.SysAccessLogService;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
/**
 * @projectName: xtbg
 * @package: cn.trasen.homs.base.service.impl
 * @className: SysAccessLogServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2023/11/16 16:13
 * @version: 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SysAccessLogServiceImpl implements SysAccessLogService {

    @Autowired
    private SysAccessLogMapper mapper;

    @Override
    public Integer getLogins(String loginDate, String[] loginType,String queryDate) {
        int r = 0;

        r = mapper.getLogins(loginDate, loginType,UserInfoHolder.getCurrentUserCorpCode(),queryDate);

        return r;
    }

    @Override
    public Integer getAccesses(String loginDate,String queryDate) {
        int r = 0;

        r = mapper.getAccesses(loginDate,UserInfoHolder.getCurrentUserCorpCode(),queryDate);
        
        return r;
    }

    @Override
    public List<CommLoginLogs> getLoginList(String loginDate, String[] loginType, String queryDate) {
    	
        String orgCode = UserInfoHolder.getCurrentUserCorpCode();

        return mapper.getLoginList(loginDate, loginType, orgCode,queryDate);

    }


    @Override
    public List<CommLoginLogs> getAccessList(String loginDate,String queryDate) {
        return mapper.getAccessList(loginDate,UserInfoHolder.getCurrentUserCorpCode(),queryDate);
    }

	@Override
	public List<Map<String, String>> selectOrgInfo(List<String> userCodes) {
		return mapper.selectOrgInfo(userCodes);
	}

	@Override
	public List<Map<String, Object>> getLoginsCount(String queryYear) {
		return mapper.getLoginsCount(queryYear,UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	public List<Map<String, Object>> getLoginsNumbers(String queryYear,String source) {
		return mapper.getLoginsNumbers(queryYear,UserInfoHolder.getCurrentUserCorpCode(),source);
	}
	
	
	@Override
	public List<Map<String, Object>> getLoginsMonth(String queryYear,String source) {
		return mapper.getLoginsMonth(queryYear,source);
	}

	@Override
	@Transactional(readOnly = false)
	public void calculationLoginStackedLine() {
		
//		List<Map<String, Object>> oldStackedLine = mapper.getLoginsCount("2023",null);
//		
//		if(CollectionUtils.isNotEmpty(oldStackedLine)) {
//			for (Map<String, Object> map : oldStackedLine) {
//				String loginMonth = (String) map.get("loginMonth");
//				Long loginNumbers = (Long) map.get("loginNumbers");
//				
//				Map<String,Object> accessMonth = mapper.selectAccessMonth(loginMonth);
//				
//				if(null != accessMonth) {
//					mapper.updateAccessMonth(loginMonth,loginNumbers);
//				}else {
//					mapper.insertAccessMonth(IdGeneraterUtils.nextId(),loginMonth,loginNumbers);
//				}
//				
//			}
//		}
		
		String queryYear = String.valueOf(DateUtil.year(new Date()));
		
		List<Map<String, Object>> stackedLine = mapper.getLoginsCount(queryYear,null);
		
		if(CollectionUtils.isNotEmpty(stackedLine)) {
			for (Map<String, Object> map : stackedLine) {
				String loginMonth = (String) map.get("loginMonth");
				Long loginNumbers = (Long) map.get("loginNumbers");
				
				Map<String,Object> accessMonth = mapper.selectAccessMonth(loginMonth);
				
				if(null != accessMonth) {
					mapper.updateAccessMonth(loginMonth,loginNumbers,null,null);
				}else {
					mapper.insertAccessMonth(IdGeneraterUtils.nextId(),loginMonth,loginNumbers);
				}
				
			}
		}
		
		List<Map<String, Object>> stackedLinePC = mapper.getLoginsNumbers(queryYear,UserInfoHolder.getCurrentUserCorpCode(),"PC");
		if(CollectionUtils.isNotEmpty(stackedLinePC)) {
			for (Map<String, Object> map : stackedLinePC) {
				String loginMonth = (String) map.get("loginMonth");
				Long loginNumbers = (Long) map.get("loginNumbers");
				
				Map<String,Object> accessMonth = mapper.selectAccessMonth(loginMonth);
				
				if(null != accessMonth) {
					mapper.updateAccessMonth(loginMonth,null,loginNumbers,null);
				}
				
			}
		}
		
		List<Map<String, Object>> stackedLineWX = mapper.getLoginsNumbers(queryYear,UserInfoHolder.getCurrentUserCorpCode(),"WX");
		if(CollectionUtils.isNotEmpty(stackedLineWX)) {
			for (Map<String, Object> map : stackedLineWX) {
				String loginMonth = (String) map.get("loginMonth");
				Long loginNumbers = (Long) map.get("loginNumbers");
				
				Map<String,Object> accessMonth = mapper.selectAccessMonth(loginMonth);
				
				if(null != accessMonth) {
					mapper.updateAccessMonth(loginMonth,null,null,loginNumbers);
				}
			}
		}
	}

	@Override
	public Long selectTotalEmployee(String currentUserCorpCode) {
		return mapper.selectTotalEmployee(currentUserCorpCode);
	}

	@Override
	public List<Map<String, Object>> sysUseAnalysisList(Map<String,Object> params) {
		return mapper.sysUseAnalysisList(params);
	}

	@Override
	public List<Map<String, Object>> sysUseAnalysisDetailList(Map<String, Object> params) {
		return mapper.sysUseAnalysisDetailList(params);
	}
    
	
	
	
	
}
