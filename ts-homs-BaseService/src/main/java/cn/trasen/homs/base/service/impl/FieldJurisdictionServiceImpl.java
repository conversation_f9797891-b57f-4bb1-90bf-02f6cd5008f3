/**
 * @Title: FieldJurisdictionServiceImpl.java  
 * @Package: cn.trasen.homs.base.service.impl  
 * @Date: 2021年6月21日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.base.dao.CommGroupJurisdictionFieldMapper;
import cn.trasen.homs.base.dao.CommGroupJurisdictionMapper;
import cn.trasen.homs.base.model.CommGroupJurisdiction;
import cn.trasen.homs.base.model.CommGroupJurisdictionField;
import cn.trasen.homs.base.service.FieldJurisdictionService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
* @ClassName: FieldJurisdictionServiceImpl  
 * @Author: 86189
 * @Date: 2021年6月21日
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class FieldJurisdictionServiceImpl implements FieldJurisdictionService {
    
    @Autowired
    private CommGroupJurisdictionMapper commFieldJurisdictionMapper;
    
    @Autowired
    private CommGroupJurisdictionFieldMapper commGroupJurisdictionFieldMapper;
    
    /**
     * 
    * @Title: insert  
    * @Description: 新增人员字段权限
    * @Params: @param record
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int insert(List<CommGroupJurisdiction> records) {
        
        if(CollectionUtils.isNotEmpty(records)) {
            
            String groupId = records.get(0).getFieldGroupId();
            
            Example example = new Example(CommGroupJurisdiction.class);
            example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            example.and().andEqualTo("fieldGroupId",groupId);
            
            CommGroupJurisdiction commGroupJurisdiction = new CommGroupJurisdiction();
            commGroupJurisdiction.setUpdateDate(new Date());
            commGroupJurisdiction.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            commGroupJurisdiction.setIsDeleted(Contants.IS_DELETED_TURE);
            
            commFieldJurisdictionMapper.updateByExampleSelective(commGroupJurisdiction, example);
            
            
            for(CommGroupJurisdiction record : records) {
                
                String groupJurisdictionId = ApplicationUtils.GUID32();
                record.setCreateDate(new Date());
                record.setCreateUser(UserInfoHolder.getCurrentUserCode());
                record.setCreateUserName(UserInfoHolder.getCurrentUserName());
                record.setId(groupJurisdictionId);
                record.setIsDeleted(Contants.IS_DELETED_FALSE);
                record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                
                if(CollectionUtils.isNotEmpty(record.getFields())) {
                    
                    for(CommGroupJurisdictionField field : record.getFields()) {
                        
                        field.setCreateDate(new Date());
                        field.setCreateUser(UserInfoHolder.getCurrentUserCode());
                        field.setCreateUserName(UserInfoHolder.getCurrentUserName());
                        field.setId(ApplicationUtils.GUID32());
                        field.setIsDeleted(Contants.IS_DELETED_FALSE);
                        field.setGroupJurisdictionId(groupJurisdictionId);
                        field.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                        commGroupJurisdictionFieldMapper.insertSelective(field);
                    }
                }
                
                commFieldJurisdictionMapper.insertSelective(record);
            }
        }
        
        return 1;
         
    }
    
    /**
     * 
    * @Title: update  
    * @Description: 修改人员字段权限
    * @Params: @param record
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int update(CommGroupJurisdiction record) {
        
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        
        if(CollectionUtils.isNotEmpty(record.getFields())) {
            
            List<CommGroupJurisdictionField> fields = getListByGroupJurisdictionId(record.getId());
            
            if(CollectionUtils.isNotEmpty(fields)) {
                
                fields.stream().forEach(item->{
                    item.setUpdateDate(new Date());
                    
                    item.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                    
                    item.setUpdateUserName(UserInfoHolder.getCurrentUserName());
                    
                    item.setIsDeleted(Contants.IS_DELETED_TURE);
                    
                    commGroupJurisdictionFieldMapper.updateByPrimaryKeySelective(item);
                });
            }
            
            for(CommGroupJurisdictionField field : record.getFields()) {
                    field.setCreateDate(new Date());
                    field.setCreateUser(UserInfoHolder.getCurrentUserCode());
                    field.setCreateUserName(UserInfoHolder.getCurrentUserName());
                    field.setId(ApplicationUtils.GUID32());
                    field.setIsDeleted(Contants.IS_DELETED_FALSE);
                    field.setGroupJurisdictionId(record.getId());
                    field.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    commGroupJurisdictionFieldMapper.insertSelective(field);
                
            }
        }
        
        return commFieldJurisdictionMapper.updateByPrimaryKeySelective(record);
    }
    
    /**
     * 
    * @Title: deleted  
    * @Description: 删除人员字段权限
    * @Params: @param id
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    @Transactional(readOnly = false)
    public int deleted(String id) {
        CommGroupJurisdiction record = new CommGroupJurisdiction();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setIsDeleted(Contants.IS_DELETED_TURE);
        return commFieldJurisdictionMapper.updateByPrimaryKeySelective(record);
    }
    
    /**
     * 
    * @Title: getList  
    * @Description: 获取人员字段权限
    * @Params: @param page
    * @Params: @param record
    * @Params: @return      
    * @Return: List<CommGroupJurisdiction>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    public List<CommGroupJurisdiction> getList(CommGroupJurisdiction record) {
        
        Example example = new Example(CommGroupJurisdiction.class);
        
        example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        if(StringUtils.isNoneBlank(record.getFieldGroupId())) {
            
            example.and().andEqualTo("fieldGroupId",record.getFieldGroupId());
            
        }else {//默认查询个人信息
            
            example.and().andEqualTo("fieldGroupId","1");
        }
        
        List<CommGroupJurisdiction> list = commFieldJurisdictionMapper.selectByExample(example);
        
        for(CommGroupJurisdiction item : list) {
            
            List<CommGroupJurisdictionField> fields = getListByGroupJurisdictionId(item.getId());
            
            item.setFields(fields);
        }
        

        
        return list;
    }
    
    /**
     * 
    * @Title: getListByGroupJurisdictionId  
    * @Description: 根据权限分组id查询权限分组字段
    * @Params: @param groupJurisdictionId
    * @Params: @return      
    * @Return: List<CommGroupJurisdictionField>
    * <AUTHOR>
    * @date:2021年6月21日
    * @Throws
     */
    private List<CommGroupJurisdictionField> getListByGroupJurisdictionId(String groupJurisdictionId){
        
        Example example = new Example(CommGroupJurisdictionField.class);
        
        example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        example.and().andEqualTo("groupJurisdictionId",groupJurisdictionId);
        
        List<CommGroupJurisdictionField> fields = commGroupJurisdictionFieldMapper.selectByExample(example);
        
        return fields;
    }
}

