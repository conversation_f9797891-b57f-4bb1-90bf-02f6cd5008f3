package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommSealManage;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommSealManageService
 * @Description TODO
 * @date 2025��6��21�� ����5:44:39
 * <AUTHOR>
 * @version 1.0
 */
public interface CommSealManageService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	Integer save(CommSealManage record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	Integer update(CommSealManage record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommSealManage
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	CommSealManage selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommSealManage>
	 * @date 2025��6��21�� ����5:44:39
	 * <AUTHOR>
	 */
	DataSet<CommSealManage> getDataSetList(Page page, CommSealManage record);

	List<CommSealManage> getCommSealManageList();
}
