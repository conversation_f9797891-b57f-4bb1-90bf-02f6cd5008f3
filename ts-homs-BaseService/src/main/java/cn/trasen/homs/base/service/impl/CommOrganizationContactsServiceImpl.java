package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.homs.base.bean.OrganizationContactsImport;
import cn.trasen.homs.base.bean.OrganizationContactsImportResp;
import cn.trasen.homs.base.mapper.OrganizationMapper;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.model.CommOrganizationContacts;
import cn.trasen.homs.base.mapper.CommOrganizationContactsMapper;
import cn.trasen.homs.base.service.CommOrganizationContactsService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommOrganizationContactsServiceImpl
 * @Description TODO
 * @date 2023年11月20日 上午11:43:39
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommOrganizationContactsServiceImpl implements CommOrganizationContactsService {

    @Autowired
    private CommOrganizationContactsMapper mapper;

    @Autowired
    private OrganizationMapper organizationMapper;
    
    @Autowired
    private GlobalSettingsService globalSettingsService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Transactional(readOnly = false)
    @Override
    public Integer save(CommOrganizationContacts record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        record.setExternalOrgName(record.getOrgName());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(CommOrganizationContacts record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        CommOrganizationContacts record = new CommOrganizationContacts();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CommOrganizationContacts selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }


    @Override
    public DataSet<CommOrganizationContacts> getDataSetList(Page page, Set<String> id, String keyword) {
    	
	    GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
      
        if(2 == globalSetting.getOrgContactType()) {  //自定义科室通讯录
        	List<CommOrganizationContacts> list = mapper.getExternalDataSetList(page, keyword, UserInfoHolder.getCurrentUserCorpCode());
        	return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        }else {
        	List<CommOrganizationContacts> list = mapper.getDataSetList(page, id, keyword, UserInfoHolder.getCurrentUserCorpCode());
        	return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),list);
        }
       
        
    }

    @Override
    public Boolean checkExist(CommOrganizationContacts record) {
        Example example = new Example(CommOrganizationContacts.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("orgId", record.getOrgId());
        example.and().andEqualTo("name", record.getName());

        if (record.getId() != null && !record.getId().equals("")) {
            example.and().andNotEqualTo("id", record.getId());
        }
        Integer i = mapper.selectCountByExample(example);
        return i > 0 ? true : false;
    }

    @Transactional(readOnly = false)
    @Override
    public OrganizationContactsImportResp excelImport(List<OrganizationContactsImport> list) {
    	
        if (list.size() <= 0) {
            throw new BusinessException("错误信息：模板无数据！");
        }
        
        GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
        
        if(2 == globalSetting.getOrgContactType()) {  //自定义导入科室通讯录
        	
        	//需要先清空所有数据
        	mapper.deleteAll();
        	
        	Map<Integer, String> errorMap = Maps.newLinkedHashMap();
        	
        	ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        	
        	List<CommOrganizationContacts> insertList = new ArrayList<>();
        	
        	int row = 1;
        	
        	for (OrganizationContactsImport oc : list) {
                if (oc.getOrgName() == null || oc.getName() == null) {
                    setErrorMap(errorMap, row, "不允许存在空数据");
                    continue;
                }

                if (oc.getTel() == null) {
                    oc.setTel("");
                }
                
                CommOrganizationContacts record = new CommOrganizationContacts();
                
                record.setId(IdGeneraterUtils.nextId());
                record.setCreateDate(new Date());
                record.setUpdateDate(new Date());
                record.setIsDeleted("N");

                if (user != null) {
                    record.setCreateUser(user.getUsercode());
                    record.setCreateUserName(user.getUsername());
                    record.setUpdateUser(user.getUsercode());
                    record.setUpdateUserName(user.getUsername());
                }

                record.setOrgName(oc.getOrgName().trim());
                record.setName(oc.getName().trim());
                record.setTel(oc.getTel().trim());
                record.setSort(row);

                insertList.add(record);
                
                row++;
            }
        	
        	if (insertList.size() > 0) {
                mapper.batchInsert(insertList);
            }
        	
        	OrganizationContactsImportResp res = new OrganizationContactsImportResp();
            res.setInserts(row);
            res.setUpdates(row);
            return res;
        	
        }else {
        	// 获取所有的科室名称
            Example organizationExample = new Example(Organization.class);

            organizationExample.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            List<Organization> oList = organizationMapper.selectByExample(organizationExample);

            // 没有科室不做导入
            if (oList.size() <= 0) {
                throw new BusinessException("错误信息：当前没有科室信息！");
            }

            // 科室的kv数据
            HashMap<String, String> organizationKv = new HashMap<>();

            int i = 0;
            for (Organization o : oList) {
                i++;
                organizationKv.put(o.getName(), o.getOrganizationId());
            }

            // 检查是否有重复数据
            Example commOrganizationContactsExample = new Example(CommOrganizationContacts.class);
            commOrganizationContactsExample.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
            List<CommOrganizationContacts> ocList = mapper.selectByExample(commOrganizationContactsExample);


            // 已经存在的hash数据的 验证数据是新的还是老的
            HashSet<String> contactsExistHash = new HashSet<>();

            // 用于通过hash取数据
            HashMap<String, CommOrganizationContacts> contactsExistHashMap = new HashMap<>();

            for (CommOrganizationContacts ocExist : ocList) {
                String hash = MD5.string2MD5(ocExist.getOrgId() + ocExist.getName().trim());
                contactsExistHash.add(hash);
                contactsExistHashMap.put(hash, ocExist);
            }


            Map<Integer, String> errorMap = Maps.newLinkedHashMap();
            // 检查是否有科室数据不存在
            HashSet<String> contactsHash = new HashSet<>();
            int row = 1;
            for (OrganizationContactsImport oc : list) {
                row++;
                String orgName = oc.getOrgName();
                String name = oc.getName();
                if (orgName == null || name == null) {
                    setErrorMap(errorMap, row, "不允许存在空数据");
                    continue;
                }
                // 去首尾空
                orgName = orgName.trim();
                name = name.trim();

                if (oc.getTel() == null) {
                    oc.setTel("");
                }
                // 检测execl中单行科室名是否存在
                if (!organizationKv.containsKey(orgName)) {
                    setErrorMap(errorMap, row, "没有这个科室:" + orgName);
                    continue;
                }
                // 取出来科室的ID
                oc.setOrgId(organizationKv.get(orgName));
                // 这里是前端提交的数据 orgId + name 取md5 便于验证
                String hash = MD5.string2MD5(oc.getOrgId() + name);
                if (contactsHash.contains(hash)) {
                    setErrorMap(errorMap, row, "数据重复了（" + orgName + "-" + name + "）");
                    continue;
                }
                contactsHash.add(hash);
                oc.setHash(hash);
            }
            errorMap2BusinessException(errorMap);

            // 准备update 和 insert
            // 确保了提交的数据无重复 科室全部存在
            // 如果在老的contactsList 有数据则进行update,否则insert

            ThpsUser user = UserInfoHolder.getCurrentUserInfo();

            List<CommOrganizationContacts> insertList = new ArrayList<>();

            Integer updates = 0;
            Integer inserts = 0;
            for (OrganizationContactsImport oc : list) {
                CommOrganizationContacts record = new CommOrganizationContacts();
                String tel = oc.getTel();
                if (tel == null) {
                    oc.setTel("");
                }
                if (contactsExistHash.contains(oc.getHash())) {
                    updates++;
                    // 修改
                    record = contactsExistHashMap.get(oc.getHash());
                    // 这里只可能该电话 不然hash值对不上

                    record.setUpdateUser(user.getUsercode());
                    record.setUpdateUserName(user.getUsername());
                    // 电话号码一样则不更新
                    if (oc.getTel().trim().equals(record.getTel().trim())) {
                        continue;
                    }
                    record.setTel(oc.getTel().trim());
                    mapper.updateByPrimaryKeySelective(record);
                } else {
                    inserts++;
                    // 添加
                    record.setId(IdGeneraterUtils.nextId());
                    record.setCreateDate(new Date());
                    record.setUpdateDate(new Date());
                    record.setIsDeleted("N");

                    if (user != null) {
                        record.setCreateUser(user.getUsercode());
                        record.setCreateUserName(user.getUsername());
                        record.setUpdateUser(user.getUsercode());
                        record.setUpdateUserName(user.getUsername());
                    }

                    record.setOrgId(oc.getOrgId().trim());
                    record.setName(oc.getName().trim());
                    record.setTel(oc.getTel().trim());
                    record.setSort(0);

                    insertList.add(record);
                }
                // 因为新增和修改是同一个逻辑所以这里不做区分 统一按照add 处理
                syncContacts2OrganizationSingleTime("add", record);
            }

            if (insertList.size() > 0) {
                mapper.batchInsert(insertList);
            }


            OrganizationContactsImportResp res = new OrganizationContactsImportResp();
            res.setInserts(inserts);
            res.setUpdates(updates);
            return res;
        }
        
    }

    private void setErrorMap(Map<Integer, String> errorMap, Integer rowId, String error) {
        errorMap.put(rowId, "第" + rowId + "行出现错误：" + "," + error + "</br>");
    }

    private void errorMap2BusinessException(Map<Integer, String> errorMap) {
        if (errorMap.size() > 0) {
            String error = "";
            for (Map.Entry<Integer, String> s : errorMap.entrySet()) {
                error += s.getValue();
            }
            throw new BusinessException(error);
        }
    }

    @Transactional(readOnly = false)
    public void syncOrganization2Contacts() {
        String cacheKey = "thps:oa:contacts:sync";
        Object hasSync = redisTemplate.opsForValue().get(cacheKey);
        if (hasSync != null && !hasSync.equals("")) {
            return;
        }

        Example Contacts = new Example(CommOrganizationContacts.class);

        Integer has = mapper.selectCountByExample(Contacts);
        if (has != 0) {
            redisTemplate.opsForValue().set(cacheKey, 1);
            return;
        }

        Example organizationExample = new Example(Organization.class);
        organizationExample.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        List<Organization> organizationList = organizationMapper.selectByExample(organizationExample);

        if (organizationList.size() <= 0) {
            return;
        }

        List<CommOrganizationContacts> insertList = new ArrayList<>();
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        for (Organization o : organizationList) {
            CommOrganizationContacts record = new CommOrganizationContacts();

            record.setId(IdGeneraterUtils.nextId());
            record.setCreateDate(new Date());
            record.setUpdateDate(new Date());
            record.setIsDeleted("N");

            if (user != null) {
                record.setCreateUser(user.getUsercode());
                record.setCreateUserName(user.getUsername());
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
            }

            record.setOrgId(o.getOrganizationId().trim());
            record.setName(o.getName().trim());

            if (o.getTel() != null) {
                record.setTel(o.getTel().trim());
            }

            record.setSort(0);
            insertList.add(record);
        }

        if (insertList.size() > 0) {
            mapper.batchInsert(insertList);
            redisTemplate.opsForValue().set(cacheKey, 1);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void syncContacts2OrganizationSingleTime(String act, CommOrganizationContacts record) {
        // 从通讯录到 组织，不会有不存在的情况

        Organization o = new Organization();
        o.setOrganizationId(record.getOrgId());
        o = organizationMapper.selectOne(o);

        // 如果科室联系人名称不等于组织名称直接return
        if (!(record.getName().equals(o.getName()))) {
            return;
        }
        boolean matchSwitch = false;
        switch (act) {
            case "add":
                //说明是顶级科室
            case "edit":
                // 修改
                o.setTel(record.getTel());
                matchSwitch = true;
                break;
            case "delete":
                o.setTel("");
                matchSwitch = true;
                break;
        }
        if (!matchSwitch) {
            return;
        }

        organizationMapper.updateByPrimaryKey(o);
    }

    @Override
    public int batchInsert(List<CommOrganizationContacts> list) {
        if (list.size() <= 0) {
            return 0;
        }
        return mapper.batchInsert(list);
    }

}
