package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationReq {

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String eqName;
    
    @ApiModelProperty(value = "组织机构编码")
    private String eqCode;
    
    @ApiModelProperty(value = "组织机构所属机构编码")
    private String ssoOrgCode;


    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String tel;

    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnable;

}