package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_error_logs")
@Setter
@Getter
public class CommErrorLogs {
    /**
     * 唯一标识
     */
    @Column(name = "ID")
    @ApiModelProperty(value = "唯一标识")
    private String id;

    /**
     * 微服务名称
     */
    @Column(name = "SERVICE_NAME")
    @ApiModelProperty(value = "微服务名称")
    private String serviceName;

    /**
     * 类名称
     */
    @Column(name = "CLASS_NAME")
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * 方法名称
     */
    @Column(name = "METHOD_NAME")
    @ApiModelProperty(value = "方法名称")
    private String methodName;

    /**
     * 异常信息
     */
    @Column(name = "EXCEPTION_MSG")
    @ApiModelProperty(value = "异常信息")
    private String exceptionMsg;

    /**
     * 异常产生时间
     */
    @Column(name = "CREATE_TIME")
    @ApiModelProperty(value = "异常产生时间")
    private Date createTime;

    /**
     * 异常详细信息
     */
    @Column(name = "STACK_TRACE")
    @ApiModelProperty(value = "异常详细信息")
    private String stackTrace;
    
    @Transient
    @ApiModelProperty(value = "查询条件")
    private String condition;
    
    @Transient
    @ApiModelProperty(value = "开始时间")
    private String createTimeBegin;
    
    @Transient
    @ApiModelProperty(value = "结束时间")
    private String createTimeEnd;
}