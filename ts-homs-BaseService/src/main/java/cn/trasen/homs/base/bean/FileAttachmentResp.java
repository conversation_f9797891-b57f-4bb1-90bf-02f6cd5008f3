package cn.trasen.homs.base.bean;

//import cn.trasen.model.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

//import javax.persistence.Column;
//import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021/6/17 13:25
 * @description
 */

@Data
public class FileAttachmentResp {

	@ApiModelProperty(value = "id")
	String id;

	@ApiModelProperty(value = "模块名字")
	String moduleName;

	@ApiModelProperty(value = "文件名称")

	String originalName;

	@ApiModelProperty(value = "文件扩展名")

	String fileExtension;
	@ApiModelProperty(value = "文件大小")

	Long fileSize;
	@ApiModelProperty(value = "预览地址")
	String realPath;

	String filePath;

	Date createDate;

	String createUserName;

	String businessId;

	String createUser;

	Date updateDate;

	String updateUser;

	String isDeleted;
	String fileName;
}