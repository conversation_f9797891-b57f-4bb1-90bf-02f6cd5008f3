package cn.trasen.homs.base.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.support.SessionStatus;

import com.alibaba.fastjson.JSON;
import com.ramostear.captcha.HappyCaptcha;

import cn.hutool.core.codec.Base64;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpRequest;
import cn.trasen.BootComm.utils.RedisUtil;
//import cn.trasen.homs.bean.base.GlobalSetting;
import cn.trasen.homs.base.bean.UserChgPwdReq;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.service.CommLoginLogsService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.utils.AESUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsOrgResp;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.bean.ThpsUserResp;
import cn.trasen.homs.core.service.UserFeignService;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.MathUtil;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.homs.feign.sso.OrgFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @createTime 2021/7/2 11:24
 * @description
 */
@Api(tags = "用户管理")
@RestController
@Slf4j
@RequestMapping("/user")
public class UserController {

    @Autowired
    UserFeignService userFeignService;
    
    @Value("${changePwdByUserCodePlatformUrl}")
    private String changePwdByUserCodePlatformUrl;
    
    @Value("${enableOldUserCode:false}")
    private Boolean enableOldUserCode;
    
    @Autowired
    private GlobalSettingsService globalSettingsService;


    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private CommLoginLogsService commLoginLogsService;
    
    @Autowired
    private InformationFeignService informationFeignService;
    
    @Autowired
    private OrgFeignService orgFeignService;
    
    @Resource
	RedisUtil redisUtil;

    @ApiOperation(value = "修改密码", notes = "修改密码")
    @PostMapping("/chgpwd")
    @NoRepeatSubmit(lockTime = 3)
    public PlatformResult chgpwd(@RequestBody UserChgPwdReq userChgPwdReq) {

    	 String oldpassword = "";
         String newpassword = "";
         try {
             log.info("加密前oldpassword数据：" + userChgPwdReq.getOldpassword());
             log.info("加密前newpassword数据：" + userChgPwdReq.getOldpassword());
             oldpassword = AESUtil.desEncrypt(userChgPwdReq.getOldpassword()).trim();
             newpassword = AESUtil.desEncrypt(userChgPwdReq.getNewpassword()).trim();
             log.info("解密后oldpassword数据：" + oldpassword);
             log.info("解密后newpassword数据：" + newpassword);
         } catch (Exception e1) {
             e1.printStackTrace();
         }

         try {

         	PlatformResult<Object> platformResult = null;
         	List<Map<String, String>> multipleOrg = commLoginLogsService.getMultipleOrg(null,UserInfoHolder.getCurrentUserCode());
         	
         	if(CollectionUtils.isNotEmpty(multipleOrg)){
         		
         		for (Map<String, String> map : multipleOrg) {
         			
         			platformResult = userFeignService.changePwd(map.get("employee_id"), oldpassword, newpassword);
         			
         			globalSettingsService.updatePasswordExpireDate(map.get("employee_id"));
         			
         			log.info("修改数据SSO返回数据：" + platformResult.getMessage());
 				}
         		
         	}else{
         		 platformResult = userFeignService.changePwd(UserInfoHolder.getCurrentUserId(), oldpassword, newpassword);
         		 
         		 globalSettingsService.updatePasswordExpireDate(UserInfoHolder.getCurrentUserId());
         		
         		 log.info("修改数据SSO返回数据：" + platformResult.getMessage());
         	}
             
             if (StringUtils.isNotBlank(changePwdByUserCodePlatformUrl)) {
                 Map<String, String> requestJson = new HashMap<>();
                 requestJson.put("userCode", UserInfoHolder.getCurrentUserCode());
                 String newpassword2 = new String(Base64.encode(newpassword.getBytes()));
                 requestJson.put("password", newpassword2);
                 String result2 = HttpRequest.post(changePwdByUserCodePlatformUrl)
                         .body(JSON.toJSONString(requestJson))
                         .execute().body();

                 log.info("密码统计集成平台返回的数据：" + result2);
             }

             if (platformResult.isSuccess()) {
            	 

                 //更新登录记录表
                 CommLoginLogs record = new CommLoginLogs();
                 record.setUserCode(UserInfoHolder.getCurrentUserCode());
                 record.setRectificationDate(new Date());
                 record.setRectificationType("1");

                 //弱密码规则校验  至少8个字符，至少1个字母，1个数字和1个特殊字符
               //String regex = "^(?=.*[a-z])(?=.*\\d)(?=.*[@#.~$!%*^?&_+-])[A-Za-z\\d@#.~$!%*^?&_+-]{8,50}$";
                 String regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";

                 if (!newpassword.matches(regex)) {
                     record.setPasswordType("2");
                     record.setRectification("0");
                 } else {
                     record.setPasswordType("1");
                     record.setRectification("1");
                 }

                 commLoginLogsService.updateByUserCode(record);

                 PlatformResult p = new PlatformResult();
                 p.setMessage("密码修改成功");
                 p.setSuccess(true);
                 p.setStatusCode(200);
                 return p;
             } else {
                 return PlatformResult.failure(platformResult.getMessage());
             }
         } catch (Exception e) {
             e.printStackTrace();
         }

         return PlatformResult.failure("密码修改失败！");
    }


    @ApiOperation(value = "退出", notes = "退出")
    @RequestMapping(value = "/logout", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult logout(SessionStatus sessionStatus, HttpSession session) {

    	sessionStatus.setComplete();

        // 取机构代码 --兼容saas和普通模式
        String orgCode = UserInfoHolder.getCurrentUserCorpCode();
        String token = UserInfoHolder.getToken();
        String cacheKey = "thps:oa:online:" + token;

        if (orgCode != null && !orgCode.equals("")) {
            cacheKey = "thps:oa:online:" + orgCode + ":" + token;
        }

        redisTemplate.delete(cacheKey);

        userFeignService.loginOut(UserInfoHolder.getToken());

        return PlatformResult.success();
    }


    @ApiOperation(value = "是否管理员", notes = "是否管理员")
    @RequestMapping(value = "/isAdmin", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult isAdmin() {
        return PlatformResult.success(UserLoginService.ISADMIN());
    }


    @ApiOperation(value = "是否人事管理员", notes = "是否人事管理员")
    @RequestMapping(value = "/isHrmAdmin", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult isHrmAdmin() {
        return PlatformResult.success(UserLoginService.getRight("SYS_ARCHIVIST"));
    }

    /**
     * 切换机构登录
     * @param request
     * @param thpsUser
     * @return
     * <AUTHOR>
     * @update 2025-07-28
     */
    @ApiOperation(value = "切换机构登录", notes = "切换机构登录")
    @RequestMapping(value = "/switchAccount", method = {RequestMethod.POST})
    public PlatformResult switchAccountByOrgId(HttpServletRequest request,@RequestBody ThpsUserReq thpsUser) {
    	Assert.hasText(thpsUser.getCorpcode(), "切换机构编码不能为空.");
    	PlatformResult<ThpsOrgResp> resp = orgFeignService.selectThpsOrgByCodeOrName(thpsUser.getCorpcode(), null);
    	if(resp.isSuccess() && null != resp.getObject()){
    		thpsUser.setOrgId(resp.getObject().getOrgId());
    	} else {
    		return PlatformResult.failure("机构不存在");
    	}
    	String token = request.getHeader("token");
    	if(ObjectUtils.isEmpty(token)){
    		token = request.getParameter("token");
    	}
    	if(ObjectUtils.isEmpty(token)){
    		return PlatformResult.failure("token不能为空！");
    	}
    	return userFeignService.switchAccount(thpsUser, token);
    }


    @ApiOperation(value = "登录", notes = "登录")
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public PlatformResult login(HttpServletRequest request,@RequestBody ThpsUserReq thpsUser) {
    	
    	Assert.hasText(thpsUser.getPassword(), "密码不能为空.");
        Assert.hasText(thpsUser.getUsercode(), "账号不能为空.");

        GlobalSetting globalSetting = globalSettingsService.getSafeGlobalSetting();
        if (null != globalSetting.getVerifyCode() && 1 == globalSetting.getVerifyCode()
                && null != globalSetting.getVerifyCodeType() && 1 == globalSetting.getVerifyCodeType()
                && !"myself".equals(thpsUser.getSessionid())) {
            log.info("======" + thpsUser.getVerifyCode());
            log.info("======" + (String) request.getSession().getAttribute("happy-captcha"));
            boolean flag = HappyCaptcha.verification(request, thpsUser.getVerifyCode(), true);
            if (!flag) {
                return PlatformResult.failure("验证码错误，请重新输入");
            }
        }

        thpsUser.setSessionid("");
        thpsUser.setRemarkid("");

        log.info("加密前password数据：" + thpsUser.getPassword());
        try {
            thpsUser.setPassword(AESUtil.desEncrypt(thpsUser.getPassword().trim()).trim());
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        log.info("解密后password数据：" + thpsUser.getPassword());

        
        if(enableOldUserCode) {
        	String userCode = commLoginLogsService.getUserCodeByOldCode(thpsUser.getUsercode());
        	if(StringUtils.isNoneBlank(userCode)) {
        		thpsUser.setUsercode(userCode);
        	}
        }

        //需要去查下一下机构编码
        String orgCode = "";
        if(StringUtils.isNotBlank(thpsUser.getOrgCode())){
        	 //多机构
        	 orgCode = thpsUser.getOrgCode();
        	 List<Map<String, String>> multipleOrg = commLoginLogsService.getMultipleOrg(orgCode,thpsUser.getUsercode());
        	 thpsUser.setOldusercode(multipleOrg.get(0).get("userCode"));
        	 thpsUser.setUsercode(multipleOrg.get(0).get("userCode"));
        	 thpsUser.setOrgCode(thpsUser.getOrgCode());
        }else{
        	//单机构
        	 orgCode = commLoginLogsService.selectOrgCodeByUserCode(thpsUser.getUsercode());
             thpsUser.setOrgCode(orgCode);
        }
       
        PlatformResult platformResult = userFeignService.login(thpsUser);

        if (platformResult.isSuccess() && platformResult.getStatusCode() == 200) {

            ThpsUserResp obj = (ThpsUserResp) platformResult.getObject();

            CommLoginLogs record = new CommLoginLogs();

            //弱密码规则校验  至少8个字符，至少1个字母，1个数字和1个特殊字符
          //String regex = "^(?=.*[a-z])(?=.*\\d)(?=.*[@#.~$!%*^?&_+-])[A-Za-z\\d@#.~$!%*^?&_+-]{8,50}$";
            String regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";


            if (!thpsUser.getPassword().matches(regex)) {
                record.setPasswordType("2");
                record.setRectification("0");
            } else {
                record.setPasswordType("1");
                record.setRectification("1");
            }
            record.setLoginTime(new Date());
            String clientIP = ServletUtil.getClientIP(request, null);
            record.setLoginIp(clientIP);
            record.setUserCode(thpsUser.getUsercode());
            record.setLoginType("PC");
            record.setSsoOrgCode(obj.getCorpcode());
            record.setUserName(obj.getUsername());
            commLoginLogsService.save(record,obj.getCorpcode());
            
            String cacheKey = "thps:oa:online:" + obj.getToken();
            if (orgCode != null && !orgCode.equals("")) {
                cacheKey = "thps:oa:online:" + orgCode + ":" + obj.getToken();
            }
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(record), 36000, TimeUnit.SECONDS);
        }


        String message = platformResult.getMessage();
        if (message.contains("登录帐号不存在") || message.contains("登录密码错误")) {
            platformResult.setMessage("账号或密码错误");
        }

        return platformResult;
    }
    
    
    @ApiOperation(value = "企业微信验证码", notes = "企业微信验证码")
    @RequestMapping(value = "/login/sendLoginVerifyCode", method = {RequestMethod.POST})
    public PlatformResult sendLoginVerifyCode(HttpServletRequest request,@RequestBody ThpsUserReq thpsUser){
    	
    	Assert.hasText(thpsUser.getPassword(), "密码不能为空.");
    	Assert.hasText(thpsUser.getUsercode(), "账号不能为空.");
    	
    	log.info("加密前password数据：" + thpsUser.getPassword());
     	try {
     	 	thpsUser.setPassword(AESUtil.desEncrypt(thpsUser.getPassword().trim()).trim());
  		} catch (Exception e1) {
  			e1.printStackTrace();
  		}
  		log.info("解密后password数据：" + thpsUser.getPassword());
          
  		PlatformResult platformResult = userFeignService.login(thpsUser);
        
  		if(platformResult.isSuccess() && platformResult.getStatusCode() == 200){
  			String openId = commLoginLogsService.selectOpenIdByUserCode(thpsUser.getUsercode());
  			
  			if(StringUtils.isEmpty(openId)){
  				platformResult.setSuccess(false);
  				platformResult.setMessage("您还未绑定企业微信，请先登录企业微信OA！");
  				
  				return platformResult;
  			}
  			
  			String captchaCode = String.valueOf(MathUtil.getRandom(100000,999999).intValue());
  			
  			String content = "登录验证码为:"+ captchaCode+",此验证码仅用于OA系统登录使用，请勿泄露，五分钟内有效。";
  		  
  			redisUtil.set("loginVerifyCode_" + thpsUser.getUsercode(), captchaCode, 300);
  			
  			ThpsUserResp thpsUserResp = (ThpsUserResp) platformResult.getObject();
  			
  			NoticeReq notice = 
  					NoticeReq.builder()
	    				.content(content)
	    				.noticeType("3")
	    				.wxSendType("2")
	    				.receiver(thpsUser.getUsercode())
	    				.sender("admin")
	    				.senderName("admin")
	    				.build();
  			informationFeignService.sendNotice(notice,thpsUserResp.getToken());
  			
  			platformResult.setMessage("验证码已发送至您的企业微信，请登录企业微信查看！");
  		}
  		
  		 String message = platformResult.getMessage();
         if(message.contains("登录帐号不存在") || message.contains("登录密码错误")){
         	platformResult.setMessage("账号或密码错误");
         }
         return platformResult;
    }
    
    
    @RequestMapping(value = "/login/loginVerifyCode",method = RequestMethod.POST)
    public PlatformResult<String> getLoginVerifyCode(String usercode,String captcha) throws Exception {
    	
    	Assert.hasText(usercode, "账号不能为空.");
    	Assert.hasText(captcha, "验证码不能为空.");
    	
    	try{
    		//获取验证码
    		String redVerifyCode = (String) redisUtil.get("loginVerifyCode_"+usercode);
    		if(null  == redVerifyCode) {
    			return PlatformResult.failure("验证码已过期");
    		}else{
    			   if (captcha.equals(redVerifyCode)){
    				   return PlatformResult.success("验证码验证成功");
    			   }else{
    				   return PlatformResult.failure("验证码错误");
    			   }
    		}
    	} catch (Exception e) {
			e.printStackTrace();
			return PlatformResult.failure("校验验证码失败,失败原因:" + e.getMessage());
		}
    }
    
    @RequestMapping(value = "/login/getMultipleOrg", method = RequestMethod.POST)
    public PlatformResult<List<Map<String,String>>> getMultipleOrg(String usercode){

        try {
        	
        	List<Map<String,String>> orgList = commLoginLogsService.getMultipleOrg(null,usercode);
        	
        	return PlatformResult.success(orgList);
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("查询失败,失败原因:" + e.getMessage());
        }
    }

}
