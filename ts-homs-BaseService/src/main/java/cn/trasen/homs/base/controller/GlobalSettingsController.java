package cn.trasen.homs.base.controller;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "全局设置")
@RestController
public class GlobalSettingsController {

    private static final Logger logger = LoggerFactory.getLogger(GlobalSettingsController.class);

    @Resource
    private GlobalSettingsService globalSettingsService;

    /**
     * @Author: Liz<PERSON>huo
     * @Description: 查询全局设置
     * @Date: 2020/4/29 16:11
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.system.model.DictItem>
     **/
    @ApiOperation(value = "查询全局设置", notes = "查询全局设置")
    @GetMapping("/globalSetting/getGlobalSetting")
    public PlatformResult<GlobalSetting> getGlobalSetting(String isAll) {
        try {
            GlobalSetting globalSetting = globalSettingsService.getGlobalSetting(isAll);
            return PlatformResult.success(globalSetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    
    @ApiOperation(value = "查询全局设置", notes = "查询全局设置")
    @GetMapping("/globalSetting/getSafeGlobalSetting")
    public PlatformResult<GlobalSetting> getSafeGlobalSetting() {
        try {
            GlobalSetting globalSetting = globalSettingsService.getSafeGlobalSetting();
            return PlatformResult.success(globalSetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    
    @ApiOperation(value = "查询全局设置", notes = "查询全局设置")
    @GetMapping("/globalSetting/getAllGlobalSetting")
    public PlatformResult<GlobalSetting> getAllGlobalSetting() {
        try {
            GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
            return PlatformResult.success(globalSetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 通过ID查询全局设置
     * @Date: 2020/4/29 16:11
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.system.model.DictItem>
     **/
    @ApiOperation(value = "通过ID查询全局设置", notes = "通过ID查询全局设置")
    @GetMapping("/globalSetting/getGlobalSettingById")
    public PlatformResult<GlobalSetting> getGlobalSettingById(String id) {
        GlobalSetting entity = new GlobalSetting();
        entity.setId(id);
        try {
            GlobalSetting globalSetting = globalSettingsService.getGlobalSettingById(entity);
            return PlatformResult.success(globalSetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    /**
     *
     * @Title: insert
     * @Description: 新增全局设置
     * @param: @param record
     * @param: @return
     * @return: PlatformResult<String>
     * @author: YueC
     * @date:   2020年4月20日 下午5:10:45
     * @throws
     */
    @ApiOperation(value = "新增全局设置", notes = "新增全局设置")
    @PostMapping("/globalSetting/save")
    @NoRepeatSubmit(lockTime = 2)
    public PlatformResult<String> insert(@RequestBody GlobalSetting globalSetting) {
        try {
            if(StringUtils.isEmpty(globalSetting.getWebTitle())){
                globalSetting.setWebTitle(globalSetting.getOrgCode());
            }
            globalSettingsService.insert(globalSetting);
            return PlatformResult.success("新增成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("新增失败,失败原因:"+e.getMessage());
        }
    }

    /**
     *
     * @Title: update
     * @Description: 修改友情链接
     * @param: @param record
     * @param: @return
     * @return: PlatformResult<String>
     * @author: YueC
     * @date:   2020年4月20日 下午5:10:50
     * @throws
     */
    @ApiOperation(value = "修改全局设置", notes = "修改全局设置")
    @PostMapping("/globalSetting/update")
    @NoRepeatSubmit(lockTime = 2)
    public PlatformResult<String> update(@RequestBody GlobalSetting globalSetting) {
        try {
            globalSettingsService.update(globalSetting);
            return PlatformResult.success("修改成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("修改失败,失败原因:"+e.getMessage());
        }
    }

    /**
     *
     * @Title: deleteById
     * @Description: 删除友情链接
     * @param: @param id
     * @param: @return
     * @return: PlatformResult<String>
     * @author: YueC
     * @date:   2020年4月20日 下午6:21:13
     * @throws
     */
    @ApiOperation(value = "删除全局设置   ", notes = "删除全局设置   ")
    @PostMapping("/globalSetting/deletedById")
    @NoRepeatSubmit(lockTime = 2)
    public PlatformResult<String> deleteById(String id) {
        try {
            globalSettingsService.deleted(id);
            return PlatformResult.success("删除成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("删除失败,失败原因:"+e.getMessage());
        }
    }

}
