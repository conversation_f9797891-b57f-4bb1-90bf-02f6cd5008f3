package cn.trasen.homs.base.model;

import javax.persistence.Column;
import javax.persistence.Table;

import cn.trasen.homs.core.model.BaseBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 11:36
 * @description
 */
@Table(name = "comm_organization_allocation_config")
@Data
public class OrganizationAllocationConfig  extends BaseBean {
    String operator;
    Float num1;
    Float num2;
    String code;
    @Column(name = "sso_org_code")
    String ssoOrgCode;
}