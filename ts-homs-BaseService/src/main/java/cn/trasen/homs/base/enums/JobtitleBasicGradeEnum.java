package cn.trasen.homs.base.enums;


import lombok.Getter;

/**
* @description:
* @return:
* @author: l<PERSON><PERSON>
* @createTime: 2021/8/7 11:24
*/

@Getter
public enum JobtitleBasicGradeEnum {

	CLASS(1, "职称类别"),

	LEVEL(2, "职称等级"),
	NAME(3, "职称名称");


	private final Integer key;
	private final String val;

	private JobtitleBasicGradeEnum(Integer key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(Integer key) {
		for (JobtitleBasicGradeEnum item : JobtitleBasicGradeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "N";
	}

	/**
	 * @param val
	 * @Title: getKeyByVal
	 * @Description: 根据val获得key值
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:30:51
	 */
	public static Integer getKeyByVal(String val) {
		for (JobtitleBasicGradeEnum item : JobtitleBasicGradeEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return 0;
	}
}