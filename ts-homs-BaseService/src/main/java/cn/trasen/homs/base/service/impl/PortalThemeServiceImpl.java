package cn.trasen.homs.base.service.impl;



import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.dao.PortalElementMapper;
import cn.trasen.homs.base.dao.PortalThemeMapper;
import cn.trasen.homs.base.model.PortalElement;
import cn.trasen.homs.base.model.PortalElementCustom;
import cn.trasen.homs.base.model.PortalTheme;
import cn.trasen.homs.base.service.OrgGroupService;
import cn.trasen.homs.base.service.PortalElementCustomService;
import cn.trasen.homs.base.service.PortalThemeService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.OAEmployeeFeignService;
import tk.mybatis.mapper.entity.Example;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class PortalThemeServiceImpl implements PortalThemeService{

    @Resource
    private PortalThemeMapper portalThemeMapper;
    
    @Resource
    private PortalElementMapper portalElementMapper;
    
    @Resource
    private OrgGroupService orgGroupService;
    
    @Autowired
    private OAEmployeeFeignService oaEmployeeFeignService;
    
    @Autowired
    private PortalElementCustomService portalElementCustomService;

    @Override
    @Transactional(readOnly = false)
    public void insert(PortalTheme portalTheme) {
        String themeId = String.valueOf(IdWork.id.nextId());
        portalTheme.setId(themeId);
        portalTheme.setCreateUser(UserInfoHolder.getCurrentUserCode());
        portalTheme.setCreateUserName(UserInfoHolder.getCurrentUserName());
        portalTheme.setIsDeleted(Contants.IS_DELETED_FALSE);
        portalTheme.setCreateTime(new Date());
        portalTheme.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        portalThemeMapper.insertSelective(portalTheme);
        
        if(null != portalTheme.getPortalElement() && portalTheme.getPortalElement().size() > 0) {
            List<PortalElement> elementList = portalTheme.getPortalElement();
            for (PortalElement portalElement : elementList) {
                portalElement.setId(String.valueOf(IdWork.id.nextId()));
                portalElement.setCreateTime(new Date());
                portalElement.setCreateUser(UserInfoHolder.getCurrentUserCode());
                portalElement.setCreateUserName(UserInfoHolder.getCurrentUserName());
                portalElement.setIsDeleted(Contants.IS_DELETED_FALSE);
                portalElement.setThemeId(themeId);
                portalElement.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                
                portalElementMapper.insertSelective(portalElement);
            }
            //portalElementMapper.bacthInsert(elementList);
        }
    }

    @Override
    @Transactional(readOnly = false)
    public void update(PortalTheme portalTheme) {
        
        if(null != portalTheme.getPortalElement() && portalTheme.getPortalElement().size() > 0) {
            List<PortalElement> elementList = portalTheme.getPortalElement();
            PortalElement record = new PortalElement();
            record.setThemeId(portalTheme.getId());
            portalElementMapper.delete(record);
            for (PortalElement portalElement : elementList) {
                portalElement.setId(String.valueOf(IdWork.id.nextId()));
                portalElement.setCreateTime(new Date());
                portalElement.setCreateUser(UserInfoHolder.getCurrentUserCode());
                portalElement.setCreateUserName(UserInfoHolder.getCurrentUserName());
                portalElement.setIsDeleted(Contants.IS_DELETED_FALSE);
                portalElement.setThemeId(portalTheme.getId());
                portalElement.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                portalElementMapper.insertSelective(portalElement);
            }
        }
        
        portalThemeMapper.updateByPrimaryKeySelective(portalTheme);
    }

    @Override
    @Transactional(readOnly = false)
    public void delete(String id) {
        
        PortalTheme portalTheme = portalThemeMapper.selectByPrimaryKey(id);
        portalTheme.setIsDeleted(Contants.IS_DELETED_TURE);
        portalThemeMapper.updateByPrimaryKeySelective(portalTheme);
        
        Example example = new Example(PortalElement.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("themeId", id);
        List<PortalElement> elementList = portalElementMapper.selectByExample(example);
        for (PortalElement portalElement : elementList) {
            portalElement.setIsDeleted(Contants.IS_DELETED_TURE);
            portalElementMapper.updateByPrimaryKeySelective(portalElement);
        }
    }

    @Override
    public PortalTheme selectById(String id) {
        
        PortalTheme portalTheme = portalThemeMapper.selectByPrimaryKey(id);
        
        //查询子元素
        Example example = new Example(PortalElement.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("themeId", id);
        example.setOrderByClause("sord asc");
        List<PortalElement> list = portalElementMapper.selectByExample(example);
        if(list != null && list.size() > 0) {
            portalTheme.setPortalElement(list);
        }
       
        return portalTheme;
    }

    @Override
    public PortalTheme selectDefaultPortalTheme(String themeId) {
    	
    	PortalTheme portalTheme = null;
    	if(StringUtils.isNotBlank(themeId)) {
    		 portalTheme = portalThemeMapper.selectByPrimaryKey(themeId);
    	}else {
    		 portalTheme = portalThemeMapper.selectDefaultPortalTheme(UserInfoHolder.getCurrentUserCode(),UserInfoHolder.getCurrentUserCorpCode());
    	}
        
        if(null == portalTheme) {
            portalTheme = portalThemeMapper.selectDefaultPortalTheme(null,UserInfoHolder.getCurrentUserCorpCode());
        }
        
        //查询子元素
        List<PortalElementCustom> elementCustomList = portalElementCustomService.getElementCustomByUserCode(portalTheme.getId(),UserInfoHolder.getCurrentUserCode());
        
        Example example = new Example(PortalElement.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("themeId", portalTheme.getId());
        example.setOrderByClause("sord asc");
        List<PortalElement> list = portalElementMapper.selectByExample(example);
        
        if(list != null && list.size() > 0) {
        	
        	if(CollectionUtils.isNotEmpty(elementCustomList)) {
             	for (PortalElement portalElement : list) {
					for (PortalElementCustom portalElementCustom : elementCustomList) {
						if(portalElement.getId().equals(portalElementCustom.getElementId())) {
							portalElement.setHeight(portalElementCustom.getHeight());
							portalElement.setWidth(portalElementCustom.getWidth());
							portalElement.setMaxHeight(portalElementCustom.getMaxHeight());
							portalElement.setMaxWidth(portalElementCustom.getMaxWidth());
							portalElement.setMinHeight(portalElementCustom.getMinHeight());
							portalElement.setMinWidth(portalElementCustom.getMinWidth());
							portalElement.setXpos(portalElementCustom.getXpos());
							portalElement.setYpos(portalElementCustom.getYpos());
							portalElement.setIsDraggable(portalElementCustom.getIsDraggable());
							portalElement.setIsResizable(portalElementCustom.getIsResizable());
						}
					}
				}
            }
        	
            portalTheme.setPortalElement(list);
        }
       
        return portalTheme;
    }

    @Override
    public List<PortalTheme> selectPortalThemeList(PortalTheme portalTheme) {
        
        if("Y".equals(portalTheme.getAuthorization())) {
            portalTheme.setUserCode(UserInfoHolder.getCurrentUserCode());
            portalTheme.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptId());
            List<String> groupIds = orgGroupService.selectGroupIdByUserCode(UserInfoHolder.getCurrentUserCode());
            portalTheme.setGroupIds(groupIds);
            PlatformResult<List<String>> result = oaEmployeeFeignService.getRoleIdsByUserCode(UserInfoHolder.getCurrentUserCode());
            if(result.isSuccess()) {
                List<String> roleIds = result.getObject();
                portalTheme.setRoleIds(roleIds);
            }
        }
        
        portalTheme.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<PortalTheme> list = portalThemeMapper.selectPortalThemeList(portalTheme);
        
        if("Y".equals(portalTheme.getIsFindChild())) {//查询子元素
            for (PortalTheme theme : list) {
                Example example = new Example(PortalElement.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("themeId", theme.getId());
                example.setOrderByClause("sord asc");
                
                List<PortalElement> elementList = portalElementMapper.selectByExample(example);
                
                if(elementList != null && elementList.size() > 0) {
                    theme.setPortalElement(elementList);
                }
            }
        }
        return list;
    }
    
}
