package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:41
 * @description
 */

@Data
public class PostCategoryListResp {
    @ApiModelProperty(value = "类别ID")
    String postCategoryId;

    @ApiModelProperty(value = "类别名称")
    String postCategoryName;

    @ApiModelProperty(value = "是否启用")
    private String isEnable;

    @ApiModelProperty(value = "字典类型ID")
    private String dictTypeId;

    @ApiModelProperty(value = "是否启用")
    private String isEnableLable;

    @ApiModelProperty(value = "主键")
    private String id;
}
