package cn.trasen.homs.base.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.base.bean.JobtitleInfoReq;
import cn.trasen.homs.base.mapper.JobtitleInfoMapper;
import cn.trasen.homs.base.model.JobtitleInfo;
import cn.trasen.homs.base.service.IJobtitleInfoService;
import cn.trasen.homs.core.contants.Contants;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 17:44
 * @description
 */

@Service
public class JobtitleInfoService implements IJobtitleInfoService {


    @Autowired
    JobtitleInfoMapper jobtitleInfoMapper;


    @Override
    /**
     * 是否存在
     *
     * @description:
     * @param: jobtitleInfoReq
     * @return: boolean
     * @author: liyuan
     * @createTime: 2021/8/7 17:45
     */
    public boolean exists(JobtitleInfoReq jobtitleInfoReq) {

        Example example = new Example(JobtitleInfo.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if (!StringUtils.isBlank(jobtitleInfoReq.getJobtitleName())) {
            criteria.andEqualTo("jobtitleName", jobtitleInfoReq.getJobtitleName());
        }

        int count = jobtitleInfoMapper.selectCountByExample(example);
        if (count > 0) {
            return true;
        }
        return false;
    }

}