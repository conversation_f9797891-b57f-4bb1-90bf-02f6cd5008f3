package cn.trasen.homs.base.groupLeader.service;

import java.util.List;

import cn.trasen.homs.base.groupLeader.model.HrmsColumnAuthority;

/**
 * @ClassName HrmsColumnAuthorityService
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsColumnAuthorityService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	Integer save(HrmsColumnAuthority record);



	/**
	 * @Title selectById
	 * @Description 根据岗位查询
	 * @return HrmsColumnAuthority
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	List<HrmsColumnAuthority> selectById(String id);


	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsColumnAuthority
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	List<HrmsColumnAuthority> bycolumnId(String id);
}
