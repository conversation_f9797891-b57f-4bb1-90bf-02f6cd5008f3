package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:41
 * @description
 */

@Data
public class PostCategorySaveReq {
    @ApiModelProperty(value = "类别ID")
    String postCategoryId;

    @ApiModelProperty(value = "类别名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 20, message = "名称不能超过20个字符")
    String postCategoryName;

    @ApiModelProperty(value = "主键")
    private String id;
}
