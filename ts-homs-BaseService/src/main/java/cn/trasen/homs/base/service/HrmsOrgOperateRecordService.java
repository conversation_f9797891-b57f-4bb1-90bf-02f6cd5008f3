package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.HrmsOrgOperateRecord;

/**   
 * @Title: HrmsOrgOperateRecordService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 机构操作记录 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月23日 上午10:32:24 
 * @version V1.0   
 */
public interface HrmsOrgOperateRecordService {
	
	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月23日 上午11:27:58
	 */
	int batchInsert(List<HrmsOrgOperateRecord> list);

}
