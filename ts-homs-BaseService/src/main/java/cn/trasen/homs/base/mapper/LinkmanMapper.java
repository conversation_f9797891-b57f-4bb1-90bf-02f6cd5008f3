package cn.trasen.homs.base.mapper;

import java.util.List;

import cn.trasen.homs.base.model.Linkman;
import tk.mybatis.mapper.common.Mapper;

public interface LinkmanMapper extends Mapper<Linkman> {
//
//    /**
//     * @Author: <PERSON><PERSON><PERSON><PERSON>
//     * @Description: 根据登录人的部门编码查询内部联系人
//     * @Date: 2020/3/6 11:25
//     * @Param:
//     * @return: java.util.List<cn.trasen.hrm.model.Employee>
//     **/
//    List<Employee> selectInnerLinkMan(Employee employee, Page page);
//
//    /**
//     * @Author: Liz<PERSON>huo
//     * @Description: 通过兼职科室查询内部联系人信息
//     * @Date: 2020/3/6 14:05
//     * @Param:
//     * @return: java.util.List<cn.trasen.hrm.model.Employee>
//     **/
//    List<Employee> selectInnerLinkManByEmpDeptCode(String[] empDeptCode);
//
//    /**
//     * @Author: <PERSON><PERSON>hu<PERSON>
//     * @Description: 内部联系人列表导出
//     * @Date: 2020/5/14 10:14
//     * @Param:
//     * @return: java.util.List<cn.trasen.hrm.model.Employee>
//     **/
//    List<Employee> selectExpotInnerLinkMan(Employee employee);

    /**
     * @Author: Lizhihuo
     * @Description: 查询个人联系人列表(导出)
     * @Date: 2020/5/14 10:33
     * @Param:
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
    List<Linkman> findExpotLinkman(Linkman linkman);
}