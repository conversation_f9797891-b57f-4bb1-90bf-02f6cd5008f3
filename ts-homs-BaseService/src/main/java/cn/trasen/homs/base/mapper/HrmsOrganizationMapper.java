package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.model.HrmsOrganization;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsOrganizationMapper extends Mapper<HrmsOrganization> {

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月16日 下午5:04:38
	 */
	int batchInsert(List<HrmsOrganization> list);
	
	/**
	 * @Title: findListByParentId
	 * @Description: 根据上级ID查询机构列表
	 * @param parentId
	 * @Return List<HrmsOrganization>
	 * <AUTHOR>
	 * @date 2020年6月29日 下午5:49:17
	 */
	List<HrmsOrganization> findListByParentId(String parentId);

	/**查询组织机构返回带人数
	 * @param parMap
	 * @return
	 */
	List<HrmsOrganization> getOrgTree(Map<String, Object> parMap);

	/**查询组织机构返回带人数
	 * @param parMap
	 * @return
	 */
	List<HrmsOrganization> getOrgTreeByTmpEmp(Map<String, Object> parMap);



	/**
	 * 
	 * @MethodName: selectChildList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param organizationId
	 * @return String
	 * @date 2022-11-11 08:05:45
	 */
	String selectChildList(String organizationId);

	/**
	 * 
	 * @param orgId
	 * @return
	 */
	List<String> getChildOrgIdsList(String orgId);

	/**
	 * 
	 * @param syscode
	 * @return
	 */
	List<String> getMappingDeptIdsList(String syscode);
	
}
