package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationListReq {
    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String name;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String eqName;
    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String eqParentId;

    /**
     * 父类ID
     */
    @ApiModelProperty(value = "是否启用")
    private String isEnable;

    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String tel;

    @ApiModelProperty(value = "领导名称")
    private String empCode;

    
    String ssoOrgCode;
}