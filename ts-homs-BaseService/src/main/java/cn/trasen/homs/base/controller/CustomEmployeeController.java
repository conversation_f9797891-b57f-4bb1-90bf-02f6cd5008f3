/**
 * @Title: CustomEmployeeController.java
 * @Package: cn.trasen.homs.base.controller
 * @Date: 2021年6月19日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.base.bean.CustomEmployeeResp;
import cn.trasen.homs.base.bean.EmployeeStorageData;
import cn.trasen.homs.base.bean.HistoricalListResp;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bo.EmployeeDetails;
import cn.trasen.homs.base.model.*;
import cn.trasen.homs.base.service.*;
import cn.trasen.homs.base.utils.DateUtils;
import cn.trasen.homs.base.utils.ExcelUtils;
import cn.trasen.homs.core.annotation.RequestLimit;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.PartyBuildingFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.export.ExcelExportServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

//import org.apache.commons.collections.CollectionUtils;


/**
 * @ClassName: CustomEmployeeController
 * @Author: 86189
 * @Date: 2021年6月19日
 */
@RestController
@Api(tags = "自定义人员档案操作Controller")
public class CustomEmployeeController {

    private static final Logger logger = LoggerFactory.getLogger(CustomEmployeeController.class);

    @Autowired
    private CustomEmployeeService customEmployeeService;

    @Autowired
    CommEmployeeShowService commEmployeeShowService;

    @Autowired
    private HrmsEmployeeService hrmsEmployeeService;

    @Autowired
    private CommEmployeeFieldGroupService commEmployeeFieldGroupService;

    @Autowired
    private CommEmployeeFieldService commEmployeeFieldService;

    @Autowired
    private PartyBuildingFeignService partyBuildingFeignService;

    @Autowired
    private IDictItemService iDictItemService;
    
    @Autowired
    private CommInterfaceRegisterService commInterfaceRegisterService;
    
	@Autowired
	SystemUserFeignService systemUserFeignService;

    @ApiOperation(value = "查询员工信息列表(分页)", notes = "查询员工信息列表(分页)")
    @PostMapping(value = "/cusotm/getEmployeePageListByCustom")
    public DataSet<Map<String, String>> getEmployeePageListByCustom(Page page, HrmsEmployee record) {
        List<Map<String, String>> list = customEmployeeService.getEmployeePageListByCustom(page, record);
        return new DataSet<Map<String, String>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),page.getTotalCount(), list);
    }

    @ApiOperation(value = "根据id查询自定义人员档案", notes = "根据id查询自定义人员档案")
    @PostMapping("/cusotmEmployee/findByIdAndDetails/{id}")
    public PlatformResult<Map<String, List<Map<String, String>>>> findByIdAndDetails(@PathVariable String id) {
        try {
            return PlatformResult.success(customEmployeeService.findByIdAndDetails(id));
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "根据id删除自定义人员档案", notes = "根据id删除自定义人员档案")
    @PostMapping("/cusotmEmployee/deleteById/{id}")
    public PlatformResult<String> deleteById(@PathVariable String id) {
        try {
            customEmployeeService.deleteById(id);

            try {
               commInterfaceRegisterService.updateSyncEmp(id);
            } catch (Exception e) {
                logger.error("同步人员异常：" + e.getMessage(), e);
            }

            return PlatformResult.success();
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "新增/修改自定义人员档案", notes = "新增/修改自定义人员档案")
    @PostMapping("/cusotmEmployee/save")
    @RequestLimit(period = 3)
    public PlatformResult<String> save(@RequestBody CustomEmpModel record) {
        try {
            String message = customEmployeeService.uniqueCheck(record);
            String employeeId = "";
            boolean isAdd = true;
            if (StringUtils.isBlank(record.getEmployeeId())) {
                isAdd = true;
            } else {
                isAdd = false;
            }

            if (StringUtils.isBlank(message)) {

                employeeId = customEmployeeService.save(record);
                customEmployeeService.storage(null,employeeId);

            } else {

                return PlatformResult.failure(message);
            }

            try {
                PlatformResult<String> updatePartyBuilding = null;
                updatePartyBuilding = partyBuildingFeignService.updatePartyBuildingManageByHrms(employeeId);
                logger.error("更新党建人员：" + updatePartyBuilding.toString());
            } catch (Exception e) {
                logger.error("更新党建人员异常：" + e.getMessage(), e);
            }

            try {
                if (isAdd) {
                	commInterfaceRegisterService.addSyncEmp(employeeId);
                } else {
                	commInterfaceRegisterService.updateSyncEmp(employeeId);
                }
            } catch (Exception e) {
                logger.error("同步人员异常：" + e.getMessage(), e);
            }

            return PlatformResult.success("操作成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "停用员工账号", notes = "停用员工账号")
	@PostMapping(value = "/cusotmEmployee/disable")
	public PlatformResult<String> disable(@RequestBody ThpsUserReq entity) {
		Result disable = null;
		try {
			 disable = systemUserFeignService.disable(entity.getId(),String.valueOf(entity.getStatus()));
			 
			 hrmsEmployeeService.updateDisable(entity.getId(),String.valueOf(entity.getStatus()));
			 
			 try {
				 commInterfaceRegisterService.updateSyncEmp(entity.getId());
	          } catch (Exception e) {
	                logger.error("同步人员异常：" + e.getMessage(), e);
	          }

			 return PlatformResult.success(disable.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}

    @ApiOperation(value = "人员修改审核列表", notes = "人员修改审核列表")
    @PostMapping("/cusotmEmployee/update/workflowList")
    public DataSet<CustomEmployeeResp> getDataWorkflowList(Page page, CustomEmployeeResp record) {
        try {
            List<CustomEmployeeResp> list = customEmployeeService.getDataWorkflowList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
                    list);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation(value = "人员修改审核明细", notes = "人员修改审核明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "operationId", value = "修改时操作id", required = false, paramType = "query", dataType = "String")})
    @PostMapping("/cusotmEmployee/update/auditDetails/{operationId}")
    public PlatformResult<List<CommEmployeeUpdateDetail>> auditDetails(@PathVariable String operationId) {

        List<CommEmployeeUpdateDetail> list = customEmployeeService.auditDetails(operationId);

        return PlatformResult.success(list);
    }

    @ApiOperation(value = "人员修改审核", notes = "人员修改审核")
    @PostMapping("/cusotmEmployee/update/audit")
    public PlatformResult<String> audit(@RequestBody CustomEmployeeResp record, HttpServletRequest request) {
        try {
            String token = UserInfoHolder.getToken();
            if (StringUtil.isEmpty(token)) {
                token = request.getParameter("token");
            }
            customEmployeeService.audit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }

    }

    @ApiOperation(value = "获取员工表字段信息", notes = "获取员工表字段信息")
    @PostMapping("/employee/getEmployeeFields")
    public PlatformResult<List<Map<String, String>>> getEmployeeFields() {
        try {
            List<Map<String, String>> map = customEmployeeService.getEmployeeFields();
            return PlatformResult.success(map);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    @ApiOperation(value = "修改人员信息流程审核完接口", notes = "修改人员信息流程审核完接口")
    @PostMapping("/cusotmEmployee/auditFinish")
    public void auditFinish(HttpServletRequest request) {
        logger.error("进入回调方法");
        try {
            Map<String, Object> formData = new HashMap<>();
            Enumeration<String> enu = request.getParameterNames();
            while (enu.hasMoreElements()) {
                String key = (String) enu.nextElement();
                formData.put(key, request.getParameter(key));
                logger.error("========" + key + "==========" + request.getParameter(key));
            }
            // 业务id
            String operationId = formData.get("L_BusinessId").toString();
            String response = formData.get("L_SelectResponse").toString();
            logger.error("========operationId==========" + operationId);
            logger.error("========response==========" + response);

            customEmployeeService.auditFinish(operationId);


        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            logger.error("人员同步回调接口异常" + e.getMessage(), e);
        }
    }

    @ApiOperation(value = "导出员工档案", notes = "导出员工档案")
    @GetMapping("/cusotmEmployee/exportEmployee")
    public void signContractExport(Page page, HttpServletRequest request, HttpServletResponse response,
                                   HrmsEmployee record) {

        page.setPageSize(Integer.MAX_VALUE);
        if (record.getOrgIds() != null && record.getOrgIds().size() == 1) {
            if (null == record.getOrgIds().get(0) || "null".equals(record.getOrgIds().get(0))) {
                record.setOrgIds(null);
            }
        }
        // 导出文件名称
        String name = "人员档案.xlsx";

        // 模板位置
        String templateUrl = "template/customEmployee.xlsx";
        // 导出数据列表
        try {
            List<Map<String, String>> list = customEmployeeService.getEmployeePageListByCustom(page, record);
            int index = 1;
            for (int i = 0; i < list.size(); i++) {

                Map<String, String> map = list.get(i);

                map.put("no", String.valueOf(index));

                index++;
            }
            if (CollectionUtils.isNotEmpty(list)) {
                ExportUtil.export(request, response, list, name, templateUrl);
            } else {
                ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	/*@ApiOperation(value = "导出员工档案", notes = "导出员工档案")
	@GetMapping("/cusotmEmployee/exportColumnEmployee")
	public void exportColumnEmployee(Page page, HttpServletRequest request, HttpServletResponse response,
			HrmsEmployee record) {

		page.setPageSize(Integer.MAX_VALUE);
		if(record.getOrgIds() != null && record.getOrgIds().size() == 1) {
			if(null == record.getOrgIds().get(0) || "null".equals(record.getOrgIds().get(0))) {
				record.setOrgIds(null);
			}
		}
		// 导出文件名称
		String filename = "人员花名册";

	
		// 导出数据列表
		try {
			List<Map<String, String>> list = customEmployeeService.getEmployeePageListByCustom(page, record);
			int index = 1;
			for(int i = 0;i <list.size();i++) {
				
				Map<String,String> map = list.get(i);
				
				map.put("no", String.valueOf(index));
				
				index++;
			}
			// 获取导出表头
			List<CommEmployeeShow> exportList = commEmployeeShowService.getColumnExport();
			
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
			colList.add(new ExcelExportEntity("序号", "no",6));
			if (exportList != null && exportList.size() > 0) {
				
				for(int i =0 ;i < exportList.size();i++) {
					colList.add(new ExcelExportEntity(exportList.get(i).getFieldLable(), exportList.get(i).getFieldName()));
				}
			}
			
			try {
	            Workbook workbook = ExcelExportUtil
	                    .exportExcel(new ExportParams(filename, "花名册", ExcelType.XSSF), colList, list);

	            response.setContentType("application/vnd.ms-excel");
	            response.setCharacterEncoding("UTF-8");
	            response.setHeader("Content-disposition", "attachment; filename="
	                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

	            OutputStream fos = response.getOutputStream();
	            
	            workbook.write(fos);
	            fos.close();

	        } catch (FileNotFoundException e) {
	        	logger.error(e.getMessage(),e);
	        } catch (IOException e) {
	        	logger.error(e.getMessage(),e);
	        }
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}*/


    @ApiOperation(value = "导出员工档案全量信息", notes = "导出员工档案全量信息")
    @GetMapping("/cusotmEmployee/exportColumnEmployee")
    public void exportColumnEmployeeFiles(Page page, HttpServletRequest request, HttpServletResponse response,
                                          HrmsEmployee record) {
        page.setPageSize(Integer.MAX_VALUE);
        if (record.getOrgIds() != null && record.getOrgIds().size() == 1) {
            if (null == record.getOrgIds().get(0) || "null".equals(record.getOrgIds().get(0))) {
                record.setOrgIds(null);
            }
        }
        //HSSFWorkbook  workbook = new  HSSFWorkbook();
        //创建一个空工作本;
        Workbook workbook = null;
        // 导出数据列表
        try {
            List<CommEmployeeFieldGroup> fieldGrouplist = commEmployeeFieldGroupService.getDataList();
            if (fieldGrouplist != null && fieldGrouplist.size() > 0) {
                for (CommEmployeeFieldGroup fieldGroup : fieldGrouplist) {
                    // 导出文件名称
                    String filename = "员工档案";
                    String fileSheetName = fieldGroup.getGroupName();

                    record.setGroupName(fieldGroup.getGroupName());
                    record.setTableName(fieldGroup.getTableName());
                    record.setFieldGroupId(fieldGroup.getId());

                    List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

                    List<Map<String, String>> list = new ArrayList<Map<String, String>>();
                    //根据组动态表、查询该表数据列和姓名、工号
                    if (!StringUtil.isEmpty(fieldGroup.getId()) && "1".equals(fieldGroup.getId())) {
                        fileSheetName = "花名册";

                        list = customEmployeeService.getEmployeePageListByCustom(page, record);
                        // 获取导出表头
                        List<CommEmployeeShow> exportList = commEmployeeShowService.getColumnExport();
                        //如果经开导出离职  追加离职原因
                        //经开医院排除8开头的账号
                        if (exportList != null && exportList.size() > 0) {
                            for (int i = 0; i < exportList.size(); i++) {
                                colList.add(new ExcelExportEntity(exportList.get(i).getFieldLable(), exportList.get(i).getFieldName()));
                            }
                        }

                        //单独导出离职人员的话 最后追加上离职原因 离职时间
                        List<String> employeeStatuses = record.getEmployeeStatuses();
                        if (null != employeeStatuses && employeeStatuses.size() == 1 && "4".equals(employeeStatuses.get(0))) {
                            colList.add(new ExcelExportEntity("离职时间", "jk_lzsj", 20));
                            colList.add(new ExcelExportEntity("离职原因", "jk_lzyy", 30));
                            //追加上离职原因
                            List<Map<String, String>> jklzyy = customEmployeeService.getJklzyy();
                            Map<String, String> _jklzyy = jklzyy.stream().collect(Collectors.toMap(m -> m.get("employeeId"), m -> m.get("remark") + ""));
                            Map<String, String> _jklzsj = jklzyy.stream().collect(Collectors.toMap(m -> m.get("employeeId"), m -> m.get("jk_lzsj") + ""));
                            for (Map<String, String> _dataMap : list) {
                                _dataMap.put("jk_lzsj", _jklzsj.get(_dataMap.get("employee_id")));
                                _dataMap.put("jk_lzyy", _jklzyy.get(_dataMap.get("employee_id")));
                            }
                        }
                        //退休的加上退休时间
                        if (null != employeeStatuses && employeeStatuses.size() == 1 && "8".equals(employeeStatuses.get(0))) {
                            colList.add(new ExcelExportEntity("退休时间", "tx_date", 20));
                            //追加上退休时间
                            List<Map<String, String>> jklzyy = customEmployeeService.getJkTx();
                            Map<String, String> _jklzyy = jklzyy.stream().collect(Collectors.toMap(m -> m.get("employeeId"), m -> m.get("tx_date") + ""));
                            for (Map<String, String> _dataMap : list) {
                                _dataMap.put("tx_date", _jklzyy.get(_dataMap.get("employee_id")));
                            }
                        }


                    } else {
                        list = commEmployeeFieldGroupService.getGroupListTrends(record);

                        CommEmployeeField commEmployeeField = new CommEmployeeField();
                        commEmployeeField.setGroupId(fieldGroup.getId());
                        commEmployeeField.setIsHide(0);
                        // 获取导出表头
                        List<CommEmployeeField> exportList = commEmployeeFieldService.getFielListByGroupid(commEmployeeField);

                        colList.add(new ExcelExportEntity("序号", "no", 6));
                        colList.add(new ExcelExportEntity("姓名", "employee_name", 14));
                        colList.add(new ExcelExportEntity("工号", "employee_no", 14));
                        if (exportList != null && exportList.size() > 0) {
                            for (int i = 0; i < exportList.size(); i++) {
                                if (!"employee_name".equals(exportList.get(i).getFieldName()) && !"employee_no".equals(exportList.get(i).getFieldName()) && !"file".equals(exportList.get(i).getFieldType())) {//去除原来表里的姓名/工号字段
                                    colList.add(new ExcelExportEntity(exportList.get(i).getShowName(), exportList.get(i).getFieldName()));
                                }
                            }
                        }
                    }

                    int index = 1;
                    for (int i = 0; i < list.size(); i++) {
                        Map<String, String> map = list.get(i);
                        map.put("no", String.valueOf(index));
                        index++;
                    }

                    try {

                        if (fieldGrouplist.indexOf(fieldGroup) == 0) {
                            workbook = ExcelExportUtil
                                    .exportExcel(new ExportParams(fileSheetName, fileSheetName, ExcelType.XSSF), colList, list);
                        }

                        if (fieldGrouplist.indexOf(fieldGroup) > 0) {
                            new ExcelExportServer().createSheetForMap(workbook, new ExportParams(fileSheetName, fileSheetName, ExcelType.XSSF), colList, list);
                            //Sheet sh2 = workbook.createSheet();
                            //ExcelExportServer EE = new  ExcelExportServer();
                            //EE.insertDataToSheet(workbook, new ExportParams(filename, filename, ExcelType.XSSF), colList, list,sh2);
                            response.setContentType("application/vnd.ms-excel");
                            response.setCharacterEncoding("UTF-8");
                            response.setHeader("Content-disposition", "attachment; filename="
                                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

                            if (fieldGroup.equals(fieldGrouplist.get(fieldGrouplist.size() - 1))) {//判断是否最后一个list
                                OutputStream fos = response.getOutputStream();
                                workbook.write(fos);
                                fos.close();
                            }
                        }

                    } catch (FileNotFoundException e) {
                        logger.error(e.getMessage(), e);
                    } catch (IOException e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }


    @ApiOperation(value = "自定义导出", notes = "自定义导出")
    @GetMapping("/cusotmEmployee/customeExport")
    public void exportEmployee(HrmsEmployee record, HttpServletRequest request, HttpServletResponse response) {

        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(99999);
        List<Map<String, String>> list = customEmployeeService.getEmployeePageListByCustom(page, record);

        String name = "员工信息.xls";
        String templateUrl = "template/roster.xlsx";

        ExcelUtils.exportByChooseFields(record, response, list, name, templateUrl);
    }

    @ApiOperation(value = "浏阳中导出个人信息采集表", notes = "浏阳中导出个人信息采集表")
    @GetMapping("/cusotmEmployee/grxxcjb/{employeeId}")
    public PlatformResult<Map<String, Object>> grxxcjb(@PathVariable String employeeId, HttpServletRequest request, HttpServletResponse response) {

        try {
            List<String> ids = new ArrayList<>();
            ids.add(employeeId);
            List<HrmsEmployeeResp> employeeDetailByIds = hrmsEmployeeService.getEmployeeDetailByIds(ids);
            HrmsEmployeeResp hrmsEmployeeResp = employeeDetailByIds.get(0);
            Map<String, Object> returnMap = new HashMap<>();

            //查询个人信息
            returnMap.put("avatar", hrmsEmployeeResp.getAvatar());  //头像
            returnMap.put("employeeName", hrmsEmployeeResp.getEmployeeName());  //姓名
            returnMap.put("genderText", hrmsEmployeeResp.getGenderText());   //性别
            returnMap.put("nationalityName", hrmsEmployeeResp.getNationalityName());   //民族
            returnMap.put("orgName", hrmsEmployeeResp.getOrgName());   //科室
            returnMap.put("establishmentTypeText", hrmsEmployeeResp.getEstablishmentTypeText());   //编制类型
            returnMap.put("empAge", hrmsEmployeeResp.getEmpAge());   //年龄
            returnMap.put("workStartDate", hrmsEmployeeResp.getWorkStartDate());   //参加工作时间
            returnMap.put("entryDate", hrmsEmployeeResp.getEntryDate());   //入院时间
            returnMap.put("positionName", hrmsEmployeeResp.getPositionName());   // 职务
            returnMap.put("personalIdentityName", hrmsEmployeeResp.getPersonalIdentityName());   //岗位名称
            returnMap.put("phoneNumber", hrmsEmployeeResp.getPhoneNumber());   //电话
            returnMap.put("politicalStatusText", hrmsEmployeeResp.getPoliticalStatusText());   //政治面貌
            returnMap.put("partyDate", hrmsEmployeeResp.getPartyDate());   //入党时间

            returnMap.put("address", hrmsEmployeeResp.getAddress());   //现住址
            returnMap.put("birthplace", hrmsEmployeeResp.getBirthplace());  // 籍贯

            if (hrmsEmployeeResp.getBirthday() != null) {
                returnMap.put("birthday", DateUtils.getStringDateShort(hrmsEmployeeResp.getBirthday()));   //出生年月
            } else {
                returnMap.put("birthday", null);   //出生年月
            }

            returnMap.put("idCard", hrmsEmployeeResp.getIdentityNumber());  // 身份证


            //处理员工状态 employee_status
            String employeeStatus = hrmsEmployeeResp.getEmployeeStatus();

            Map<String, String> stringStringMap = iDictItemService.convertDictMap("employee_status");
            returnMap.put("employeeStatus", stringStringMap.get(employeeStatus));
            //根据id查询学习经历
            List<EmployeeDetails> lyzXuexi = hrmsEmployeeService.getLyzXuexi(employeeId);
            if (lyzXuexi != null && lyzXuexi.size() > 0) {
                returnMap.put("lyzXuexi", lyzXuexi);
            } else {
                returnMap.put("lyzXuexi", null);
            }

            //工作经历 院外
            List<EmployeeDetails> lyzYuanwai = hrmsEmployeeService.getLyzYuanwai(employeeId);
            if (lyzYuanwai != null && lyzYuanwai.size() > 0) {
                returnMap.put("lyzYuanwai", lyzYuanwai);
            } else {
                returnMap.put("lyzYuanwai", null);
            }

            //工作经历 院内
            List<EmployeeDetails> lyzYuannei = hrmsEmployeeService.getLyzYuannei(employeeId);
            if (lyzYuannei != null && lyzYuannei.size() > 0) {
                returnMap.put("lyzYuannei", lyzYuannei);
            } else {
                returnMap.put("lyzYuannei", null);
            }

            //职称简历
            List<EmployeeDetails> lyzZhicheng = hrmsEmployeeService.getLyzZhicheng(employeeId);
            if (lyzZhicheng != null && lyzZhicheng.size() > 0) {
                returnMap.put("lyzZhicheng", lyzZhicheng);
            } else {
                returnMap.put("lyzZhicheng", null);
            }

            //进修经历
            List<EmployeeDetails> lyzJinxiu = hrmsEmployeeService.getLyzJinxiu(employeeId);
            if (lyzJinxiu != null && lyzJinxiu.size() > 0) {
                returnMap.put("lyzJinxiu", lyzJinxiu);
            } else {
                returnMap.put("lyzJinxiu", null);
            }
            //规培经历
            List<EmployeeDetails> lyzGuipei = hrmsEmployeeService.getLyzGuipei(employeeId);
            if (lyzGuipei != null && lyzGuipei.size() > 0) {
                returnMap.put("lyzGuipei", lyzGuipei);
            } else {
                returnMap.put("lyzGuipei", null);
            }

            String zuigaoxueli = "";
            String zhuanye = "";
            if (lyzXuexi != null && lyzXuexi.size() > 0) {
                zuigaoxueli = lyzXuexi.get(0).getV1();
                zhuanye = lyzXuexi.get(0).getV5();
            }

            String xianzyouzhicheng = "";
            if (lyzZhicheng != null && lyzZhicheng.size() > 0) {
                for (int i = 0; i < lyzZhicheng.size(); i++) {
                    if ("1".equals(lyzZhicheng.get(i).getV7())) {
                        xianzyouzhicheng = lyzZhicheng.get(i).getV1();
                    }
                }
            }
            returnMap.put("caijishijian", DateUtils.getStringDateShort(new Date()));
            returnMap.put("zuigaoxueli", zuigaoxueli);  // 最高学历
            returnMap.put("zhuanye", zhuanye);    //专业
            returnMap.put("xianzyouzhicheng", xianzyouzhicheng);  // 现有职称
            return PlatformResult.success(returnMap);
        } catch (Exception e) {
            logger.error("个人信息采集导出异常" + e.getMessage(), e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    //人才画像 数据
    @ApiOperation(value = "人才画像", notes = "人才画像")
    @GetMapping("/cusotmEmployee/rchx/{employeeId}")
    public PlatformResult<LinkedHashMap<String, Object>> rchx(@PathVariable String employeeId, HttpServletRequest request, HttpServletResponse response) {
        try {
            String year1 = String.valueOf(getCurrentYearByYear() - 1);
            String year2 = String.valueOf(getCurrentYearByYear() - 2);
            String year3 = String.valueOf(getCurrentYearByYear() - 3);
            String year4 = String.valueOf(getCurrentYearByYear() - 4);
            String year5 = String.valueOf(getCurrentYearByYear() - 5);
            String year6 = String.valueOf(getCurrentYearByYear() - 6);

            String ky_year1 = String.valueOf(getCurrentYearByYear());
            String ky_year2 = String.valueOf(getCurrentYearByYear() - 1);
            String ky_year3 = String.valueOf(getCurrentYearByYear() - 2);
            String ky_year4 = String.valueOf(getCurrentYearByYear() - 3);
            String ky_year5 = String.valueOf(getCurrentYearByYear() - 4);
            String ky_year6 = String.valueOf(getCurrentYearByYear() - 5);


            List<String> list = new ArrayList<>();
            list.add(year6);
            list.add(year5);
            list.add(year4);
            list.add(year3);
            list.add(year2);
            list.add(year1);

            List<Map<String, Object>> khjg = hrmsEmployeeService.getKhjg(employeeId);

            Map<String, Object> kaoping = new LinkedHashMap<>();
            kaoping.put(year1 + "年", "");
            kaoping.put(year2 + "年", "");
            kaoping.put(year3 + "年", "");
            kaoping.put(year4 + "年", "");
            kaoping.put(year5 + "年", "");
            kaoping.put(year6 + "年", "");


            for (int i = 0; i < list.size(); i++) {
                if (!khjg.isEmpty()) {

                    if (khjg != null && khjg.size() > 0) {
                        for (int j = 0; j < khjg.size(); j++) {
                            if (list.get(i).equals(khjg.get(j).get("key"))) {
                                kaoping.put(list.get(i) + "年", khjg.get(j).get("val"));
                            }
                        }
                    }
                }
            }

            LinkedHashMap<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("ndkp", kaoping);

            //查询科研论文信息
            List<Map<String, Object>> kylw = hrmsEmployeeService.getKylw(employeeId);
            Map<String, Object> _kylw = new LinkedHashMap<>();
            _kylw.put(ky_year1 + "年", "");
            _kylw.put(ky_year2 + "年", "");
            _kylw.put(ky_year3 + "年", "");
            _kylw.put(ky_year4 + "年", "");
            _kylw.put(ky_year5 + "年", "");
            _kylw.put(ky_year6 + "年", "");
            if (kylw != null && kylw.size() > 0) {
                Map<String, List<Map<String, Object>>> kylwListMap =
                        kylw.stream().collect(Collectors.groupingBy(item -> item.get("publish_date").toString().substring(0, 4)));
                List<Map<String, Object>> maps = kylwListMap.get(ky_year6);
                if (null != kylwListMap.get(ky_year6)) {
                    Map<String, Object> _year6 = new LinkedHashMap<>();
                    _year6.put("data", kylwListMap.get(ky_year6));
                    _year6.put("size", kylwListMap.get(ky_year6).size());
                    _kylw.put(ky_year6 + "年", _year6);
                }
                if (null != kylwListMap.get(ky_year5)) {
                    Map<String, Object> _year5 = new LinkedHashMap<>();
                    _year5.put("data", kylwListMap.get(ky_year5));
                    _year5.put("size", kylwListMap.get(ky_year5).size());
                    _kylw.put(ky_year5 + "年", _year5);
                }
                if (null != kylwListMap.get(ky_year4)) {
                    Map<String, Object> _year4 = new LinkedHashMap<>();
                    _year4.put("data", kylwListMap.get(ky_year4));
                    _year4.put("size", kylwListMap.get(ky_year4).size());
                    _kylw.put(ky_year4 + "年", _year4);
                }
                if (null != kylwListMap.get(ky_year3)) {
                    Map<String, Object> _year3 = new LinkedHashMap<>();
                    _year3.put("data", kylwListMap.get(ky_year3));
                    _year3.put("size", kylwListMap.get(ky_year3).size());
                    _kylw.put(ky_year3 + "年", _year3);
                }
                if (null != kylwListMap.get(ky_year2)) {
                    Map<String, Object> _year2 = new LinkedHashMap<>();
                    _year2.put("data", kylwListMap.get(ky_year2));
                    _year2.put("size", kylwListMap.get(ky_year2).size());
                    _kylw.put(ky_year2 + "年", _year2);
                }
                if (null != kylwListMap.get(ky_year1)) {
                    Map<String, Object> _year1 = new LinkedHashMap<>();
                    _year1.put("data", kylwListMap.get(ky_year1));
                    _year1.put("size", kylwListMap.get(ky_year1).size());
                    _kylw.put(ky_year1 + "年", _year1);
                }
            }
            returnMap.put("kylw", _kylw);

            //奖惩情况
            //查询科研论文信息
            List<Map<String, Object>> jcjl = hrmsEmployeeService.getJcjl(employeeId);
            Map<String, Object> _jcjlData = new LinkedHashMap<>();
            _jcjlData.put("奖励", "");
            _jcjlData.put("荣誉称号", "");
            _jcjlData.put("惩处", "");
            if (jcjl != null && jcjl.size() > 0) {
                Map<String, List<Map<String, Object>>> jcjlListMap =
                        jcjl.stream().collect(Collectors.groupingBy(item -> item.get("reward_penalty_type").toString()));


                if (null != jcjlListMap.get("1")) {
                    Map<String, Object> _jcjl = new LinkedHashMap<>();
                    _jcjl.put("size", jcjlListMap.get("1").size());
                    _jcjl.put("data", jcjlListMap.get("1"));
                    _jcjlData.put("奖励", _jcjl);

                   int _size =  jcjlListMap.get("1").size();
                    for (int i = 0; i < _size; i++) {
                        jcjlListMap.get("1").get(i).put("reward_penalty_type","奖励");
                    }

                }
                if (null != jcjlListMap.get("2")) {
                    Map<String, Object> _jcjl = new LinkedHashMap<>();
                    _jcjl.put("size", jcjlListMap.get("2").size());
                    _jcjl.put("data", jcjlListMap.get("2"));
                    _jcjlData.put("荣誉称号", _jcjl);
                    int _size =  jcjlListMap.get("2").size();
                    for (int i = 0; i < _size; i++) {
                        jcjlListMap.get("2").get(i).put("reward_penalty_type","荣誉称号");
                    }
                }
                if (null != jcjlListMap.get("3")) {
                    Map<String, Object> _jcjl = new LinkedHashMap<>();
                    _jcjl.put("size", jcjlListMap.get("3").size());
                    _jcjl.put("data", jcjlListMap.get("3"));
                    _jcjlData.put("惩处", _jcjl);
                    int _size =  jcjlListMap.get("3").size();
                    for (int i = 0; i < _size; i++) {
                        jcjlListMap.get("3").get(i).put("reward_penalty_type","惩处");
                    }
                }
            }
            returnMap.put("jcqk", _jcjlData);

            //查询进修记录
            List<Map<String, Object>> jxjl = hrmsEmployeeService.getJxjl(employeeId);

            List<Map<String, Object>> gpjl = hrmsEmployeeService.getGpjl(employeeId);

            Map<String, Object> _jxjlData = new LinkedHashMap<>();
            _jxjlData.put("规培", "");
            _jxjlData.put("规范化培训", "");
            _jxjlData.put("进修", "");



            if(jxjl != null && jxjl.size() > 0 ){
                Map<String, Object> _jxjl = new LinkedHashMap<>();
                _jxjl.put("size", jxjl.size());
                _jxjl.put("data", jxjl);
                _jxjlData.put("进修", _jxjl);
            }else{
                Map<String, Object> _jxjl = new LinkedHashMap<>();
                _jxjl.put("size", 0);
                _jxjl.put("data", jxjl);
                _jxjlData.put("进修", _jxjl);
            }

            if (gpjl != null && gpjl.size() > 0) {
                Map<String, Object> _jxjl = new LinkedHashMap<>();
                _jxjl.put("size", gpjl.size());
                _jxjl.put("data", gpjl);
                _jxjlData.put("规培", _jxjl);
            }else{
                Map<String, Object> _jxjl = new LinkedHashMap<>();
                _jxjl.put("size", 0);
                _jxjl.put("data", gpjl);
                _jxjlData.put("规培", _jxjl);
            }


     /*       if (null != jxjl && jxjl.size() > 0) {
                Map<String, List<Map<String, Object>>> jxjlListMap =
                        jxjl.stream().collect(Collectors.groupingBy(item -> item.get("out_type").toString()));
                if (null != jxjlListMap.get("进修")) {
                    Map<String, Object> _jxjl = new LinkedHashMap<>();
                    _jxjl.put("size", jxjlListMap.get("进修").size());
                    _jxjl.put("data", jxjlListMap.get("进修"));
                    _jxjlData.put("进修", _jxjl);
                }
                if (null != jxjlListMap.get("规培")) {
                    Map<String, Object> _jxjl = new LinkedHashMap<>();
                    _jxjl.put("size", jxjlListMap.get("规培").size());
                    _jxjl.put("data", jxjlListMap.get("规培"));
                    _jxjlData.put("规培", _jxjl);
                }
            }*/
            //获取住院医生规范化培训
            List<Map<String, Object>> zyysgfhpx = hrmsEmployeeService.getZyysgfhpx(employeeId);
            if (null != zyysgfhpx && zyysgfhpx.size() > 0) {
                Map<String, Object> _jxjl = new LinkedHashMap<>();
                _jxjl.put("size", zyysgfhpx.size());
                _jxjl.put("data", zyysgfhpx);
                _jxjlData.put("规范化培训", _jxjl);
            }
            returnMap.put("jxgp", _jxjlData);

            return PlatformResult.success(returnMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    //获取当前年
    public static Integer getCurrentYearByYear() {
        Year year = Year.now();
        int yearValue = year.getValue();
        return Integer.valueOf(yearValue);
    }

    //根据员工id查询技术档案
    @ApiOperation(value = "根据员工id查询技术档案", notes = "根据员工id查询技术档案")
    @GetMapping("/employee/jsdaList/{employeeId}")
    public PlatformResult<List<Map<String, String>>> getEmployeeJsda(@PathVariable String employeeId) {
        try {
            List<Map<String, String>> map = customEmployeeService.getEmployeeJsda(employeeId);
            return PlatformResult.success(map);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    //查询人员是否有在审核的流程
    @ApiOperation(value = "根据员工id查询是否有在审核的流程", notes = "根据员工id查询是否有在审核的流程")
    @GetMapping("/employee/reviewStatus/{employeeId}")
    public PlatformResult<Map<String, Object>> reviewStatusByEmployeeId(@PathVariable String employeeId) {
        try {
            Map<String, Object> map = customEmployeeService.authStatusByEmployeeId(employeeId);
            return PlatformResult.success(map);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    //暂存员工信息的接口
    @ApiOperation(value = "暂存", notes = "暂存")
    @PostMapping("/cusotmEmployee/storage")
    public PlatformResult<String> storage(@RequestBody EmployeeStorageData data) {
        try {
            customEmployeeService.storage(data.getContent(),data.getEmployeeId());
            return PlatformResult.success("操作成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取暂存", notes = "获取暂存")
    @GetMapping("/cusotmEmployee/getstorage/{employeeId}")
    public PlatformResult<EmployeeStorageData> getstorage(@PathVariable String employeeId) {
        try {
             EmployeeStorageData content = customEmployeeService.getstorage(employeeId);
            return PlatformResult.success(content);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    //标记数据合格不合格
    @ApiOperation(value = "标记数据合格不合格", notes = "标记数据合格不合格")
    @PostMapping("/cusotmEmployee/updateDetailsStatus")
    public PlatformResult<String> updateDetailsStatus(@RequestBody List<CommEmployeeUpdateDetail> list) {
        try {
            if (list != null && list.size() >0){
                customEmployeeService.updateDetailsStatus(list);
            }else{
                return PlatformResult.failure("未核对数据");
            }
            return PlatformResult.success("操作成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "获取人员信息修改的审批节点和审批人", notes = "获取人员信息修改的审批节点和审批人")
    @GetMapping("/cusotmEmployee/getEmployeeTask/{employeeNo}")
    public PlatformResult<Map<String,String>> getEmployeeTask(@PathVariable String employeeNo) {
        try {
            return PlatformResult.success(customEmployeeService.getEmployeeTask(employeeNo));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return PlatformResult.failure("服务端异常，异常信息：" + e.getMessage());
        }
    }

    @ApiOperation(value = "查看历史记录", notes = "查看历史记录")
    @GetMapping("/cusotmEmployee/getHistoricalRecords")
    public DataSet<HistoricalListResp> getHistoricalRecords(Page page, String employeeId) {
        try {
            List<HistoricalListResp> historicalRecords = customEmployeeService.getHistoricalRecords(employeeId);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), historicalRecords);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            page.setTotalCount(0);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), Collections.EMPTY_LIST);
        }
    }

    @ApiOperation(value = "导出员工档案报表", notes = "导出员工档案报表")
    @GetMapping("/cusotmEmployee/exportReport")
    public void exportReport(Page page, HttpServletResponse response,
                                          HrmsEmployee record) {
        customEmployeeService.exportReport(page,response,record);
    }

    @ApiOperation(value = "查询员工报表(分页)", notes = "查询员工报表(分页)")
    @PostMapping(value = "/cusotm/getEmployeePageListReport")
    public DataSet<Map<String, String>> getEmployeePageListReport(Page page, HrmsEmployee record) {
        List<Map<String, String>> list = customEmployeeService.getEmployeePageListReport(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
                page.getTotalCount(), list);
    }
}
