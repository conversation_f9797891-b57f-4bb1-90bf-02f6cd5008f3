package cn.trasen.homs.base.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class SysLogsReq implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 方法
     */
    @Column(name = "func_name")
    @ApiModelProperty(value = "方法")
    private String funcName;

    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    private String params;

    /**
     * 操作描述
     */
    @Column(name = "operation_desc")
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    /**
     * 操作人
     */
    @Column(name = "operation_user")
    @ApiModelProperty(value = "操作人")
    private String operationUser;
    
    /**
     * 操作人名称
     */
    @Column(name = "operation_user_name")
    @ApiModelProperty(value = "操作人名称")
    private String operationUserName;

    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date operationTime;

    /**
     * 操作IP
     */
    @Column(name = "operation_ip")
    @ApiModelProperty(value = "操作IP")
    private String operationIp;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作来源
     */
    @ApiModelProperty(value = "操作来源")
    private String recource;
    
    /**
     *  查询条件（开始时间）
     */
    @Transient
    private String beginDate;
    
    /**
     * 查询条件（结束时间）
     */
    @Transient
    private String endDate;
}