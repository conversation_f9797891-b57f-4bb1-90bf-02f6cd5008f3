package cn.trasen.homs.base.enums;

import lombok.Getter;

/**
* 文件访问类型
* <AUTHOR>
* @date 2021/12/17 12:57
*/
@Getter
public enum FileScopeEnum {

	PUBLIC(2, "公共"),

	PRIVATE(1, "私有");


	private final Integer key;
	private final String val;

	private FileScopeEnum(Integer key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String FileScopeEnum(Integer key) {
		for (FileScopeEnum item : FileScopeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return FileScopeEnum.PRIVATE.getVal();
	}

	/**
	 * @Title: getKeyByVal
	 * @Description: 根据val获得key值
	 * @param val
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:30:51
	 */
	public static Integer getKeyByVal(String val) {
		for (FileScopeEnum item : FileScopeEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return FileScopeEnum.PRIVATE.getKey();
	}

}
