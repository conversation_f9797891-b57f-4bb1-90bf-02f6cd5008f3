package cn.trasen.homs.base.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "comm_employee_field")
@Setter
@Getter
public class CommEmployeeField {
    @Id
    private String id;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "分组id")
    private String groupId;

    /**
     * 显示名称
     */
    @Column(name = "show_name")
    @ApiModelProperty(value = "显示名称")
    private String showName;

    /**
     * 字段名称
     */
    @Column(name = "field_name")
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段类型
     */
    @Column(name = "field_type")
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 字段长度
     */
    @Column(name = "field_length")
    @ApiModelProperty(value = "字段长度")
    private Integer fieldLength;

    /**
     * 时间格式
     */
    @Column(name = "data_format")
    @ApiModelProperty(value = "时间格式")
    private String dataFormat;
    
    @Column(name = "dict_source")
    @ApiModelProperty(value = "字典来源")
    private String dictSource;

    /**
     * 处理方式(1四舍五入2向上取整3向下取整)
     */
    @Column(name = "treatment_method")
    @ApiModelProperty(value = "处理方式(1四舍五入2向上取整3向下取整)")
    private String treatmentMethod;

    /**
     * 提示文字
     */
    @Column(name = "prompt_text")
    @ApiModelProperty(value = "提示文字")
    private String promptText;

    /**
     * 数据来源(1手动录入2常用字段 3 公式编辑4数据字典)
     */
    @Column(name = "data_source")
    @ApiModelProperty(value = "数据来源(1手动录入2 公式编辑3其他分组)")
    private Integer dataSource;

    /**
     * 流水号规则
     */
    @Column(name = "serial_number_rule")
    @ApiModelProperty(value = "流水号规则")
    private String serialNumberRule;

    /**
     * 选项值
     */
    @Column(name = "option_value")
    @ApiModelProperty(value = "选项值")
    private String optionValue;

    /**
     * 是否必填
     */
    @Column(name = "is_must")
    @ApiModelProperty(value = "是否必填")
    private Integer isMust;

    /**
     * 是否对条
     */
    @Column(name = "is_multiple")
    @ApiModelProperty(value = "是否多条")
    private Integer isMultiple;

    /**
     * 是否去重(0 否 1 是)
     */
    @Column(name = "is_remove_duplicate")
    @ApiModelProperty(value = "是否去重(0 否 1 是)")
    private Integer isRemoveDuplicate;

    /**
     * 是否只读 (0:否 1 是)
     */
    @Column(name = "is_only")
    @ApiModelProperty(value = "是否只读 (0:否 1 是)")
    private Integer isOnly;
    
    @Column(name = "is_hide")
    @ApiModelProperty(value = "是否新增时隐藏字段(0:不是 1 是)")
    private Integer isHide;
    
    @Column(name = "is_table_show")
    @ApiModelProperty(value = "是否列表展示(0:否,1 是)")
    private Integer isTableShow;

    /**
     * 是否允许删除(0 否 1 是)
     */
    @Column(name = "is_allow_deleted")
    @ApiModelProperty(value = "是否允许停用(0 否 1 是)")
    private Integer isAllowDeleted;
    
    @Column(name = "sys_code")
    @ApiModelProperty(value = "系统标识(1 oa 2 人事)")
    private Integer sysCode;

    /**
     * 是否停用(0:否 1 是)
     */
    @Column(name = "is_disabled")
    @ApiModelProperty(value = "是否停用(0:否 1 是)")
    private Integer isDisabled;
    
    @ApiModelProperty(value = "排序字段")
    private Integer seq;
    
    @ApiModelProperty(value = "保存值")
    @Transient
    private String value;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    /**
     * 计算规则
     */
    @Column(name = "calculation_role")
    @ApiModelProperty(value = "计算规则")
    private String calculationRole;
    
    @Column(name = "source_group")
    @ApiModelProperty(value = "来源分组")
    private String sourceGroup;
    
    @Column(name = "group_field")
    @ApiModelProperty(value = "分组字段")
    private String groupField;
    
    @Column(name = "rule_type")
    @ApiModelProperty(value = "规则类型(1最新录入 2 首次录入 3 条件查询)")
    private Integer ruleType;
    
    @Column(name = "query_condition")
    @ApiModelProperty(value = "查询条件")
    private String queryCondition;
    
    @Transient
    private String columnName;
    
    @Transient
    private String columnComment;
    
    @Transient
    private String columnType;
    
    @Transient
    private String userCode;
    
    @Transient
    @ApiModelProperty(value = "是否有编辑权限")
    private String isEdit;
    
    @Transient
    @ApiModelProperty(value = "分组集合")
    private List<String> groupIds;
    
    @Transient
    @ApiModelProperty(value = "当前用户Id,用于查询")
    private String userId;

    @Column(name = "show_pass_by_val")
    @ApiModelProperty(value = "履职是否显示此内容显示 0 不显示  1 显示")
    private Integer showPassByVal;

    @Column(name = "show_pass_by_date")
    @ApiModelProperty(value = "履职内容指定日期 0 不显示  1 展示")
    private Integer showPassByDate;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}