package cn.trasen.homs.base.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.HrmsEmployeeSaveReq;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.RequestContent;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeBaseMapper;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.dao.CommInterfaceRegisterMapper;
import cn.trasen.homs.base.dto.EmpDTO;
import cn.trasen.homs.base.dto.MemberDepts;
import cn.trasen.homs.base.dto.OrgDTO;
import cn.trasen.homs.base.mapper.OrganizationMapper;
import cn.trasen.homs.base.model.CommInterfaceLogs;
import cn.trasen.homs.base.model.CommInterfaceRegister;
import cn.trasen.homs.base.model.FileAttachment;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.service.CommInterfaceLogsService;
import cn.trasen.homs.base.service.CommInterfaceRegisterService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IFileAttachmentService;
import cn.trasen.homs.base.utils.FileConvertUtil;
import cn.trasen.homs.base.utils.HttpClient;
import cn.trasen.homs.base.utils.StringConvertPinyin;
import cn.trasen.homs.base.utils.Utils;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommInterfaceRegisterServiceImpl
 * @Description TODO
 * @date 2024��8��22�� ����7:02:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommInterfaceRegisterServiceImpl implements CommInterfaceRegisterService {

	@Autowired
	private CommInterfaceRegisterMapper mapper;
	
	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;
	
	@Autowired
	private OrganizationMapper organizationMapper;
	
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
    private GlobalSettingsService globalSettingsService;

	@Autowired
	private CustomEmployeeInfoService customEmployeeInfoService;
	
	@Autowired
	private CustomEmployeeBaseService customEmployeeBaseService;
	
	@Autowired
	private CustomEmployeeBaseMapper customEmployeeBaseMapper;
	
	@Autowired
	private IFileAttachmentService fileAttachmentService;

	@Value("${syncBhfyHis5Param}")
	private String syncBhfyHis5Param; // 北海妇幼地址

	@Value("${syncBhfyHis5Address}")
	private String syncBhfyHis5Address; // 北海妇幼地址
	

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommInterfaceRegister record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommInterfaceRegister record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CommInterfaceRegister record = new CommInterfaceRegister();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CommInterfaceRegister selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommInterfaceRegister> getDataSetList(Page page, CommInterfaceRegister record) {
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getInterfaceName())) {
			example.and().andLike("interfaceName", "%" + record.getInterfaceName() + "%");
		}
		example.orderBy("moduleName");
		List<CommInterfaceRegister> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public List<OrganizationListSimpleRes> getOrganization(RequestContent requestContent) {
		
		Assert.hasText(requestContent.getCallName(), "callName值不能为空");
		Assert.hasText(requestContent.getCipher(), "密钥缺失");
		
		long startTime = System.currentTimeMillis();    //获取开始时间
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "组织机构");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if(commInterfaceRegister.getCipher().equals(requestContent.getCipher())) {
				
				if("1".equals(commInterfaceRegister.getStatus())) {
					
					Example example2 = new Example(Organization.class);
					Example.Criteria criteria2 = example2.createCriteria();
					
					if(StringUtils.isNotBlank(requestContent.getIsDeleted())) {
						criteria2.andEqualTo(Contants.IS_DELETED_FIELD, requestContent.getIsDeleted());
					}
					
					if(StringUtils.isNotBlank(requestContent.getIsEnable())) {
						criteria2.andEqualTo("isEnable", requestContent.getIsEnable());
					}
					
					List<Organization> organizationList = organizationMapper.selectByExample(example2);
					
					List<OrganizationListSimpleRes> organizationListSimpleResList = BeanUtil.copyToList(organizationList, OrganizationListSimpleRes.class);
					long endTime = System.currentTimeMillis();    //获取结束时间
					
					CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
					commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
					commInterfaceLogs.setInterfaceName("组织机构");
					commInterfaceLogs.setInterworkPlatform(requestContent.getCallName());
					commInterfaceLogs.setRequestUrl(requestContent.getIpAddress());
					commInterfaceLogs.setRequestParams(JSON.toJSONString(requestContent));
					commInterfaceLogs.setResponseParams(JSON.toJSONString(organizationListSimpleResList));
					commInterfaceLogs.setResponseStatus("1");
					commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
					commInterfaceLogsService.save(commInterfaceLogs);
					return organizationListSimpleResList;
				}else {
					Assert.isTrue(false, "接口未开放，请联系管理员");
				}
			}else {
				Assert.isTrue(false, "密钥验证失败");
			}
		}else {
			Assert.isTrue(false, "接口未开放，请联系管理员");
		}
		
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public List<EmployeeListReq> getEmployee(RequestContent requestContent) {
		Assert.hasText(requestContent.getCallName(), "callName值不能为空");
		Assert.hasText(requestContent.getCipher(), "密钥缺失");
		
		long startTime = System.currentTimeMillis();    //获取开始时间
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "人员信息");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if(commInterfaceRegister.getCipher().equals(requestContent.getCipher())) {
				
				if("1".equals(commInterfaceRegister.getStatus())) {
					
					GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
					
					requestContent.setOrgCode(globalSetting.getOrgCode());
					
					List<EmployeeListReq> hrmsEmployeeList = hrmsEmployeeService.getEmployeeListReqList(requestContent);
					
					long endTime = System.currentTimeMillis();    //获取结束时间
					
					CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
					commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
					commInterfaceLogs.setInterfaceName("人员信息");
					commInterfaceLogs.setInterworkPlatform(requestContent.getCallName());
					commInterfaceLogs.setRequestUrl(requestContent.getIpAddress());
					commInterfaceLogs.setRequestParams(JSON.toJSONString(requestContent));
					commInterfaceLogs.setResponseParams(JSON.toJSONString(hrmsEmployeeList));
					commInterfaceLogs.setResponseStatus("1");
					commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
					commInterfaceLogsService.save(commInterfaceLogs);
					return hrmsEmployeeList;
				}else {
					Assert.isTrue(false, "接口未开放，请联系管理员");
				}
			}else {
				Assert.isTrue(false, "密钥验证失败");
			}
		}else {
			Assert.isTrue(false, "接口未开放，请联系管理员");
		}
		
		return null;
	}
	
	@Async
	@Override
	@Transactional(readOnly = false)
	public void addSyncOrg(String orgId) {
		
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "1");
		criteria.andEqualTo("interfaceName", "集成平台-机构新增");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				Organization organization = organizationMapper.selectByPrimaryKey(orgId);
				
				OrgDTO orgDto = toDataOrg(organization,commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				
				Map<String,Object> head = new HashMap<>();
				head.put("id", String.valueOf(IdWork.id.nextId()));
				head.put("createTime", DateUtil.format(new Date(), "yyyyMMddhhmmss"));
				head.put("messageId", "M1603001");
				head.put("sender", "OA");
				
				requestParams.put("head", head);
				requestParams.put("body", orgDto);
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty);
				
				Map<String, String> sign = toSign(commInterfaceRegister);
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl,jsonString, sign);
			
				long endTime = System.currentTimeMillis();   
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-机构新增");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(bodyStr);
				
				JSONObject parseObject = JSONObject.parseObject(bodyStr);
				if("请求成功".equals(parseObject.getString("message"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}

	@Async
	@Override
	@Transactional(readOnly = false)
	public void updateSyncOrg(String orgId) {
		
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "1");
		criteria.andEqualTo("interfaceName", "集成平台-机构修改");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				Organization organization = organizationMapper.selectByPrimaryKey(orgId);
				
				OrgDTO orgDto = toDataOrg(organization,commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				
				Map<String,Object> head = new HashMap<>();
				head.put("id", String.valueOf(IdWork.id.nextId()));
				head.put("createTime", DateUtil.format(new Date(), "yyyyMMddhhmmss"));
				head.put("messageId", "M1603002");
				head.put("sender", "OA");
				
				requestParams.put("head", head);
				requestParams.put("body", orgDto);
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				Map<String, String> sign = toSign(commInterfaceRegister);
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				
				long endTime = System.currentTimeMillis();   
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-机构修改");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(bodyStr);
				
				JSONObject parseObject = JSONObject.parseObject(bodyStr);
				if("请求成功".equals(parseObject.getString("message"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
				
			}
		}
		
	}

	private OrgDTO toDataOrg(Organization orgResp,CommInterfaceRegister commInterfaceRegister) {
		
		OrgDTO orgDto = new OrgDTO();
		
		if(StringUtils.isNoneBlank(orgResp.getPlatformId())) {
			orgDto.setDeptId(orgResp.getPlatformId());
		}else {
			orgDto.setDeptId(orgResp.getOrganizationId());
		}
		
		Organization organization = organizationMapper.selectByPrimaryKey(orgResp.getParentId());
		
		if(StringUtils.isNoneBlank(organization.getPlatformId())) {
			orgDto.setParentCode(organization.getPlatformId());
			orgDto.setParentName(organization.getName());
		}else {
			orgDto.setParentCode(orgResp.getParentId());
			orgDto.setParentName(organization.getName());
		}
		
		orgDto.setSysCode("3");
		orgDto.setSysName("OA");
		orgDto.setOrgCode(commInterfaceRegister.getPlatformOrgCode());
		orgDto.setOrgName(commInterfaceRegister.getPlatformOrgName());
		orgDto.setHospCode(commInterfaceRegister.getPlatformHospCode());
		orgDto.setHospName(commInterfaceRegister.getPlatformHospName());
		orgDto.setCreateUserId(UserInfoHolder.getCurrentUserId()); 
		orgDto.setUpdateUserId(UserInfoHolder.getCurrentUserId());
		orgDto.setCreateDate(DateUtil.now());
		orgDto.setUpdateDate(DateUtil.now());
		orgDto.setDeptCode(orgResp.getCode()); 
		orgDto.setName(orgResp.getName());
		orgDto.setAddress(orgResp.getRemark());
		orgDto.setDutyPhone(orgResp.getTel());
		orgDto.setPyCode(PinyinUtil.getFirstLetter(orgResp.getName(), ""));
		orgDto.setWbCode(StringConvertPinyin.getWBCode(orgResp.getName()));
		
		//查询科主任
		List<String> roleIds = new ArrayList<>();
		roleIds.add("3");
		List<String> headDeptIds = organizationMapper.selectLeaderListByDeptCode(orgResp.getOrganizationId(), roleIds);
		
		if(CollectionUtils.isNotEmpty(headDeptIds)) {
			
			List<HrmsEmployeeResp> employeeList = hrmsEmployeeService.getEmployeeByCodes(headDeptIds);
			
			if(CollectionUtils.isNotEmpty(employeeList)) {
				HrmsEmployeeResp headDeptId = employeeList.get(0);
				orgDto.setHeadDeptId(headDeptId.getEmployeeId());
				orgDto.setHeadDeptName(headDeptId.getEmployeeName());
			}
			
		}
		
		//查询护士长
		List<String> nurseRole = new ArrayList<>();
		nurseRole.add("2");
		List<String> nurseDeptIds = organizationMapper.selectLeaderListByDeptCode(orgResp.getOrganizationId(), nurseRole);
		
		if(CollectionUtils.isNotEmpty(nurseDeptIds)) {
			
			List<HrmsEmployeeResp> employeeList = hrmsEmployeeService.getEmployeeByCodes(nurseDeptIds);
			
			if(CollectionUtils.isNotEmpty(employeeList)) {
				HrmsEmployeeResp nurseDeptId = employeeList.get(0);
				
				orgDto.setHeadNurseId(nurseDeptId.getEmployeeId());
				orgDto.setHeadNurseName(nurseDeptId.getEmployeeName());
			}
			
		}
		
		GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
		
		if("pjxdyrmyy".equals(globalSetting.getOrgCode())) {
			if(StringUtils.isNotBlank(orgResp.getOrgType())) {
				orgDto.setTypeCode(orgResp.getOrgType());
				if("003".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("职能科室");
				}
				if("001".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("临床科室");
				}
				if("002".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("药剂科室");
				}
				if("004".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("医技科室");
				}
				if("005".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("外科科室");
				}
				if("011".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("财务科室");
				}
			}
		}else {
			if(StringUtils.isNotBlank(orgResp.getOrgType())) {
				orgDto.setTypeCode(orgResp.getOrgType());
				if("003".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("机关");
				}
				if("001".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("临床");
				}
				if("002".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("药剂");
				}
				if("004".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("医技");
				}
				if("005".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("外科");
				}
				if("011".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("财务");
				}
				if("006".equals(orgResp.getOrgType())) {
					orgDto.setTypeName("其他");
				}
			}
		}
		
		if("1".equals(orgResp.getIsEnable())) {
			orgDto.setEnabled("Y");
		}else {
			orgDto.setEnabled("N");
		}
		orgDto.setOrderNum(orgResp.getSeqNo());
		orgDto.setIsFact(1);
		orgDto.setIsWard("0");
		orgDto.setDutyScope("-1");
		if ("N".equals(orgResp.getIsDeleted())) {
			orgDto.setIsDelete("0"); 
		} else {
			orgDto.setEnabled("N");
			orgDto.setIsDelete("1"); 
		}
		
		//判断一下当前科室是否为页子节点,0表示有上级，1表示无上级
//		List<HrmsOrganization> ls = hrmsOrganizationMapper.findListByParentId(orgResp.getOrganizationId());
//		if (ls.size() > 0 ) {
//			orgDto.setIsFact(0);
//		}else {
//			orgDto.setIsFact(1);
//		}
		
		return orgDto;
	}
	
	
	
	@Override
	@Transactional(readOnly = false)
	public void addSyncEmp(String empId) {
		
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "1");
		criteria.andEqualTo("interfaceName", "集成平台-人员新增");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				CustomEmployeeInfo hrmsEmployeeResp = customEmployeeInfoService.findByEmployeeId(empId);
				
				EmpDTO empDto = toDataEmp(hrmsEmployeeResp,commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				
				Map<String,Object> head = new HashMap<>();
				head.put("id", String.valueOf(IdWork.id.nextId()));
				head.put("createTime", DateUtil.format(new Date(), "yyyyMMddhhmmss"));
				head.put("messageId", "M1601001");
				head.put("sender", "OA");
				
				requestParams.put("head", head);
				requestParams.put("body", empDto);
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				Map<String, String> sign = toSign(commInterfaceRegister);
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-人员新增");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(bodyStr);
				
				JSONObject parseObject = JSONObject.parseObject(bodyStr);
				if("请求成功".equals(parseObject.getString("message"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}
	
	@Override
	@Transactional(readOnly = false)
	public void updateSyncEmp(String empId) {
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "1");
		criteria.andEqualTo("interfaceName", "集成平台-人员修改");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				CustomEmployeeInfo hrmsEmployeeResp = customEmployeeInfoService.findByEmployeeId(empId);
				
				EmpDTO empDto = toDataEmp(hrmsEmployeeResp,commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				
				Map<String,Object> head = new HashMap<>();
				head.put("id", String.valueOf(IdWork.id.nextId()));
				head.put("createTime", DateUtil.format(new Date(), "yyyyMMddhhmmss"));
				head.put("messageId", "M1601002");
				head.put("sender", "OA");
				
				requestParams.put("head", head);
				requestParams.put("body", empDto);
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				Map<String, String> sign = toSign(commInterfaceRegister);
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-人员修改");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(bodyStr);
				
				JSONObject parseObject = JSONObject.parseObject(bodyStr);
				if("请求成功".equals(parseObject.getString("message"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}

	private EmpDTO toDataEmp(CustomEmployeeInfo empResp,CommInterfaceRegister commInterfaceRegister) {
		
		EmpDTO empDto = new EmpDTO();
		
		empDto.setMemberId(empResp.getEmployeeId());
		
		if(StringUtils.isNotBlank(empResp.getHisEmployeeNo())) {
			empDto.setCode(empResp.getHisEmployeeNo());
		}else {
			empDto.setCode(empResp.getEmployeeNo());
		}
		
		Organization organization = organizationMapper.selectByPrimaryKey(empResp.getOrgId());
		if(StringUtils.isNotBlank(organization.getPlatformId())) {
			empDto.setOffice(organization.getPlatformId());
		}else {
			empDto.setOffice(empResp.getOrgId());
		}
		
		empDto.setOfficeName(empResp.getOrgName());
		
		if("4".equals(empResp.getEmployeeStatus()) || "7".equals(empResp.getEmployeeStatus()) || "8".equals(empResp.getEmployeeStatus())
				|| "Y".equals(empResp.getIsDeleted()) || "0".equals(empResp.getIsEnable())) {
			empDto.setStatus("2");
		}else {
			empDto.setStatus("1"); // 有效状态 2无效 1有效
		}
		
		empDto.setSysCode("3");
		empDto.setSysName("OA");
		
		//多机构的情况 这里需要优化调整
		empDto.setOrgCode(commInterfaceRegister.getPlatformOrgCode());
		empDto.setOrgName(commInterfaceRegister.getPlatformOrgName());
		empDto.setHospCode(commInterfaceRegister.getPlatformHospCode());
		
		
		empDto.setCreateUserId(UserInfoHolder.getCurrentUserInfo().getId()); 
		empDto.setOperatorId(UserInfoHolder.getCurrentUserInfo().getId());
		empDto.setCreateDate(DateUtil.now());
		
		empDto.setName(empResp.getEmployeeName()); // 姓名
		if ("0".equals(empResp.getGender())) {
			empDto.setSex("1"); // 性别 转 国标 GBT 2261.1-2003 (国标 0 未知 1 男 2女 9 未说明的性别)
		} else if ("1".equals(empResp.getGender())) {
			empDto.setSex("2"); // 性别 转 国标 GBT 2261.1-2003 (国标 0 未知 1 男 2女 9 未说明的性别)
		} else {
			empDto.setSex("9"); // 性别 转 国标 GBT 2261.1-2003 (国标 0 未知 1 男 2女 9 未说明的性别)
		}
		empDto.setBirthday(DateUtil.format(empResp.getBirthday(), "yyyy-MM-dd"));
		empDto.setIdCard(empResp.getIdentityNumber()); // 身份证
		empDto.setPhone(empResp.getPhoneNumber());
		empDto.setEmail(empResp.getEmail());
		empDto.setNationCode(empResp.getNationality()); // 名族代码
		empDto.setNationName(empResp.getNationalityName()); // 名族名称
		empDto.setAge(empResp.getEmpAge());
		empDto.setEntryDate(empResp.getEntryDate());
		empDto.setMemberType(empResp.getPersonalIdentity());
		empDto.setIsDelete(empResp.getIsDeleted()); // 删除状态 Y已删除 N 未删除
		empDto.setPyCode(empResp.getNameSpell()); //拼音码
		empDto.setWbCode(StringConvertPinyin.getWBCode(empResp.getEmployeeName())); //五笔码
		empDto.setMedicalInsuranceCode(empResp.getGjybbm()); //国家医保编码
		
		//empDto.setMemberRole(null);
		
		// 在职 、转岗 -->在编
		if("1".equals(empResp.getEmployeeStatus()) || "13".equals(empResp.getEmployeeStatus())) {
			empDto.setOnState("1");
		}
		// 退休 -->退休
		if("8".equals(empResp.getEmployeeStatus())) {
			empDto.setOnState("2");
		}
		// 延聘、院内返聘、院外返聘 -->聘用
		if("5".equals(empResp.getEmployeeStatus()) || "6".equals(empResp.getEmployeeStatus()) || "12".equals(empResp.getEmployeeStatus())) {
			empDto.setOnState("3");
		}
		// 试用期、借调、死亡 -->临时
		if("99".equals(empResp.getEmployeeStatus()) || "9".equals(empResp.getEmployeeStatus()) || "7".equals(empResp.getEmployeeStatus())) {
			empDto.setOnState("4");
		}
		
		if("1".equals(commInterfaceRegister.getMemberDept())) {
			// 处理主科室
			List<MemberDepts> memberDeptsList = new ArrayList<MemberDepts>();
			
			MemberDepts memberDept = new MemberDepts();
			
			GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
			
			if("LYJYYY".equals(globalSetting.getOrgCode())) { //金阳医院 固定科室
				memberDept.setDeptId("224705918171770880");
				memberDept.setDeptName("信息科");
			}else {
				if(StringUtils.isNotBlank(organization.getPlatformId())) {
					memberDept.setDeptId(organization.getPlatformId());
				}else {
					memberDept.setDeptId(empResp.getOrgId());
				}
				
				memberDept.setDeptName(empResp.getOrgName());
			}
			
			memberDept.setMasterFlag("Y"); // 主科室
			memberDeptsList.add(memberDept);
			
			empDto.setMemberDepts(memberDeptsList);
		}
		
		return empDto;
	}

	private Map<String, String> toSign(CommInterfaceRegister commInterfaceRegister) {
		Long timestamp = System.currentTimeMillis() / 1000;
		Map<String, String> params = new TreeMap<>();
		params.put("appId", commInterfaceRegister.getPlatformAppId());
		params.put("timestamp", String.valueOf(timestamp));
		params.put("randomStr", "123456"); 
		params.put("version", "V5.0.0"); 
		StringBuffer signTemp = new StringBuffer();
		params.forEach((k, v) -> {
			signTemp.append(k).append("=").append(v).append("&");
		});
		signTemp.append(commInterfaceRegister.getPlatformSign());
		String checkSign = DigestUtils.md5Hex(signTemp.toString()).toUpperCase();
		params.put("sign", checkSign);
		return params;
	}

	@Override
	public String bhfyempsync() {
		String retText = "同步完成";
		// 获取所有人员
		List<Map<String, String>> empAll = hrmsEmployeeService.getEmpAll(); // 获取所有人员

		// 转换为map list<map> 人员id为标识
		Map<String, List<Map<String, String>>> allEmpMap = empAll.stream().collect(Collectors
				.groupingBy(map -> map.get("empCode").toString(), Collectors.mapping(map -> map, Collectors.toList())));

		String bodyStr = HttpClient.doPostJson(syncBhfyHis5Address, syncBhfyHis5Param);
		
		// 对比 不存在就新增 存在就修改
		JSONObject parseObject = JSON.parseObject(bodyStr);
		String SuccessStr = parseObject.get("Success").toString();
		if ("true".equals(SuccessStr)) {
			String ret_data = parseObject.get("data").toString();
			JSONObject json_Data = JSON.parseObject(ret_data);
			String ret_val = json_Data.get("Value").toString();
			JSONObject json_Val = JSON.parseObject(ret_val);
			if ("200".equals(json_Val.get("Code"))) {
				JSONArray r_list = json_Val.getJSONArray("list"); // 返回人员
				try {
					if (r_list != null && r_list.size() > 0) {
						// 遍历人员
						for (int i = 0; i < r_list.size(); i++) {
							JSONObject _hisEmp = JSON.parseObject(r_list.get(i).toString());
							List<Map<String, String>> list = allEmpMap.get(_hisEmp.get("code"));
							String employeeId = "";
							if (list != null && list.size() > 0) { // oa有这个人员就修改
								Map<String, String> _oaEmp = list.get(0);
								employeeId = _oaEmp.get("empId");
								CustomEmployeeBase saveReq = new CustomEmployeeBase();
								saveReq.setEmployeeId(employeeId);
								saveReq.setEmployeeNo(_hisEmp.get("code").toString());
								saveReq.setEmployeeName(_hisEmp.get("name").toString());
								if (!StringUtil.isEmptyObj(_hisEmp.get("sex"))) {
									if ("1".equals(_hisEmp.get("sex").toString())) {
										saveReq.setGender("0");
									} else if ("2".equals(_hisEmp.get("sex").toString())) {
										saveReq.setGender("1");
									}
								}

								if (!StringUtil.isEmptyObj(_hisEmp.get("status"))) {
									saveReq.setIsEnable(_hisEmp.get("status").toString());
								}
//								if (!StringUtil.isEmptyObj(_hisEmp.get("nationCode"))) {
//									saveReq.setNationality(_hisEmp.get("nationCode").toString()); // 民族
//								}
								if (!StringUtil.isEmptyObj(_hisEmp.get("phone"))) {
									saveReq.setPhoneNumber(_hisEmp.get("phone").toString());
								}
								if (!StringUtil.isEmptyObj(_hisEmp.get("idCard"))) {
									saveReq.setIdentityNumber(_hisEmp.get("idCard").toString());
								}
								
								customEmployeeBaseService.update(saveReq);
							} else { // oa新增人员
								if ("N".equals(_hisEmp.get("isDelete").toString())) {
									employeeId = String.valueOf(IdWork.id.nextId());
									CustomEmployeeInfo saveReq = new CustomEmployeeInfo();
									saveReq.setEmployeeId(employeeId);
									saveReq.setEmployeeNo(_hisEmp.get("code").toString());
									saveReq.setEmployeeName(_hisEmp.get("name").toString());
									if (!StringUtil.isEmptyObj(_hisEmp.get("sex"))) {
										if ("1".equals(_hisEmp.get("sex").toString())) {
											saveReq.setGender("0");
										} else if ("2".equals(_hisEmp.get("sex").toString())) {
											saveReq.setGender("1");
										}
									}
									if (!StringUtil.isEmptyObj(_hisEmp.get("status"))) {
										saveReq.setIsEnable(_hisEmp.get("status").toString());
									}
									if (!StringUtil.isEmptyObj(_hisEmp.get("nationCode"))) {
										saveReq.setNationality(_hisEmp.get("nationCode").toString()); // 民族
									}
									if (!StringUtil.isEmptyObj(_hisEmp.get("phone"))) {
										saveReq.setPhoneNumber(_hisEmp.get("phone").toString());
									}
									if (!StringUtil.isEmptyObj(_hisEmp.get("idCard"))) {
										saveReq.setIdentityNumber(_hisEmp.get("idCard").toString());
									}
									saveReq.setOrgId("71433216"); // 北海市妇幼保健院 科室id
									
									customEmployeeBaseService.add(saveReq);
								}
							}
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				retText = "接口返回数据异常";
			}
		} else {
			// 返回数据有问题
			retText = "接口返回数据异常";
		}

		return retText;
	}

	@Override
	@Transactional(readOnly = false)
	public List<Map<String, String>> getSignatureImg(RequestContent requestContent) {
		
		Assert.hasText(requestContent.getCallName(), "callName值不能为空");
		Assert.hasText(requestContent.getCipher(), "密钥缺失");
		
		long startTime = System.currentTimeMillis();    //获取开始时间
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "获取人员签名图片");
		List<CommInterfaceRegister> registerList = mapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if(commInterfaceRegister.getCipher().equals(requestContent.getCipher())) {
				
				if("1".equals(commInterfaceRegister.getStatus())) {
					
					Example example2 = new Example(CustomEmployeeBase.class);
			        example2.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
			        example2.createCriteria().andIsNotNull("signatureImgName");
			        
			        if (StringUtils.isNotBlank(requestContent.getEmployeeNo())) {
			            example2.and().andEqualTo("employeeNo", requestContent.getEmployeeNo());
			        }
			        
			        List<CustomEmployeeBase> list = customEmployeeBaseMapper.selectByExample(example2);
			        
			        List<Map<String,String>> resultList = new ArrayList<>();
			        for (CustomEmployeeBase customEmployeeBase : list) {
			        	Map<String,String> map = new HashMap<>();
			        	map.put("employeeName", customEmployeeBase.getEmployeeName());
			        	map.put("employeeNo", customEmployeeBase.getEmployeeNo());
			        	
			        	if(StringUtils.isNoneBlank(customEmployeeBase.getSignatureImgName())) {
			        		String signatureImgName = customEmployeeBase.getSignatureImgName();
			            	String lastElement = Utils.getLastElement(signatureImgName,"/");
			            	FileAttachment fileAttachment = fileAttachmentService.getById(lastElement,null);
			            	if(null != fileAttachment) {
			            		try {
			        				map.put("base64Str", FileConvertUtil.convertImageToBase64(fileAttachment.getFilePath()));
			        			} catch (IOException e) {
			        				e.printStackTrace();
			        			}
			            	}else {
			            		map.put("base64Str", "");
			            	}
			        	}else {
			        		map.put("base64Str", "");
			        	}
			        	
			        	resultList.add(map);
					}
			        
			        long endTime = System.currentTimeMillis();    //获取结束时间
					
					CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
					commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
					commInterfaceLogs.setInterfaceName("组织机构");
					commInterfaceLogs.setInterworkPlatform(requestContent.getCallName());
					commInterfaceLogs.setRequestUrl(requestContent.getIpAddress());
					commInterfaceLogs.setRequestParams(JSON.toJSONString(requestContent));
					commInterfaceLogs.setResponseParams(JSON.toJSONString(resultList));
					commInterfaceLogs.setResponseStatus("1");
					commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
					commInterfaceLogsService.save(commInterfaceLogs);
			        
					return resultList;
				}else {
					Assert.isTrue(false, "接口未开放，请联系管理员");
				}
			}else {
				Assert.isTrue(false, "密钥验证失败");
			}
		}else {
			Assert.isTrue(false, "接口未开放，请联系管理员");
		}
		
		return null;
	}
	
	
}
