package cn.trasen.homs.base.saasOrg.service;

import java.util.List;

import cn.trasen.homs.base.saasOrg.model.EmployeeOrgMap;
import cn.trasen.homs.base.saasOrg.vo.AddEmployeeOrgMapVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName EmployeeOrgMapService
 * @Description 员工机构映射表服务类
 * @date 2025-06-28 11:30:00
 * <AUTHOR>
 * @version 1.0
 */
public interface EmployeeOrgMapService {
	
	/**
	 * @Title batchSave
	 * @Description 批量授权多机构
	 * @param record
	 * @return Integer
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	Integer batchSave(AddEmployeeOrgMapVo record);
	
	/**
	 * @Title saveOrUpdate
	 * @Description 用户多机构关联表保存-用于OA系统保存用户时调用
	 * @param record
	 * @return Integer
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	Integer saveOrUpdate(EmployeeOrgMap record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	Integer update(EmployeeOrgMap record);
	
	/**
	 * @Title updateStatus
	 * @Description 更新状态
	 * @param record
	 * @return Integer
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	Integer updateStatus(String id, Integer status);
	
	/**
	 * @Title selectByEmployeeId
	 * @Description 根据员工查询授权的机构列表
	 * @param employeeId
	 * @return List<EmployeeOrgMap>
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	List<EmployeeOrgMap> selectByEmployeeId(String employeeId);
	
	/**
	 * @Title selectList
	 * @Description 查询授权的机构列表
	 * @param page
	 * @param record
	 * @return DataSet<EmployeeOrgMap>
	 * @date 2025-06-28 11:30:00
	 * <AUTHOR>
	 */
	DataSet<EmployeeOrgMap> selectList(Page page, EmployeeOrgMap record);

}
