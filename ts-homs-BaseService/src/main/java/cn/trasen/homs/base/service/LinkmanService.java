package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.LinkmanResp;
import cn.trasen.homs.base.model.Linkman;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 联系人Service层
 * @Date: 2020/1/13 18:17
 * @Author: Liz<PERSON>huo
 * @Company: 湖南创星
 */
public interface LinkmanService {

	/**
	 * @Author: <PERSON><PERSON><PERSON><PERSON>
	 * @Description: 查询个人联系人列表
	 * @Date: 2020/1/11 17:01
	 * @Param:
	 * @return: java.util.List<cn.trasen.hrm.model.EmployeeTransfer>
	 **/
	List<Linkman> getDataList(Page page, Linkman linkman);

	/**
	 * @description: 获取内部联系人
	 * @param: page
	 * @param: employeeReq
	 * @return: java.util.List<cn.trasen.homs.base.bean.HrmsEmployeeResp>
	 * @author: liyuan
	 * @createTime: 2021/6/21 14:38
	 */
	List<LinkmanResp> innerLinkManlist(Page page, EmployeeListReq employeeReq);

	/**
	 * @Author: Lizhihuo
	 * @Description: 内部联系人列表导出
	 * @Date: 2020/3/6 10:56
	 * @Param:
	 * @return: java.util.List<cn.trasen.hrm.model.Employee>
	 **/
	List<LinkmanResp> selectExpotInnerLinkMan(EmployeeListReq employeeReq);

	/**
	 * @Author: Lizhihuo
	 * @Description: 查询个人联系人列表(导出)
	 * @Date: 2020/3/5 15:28
	 * @Param:
	 * @return: java.util.List<cn.trasen.hrm.model.Linkman>
	 **/
	List<Linkman> findExpotLinkman(Linkman linkman);

	/**
	 * @Author: Lizhihuo
	 * @Description: 新增个人联系人
	 * @Date: 2020/1/13 8:42
	 * @Param:
	 * @return: int
	 **/
	int insert(Linkman linkman);

	/**
	 * @Author: Lizhihuo
	 * @Description: 修改个人联系人
	 * @Date: 2020/1/13 9:24
	 * @Param:
	 * @return: int
	 **/
	int update(Linkman linkman);

	/**
	 * @Author: Lizhihuo
	 * @Description: 删除个人联系人
	 * @Date: 2020/1/13 10:25
	 * @Param:
	 * @return: int
	 **/
	int deleted(String id);

}
