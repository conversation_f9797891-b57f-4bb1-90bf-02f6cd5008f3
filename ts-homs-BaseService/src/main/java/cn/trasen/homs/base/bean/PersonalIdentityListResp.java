package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:41
 * @description
 */

@Data
public class PersonalIdentityListResp {
    @ApiModelProperty(value = "ID")
    String personalIdentityId;

    @ApiModelProperty(value = "名称")
    String personalIdentityName;

    @ApiModelProperty(value = "是否启用")
    private String isEnable;

    @ApiModelProperty(value = "字典类型ID")
    private String dictTypeId;

    @ApiModelProperty(value = "是否启用")
    private String isEnableLable;

    @ApiModelProperty(value = "备注")
    String remark;

    @ApiModelProperty(value = "排序")
    Short sort;

    @ApiModelProperty(value = "主键")
    private String id;
}
