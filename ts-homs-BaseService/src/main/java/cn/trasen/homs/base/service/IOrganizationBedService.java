package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrganizationAllocationSaveNumReq;
import cn.trasen.homs.base.model.OrganizationBed;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 11:39
 * @description
 */
public interface IOrganizationBedService {
    List<OrganizationBed> getList();

    @Transactional(rollbackFor = Exception.class)
    /**
    * @description: 删除所有数据
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/5 9:34
    */
    void delAll();

    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 保存编制
     * @param: organizationAllocationSaveNumReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/4 13:34
     */
    void saveNum(OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq);
}
