package cn.trasen.homs.base.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class JobtitleBasicResp {
	/**
	 * 主键ID
	 */

	private String jobtitleBasicId;

	/**
	 * 名称
	 */

	private String jobtitleBasicName;

	/**
	 * 等级
	 */

	private Integer jobtitleBasicGrade;

	/**
	 * 父级ID
	 */

	private String jobtitleBasicPid;
	
	/**
	 * 分类名称
	 */

	private String classificationName;
	
	/**
	 * 树ID
	 */

	private String treeIds;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业ID
	 */

	private String enterpriseId;

	/**
	 * 创建时间
	 */

	private Date createDate;

	/**
	 * 创建者ID
	 */

	private String createUser;

	/**
	 * 创建者姓名
	 */

	private String createUserName;

	/**
	 * 更新时间
	 */

	private Date updateDate;

	/**
	 * 更新者ID
	 */

	private String updateUser;

	/**
	 * 更新者姓名
	 */

	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */

	private String isDeleted;

	// ------- 扩展字段 ------ //
	/**
	 * 父级名称
	 */

	private String jobtitleBasicPName;
	
	/**
	 * 职称类别ID
	 */

	private String jobtitleCategoryId;

	/**
	 * 职称类别名称
	 */

	private String jobtitleCategoryName;
	
	/**
	 * 创建者姓名
	 */

	private double jobtitleSalary;
	

	private Integer jobtitleUpgradeTime;
	
	
	
	
	//益阳妇幼字段
	
	/**
	 * 职称工资
	 */

	private double baseZcgz;
	
	/**
	 * 院龄工资
	 */

	private double baseYlgz;

}