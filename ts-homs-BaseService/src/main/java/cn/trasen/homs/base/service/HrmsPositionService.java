package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.PositionSaveReq;
import cn.trasen.homs.base.model.HrmsPosition;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;

/**   
 * @Title: HrmsPositionService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 职务 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月15日 下午4:44:46 
 * @version V1.0   
 */
public interface HrmsPositionService {
	
	/**
	 * @Title: validate
	 * @Description: 数据校验
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月15日 下午2:01:00
	 */
	PlatformResult<String> validate(HrmsPosition entity);
	
	/**
	 * @Title: insert
	 * @Description: 新增职务
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int insert(HrmsPosition entity);

	/**
	 * @Title: update
	 * @Description: 更新职务
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int update(HrmsPosition entity);

	/**
	 * @description: 批量修改排序
	 * @param: postSaveReq
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/8/6 10:48
	 */
	@Transactional(rollbackFor = Exception.class)
	void updateSort(List<PositionSaveReq> positionSaveReqs);

	/**
	 * @Title: deleted
	 * @Description: 删除职务
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	int deleted(String id);

	/**
	 * @Title: getDataList
	 * @Description: 查询职务列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsPosition>
	 * <AUTHOR>
	 */
	List<HrmsPosition> getDataList(Page page, HrmsPosition entity);
	
	/**
	 * @Title: getList
	 * @Description: 查询职务列表
	 * @param entity
	 * @Return List<HrmsPosition>
	 * <AUTHOR>
	 * @date 2020年4月16日 下午2:56:02
	 */
	List<HrmsPosition> getList(HrmsPosition entity);

	/**
	 * @description: 修改
	 * @param: id
	 * @param: enable
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/7/29 17:43
	 */
	@Transactional(readOnly = false)
	void enable(String id, String enable);

	void verify(String id);
}
