package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsFamilyInfo {
	
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    @ApiModelProperty(value = "家庭成员姓名")
    private String memberName;

    @ApiModelProperty(value = "关系")
    private String relationship;

    @ApiModelProperty(value = "工作单位")
    private String workUnit;

    @ApiModelProperty(value = "职务")
    private String post;
    
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "联系电话")
    private String contactNumber;
    
}