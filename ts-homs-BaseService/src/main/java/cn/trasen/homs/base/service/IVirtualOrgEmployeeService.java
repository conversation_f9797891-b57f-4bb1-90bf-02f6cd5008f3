package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeImport;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeListReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;

/**
 * <AUTHOR>
 * @createTime 2021/8/2 17:27
 * @description
 */
public interface IVirtualOrgEmployeeService {
	/**
	 * @description: 保存员工信息 后期优化批量写入
	 * @param: orgId
	 * @param: leaders
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/7/27 16:59
	 */
	void saveEmployee(String orgId, List<String> employeeIds);

	/**
	 * @description: 删除
	 * @param: orgId
	 * @param: employeeId
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/8/4 16:24
	 */
	@Transactional(rollbackFor = Exception.class)
	void deleteEmployee(String orgId, String employeeId);

	/**
	 * @description: 获取领导列表
	 * @param: orgId
	 * @return: java.util.List<cn.trasen.homs.base.bean.OrganizationLeaderResp>
	 * @author: liyuan
	 * @createTime: 2021/7/28 17:05
	 */
	DataSet<HrmsEmployeeResp> getPageList(Page page, VirtualOrgEmployeeListReq virtualOrgEmployeeListReq);

	/**
	 * @description: 获取列表
	 * @param: orgId
	 * @return: java.util.List<cn.trasen.homs.base.bean.OrganizationLeaderResp>
	 * @author: liyuan
	 * @createTime: 2021/7/28 17:05
	 */
	List<HrmsEmployeeResp> getList(VirtualOrgEmployeeListReq virtualOrgEmployeeListReq);

	/**
	 * @description: 导入
	 * @param: virtualOrgEmployeeImportList
	 * @param: orgId
	 * @param: type
	 * @return: cn.trasen.BootComm.utils.PlatformResult
	 * @author: liyuan
	 * @createTime: 2021/8/3 16:39
	 */
	@Transactional(rollbackFor = Exception.class)
	PlatformResult excelImportEmployee(List<VirtualOrgEmployeeImport> virtualOrgEmployeeImportList, String orgId,
			String type);
}
