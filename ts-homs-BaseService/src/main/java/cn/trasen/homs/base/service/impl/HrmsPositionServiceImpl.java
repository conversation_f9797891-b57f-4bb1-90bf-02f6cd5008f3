package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.base.bean.PositionSaveReq;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.mapper.HrmsPositionMapper;
import cn.trasen.homs.base.model.HrmsPosition;
import cn.trasen.homs.base.service.HrmsPositionService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsPositionServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 职务 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月15日 下午4:45:39 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPositionServiceImpl implements HrmsPositionService {

    @Autowired
    private HrmsPositionMapper hrmsPositionMapper;
    
    @Autowired
    private CustomEmployeeInfoService customEmployeeInfoService;

    /**
     * @param entity
     * @Title: validate
     * @Description: 数据校验
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年5月15日 下午2:01:00
     */
    @Override
    public PlatformResult<String> validate(HrmsPosition entity) {
        Assert.hasText(entity.getPositionName(), "positionName must be not null.");

        Example example = new Example(HrmsPosition.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("positionName", entity.getPositionName()); // 职务名称
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isNotBlank(entity.getPositionId())) { // 职务ID
            example.and().andNotEqualTo("positionId", entity.getPositionId());
        }

        if (hrmsPositionMapper.selectByExample(example).size() > 0) {
            return PlatformResult.failure("职务名称已存在");
        }
        return PlatformResult.success();
    }

    /**
     * @Title: insert
     * @Description: 新增职务
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int insert(HrmsPosition entity) {
        entity.setPositionId(String.valueOf(IdWork.id.nextId()));
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDate(new Date());
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsPositionMapper.insert(entity);
    }

    /**
     * @Title: update
     * @Description: 更新职务
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int update(HrmsPosition entity) {
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        entity.setUpdateDate(new Date());
        //entity.setIsEnable(EnableEnum.Y.getKey());
        return hrmsPositionMapper.updateByPrimaryKeySelective(entity);
    }



    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<PositionSaveReq> positionSaveReqs) {
        for (PositionSaveReq positionSaveReq : positionSaveReqs) {
            HrmsPosition entity = new HrmsPosition();
            entity.setPositionId(positionSaveReq.getPositionId());
            entity.setSerialNumber(positionSaveReq.getSerialNumber());
            entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            entity.setUpdateDate(new Date());
            hrmsPositionMapper.updateByPrimaryKeySelective(entity);
        }
    }



    /**
     * @Title: deleted
     * @Description: 删除职务
     * @Param: id
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        verify(id);
        HrmsPosition position = hrmsPositionMapper.selectByPrimaryKey(id);
        if (position != null) {
            position.setIsDeleted(Contants.IS_DELETED_TURE);
        }
        return hrmsPositionMapper.updateByPrimaryKeySelective(position);
    }

    /**
     * @param entity
     * @Title: getDataList
     * @Description: 获取职务列表
     * @Param: page
     * @Return: List<HrmsPosition>
     * <AUTHOR>
     */
    @Override
    public List<HrmsPosition> getDataList(Page page, HrmsPosition entity) {
        Example example = new Example(HrmsPosition.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
//        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isNotBlank(entity.getPositionName())) { // 职务名称
            example.and().andLike("positionName", "%" + entity.getPositionName() + "%");
        }
        if (StringUtils.isNotBlank(entity.getIsEnable())) { // 启用标识
            example.and().andEqualTo("isEnable", entity.getIsEnable());
        } else if(ObjectUtils.isEmpty(entity.getIsManagerPage()) || !"1".equals(entity.getIsManagerPage())) {
        	//非管理维护界面默认显示
        	example.and().andEqualTo("isEnable", "1");
        }
        example.orderBy("serialNumber").asc();
        return hrmsPositionMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @param entity
     * @Title: getList
     * @Description: 查询职务列表
     * @Return List<HrmsPosition>
     * <AUTHOR>
     * @date 2020年4月16日 下午2:56:02
     */
    @Override
    public List<HrmsPosition> getList(HrmsPosition entity) {
        Example example = new Example(HrmsPosition.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isNotBlank(entity.getPositionName())) { // 职务名称
            example.and().andLike("positionName", "%" + entity.getPositionName() + "%");
        }
        return hrmsPositionMapper.selectByExample(example);
    }


    @Override
    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(readOnly = false)
    public void enable(String id, String enable) {

        if (enable.equals(EnableEnum.N.getKey())) {
            verify(id);
        }

        HrmsPosition hrmsPosition = new HrmsPosition();
        hrmsPosition.setPositionId(id);
        hrmsPosition.setIsEnable(enable);
        hrmsPosition.setUpdateDate(new Date());
        hrmsPosition.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        hrmsPositionMapper.updateByPrimaryKeySelective(hrmsPosition);
    }


    /**
     * @description: 验证
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 11:17
     */
    @Override
    public void verify(String id) {
    	CustomEmployeeInfo employeeReq = new CustomEmployeeInfo();
        employeeReq.setPositionId(id);
        if (customEmployeeInfoService.exists(employeeReq)) {
            throw new BusinessException("职务已被使用，不能禁用和删除！");
        }
    }
}
