package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.bean.OrganizationLeaderListReq;
import cn.trasen.homs.base.bean.OrganizationLeaderResp;
import cn.trasen.homs.base.bean.OrganizationLeaderSaveReq;
import cn.trasen.homs.base.dto.ReplaceLeader;
import cn.trasen.homs.base.model.OrganizationLeader;

public interface IOrganizationLeaderService {
    /**
     * @description: 保存部门领导
     * @param: orgId
     * @param: leaders
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 16:59
     */
    void saveLeaders(String orgId, List<OrganizationLeaderSaveReq> leaders);

    /**
    * @description: 获取领导列表
* @param: orgId
    * @return: java.util.List<cn.trasen.homs.base.bean.OrganizationLeaderResp>
    * @author: liyuan
    * @createTime: 2021/7/28 17:05
    */
    List<OrganizationLeaderResp> getList(String orgId);

    /**
     * @description: 获取领导列表
     * @param: orgId
     * @return: java.util.List<cn.trasen.homs.base.bean.OrganizationLeaderResp>
     * @author: liyuan
     * @createTime: 2021/7/28 17:05
     */
    List<OrganizationLeader> getBaseList(OrganizationLeaderListReq organizationLeaderListReq);

	void bacthReplaceLeader(ReplaceLeader replaceLeader);
}
