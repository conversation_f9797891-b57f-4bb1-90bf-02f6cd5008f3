package cn.trasen.homs.base.service;


import java.util.List;

import cn.trasen.homs.base.model.PortalTheme;

public interface PortalThemeService {

	void insert(PortalTheme portalTheme);
	
	void update(PortalTheme portalTheme);
	
	void delete(String id);
	
	PortalTheme selectById(String id);
	
	PortalTheme selectDefaultPortalTheme(String themeId);

	List<PortalTheme> selectPortalThemeList(PortalTheme portalTheme);
}
