package cn.trasen.homs.base.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.trasen.homs.base.bean.ModulePermissionsListRes;
import cn.trasen.homs.base.mapper.ModuleMenuMapper;
import cn.trasen.homs.base.model.ModuleMenu;
import cn.trasen.homs.base.service.ModuleMenuService;
import cn.trasen.homs.core.contants.Contants;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/11/24 17:45
 */
@Service
public class ModuleMenuServiceImpl   implements ModuleMenuService {

    @Autowired
    ModuleMenuMapper moduleMenuMapper;

    @Override
    /**
     * 读取我的显示菜单
     *
     * @return cn.trasen.BootComm.utils.PlatformResult<cn.trasen.basicsbottom.bean.ModulePermissionsListRes>
     * <AUTHOR>
     * @date 2021/11/24 17:47
     */
    public ModulePermissionsListRes getMyMenu() {
        Example example = new Example(ModuleMenu.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("k", "my");
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        ModuleMenu moduleMenu = moduleMenuMapper.selectOneByExample(example);

        ModulePermissionsListRes modulePermissionsListRes = JSON.parseObject(moduleMenu.getV(), ModulePermissionsListRes.class);
        return modulePermissionsListRes;
    }

    @Override
    /**
     * 读取个人设置显示菜单
     *
     * @return cn.trasen.basicsbottom.bean.ModulePermissionsListRes
     * <AUTHOR>
     * @date 2021/11/24 17:47
     */
    public ModulePermissionsListRes getMyConfigMenu() {
        Example example = new Example(ModuleMenu.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("k", "myconfig");
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        ModuleMenu moduleMenu = moduleMenuMapper.selectOneByExample(example);

        ModulePermissionsListRes modulePermissionsListRes = JSON.parseObject(moduleMenu.getV(), ModulePermissionsListRes.class);
        return modulePermissionsListRes;

    }

    @Override
    /**
     * 获取模块菜单
     *
     * @param module
     * @return cn.trasen.basicsbottom.bean.ModulePermissionsListRes
     * <AUTHOR>
     * @date 2021/11/30 9:31
     */
    public ModulePermissionsListRes getMenu(String module) {
        Example example = new Example(ModuleMenu.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("k", module);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        ModuleMenu moduleMenu = moduleMenuMapper.selectOneByExample(example);
        ModulePermissionsListRes modulePermissionsListRes = JSON.parseObject(moduleMenu.getV(), ModulePermissionsListRes.class);
        return modulePermissionsListRes;
    }
}