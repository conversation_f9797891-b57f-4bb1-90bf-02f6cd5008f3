package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/2/8 15:58
 */
@Setter
@Getter
public class OrgGroupEmpListRes {

    /**
     * ID
     */
    private String groupId;

    /**
     * 群组名称
     */
    private String groupName;
    
    /**
     * 群组分类id
     */
    private String groupClassId;
    
    /**
     * 群组分类名称
     */
    private String groupClassName;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private String empId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String empName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String empCode;


    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String orgName;


    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private String orgId;


    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "性别")
    private String genderText;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String enableText;

    @ApiModelProperty(value = "员工手机")
    private String empMobile;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatar;


    /**
     * 岗位ID
     */
    @ApiModelProperty(value = "岗位ID")
    private String personalIdentity;


    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String personalIdentityName;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String positionId;

    @ApiModelProperty(value = "职务名称")
    private String positionName;

}
