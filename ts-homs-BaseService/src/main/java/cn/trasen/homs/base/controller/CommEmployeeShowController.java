package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.utils.JqGridBean;
import cn.trasen.homs.base.model.CommEmployeeShow;
import cn.trasen.homs.base.service.CommEmployeeShowService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/** 
* @ClassName: CommEmployeeShowController 
* @Description: 自定义列controller 
* <AUTHOR>  
* @date 2022年12月5日 下午3:31:47 
*  
*/

@Api(tags = "自定义列Controller")
@RestController
public class CommEmployeeShowController {
	
	@Autowired
	CommEmployeeShowService commEmployeeShowService;

    @ApiOperation(value = "获取要显示的字段", notes = "获取要显示的字段")
    @PostMapping("/commemployeesho/getList")
    public PlatformResult<List<JqGridBean>> getList() {
        try {
        	List<JqGridBean> list = commEmployeeShowService.getList();
            return PlatformResult.success(list);
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
	@ApiOperation(value = "设置列表", notes = "设置列表")
	@PostMapping("/commemployeesho/getshowColumnList")
	public DataSet<CommEmployeeShow> getshowColumnList(Page page,CommEmployeeShow record) {
		List<CommEmployeeShow> list = commEmployeeShowService.getshowColumnList(page,record);
		return new DataSet<CommEmployeeShow>(1, 1, 1, list.size(), list);
	}
    
    @ApiOperation(value = "刷新字段", notes = "刷新字段")
    @GetMapping("/commemployeesho/ref")
    public PlatformResult<String> ref() {
        try {
        	commEmployeeShowService.ref();
            return PlatformResult.success();
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
    //修改字段显示
    
    @ApiOperation(value = "刷新字段", notes = "刷新字段")
    @PostMapping("/commemployeesho/update")
    public PlatformResult<String> update(@RequestBody CommEmployeeShow record) {
        try {
        	commEmployeeShowService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
    
	
}
