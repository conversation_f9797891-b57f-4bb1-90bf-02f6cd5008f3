package cn.trasen.homs.base.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.trasen.homs.base.model.CommOrganizationParttime;
import cn.trasen.homs.base.service.CommOrganizationParttimeService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.service.UserFeignService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommOrganizationParttimeController
 * @Description TODO
 * @date 2023��8��26�� ����11:41:09
 * <AUTHOR>
 * @version 1.0
 */
@Controller
@Api(tags = "兼职科室")
public class CommOrganizationParttimeController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommOrganizationParttimeController.class);

	@Autowired
	private CommOrganizationParttimeService commOrganizationParttimeService;
	
	@Autowired
	private UserFeignService ssoFeignClient;
	
	@Value("${sso.defaultPrikey}")
	private String defaultPrikey;

	/**
	 * @Title saveCommOrganizationParttime
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/organizationParttime/save")
	@ResponseBody
	public PlatformResult<String> saveCommOrganizationParttime(@RequestBody CommOrganizationParttime record) {
		try {
			commOrganizationParttimeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommOrganizationParttime
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/organizationParttime/update")
	@ResponseBody
	public PlatformResult<String> updateCommOrganizationParttime(@RequestBody CommOrganizationParttime record) {
		try {
			commOrganizationParttimeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommOrganizationParttimeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommOrganizationParttime>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/organizationParttime/{id}")
	@ResponseBody
	public PlatformResult<CommOrganizationParttime> selectCommOrganizationParttimeById(@PathVariable String id) {
		try {
			CommOrganizationParttime record = commOrganizationParttimeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommOrganizationParttimeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/organizationParttime/delete/{id}")
	@ResponseBody
	public PlatformResult<String> deleteCommOrganizationParttimeById(@PathVariable String id) {
		try {
			commOrganizationParttimeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommOrganizationParttimeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommOrganizationParttime>
	 * @date 2023��8��26�� ����11:41:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/organizationParttime/list")
	@ResponseBody
	public DataSet<CommOrganizationParttime> selectCommOrganizationParttimeList(Page page, CommOrganizationParttime record) {
		return commOrganizationParttimeService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @MethodName: getList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<CommOrganizationParttime>
	 * @date 2023-08-26 02:18:59
	 */
	@ApiOperation(value = "兼职科室列表", notes = "兼职科室列表")
	@PostMapping("/api/organizationParttime/getList")
	@ResponseBody
	public List<CommOrganizationParttime> getList(@RequestBody CommOrganizationParttime record) {
		return commOrganizationParttimeService.getList(record);
	}
	
	/**
	 * 
	 * @MethodName: chooseOrganizationParttime
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<CommOrganizationParttime>
	 * @date 2023-08-26 02:56:11
	 */
	@ApiOperation(value = "切换兼职科室", notes = "切换兼职科室")
	@PostMapping("/api/organizationParttime/chooseOrganizationParttime")
	@ResponseBody
	public PlatformResult<String>  chooseOrganizationParttime(HttpServletRequest request,HttpServletResponse response,@RequestBody CommOrganizationParttime record) {
			try {
				commOrganizationParttimeService.chooseOrganizationParttime(record);
				
				/*logger.info( "获取到的userCode" + ">>>>>" + record.getEmployeeNo());
		    	ThpsUserReq thpsUser = new ThpsUserReq();
		    	thpsUser.setUsercode(record.getEmployeeNo());
		    	PlatformResult<Object> result = ssoFeignClient.openLogin(thpsUser);
		    	logger.info( "调取SSO登陆返回的值" + result.isSuccess() + ">>>>>" + result.getObject());
		    	
		    	if(result.isSuccess() && result.getObject() != null) {//登陆成功
	    		
		    		//先清除所有cookie
		    		Cookie[] cookies = request.getCookies();
		    		if(null != cookies) {
		    			for(Cookie cookie : cookies){
		    	    		cookie.setMaxAge(0);
		    	    		cookie.setPath("/");
		    	    		response.addCookie(cookie);
		        		}
		    		}
		    		
		    		Map obj =  (Map) result.getObject();
		    		String ssoToken = (String) obj.get("token");//拿到加密的token
		    		logger.info("拿到加密的token" + ssoToken);
		    		
		    		String token = RSAUtils.decrypt(defaultPrikey, ssoToken);//拿到解密的token
		    		
		    		Cookie cookie = new Cookie("THPMSCookie", token);
		            cookie.setPath("/");
		            response.addCookie(cookie);
		           
		            Cookie cookie2 = new Cookie("token", token);
		            cookie2.setPath("/");
		            response.addCookie(cookie2);
		            
		            Cookie cookie3 = new Cookie("OACookie", token);
		            cookie3.setPath("/");
		            response.addCookie(cookie3);
		            
		            response.setHeader("token", token);
			            
			            
					return PlatformResult.success(null,"切换兼职科室成功");
		    	}*/
		    	
		    	return PlatformResult.success(null,"切换兼职科室成功");
		    }catch (Exception e) {
				logger.error(e.getMessage(), e);
				return PlatformResult.failure("切换兼职科室失败，原因：" + e.getMessage());
			}
			
			
		}
}

