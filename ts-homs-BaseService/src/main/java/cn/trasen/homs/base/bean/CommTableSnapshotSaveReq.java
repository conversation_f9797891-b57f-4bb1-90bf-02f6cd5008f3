package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.homs.base.bean
 * @className: SaveTableSnapshotReq
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/7 09:11
 * @version: 1.0
 */

@Data
public class CommTableSnapshotSaveReq {

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "主键值")
    private String rowPkValue;

    @ApiModelProperty(value = "旧数据")
    private Object old;

    @ApiModelProperty(value = "新数据")
    private Object now;
}
