package cn.trasen.homs.base.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrgGroupListRes;
import cn.trasen.homs.base.bo.OrgGroupEmpInBO;
import cn.trasen.homs.base.bo.OrgGroupEmpOutBO;
import cn.trasen.homs.base.bo.OrgGroupInBO;
import cn.trasen.homs.base.model.OrgGroup;
import cn.trasen.homs.base.model.OrgUserGroup;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.TreeModel;

/**
 * @Description: 自定义群组Service层
 * @Date: 2020/1/13 18:29
 * @Author: Liz<PERSON>huo
 * @Company: 湖南创星
 */
public interface OrgGroupService {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 查询自定义群组列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    List<OrgGroup> getDataList(Page page, OrgGroup orgGroup);

    /**
     * @Author: Lizhihuo
     * @Description: 新增自定义群组
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    OrgGroup insert(OrgGroup orgGroup);

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组
     * @Date: 2020/1/13 9:24
     * @Param:
     * @return: int
     **/
    int update(OrgGroup orgGroup);

    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组
     * @Date: 2020/1/13 10:25
     * @Param:
     * @return: int
     **/
    int deleted(String id);

    @Transactional(readOnly = false)
    int enable(String id, String enableType);

    /**
     * @Author: Lizhihuo
     * @Description: 移动端-查询自定义群组信息列表
     * @Date: 2020/4/22 11:16
     * @Param: 
     * @return: java.util.List<cn.trasen.hrm.model.OrgGroup>
     **/
    List<OrgGroupListRes> getOrgGroupList(Page page, OrgGroup orgGroup);
    
    /**
     * 
     * @Title: getOrgGroupTree   
     * @Description: 查询自定义群组（选人）   
     * @param: @param params
     * @param: @return      
     * @return: List<OrgGroup>  
     * @author: YueC
     * @date:   2020年6月9日 下午6:13:12    
     * @throws
     */
    List<OrgGroup> getOrgGroupTree(Map<String,Object> params);

    /**
     * 
     * @Title: getOrgGroupUser   
     * @Description:查询群组人员数据 （选人）
     * @param: @param groupId
     * @param: @return      
     * @return: List<Map<String,Object>>  
     * @author: YueC
     * @date:   2020年6月10日 上午11:25:32    
     * @throws
     */
    List<Map<String, Object>> getOrgGroupUser(Page page,String groupId);

    List<String> selectGroupIdByUserCode(String userCode);

    List<Map<String,String>> selectOrgGroupList(String groupName);

    /**
     * 获取员工列表
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    List<OrgGroupEmpOutBO> listOrgGroupEmp(OrgGroupEmpInBO orgGroupEmpInBO);

    /**
 * 获取员工列表
 *
 * @return
 * <AUTHOR>
 * @date 2022/2/8 16:02
 */
    List<OrgGroupEmpOutBO> listOrgGroupEmp(Page page, OrgGroupEmpInBO orgGroupEmpInBO);

    @Transactional(readOnly = false)
    /**
    * 移除员工
    * @param groupId
    * @param empCode
    * @return void
    * <AUTHOR>
    * @date 2022/2/8 17:52
    */
    void deleteGroupEmp(String groupId, String empCode);

    @Transactional(readOnly = false)
    /**
     * 保存分组员工信息
     * @param groupId
     * @param empCode
     * @return void
     * <AUTHOR>
     * @date 2022/2/8 17:52
     */
    void saveGroupEmp(String groupId, List<String> empCodeList);

    /**
    *
    * @param orgGroupEmpInBO
    * @return byte[]
    * <AUTHOR>
    * @date 2022/2/9 10:34
    */
    byte[] downloadExportGroupEmp(OrgGroupEmpInBO orgGroupEmpInBO) throws IOException;

    /**
     * 获取当前用户用户分株
     * @param orgGroupInBO
     * @return java.util.List<cn.trasen.basicsbottom.model.OrgGroup>
     * <AUTHOR>
     * @date 2022/3/7 14:34
     */
    List<OrgGroup> listCurrentEmpOrgGroup(OrgGroupInBO orgGroupInBO);

    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void updateSort(List<OrgGroupInBO> orgGroupInBOList);
    
    /**
     * 
     * @MethodName: getOrgGroupTreeList
     * @Description: TODO
     * <AUTHOR>
     * @param record
     * @return List<TreeModel>
     * @date 2022-10-16 02:53:50
     */
    List<TreeModel> getOrgGroupTreeList(OrgGroup record);

    void updateUserGroupSort(List<OrgUserGroup> orgGroupUpdateSortList);

	void syncLeaderToGroup();
}
