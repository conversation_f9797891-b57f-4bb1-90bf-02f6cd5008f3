package cn.trasen.homs.base.webSocket;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.eclipse.jetty.websocket.servlet.WebSocketServlet;
import org.eclipse.jetty.websocket.servlet.WebSocketServletFactory;

//@WebServlet(urlPatterns= {"/messagewebsocket/{usercode}"})
public class JettyWebsocketServlet extends WebSocketServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6904595121017510577L;

	@Override
	public void configure(WebSocketServletFactory factory) {
		factory.getPolicy().setIdleTimeout(1800000);
		//factory.getPolicy().setAsyncWriteTimeout(60L * 1000L);
		factory.register(JettyWebsocket.class);
//		factory.register(MessageWebSocketHandler.class);
	}

	@Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// 获取请求的URL路径
        String requestURI = request.getRequestURI();
        
        // 从URL路径中解析出usercode
        String[] pathSegments = requestURI.split("/");
        String usercode = null;
        if (pathSegments.length > 2) { // 第一个是空字符串，第二个是"messagewebsocket"，第三个才是usercode
            usercode = pathSegments[2];
        }
	}
}
