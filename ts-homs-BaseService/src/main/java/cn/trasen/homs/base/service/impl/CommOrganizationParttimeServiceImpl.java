package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.mapper.CommOrganizationParttimeMapper;
import cn.trasen.homs.base.model.CommOrganizationParttime;
import cn.trasen.homs.base.service.CommOrganizationParttimeService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommOrganizationParttimeServiceImpl
 * @Description TODO
 * @date 2023��8��26�� ����11:41:09
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommOrganizationParttimeServiceImpl implements CommOrganizationParttimeService {

    @Autowired
    private CommOrganizationParttimeMapper mapper;
    
    @Autowired
    SystemUserFeignService systemUserFeignService;

    @Transactional(readOnly = false)
    @Override
    public void save(CommOrganizationParttime record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        if(StringUtils.isEmpty(record.getIsDefault())){
            record.setIsDefault("0");
        }
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
        }
        
        mapper.insertSelective(record);
        
        if("1".equals(record.getIsDefault())){
            chooseOrganizationParttime(record);
        }
        
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(CommOrganizationParttime record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        CommOrganizationParttime record = new CommOrganizationParttime();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CommOrganizationParttime selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<CommOrganizationParttime> getDataSetList(Page page, CommOrganizationParttime record) {
        Example example = new Example(CommOrganizationParttime.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<CommOrganizationParttime> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    @Transactional(readOnly = false)
    public void deleteByEmployeeId(String employeeId) {
        Example example = new Example(CommOrganizationParttime.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("employeeId", employeeId);
        CommOrganizationParttime commOrganizationParttime = new CommOrganizationParttime();
        commOrganizationParttime.setIsDeleted("Y");
        mapper.updateByExampleSelective(commOrganizationParttime, example);
    }

    @Override
    public List<CommOrganizationParttime> getList(CommOrganizationParttime record) {
        Example example = new Example(CommOrganizationParttime.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("employeeId", record.getEmployeeId());
        criteria.andIsNotNull("orgId");
        criteria.andCondition("org_id != ''");
        example.orderBy("isBelong").desc().orderBy("createDate").desc();
        return mapper.selectByExample(example);
    }

    @Override
    @Transactional(readOnly = false)
    public void chooseOrganizationParttime(CommOrganizationParttime record) {
        
        Example example = new Example(CommOrganizationParttime.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("employeeId", record.getEmployeeId());
        CommOrganizationParttime commOrganizationParttime = new CommOrganizationParttime();
        commOrganizationParttime.setIsDefault("0");
        mapper.updateByExampleSelective(commOrganizationParttime, example);
        
        record.setIsDefault("1");
        mapper.updateByPrimaryKeySelective(record);
        
        mapper.updateEmployee(record);
        
        /*ThpsUserReq thpsUser = new ThpsUserReq();
        thpsUser.setId(record.getEmployeeId());
        thpsUser.setUsercode(record.getEmployeeNo());
        thpsUser.setDeptcode(record.getOrgId());
        thpsUser.setDeptname(record.getOrgName());
        log.info("---同步用户：" + JSON.toJSONString(thpsUser));
        systemUserFeignService.saveOrUpdate(thpsUser);*/
    }

}