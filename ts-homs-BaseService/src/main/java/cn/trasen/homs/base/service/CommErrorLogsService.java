package cn.trasen.homs.base.service;

import cn.trasen.homs.base.model.CommErrorLogs;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommErrorLogsService
 * @Description TODO
 * @date 2024��5��15�� ����2:15:31
 * <AUTHOR>
 * @version 1.0
 */
public interface CommErrorLogsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	Integer save(CommErrorLogs record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	Integer update(CommErrorLogs record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommErrorLogs
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	CommErrorLogs selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommErrorLogs>
	 * @date 2024��5��15�� ����2:15:31
	 * <AUTHOR>
	 */
	DataSet<CommErrorLogs> getDataSetList(Page page, CommErrorLogs record);
}
