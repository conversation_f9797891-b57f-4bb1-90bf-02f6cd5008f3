package cn.trasen.homs.base.groupLeader.service;

import java.util.List;

import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader;
import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeaderBase;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName HrmsRoleGroupLeaderService
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsRoleGroupLeaderService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	Integer save(HrmsRoleGroupLeader record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	Integer update(HrmsRoleGroupLeader record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsRoleGroupLeader
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	HrmsRoleGroupLeader selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsRoleGroupLeader>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	DataSet<HrmsRoleGroupLeader> getDataSetList(Page page, HrmsRoleGroupLeader record);

	/**
	 * 获取此角色所有人员
	 * @return
	 */
	List<HrmsRoleGroupLeaderBase> getRolegroupleaderAllList();

	/**
	 * 根据usercode获取能查看的岗位类别
	 * @param usercode
	 * @return
	 */
	public List<String> getRoleByUserCode(String usercode);
}
