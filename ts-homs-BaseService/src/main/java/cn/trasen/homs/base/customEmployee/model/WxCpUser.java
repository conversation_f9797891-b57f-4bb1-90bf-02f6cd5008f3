package cn.trasen.homs.base.customEmployee.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @ClassName: WxCpUser
 * @Description: TODO(这里用一句话描述这个类的作用)
 * <AUTHOR>
 * @date 2021年12月1日
 *
 */
@Data
public class WxCpUser implements Serializable {

  private static final long serialVersionUID = -5696099236344075582L;
  private String userid;
  private String name;
  private Long[] department;
  private String position;
  private String mobile;
  private String gender;
  private String email;
  private String avatar;
  private Integer status;
  private Integer enable;
  private Integer isLeader;
  private final List<Attr> extAttrs = new ArrayList<>();
  private Integer hideMobile;
  private String englishName;
  private String telephone;

  public void addExtAttr(String name, String value) {
    this.extAttrs.add(new Attr(name, value));
  }

  public static WxCpUser fromJson(String json) {
    return WxCpGsonBuilder.INSTANCE.create().fromJson(json, WxCpUser.class);
  }

  public String toJson() {
    return WxCpGsonBuilder.INSTANCE.create().toJson(this);
  }

  @Data
  @AllArgsConstructor
  public static class Attr {
    private String name;
    private String value;
  }

}
