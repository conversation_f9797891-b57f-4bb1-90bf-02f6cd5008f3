package cn.trasen.homs.base.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "comm_employee_update_operation")
@Setter
@Getter
public class CommEmployeeUpdateOperation {
    @Id
    private String id;

    @Column(name = "employee_id")
    private String employeeId;
    
    /**
     * 修改类型(1:单表 2:子表)
     */
    @Column(name = "update_type")
    @ApiModelProperty(value = "修改类型(1:单表 2:子表)")
    private Integer updateType;
    
    @Column(name = "group_id")
    @ApiModelProperty(value = "修改的分组")
    private String groupId;
    
    @Column(name = "field_name")
    @ApiModelProperty(value = "修改的字段名称")
    private String fieldName;
    
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;
    
    /**
     * 审核状态
     */
    @Column(name = "audit_status")
    @ApiModelProperty(value = "审核状态  0草稿  1审批中  2已完结  3驳回")
    private Integer auditStatus;
    
    @Column(name = "create_user_dept_name")
    @ApiModelProperty(value = "创建人部门名称")
    private String createUserDeptName;
    
    @Column(name = "create_user_dept_code")
    @ApiModelProperty(value = "创建人部门编码")
    private String createUserDeptCode;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "remark")
    private String remark;  //备注

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name="sso_org_name")
    private String ssoOrgName;

}