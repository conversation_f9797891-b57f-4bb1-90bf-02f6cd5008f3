package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.mapper.OrgGroupClassMapper;
import cn.trasen.homs.base.model.OrgGroupClass;
import cn.trasen.homs.base.service.OrgGroupClassService;
import cn.trasen.homs.base.utils.Utils;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 自定义群组类型Impl层
 * @Date: 2020/1/13 18:33
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class OrgGroupClassServiceImpl implements OrgGroupClassService {

    @Autowired
    private OrgGroupClassMapper orgGroupClassMapper;

    /**
     * @Author: Lizhihuo
     * @Description: 查询自定义群组类型列表
     * @Date: 2020/1/14 8:50
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.OrgGroupClass>
     **/
    @Override
    public List<OrgGroupClass> getDataList(Page page, OrgGroupClass orgGroupClass) {
        Example example = new Example(OrgGroupClass.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("classType", orgGroupClass.getClassType());
        if (StringUtils.isNotBlank(orgGroupClass.getClassName())) {//群组分类名称
            example.and().andLike("className", "%" + orgGroupClass.getClassName() + "%");
        }
        example.setOrderByClause("sort_no");
        
        return orgGroupClassMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增自定义群组类型
     * @Date: 2020/1/14 8:50
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(OrgGroupClass orgGroupClass) {

        Example example = new Example(OrgGroupClass.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("className", orgGroupClass.getClassName());

        int count = orgGroupClassMapper.selectCountByExample(example);
        if (count>0) {
            throw new RuntimeException("名字不能相同！");
        }

        orgGroupClass.setId(String.valueOf(IdWork.id.nextId()));
        orgGroupClass.setIsDeleted(Contants.IS_DELETED_FALSE);
        orgGroupClass.setCreateDate(new Date());
        //orgGroupClass.setClassType((short) 1);//组分类 0:系统组  1:用户自定义组
        orgGroupClass.setCreateUser(UserInfoHolder.getCurrentUserCode());
        orgGroupClass.setCreateUserName(UserInfoHolder.getCurrentUserName());
        orgGroupClass.setDomainId(String.valueOf(Utils.STATUS_ENABLED));
        //部门\部门名称\创建人组织ID
        orgGroupClass.setCreateOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        orgGroupClass.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        orgGroupClass.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        orgGroupClass.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
        orgGroupClass.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return orgGroupClassMapper.insertSelective(orgGroupClass);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组类型
     * @Date: 2020/1/14 8:50
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(OrgGroupClass orgGroupClass) {
        orgGroupClass.setUpdateDate(new Date());
        orgGroupClass.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        orgGroupClass.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        
        
        orgGroupClassMapper.updateOrgGroupClassName(orgGroupClass.getId(),orgGroupClass.getClassName());
        
        
        return orgGroupClassMapper.updateByPrimaryKeySelective(orgGroupClass);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组类型
     * @Date: 2020/1/14 8:50
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        OrgGroupClass orgGroupClass = orgGroupClassMapper.selectByPrimaryKey(id);
        orgGroupClass.setIsDeleted(Contants.IS_DELETED_TURE);//是否删除
        return orgGroupClassMapper.updateByPrimaryKeySelective(orgGroupClass);
    }

    @Override
    public List<OrgGroupClass> getList(OrgGroupClass orgGroupClass) {
         Example example = new Example(OrgGroupClass.class);
         example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
         example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
         example.and().andEqualTo("classType", orgGroupClass.getClassType());
         if (StringUtils.isNotBlank(orgGroupClass.getClassName())) {//群组分类名称
            example.and().andLike("className", "%" + orgGroupClass.getClassName() + "%");
         }
         example.setOrderByClause("sort_no");
         return orgGroupClassMapper.selectByExample(example);
    }

    @Override
    @Transactional(readOnly = false)
    public void updateSort(List<OrgGroupClass> record) {
        for (OrgGroupClass orgGroupClass : record) {
            orgGroupClassMapper.updateByPrimaryKeySelective(orgGroupClass);
        }
    }
}

