package cn.trasen.homs.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.utils.IDCardUtil;
import cn.trasen.homs.base.bean.*;
import cn.trasen.homs.base.bo.EmployeeDetails;
import cn.trasen.homs.base.bo.EmployeeListInBO;
import cn.trasen.homs.base.bo.EmployeeListOutBO;
import cn.trasen.homs.base.contants.CommonContants;
import cn.trasen.homs.base.contants.DictContants;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.enums.GenderTypeEnum;
import cn.trasen.homs.base.enums.InsertOrUpdateEnum;
import cn.trasen.homs.base.mapper.HrmsEmployeeMapper;
import cn.trasen.homs.base.model.*;
import cn.trasen.homs.base.properties.BasicsBottomAppConfigProperties;
import cn.trasen.homs.base.service.*;
import cn.trasen.homs.base.utils.DateUtils;
import cn.trasen.homs.base.utils.TreeItem;
import cn.trasen.homs.base.utils.TreeUtil;
import cn.trasen.homs.bean.oa.EmployeeReq;
import cn.trasen.homs.bean.oa.PutUserQueueReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.BoardroomElectronicScreenFeignService;
import cn.trasen.homs.feign.oa.OAEmployeeFeignService;
import cn.trasen.homs.feign.sso.RightFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * @description: 员工
 * @return:
 * @author: liyuan
 * @createTime: 2021/6/18 15:14
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsEmployeeServiceImpl implements HrmsEmployeeService {

    @Autowired
    HrmsEmployeeMapper hrmsEmployeeMapper;
    
    @Autowired
    CustomEmployeeBaseService customEmployeeBaseService;

    @Autowired
    IDictItemService dictItemService;

    @Autowired
    HrmsOrganizationService hrmsOrganizationService;

    @Autowired
    OAEmployeeFeignService oAEmployeeFeignService;
    
    @Autowired
    SystemUserFeignService systemUserFeignService;

    @Autowired
    RightFeignService rightService;

    @Autowired
    BasicsBottomAppConfigProperties basicsBottomAppConfigProperties;

    @Autowired
    BoardroomElectronicScreenFeignService boardroomElectronicScreenFeignService;

    @Autowired
    HrmsPersonnelTransactionService hrmsPersonnelTransactionService;

    @Autowired
    OrganizationService organizationService;

    @Autowired
    GlobalSettingsService globalSettingsService;
    
    @Autowired
    CommOrganizationParttimeService commOrganizationParttimeService;

    Map<String, String> employeeCategoryMap = null;
    Map<String, String> nationalityMap = null;
    Map<String, String> politicalStatusMap = null;
    Map<String, String> marriageStatusMap = null;
    Map<String, String> healthStatusMap = null;
    Map<String, String> establishmentTypeMap = null;
    Map<String, String> personalIdentityMap = null;
    Map<String, String> employeeStatusMap = null;
    Map<String, String> firstEducationMap = null;
    Map<String, String> employeeHospCodeMap = null;


    Map<String, String> workStatusMap = null;


    @Override
    /**
     * @description: 是否存在
     * @param: employeeReq
     * @return: boolean
     * @author: liyuan
     * @createTime: 2021/8/6 14:40
     */
    @Deprecated
    public boolean exists(HrmsEmployeeReq employeeReq) {
        Example example = new Example(HrmsEmployee.class);
        Example.Criteria criteria = example.createCriteria();

        //criteria.andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (!StringUtils.isBlank(employeeReq.getGwdj())) {
            criteria.andEqualTo("gwdj", employeeReq.getGwdj());
        }
        if (!StringUtils.isBlank(employeeReq.getPlgw())) {
            criteria.andEqualTo("plgw", employeeReq.getPlgw());
        }
        if (!StringUtils.isBlank(employeeReq.getPersonalIdentity())) {
            criteria.andEqualTo("personalIdentity", employeeReq.getPersonalIdentity());
        }
        if (!StringUtils.isBlank(employeeReq.getPositionId())) {
            criteria.andEqualTo("positionId", employeeReq.getPositionId());
        }
        
        int count = hrmsEmployeeMapper.selectCountByExample(example);
        if (count > 0) {
            return true;
        }
        return false;
    }


    /**
     * @param
     * @Title: getList
     * @Description: 查询员工列表(不分页)
     * @Return List<HrmsEmployee>
     * <AUTHOR>
     * @date 2020年5月9日 上午10:37:31
     */
    @Override
    public List<HrmsEmployeeResp> getList(EmployeeListReq employeeListReq) {
        // Example example = new Example(HrmsEmployee.class);
        // example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
        // if example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD,
        // Contants.IS_DELETED_FALSE);
        // (StringUtils.isNotBlank(entity.getOrgId())) { // 组织机构ID
        // example.and().andEqualTo("orgId", entity.getOrgId());
        // }
        employeeListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsEmployeeMapper.getPageList(employeeListReq);
        // throw new RuntimeException("未实现方法");
    }

    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.bean.HrmsEmployeeResp
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    @Override
    public List<HrmsEmployeeResp> getEmployeeDetailByCodes(List<String> employeeCodes) {
        List<HrmsEmployeeResp> hrmsEmployeeList = hrmsEmployeeMapper.findByEmployeeNos(employeeCodes,UserInfoHolder.getCurrentUserCorpCode());
        for (HrmsEmployeeResp hrmsEmployee : hrmsEmployeeList) {
            fullEmployeeData(hrmsEmployee);
        }
        return hrmsEmployeeList;
    }

    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.bean.HrmsEmployeeResp
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    @Override
    public List<HrmsEmployeeResp> getEmployeeDetailByIds(List<String> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return new ArrayList<>();
        }
        List<HrmsEmployeeResp> hrmsEmployeeList = hrmsEmployeeMapper.findByEmployeeIds(employeeIds, UserInfoHolder.getCurrentUserCorpCode());
        for (HrmsEmployeeResp hrmsEmployee : hrmsEmployeeList) {
            fullEmployeeData(hrmsEmployee);
        }
        return hrmsEmployeeList;
    }


    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.bean.HrmsEmployeeResp
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    @Override
    public HrmsEmployeeResp findByEmployeePhoneNumber(String phoneNumber) {
        HrmsEmployeeResp hrmsEmployee = hrmsEmployeeMapper.findByEmployeePhoneNumber(phoneNumber,UserInfoHolder.getCurrentUserCorpCode());
        if (hrmsEmployee == null) {
            return null;
        }

        fullEmployeeData(hrmsEmployee);
        if (StringUtils.isBlank(hrmsEmployee.getAgentIds()) == false) {

            List<String> employeeCodes = new ArrayList<>();

            for (String s : hrmsEmployee.getAgentIds().split(",")) {
                employeeCodes.add(s);
            }

            List<HrmsEmployeeResp> agents = getEmployeeDetailByCodes(employeeCodes);
            hrmsEmployee.setAgents(agents);
        }
        return hrmsEmployee;
    }


    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.bean.HrmsEmployeeResp
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    @Override
    public HrmsEmployeeResp findByEmployeeNo(String employeeNo) {
    	
    	String currentUserCorpCode = UserInfoHolder.getCurrentUserCorpCode();
    	if(StringUtils.isBlank(currentUserCorpCode)) {
    		GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
    		currentUserCorpCode = globalSetting.getOrgCode();
    	}
    	
        HrmsEmployeeResp hrmsEmployee = hrmsEmployeeMapper.findByEmployeeNo(employeeNo,currentUserCorpCode);
        if (hrmsEmployee == null) {
            return null;
        }
        fullEmployeeData(hrmsEmployee);


        if (StringUtils.isBlank(hrmsEmployee.getAgentIds()) == false) {

            List<String> employeeCodes = new ArrayList<>();

            for (String s : hrmsEmployee.getAgentIds().split(",")) {
                employeeCodes.add(s);
            }

            List<HrmsEmployeeResp> agents = getEmployeeDetailByCodes(employeeCodes);
            hrmsEmployee.setAgents(agents);
        }
        
        CommOrganizationParttime commOrganizationParttime = new CommOrganizationParttime();
        commOrganizationParttime.setEmployeeId(hrmsEmployee.getEmployeeId());
        List<CommOrganizationParttime> commOrganizationParttimeList = commOrganizationParttimeService.getList(commOrganizationParttime);
        
        if(CollectionUtils.isNotEmpty(commOrganizationParttimeList)){
            hrmsEmployee.setOrganizationParttimeList(commOrganizationParttimeList);
        }
        
        return hrmsEmployee;
    }

    /**
     * @description: 根据员工工号查询员工信息
     * @param: employeeNo
     * @return: cn.trasen.basicsbottom.bean.HrmsEmployeeResp
     * @author: liyuan
     * @createTime: 2021/6/18 17:35
     */
    @Override
    public HrmsEmployeeResp findByEmployeeId(String employeeId) {
        HrmsEmployeeResp hrmsEmployee = hrmsEmployeeMapper.findByEmployeeId(employeeId, UserInfoHolder.getCurrentUserCorpCode());
        if (hrmsEmployee == null) {
            return null;
        }
        fullEmployeeData(hrmsEmployee);
        return hrmsEmployee;
    }

    /**
     * @description: 获取员工列表
     * @param: page
     * @param: employeeListReq
     * @return: java.util.List<cn.trasen.hrms.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/5/27 10:42
     */
    @Override
    public List<HrmsEmployeeResp> getEmployeePageList(Page page, EmployeeListReq record) {

        List<HrmsEmployeeResp> list = new ArrayList<>();
        if (record == null) {
            record = new EmployeeListReq();
        }

        if (record.getNoEmployeeStatusList() == null && record.getEmployeeStatusList() == null) {
            List<String> employeeStatusList = new ArrayList<>();
            employeeStatusList.add("3");
            employeeStatusList.add("4");
            employeeStatusList.add("7");
            employeeStatusList.add("8");
            record.setNoEmployeeStatusList(employeeStatusList);
        }

        if (!StringUtils.isBlank(record.getOrgId())) {
            List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(record.getOrgId());
            record.setOrgIdList(orgIdNewList);
        }


        if(StringUtils.isBlank(page.getSidx()))
        {
            page.setSidx(" e.emp_sort ");
            page.setSord(" asc ");
        }
        
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        list = hrmsEmployeeMapper.getPageList(page, record);
        fullEmployeeData(list);
        return list;
    }

    @Override
    /**
     * @description: 获取员工基础数据
     * @param: record
     * @return: java.util.List<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/7/26 14:20
     */
    public List<HrmsEmployee> getEmployeeBaseList(EmployeeListReq record) {

        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        return hrmsEmployeeMapper.getEmployeeBaseList(record);
    }


    @Override
    /**
     * @description: 获取列表
     * @param: record
     * @return: java.util.List<cn.trasen.basicsbottom.bean.HrmsEmployeeResp>
     * @author: liyuan
     * @createTime: 2021/6/23 14:41
     */
    public List<HrmsEmployeeResp> getEmployeeList(EmployeeListReq record) {

        List<HrmsEmployeeResp> list = new ArrayList<>();
        EmployeeListReq entity = new EmployeeListReq();
        BeanUtil.copyProperties(record, entity);
        if (!StringUtils.isBlank(record.getOrgId())) {
            List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(record.getOrgId());
            entity.setOrgIdList(orgIdNewList);
        }
        if (!StringUtils.isBlank(record.getEqOrgId())) {
            entity.setOrgId(record.getEqOrgId());
        }
        if (record.getNoEmployeeStatusList() == null && record.getEmployeeStatusList() == null) {
            List<String> employeeStatusList = new ArrayList<>();
            //  employeeStatusList.add("3");
            employeeStatusList.add("4");
            employeeStatusList.add("7");
            employeeStatusList.add("8");
            entity.setNoEmployeeStatusList(employeeStatusList);
        }

        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        list = hrmsEmployeeMapper.getPageList(entity);
        fullEmployeeData(list);
        return list;
    }

    private void fullEmployeeData(List<HrmsEmployeeResp> hrmsEmployees) {
        for (HrmsEmployeeResp h : hrmsEmployees) {
            fullEmployeeData(h);
        }
    }


    @Override
    /**
     * @description: 填充用户基础信息
     * @param: hrmsEmployee
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/3 14:21
     */
    public void fullEmployeeData(HrmsEmployeeResp hrmsEmployee) {

        if (hrmsEmployee == null) {
            return;
        }
        
        if (hrmsEmployee.getAgentStartTime() != null) {
            if (hrmsEmployee.getAgentStartTime().getTime() <= new Date(0).getTime()) {
                hrmsEmployee.setAgentStartTime(null);
            }
        }
        if (hrmsEmployee.getAgentEndTime() != null) {
            if (hrmsEmployee.getAgentEndTime().getTime() <= new Date(0).getTime()) {
                hrmsEmployee.setAgentEndTime(null);
            }
        }
        
        if (employeeCategoryMap == null) {
            employeeCategoryMap = dictItemService.convertDictMap(DictContants.EMPLOYEE_CATEGORY);
        }
        if (nationalityMap == null) {
            nationalityMap = dictItemService.convertDictMap(DictContants.NATIONALITY_NAME);
        }
        if (politicalStatusMap == null) {
            politicalStatusMap = dictItemService.convertDictMap(DictContants.POLITICAL_STATUS);
        }
        if (marriageStatusMap == null) {
            marriageStatusMap = dictItemService.convertDictMap(DictContants.MARRIAGE_STATUS);
        }
        if (healthStatusMap == null) {
            healthStatusMap = dictItemService.convertDictMap(DictContants.HEALTH_STATUS);
        }
        if (establishmentTypeMap == null) {
            establishmentTypeMap = dictItemService.convertDictMap(DictContants.ESTABLISHMENT_TYPE);
        }
        if (personalIdentityMap == null) {
            personalIdentityMap = dictItemService.convertDictMap(DictContants.PERSONAL_IDENTITY);
        }
        
        if (workStatusMap == null) {
            workStatusMap = dictItemService.convertDictMap("WORK_STATUS");
        }
        if (employeeStatusMap == null) {
            employeeStatusMap = dictItemService.convertDictMap(DictContants.EMPLOYEE_STATUS);
        }
        if (firstEducationMap == null) {
        	firstEducationMap = dictItemService.convertDictMap(DictContants.EDUCATION_TYPE);
        }
        if (employeeHospCodeMap == null) {
        	employeeHospCodeMap = dictItemService.convertDictMap(DictContants.HOSP_AREA);
        }
        
        
        


        // Map<String, String> genderMap =
        // hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE);

        hrmsEmployee.setEmployeeCategoryText(employeeCategoryMap.get(hrmsEmployee.getEmployeeCategory()));
        hrmsEmployee.setNationalityName(nationalityMap.get(hrmsEmployee.getNationality()));
        hrmsEmployee.setPoliticalStatusText(politicalStatusMap.get(hrmsEmployee.getPoliticalStatus()));
        hrmsEmployee.setMarriageStatus(marriageStatusMap.get(hrmsEmployee.getMarriageStatus()));
        hrmsEmployee.setHealthStatus(healthStatusMap.get(hrmsEmployee.getHealthStatus()));
        hrmsEmployee.setEstablishmentTypeText(establishmentTypeMap.get(hrmsEmployee.getEstablishmentType()));
        hrmsEmployee.setPersonalIdentityName(personalIdentityMap.get(hrmsEmployee.getPersonalIdentity()));
        hrmsEmployee.setGenderText(GenderTypeEnum.getValByKey(hrmsEmployee.getGender()));
        hrmsEmployee.setWorkStatusLable(workStatusMap.getOrDefault(hrmsEmployee.getWorkStatus(), ""));
        hrmsEmployee.setEmployeeStatusName(employeeStatusMap.get(hrmsEmployee.getEmployeeStatus()));
        hrmsEmployee.setFirstEducationTypeText(firstEducationMap.get(hrmsEmployee.getFirstEducationType()));
        hrmsEmployee.setHospName(employeeHospCodeMap.get(hrmsEmployee.getHospCode()));
    }


    @Override
    /**
     * @description: 修改员工个人信息
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/23 10:50
     */
    @Transactional(readOnly = false)
    @Deprecated
    public Integer updateMyUser(HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        BeanUtil.copyProperties(hrmsEmployeeSaveReq, hrmsEmployee);
        hrmsEmployee.setEmployeeNo(UserInfoHolder.getCurrentUserCode());
        hrmsEmployee.setEmployeeId(UserInfoHolder.getCurrentUserId());
        Integer exec = updateUser(hrmsEmployee);
        return exec;
    }

    @Override
    /**
     * @description: 修改员工个人设置
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/23 10:50
     */
    @Transactional(readOnly = false)
    public Integer updateUserConfig(HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {

        // isSmsReminder 是否接收短信提醒 否：0，是：1"
        // isWxReminder 是否接收微信消息推送 否：0，是：1"
        // isDisplayPhoneNo 是否显示个人手机号码 否：0，是：1
        // agentId 流程代理人
        // agentName 流程代理名字
        // agentStartTime 流程开始时间
        // agentEndTime 流程结束时间
        // isEnableProcessAgent 是否启用流程代理 否：0，是：1
        // signatureImgName 电子签章
        // isUseSignature 是否使用电子签章 否：0，是：1
        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        hrmsEmployee.setIsSmsReminder(hrmsEmployeeSaveReq.getIsSmsReminder());
        hrmsEmployee.setIsWxReminder(hrmsEmployeeSaveReq.getIsWxReminder());
        hrmsEmployee.setIsDisplayPhoneNo(hrmsEmployeeSaveReq.getIsDisplayPhoneNo());

        hrmsEmployee.setAgentIds(hrmsEmployeeSaveReq.getAgentIds());
        hrmsEmployee.setAgentNames(hrmsEmployeeSaveReq.getAgentNames());
        if (hrmsEmployeeSaveReq.getAgentStartTime() == null) {
            hrmsEmployee.setAgentStartTime(new Date(0));
        } else {
            hrmsEmployee.setAgentStartTime(hrmsEmployeeSaveReq.getAgentStartTime());

        }
        if (hrmsEmployeeSaveReq.getAgentEndTime() == null) {
            hrmsEmployee.setAgentEndTime(new Date(0));
        } else {
            hrmsEmployee.setAgentEndTime(hrmsEmployeeSaveReq.getAgentEndTime());

        }
        hrmsEmployee.setIsEnableProcessAgent(hrmsEmployeeSaveReq.getIsEnableProcessAgent() == null ? 0
                : hrmsEmployeeSaveReq.getIsEnableProcessAgent());
        hrmsEmployee.setSignatureImgName(hrmsEmployeeSaveReq.getSignatureImgName());
        hrmsEmployee.setIsUseSignature(hrmsEmployeeSaveReq.getIsUseSignature());

        if(StringUtils.isNotBlank(hrmsEmployeeSaveReq.getEmployeeId())){
            hrmsEmployee.setEmployeeId(hrmsEmployeeSaveReq.getEmployeeId());
        }else{
            hrmsEmployee.setEmployeeId(UserInfoHolder.getCurrentUserId());
        }
        if(StringUtils.isNotBlank(hrmsEmployeeSaveReq.getEmployeeNo())){
            hrmsEmployee.setEmployeeNo(hrmsEmployeeSaveReq.getEmployeeNo());
        }else{
            hrmsEmployee.setEmployeeNo(UserInfoHolder.getCurrentUserCode());
        }
        
        hrmsEmployee.setEmployeeFace(hrmsEmployeeSaveReq.getEmployeeFace());
        hrmsEmployee.setEmpSignimg(hrmsEmployeeSaveReq.getEmpSignimg());
        
        //兼职科室
        if(CollectionUtils.isNotEmpty(hrmsEmployeeSaveReq.getOrganizationParttimeList())){
            commOrganizationParttimeService.deleteByEmployeeId(hrmsEmployeeSaveReq.getEmployeeId());
            
            for (CommOrganizationParttime organizationParttime : hrmsEmployeeSaveReq.getOrganizationParttimeList()) {
                if(StringUtils.isNotBlank(organizationParttime.getOrgId()) ){
                    commOrganizationParttimeService.save(organizationParttime);
                }
            }
        }
        
        Integer exec = updateUser(hrmsEmployee);
        return exec;
    }


    @Override
    /**
     * @description: 修改员工
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/8/13 9:27
     */
    @Transactional(readOnly = false)
    public Integer updateEmployee(HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeId())) {
            throw new BusinessException("员工ID不能为空！");
        }
        HrmsEmployee updateHrmsEmployee = new HrmsEmployee();
        BeanUtil.copyProperties(hrmsEmployeeSaveReq, updateHrmsEmployee);
        Example example = new Example(HrmsEmployee.class);
        example.createCriteria().andEqualTo("employeeId", hrmsEmployeeSaveReq.getEmployeeId());
        updateHrmsEmployee.setEmployeeId(null);
        updateHrmsEmployee.setEmployeeNo(null);
        if("2".equals(updateHrmsEmployee.getIsEnable())){
			updateHrmsEmployee.setDisableStatus("0");
		}else{
			updateHrmsEmployee.setDisableStatus("1");
		}
        Integer exec = hrmsEmployeeMapper.updateByExampleSelective(updateHrmsEmployee, example);
        sync(hrmsEmployeeSaveReq, InsertOrUpdateEnum.UPDATE);
        return exec;
    }

    /**
     * @description:
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/25 13:28
     */
    @Override
    @Transactional(readOnly = false)
    public Integer updateUser(HrmsEmployee hrmsEmployee) {
    	
    	CustomEmployeeBase customEmployeeBase = new CustomEmployeeBase();
    	
    	BeanUtil.copyProperties(hrmsEmployee, customEmployeeBase);
    	customEmployeeBaseService.update(customEmployeeBase);
    	
//        HrmsEmployee updateHrmsEmployee = new HrmsEmployee();
//        BeanUtil.copyProperties(hrmsEmployee, updateHrmsEmployee);
//        updateHrmsEmployee.setEmployeeId(null);
//        updateHrmsEmployee.setEmployeeNo(null);
//        Example example = new Example(HrmsEmployee.class);
//        example.createCriteria().andEqualTo("employeeId", hrmsEmployee.getEmployeeId());
//        if("2".equals(updateHrmsEmployee.getIsEnable())){
//			updateHrmsEmployee.setDisableStatus("0");
//		}
//        Integer exec = hrmsEmployeeMapper.updateByExampleSelective(updateHrmsEmployee, example);
//        if (exec > 0) {
//            
//            HrmsEmployeeSaveReq hrmsEmployeeSaveReq = new HrmsEmployeeSaveReq();
//            BeanUtil.copyProperties(hrmsEmployee, hrmsEmployeeSaveReq);
//            hrmsEmployeeSaveReq.setEmployeeNo(hrmsEmployee.getEmployeeNo());
//            hrmsEmployeeSaveReq.setEmployeeId(hrmsEmployee.getEmployeeId());
//
//            sync(hrmsEmployeeSaveReq, InsertOrUpdateEnum.UPDATE);
//        }
        return 1;
    }


    @Override
    /**
     *
     * 禁用启用
     * @description: 禁用启用
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id, String enable) {
        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        hrmsEmployee.setEmployeeId(id);
        if (enable.equals("Y")) {
            hrmsEmployee.setIsEnable("1");
        } else {
            hrmsEmployee.setIsEnable("0");
        }
        updateUser(hrmsEmployee);
    }


    /**
     * @description: 批量更新员工机构 后期优化批量操作
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/25 13:28
     */
    @Override
    @Transactional(readOnly = false)
    public void updateUserOrg(List<String> hrmsEmployeeIds, String orgId) {
        for (String s : hrmsEmployeeIds) {
            HrmsEmployee updateHrmsEmployee = new HrmsEmployee();
            updateHrmsEmployee.setEmployeeId(s);
            updateHrmsEmployee.setOrgId(orgId);
            
            updateUser(updateHrmsEmployee);
        }
    }

    @Override
    /**
     * @description: 新增员工个人信息
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/23 10:50
     */
    @Deprecated
    @Transactional(readOnly = false)
    public PlatformResult<HrmsEmployeeResp> addEmployee(HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {

        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeNo())) {
            return PlatformResult.failure("编码不能为空");
        }


        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeId())) {
            hrmsEmployeeSaveReq.setEmployeeId(String.valueOf(IdWork.id.nextId()));
        }

        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        BeanUtil.copyProperties(hrmsEmployeeSaveReq, hrmsEmployee);

        hrmsEmployee.setCreateDate(new Date());
        hrmsEmployee.setUpdateDate(new Date());
        hrmsEmployee.setCreateUser(UserInfoHolder.getCurrentUserId());
        hrmsEmployee.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        hrmsEmployee.setIsDeleted(Contants.IS_DELETED_FALSE);
        if(StringUtils.isBlank(hrmsEmployee.getEmployeeStatus())){
            hrmsEmployee.setEmployeeStatus("1");
        }
        if (StringUtils.isBlank(hrmsEmployee.getEmpPayroll())) {
            hrmsEmployee.setEmpPayroll(hrmsEmployee.getEmployeeNo());
        }


        Example example = new Example(HrmsEmployee.class);
        example.and().andEqualTo("employeeNo", hrmsEmployee.getEmployeeNo());
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsEmployee> employeeList = hrmsEmployeeMapper.selectByExample(example);
        if (employeeList.size() > 0) {
            return PlatformResult.failure("重复编码");
        }
        hrmsEmployee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        hrmsEmployeeMapper.insertSelective(hrmsEmployee);
        
        Map<String, Object> dataMap = hrmsEmployeeSaveReq.getDataMap();
        if(null != dataMap){
            StringBuffer sbSql = new StringBuffer();
            if(null != dataMap.get("hrms_child")){
                sbSql.append("update hrms_employee set hrms_child='").append(dataMap.get("hrms_child")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("account_nature2")){
                sbSql.append("update hrms_employee set account_nature2='").append(dataMap.get("account_nature2")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("dy_xueli")){
                sbSql.append("update hrms_employee set dy_xueli='").append(dataMap.get("dy_xueli")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work1")){
                sbSql.append("update hrms_employee set jkhrms_work1='").append(dataMap.get("jkhrms_work1")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work2")){
                sbSql.append("update hrms_employee set jkhrms_work2='").append(dataMap.get("jkhrms_work2")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work3")){
                sbSql.append("update hrms_employee set jkhrms_work3='").append(dataMap.get("jkhrms_work3")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zhicheng")){
                sbSql.append("update hrms_employee set zhicheng='").append(dataMap.get("zhicheng")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zhichengzhuangye")){
                sbSql.append("update hrms_employee set zhichengzhuangye='").append(dataMap.get("zhichengzhuangye")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zcqudeshijian")){
                sbSql.append("update hrms_employee set zcqudeshijian='").append(dataMap.get("zcqudeshijian")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zcqudedidian")){
                sbSql.append("update hrms_employee set zcqudedidian='").append(dataMap.get("zcqudedidian")).append("' where employee_id='").append(hrmsEmployee.getEmployeeId()).append("';");
            }
            log.info("执行的sql：" + sbSql.toString());
            System.out.println("执行的sql：" + sbSql.toString());
            hrmsEmployeeMapper.excelSql(sbSql.toString());
        }
        
        
        //学历信息
        if(CollectionUtils.isNotEmpty(hrmsEmployeeSaveReq.getHrmsEducationList())){
            for (HrmsEducation hrmsEducation : hrmsEmployeeSaveReq.getHrmsEducationList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_education_info (id,employee_id,start_time,end_time,school_name,education_type,professional,learn_way,highest_level,xlfj,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(hrmsEmployee.getEmployeeId()).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsEducation.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsEducation.getEndTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(hrmsEducation.getSchoolName()).append("',");
                sbSql.append("'").append(hrmsEducation.getEducationType()).append("',");
                sbSql.append("'").append(hrmsEducation.getProfessional()).append("',");
                sbSql.append("'").append(hrmsEducation.getLearnWay()).append("',");
                sbSql.append("'").append(hrmsEducation.getHighestLevel()).append("',");
                sbSql.append("'").append(hrmsEducation.getXlfj()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                hrmsEmployeeMapper.excelSql(sbSql.toString());
            }
        }
        
        //院外工作经历
        if(CollectionUtils.isNotEmpty(hrmsEmployeeSaveReq.getHrmsWorkRecordList())){
            for (HrmsWorkRecord hrmsWorkRecord : hrmsEmployeeSaveReq.getHrmsWorkRecordList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_work_experience (id,employee_id,start_time,end_time,work_unit,dept_name,post,witness,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(hrmsEmployee.getEmployeeId()).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsWorkRecord.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsWorkRecord.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getWorkUnit()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getDeptName()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getPost()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getWitness()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                hrmsEmployeeMapper.excelSql(sbSql.toString());
            }
        }
        
        //家庭关系
        if(CollectionUtils.isNotEmpty(hrmsEmployeeSaveReq.getFamilyInfoList())){
            for (HrmsFamilyInfo hrmsFamilyInfo : hrmsEmployeeSaveReq.getFamilyInfoList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_family_info (id,employee_id,member_name,relationship,work_unit,contact_number,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(hrmsEmployee.getEmployeeId()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getMemberName()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getRelationship()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getWorkUnit()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getContactNumber()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                hrmsEmployeeMapper.excelSql(sbSql.toString());
            }
        }
        
        
//      EmployeeReq employeeReq = new EmployeeReq();
//      BeanUtil.copyProperties(hrmsEmployee, employeeReq);
//      employeeReq.setRoleCode(hrmsEmployeeSaveReq.getRoleCode());
//      employeeReq.setEmpPassword(hrmsEmployeeSaveReq.getEmpPassword());
        hrmsEmployeeSaveReq.setIsDeleted(Contants.IS_DELETED_FALSE);
        hrmsEmployeeSaveReq.setEmployeeStatus(hrmsEmployee.getEmployeeStatus());
        hrmsEmployeeSaveReq.setEmpPayroll(hrmsEmployee.getEmpPayroll());
        sync(hrmsEmployeeSaveReq, InsertOrUpdateEnum.INSERT);

        HrmsEmployeeResp hrmsEmployeeResp = new HrmsEmployeeResp();
        BeanUtil.copyProperties(hrmsEmployee, hrmsEmployeeResp);

        //插入人事异动记录
        HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction(hrmsEmployeeSaveReq.getEmployeeNo(),
                hrmsEmployeeSaveReq.getEmployeeName(), hrmsEmployeeSaveReq.getEmployeeId(), hrmsEmployeeSaveReq.getOrgId(), hrmsEmployeeSaveReq.getOrgName(), null,
                null, DateUtils.getStringDateShort(new Date()), "员工入职", "是", hrmsPersonnelTransactionService.getBatchNumber(),
                null, null, null,UserInfoHolder.getCurrentUserCorpCode());
        hrmsPersonnelTransactionService.insert(hpt); // 添加人事事件
        return PlatformResult.success(hrmsEmployeeResp);
    }


    @Override
    /**
     * @description: 新增员工个人信息
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/23 10:50
     */
    @Transactional(readOnly = false)
    @Deprecated
    public PlatformResult<HrmsEmployeeResp> addEmployeeOld(HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {

        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeNo())) {
            return PlatformResult.failure("编码不能为空");
        }


        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeId())) {
            hrmsEmployeeSaveReq.setEmployeeId(String.valueOf(IdWork.id.nextId()));
        }

        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        BeanUtil.copyProperties(hrmsEmployeeSaveReq, hrmsEmployee);

        hrmsEmployee.setCreateDate(new Date());
        hrmsEmployee.setUpdateDate(new Date());
        hrmsEmployee.setCreateUser(UserInfoHolder.getCurrentUserId());
        hrmsEmployee.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        hrmsEmployee.setIsDeleted(Contants.IS_DELETED_FALSE);
        hrmsEmployee.setEmployeeStatus("1");
        if (StringUtils.isBlank(hrmsEmployee.getEmpPayroll())) {
            hrmsEmployee.setEmpPayroll(hrmsEmployee.getEmployeeNo());
        }


        Example example = new Example(HrmsEmployee.class);
        example.and().andEqualTo("employeeNo", hrmsEmployee.getEmployeeNo());
        List<HrmsEmployee> employeeList = hrmsEmployeeMapper.selectByExample(example);
        if (employeeList.size() > 0) {
            hrmsEmployee.setEmployeeId(employeeList.get(0).getEmployeeId());
            hrmsEmployeeMapper.updateByPrimaryKeySelective(hrmsEmployee);
        } else {
            hrmsEmployeeMapper.insertSelective(hrmsEmployee);
        }

        HrmsEmployeeResp hrmsEmployeeResp = new HrmsEmployeeResp();
        BeanUtil.copyProperties(hrmsEmployee, hrmsEmployeeResp);
        return PlatformResult.success(hrmsEmployeeResp);
    }

    /**
     * 新增同步
     *
     * @description:
     * @param: employeeId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/3 11:09
     */
    @Override
    @Deprecated
    public void userSync(String employeeId, InsertOrUpdateEnum insertOrUpdateEnum) {
        HrmsEmployeeResp hrmsEmployeeResp = findByEmployeeId(employeeId);
        if (hrmsEmployeeResp != null) {

            HrmsEmployeeSaveReq hrmsEmployeeSaveReq = new HrmsEmployeeSaveReq();

            BeanUtil.copyProperties(hrmsEmployeeResp, hrmsEmployeeSaveReq);

            sync(hrmsEmployeeSaveReq, insertOrUpdateEnum);
        }
    }

    /**
     * @description:
     * @param: employeeReq
     * @param: save
     * 1新增 2修改
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/3 11:10
     */
    @Deprecated
    public void sync(HrmsEmployeeSaveReq hrmsEmployeeSaveReq, InsertOrUpdateEnum insertOrUpdateEnum) {
        ThpsUserReq thpsUser = new ThpsUserReq();
        thpsUser.setId(hrmsEmployeeSaveReq.getEmployeeId());

        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeNo())) {
            HrmsEmployeeResp hrmsEmployeeResp = findByEmployeeId(hrmsEmployeeSaveReq.getEmployeeId());
            if (hrmsEmployeeResp != null) {
                hrmsEmployeeSaveReq.setEmployeeNo(hrmsEmployeeResp.getEmployeeNo());
                hrmsEmployeeSaveReq.setEmployeeName(hrmsEmployeeResp.getEmployeeName());
            }
        }

        thpsUser.setUsercode(hrmsEmployeeSaveReq.getEmployeeNo());
        thpsUser.setOldusercode(hrmsEmployeeSaveReq.getEmployeeNo());
        thpsUser.setUsername(hrmsEmployeeSaveReq.getEmployeeName());

        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getIsEnable()) == false) {
        	if("2".equals(hrmsEmployeeSaveReq.getIsEnable())){
				thpsUser.setStatus(0);
			}else{
				thpsUser.setStatus(Integer.parseInt(hrmsEmployeeSaveReq.getIsEnable()));
			}
        }


        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getPhoneNumber()) == false) {
            thpsUser.setMobileNo(hrmsEmployeeSaveReq.getPhoneNumber());
        }
        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getGender()) == false) {
            thpsUser.setSex(hrmsEmployeeSaveReq.getGender());
        }
        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getOrgId()) == false) {
            thpsUser.setDeptcode(hrmsEmployeeSaveReq.getOrgId());
        }

        if (hrmsEmployeeSaveReq.getAgentStartTime() != null) {
            if (hrmsEmployeeSaveReq.getAgentStartTime().getTime() <= new Date(0).getTime()) {
                hrmsEmployeeSaveReq.setAgentStartTime(null);
            }
        }
        if (hrmsEmployeeSaveReq.getAgentEndTime() != null) {
            if (hrmsEmployeeSaveReq.getAgentEndTime().getTime() <= new Date(0).getTime()) {
                hrmsEmployeeSaveReq.setAgentEndTime(null);
            }
        }
        if (insertOrUpdateEnum.getCode().equals(InsertOrUpdateEnum.INSERT.getCode())) {

            GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");

            String passWord = "111111";
            if (StringUtils.isNotBlank(globalSetting.getPasswordPreset())) {
                passWord = globalSetting.getPasswordPreset();
            } else {
                if (IDCardUtil.isIdentity(hrmsEmployeeSaveReq.getIdentityNumber())) {
                    passWord = hrmsEmployeeSaveReq.getIdentityNumber().substring(hrmsEmployeeSaveReq.getIdentityNumber().length() - 6);
                }
            }

            /*try {
                thpsUser.setPassword(PasswordHash.createHash(passWord));
                String md5Password = MD5.string2MD5(passWord);//md5 加密
                thpsUser.setOldpassword(md5Password.toUpperCase());//将md5解密转换为大写
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("密码:" + ex);
            }*/
            
            thpsUser.setPassword(passWord);
            thpsUser.setOldpassword(passWord);//将md5解密转换为大写

        } else if (insertOrUpdateEnum.getCode().equals(InsertOrUpdateEnum.DELETE.getCode())) {
            thpsUser.setStatus(0);
            hrmsEmployeeSaveReq.setIsDeleted("Y");
        }
        if(ObjectUtils.isEmpty(hrmsEmployeeSaveReq.getSsoOrgCode())){
        	thpsUser.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
        } else {
        	thpsUser.setCorpcode(hrmsEmployeeSaveReq.getSsoOrgCode());
        }

        log.info("---同步用户：" + JSON.toJSONString(thpsUser));

        if (
                StringUtils.isBlank(hrmsEmployeeSaveReq.getAvatar()) == false ||
                        StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeFace()) == false
        ) {

            PutUserQueueReq putUserQueueReq = new PutUserQueueReq();
            putUserQueueReq.setEmployeeId(hrmsEmployeeSaveReq.getEmployeeId());
            putUserQueueReq.setEmployeeNo(hrmsEmployeeSaveReq.getEmployeeNo());
            putUserQueueReq.setEmployeeFace(hrmsEmployeeSaveReq.getAvatar());
            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeFace()) == false) {
                putUserQueueReq.setEmployeeFace(hrmsEmployeeSaveReq.getEmployeeFace());
            }
            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEmployeeName())) {
                HrmsEmployee hrmsEmployee = hrmsEmployeeMapper.selectByPrimaryKey(hrmsEmployeeSaveReq.getEmployeeId());
                putUserQueueReq.setEmployeeName(hrmsEmployee.getEmployeeName());

            } else {
                putUserQueueReq.setEmployeeName(hrmsEmployeeSaveReq.getEmployeeName());
            }
            try {
                boardroomElectronicScreenFeignService.syncUser(putUserQueueReq);
            } catch (Exception ex) {
                log.error("putUserQueue", ex);
            }
        }

        // 权限系统同步
        PlatformResult<String> saveOrUpdate = systemUserFeignService.saveOrUpdate(thpsUser);
        if(saveOrUpdate.isSuccess()){
            log.info("----同步用户返回信息" + saveOrUpdate.getMessage() + "---同步用户返回：" + saveOrUpdate.getObject());
        }
        
        if (insertOrUpdateEnum.getCode().equals(InsertOrUpdateEnum.INSERT.getCode())) {

            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getRoleCode())) {
                rightService.roleUserSave(hrmsEmployeeSaveReq.getEmployeeId(), basicsBottomAppConfigProperties.getDefaultRoleCode(),
                        "", "");
            } else {
                rightService.roleUserSave(hrmsEmployeeSaveReq.getEmployeeId(), hrmsEmployeeSaveReq.getRoleCode(),
                        "", "");
            }

        }
        EmployeeReq employeeReq = new EmployeeReq();
        BeanUtil.copyProperties(hrmsEmployeeSaveReq, employeeReq);

        if (hrmsEmployeeSaveReq.getAgentIds() != null) {
            employeeReq.setAgentId(hrmsEmployeeSaveReq.getAgentIds());
        }
        if (hrmsEmployeeSaveReq.getAgentNames() != null) {
            employeeReq.setAgentName(hrmsEmployeeSaveReq.getAgentNames());
        }
        if (StringUtils.isBlank(hrmsEmployeeSaveReq.getIsDeleted()) == false) {
            employeeReq.setIsDeleted(hrmsEmployeeSaveReq.getIsDeleted());
        }
        employeeReq.setOrgCode(employeeReq.getOrgId());
        employeeReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        oAEmployeeFeignService.insertOrUpdate(employeeReq);
    }

    @Override
    public HrmsEmployee findById(String id) {
        HrmsEmployee hrmsEmployee = hrmsEmployeeMapper.selectByPrimaryKey(id);
        return hrmsEmployee;
    }

    @Override
    @Transactional(readOnly = false)
    @Deprecated
    public int deleteById(String id) {
        HrmsEmployee hrmsEmployee = hrmsEmployeeMapper.selectByPrimaryKey(id);

        hrmsEmployee.setUpdateDate(new Date());

        hrmsEmployee.setUpdateUser(UserInfoHolder.getCurrentUserCode());

        hrmsEmployee.setUpdateUserName(UserInfoHolder.getCurrentUserName());

        hrmsEmployee.setIsDeleted(Contants.IS_DELETED_TURE);

        EmployeeReq employeeReq = new EmployeeReq();
        employeeReq.setUpdateDate(new Date());
        employeeReq.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        employeeReq.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        employeeReq.setIsDeleted(Contants.IS_DELETED_TURE);
        oAEmployeeFeignService.insertOrUpdate(employeeReq);

        return hrmsEmployeeMapper.updateByPrimaryKeySelective(hrmsEmployee);
    }

    /**
     * @Title: getJurisdictionEmployeePageList
     * @Description: 用于字段权限设置, 在查询后增加"本人"人员
     * @Params: @param page
     * @Params: @param record
     * @Params: @return
     * @Return: List<HrmsEmployeeResp>
     * <AUTHOR>
     * @date:2021年7月3日
     * @Throws
     */
    @Deprecated
    public List<HrmsEmployeeResp> getJurisdictionEmployeePageList(Page page, EmployeeListReq record) {

        List<HrmsEmployeeResp> list = new ArrayList<>();
        EmployeeListReq entity = new EmployeeListReq();

        BeanUtil.copyProperties(record, entity);

        if (!StringUtils.isBlank(record.getOrgId())) {
            List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(record.getOrgId());
            entity.setOrgIdList(orgIdNewList);
        }

        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        list = hrmsEmployeeMapper.getPageList(page, entity);

        // 增加本人
        HrmsEmployeeResp resp = new HrmsEmployeeResp();

        resp.setEmployeeNo(CommonContants.BEN_REN_EMP);
        resp.setEmployeeId(CommonContants.BEN_REN_EMP);
        resp.setEmployeeName("本人");

        List<HrmsEmployeeResp> returnList = new ArrayList<>();

        returnList.add(resp);

        returnList.addAll(list);

        return returnList;
    }

    /**
     * @description: 返回机构和人员树
     * @return: java.lang.String
     * @author: liyuan
     * @createTime: 2021/6/29 9:54
     */
    @Override
    public String getTreeOrgAndEmp() {
        // Example example = new Example(HrmsEmployee.class);
        // example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD,
        // Contants.IS_DELETED_FALSE);
        // example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
        // //加上条件 离职退休 ，
        // example.and().andNotEqualTo("employeeStatus",'3');
        // example.and().andNotEqualTo("employeeStatus",'4');
        // example.and().andNotEqualTo("employeeStatus",'7');
        // example.and().andNotEqualTo("employeeStatus",'8');
        // example.and().andIsNotNull("orgId");
        // example.and().andNotEqualTo("orgId", "");
        // List<TreeItem> treeItemList = new ArrayList<>();
        //
        // List<HrmsEmployee> empAllList =
        // hrmsEmployeeMapper.selectByExample(example); //获取所有人员
        EmployeeListReq employeeListReq = new EmployeeListReq();
        List<String> employeeStatusList = new ArrayList<>();
        employeeStatusList.add("3");
        employeeStatusList.add("4");
        employeeStatusList.add("7");
        employeeStatusList.add("8");
        employeeListReq.setNoEmployeeStatusList(employeeStatusList);
        List<HrmsEmployeeResp> hrmsEmployeeRespList = getEmployeeList(employeeListReq);

        List<HrmsOrganization> orgAllList = hrmsOrganizationService.getOrgAllList(); // 获取机构

        List<TreeItem> treeItemList = new ArrayList<>();
        orgAllList.forEach(item -> { // 机构信息
            TreeItem treeItem = new TreeItem(item.getOrganizationId(), item.getParentId(), item.getName());
            treeItem.setNocheck(true);
            treeItem.setOpen(true);
            treeItem.setType(1);
            treeItemList.add(treeItem);
        });

        hrmsEmployeeRespList.forEach(item -> { // 人员信息
            TreeItem treeItem = new TreeItem(item.getEmployeeId(), item.getOrgId(), item.getEmployeeName());
            treeItem.setType(2);
            treeItemList.add(treeItem);
        });

        TreeUtil treeUtil = new TreeUtil();

        TreeItem treeItem = treeUtil.enquireTree(treeItemList);

        return JSON.toJSONString(treeItem, SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullBooleanAsFalse, SerializerFeature.WriteNullListAsEmpty);
    }

    /**
     * @Title: UniqueCheck
     * @Description: 新增修改时姓名, 工号唯一校验
     * @Params: @param record
     * @Params: @return
     * @Return: boolean
     * <AUTHOR>
     * @date:2021年7月6日
     * @Throws
     */
    @Deprecated
    public String uniqueCheck(CustomEmpModel record) {

        String message = "";

        String employeeId = record.getEmployeeId();

        List<CustomEmployeeFieldModel> records = record.getCustomFileds();

        List<CommEmployeeField> allEmployeeFields = new ArrayList<CommEmployeeField>();
        for (CustomEmployeeFieldModel customEmployee : records) {
            // 个人信息
            List<CommEmployeeField> fields = customEmployee.getFields();

            if (CollectionUtils.isNotEmpty(fields)) {

                allEmployeeFields.addAll(fields);
            }
        }

        if (CollectionUtils.isNotEmpty(allEmployeeFields)) {
            String employeeNo = "";

            String empPayroll = "";

            String identityNumber = "";

            for (CommEmployeeField field : allEmployeeFields) {

                if (field.getFieldName().equals("employee_no")) {

                    employeeNo = field.getValue();
                }


                if (field.getFieldName().equals("emp_payroll")) {

                    empPayroll = field.getValue();
                }

                if (field.getFieldName().equals("identity_number")) {

                    identityNumber = field.getValue();
                }
            }
            if (StringUtils.isNotBlank(employeeNo)) {

                Example example = new Example(HrmsEmployee.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("employeeNo", employeeNo);
                if (StringUtils.isNotBlank(employeeId)) {
                    example.and().andNotEqualTo("employeeId", employeeId);
                }
                List<HrmsEmployee> employeeList = hrmsEmployeeMapper.selectByExample(example);

                if (CollectionUtils.isNotEmpty(employeeList)) {

                    message = "工号:" + employeeNo + "已存在,请勿重复添加";
                }
            }

            if (StringUtils.isNotBlank(empPayroll)) {

                Example example = new Example(HrmsEmployee.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("empPayroll", empPayroll);
                if (StringUtils.isNotBlank(employeeId)) {
                    example.and().andNotEqualTo("employeeId", employeeId);
                }
                List<HrmsEmployee> employeeList = hrmsEmployeeMapper.selectByExample(example);

                if (CollectionUtils.isNotEmpty(employeeList)) {

                    message = "发薪号:" + empPayroll + "已存在,请勿重复添加";
                }
            }

            if (StringUtils.isNotBlank(identityNumber)) {

                Example example = new Example(HrmsEmployee.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("identityNumber", identityNumber);
                if (StringUtils.isNotBlank(employeeId)) {
                    example.and().andNotEqualTo("employeeId", employeeId);
                }
                List<HrmsEmployee> employeeList = hrmsEmployeeMapper.selectByExample(example);

                if (CollectionUtils.isNotEmpty(employeeList)) {

                    message = "身份证:" + identityNumber + "已存在,请勿重复添加";
                }
            }
        }

        return message;
    }

    /**
     * @Title: findByEmployeeCode
     * @Description: 根据员工工号查询员工信息
     * @Params: @param userCode
     * @Params: @return
     * @Return: HrmsEmployee
     * <AUTHOR>
     * @date:2021年9月7日
     * @Throws
     */
    public HrmsEmployee findByEmployeeCode(String employeeNo) {

        HrmsEmployeeResp hrmsEmployeeResp = hrmsEmployeeMapper.findByEmployeeNo(employeeNo, null);
        
        HrmsEmployee hrmsEmployee = new HrmsEmployee();
        
        BeanUtil.copyProperties(hrmsEmployeeResp, hrmsEmployee);
        
        return hrmsEmployee;
    }

    /**
     * @Title: updateEmployeeInfo
     * @Description: 根据Id修改员工信息
     * @Params: @param record
     * @Params: @return
     * @Return: Integer
     * <AUTHOR>
     * @date:2021年9月7日
     * @Throws
     */
    @Deprecated
    public Integer updateEmployeeInfoById(HrmsEmployee record) {
        return hrmsEmployeeMapper.updateByPrimaryKey(record);
    }

    /**
     * @Title: getEmployeeListByEqualPay
     * @Description: 同工同酬选择人员列表
     * @Params: @param page
     * @Params: @param record
     * @Params: @return
     * @Return: List<HrmsEmployeeResp>
     * <AUTHOR>
     * @date:2021年9月14日
     * @Throws
     */
    public List<HrmsEmployeeResp> getEmployeeListByEqualPay(Page page, HrmsEmployee record) {

        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsEmployeeMapper.getEmployeeListByEqualPay(page, record);
    }


    @Override
    /**
     * 导入员工
     *
     * @param employeeImportList
     * @return void
     * <AUTHOR>
     * @date 2021/11/9 9:27
     */
    @Transactional(readOnly = false)
    @Deprecated
    public void importEmployee(List<EmployeeImport> employeeImportList) {
        log.info("导入的数据employeeImportList:" + employeeImportList);
        for (EmployeeImport employeeImport : employeeImportList) {
            HrmsEmployeeSaveReq hrmsEmployeeSaveReq;
            hrmsEmployeeSaveReq = BeanUtil.copyProperties(employeeImport, HrmsEmployeeSaveReq.class);

            OrganizationReq organizationReq = new OrganizationReq();
            organizationReq.setEqName(employeeImport.getOrgName());
            Organization organization = organizationService.getBase(organizationReq);
            if (organization != null) {
                hrmsEmployeeSaveReq.setOrgId(organization.getOrganizationId());
            }
            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getGender()) == false) {
                hrmsEmployeeSaveReq.setGender(hrmsEmployeeSaveReq.getGender().equals("男") ? "0" : "1");
            }
            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getEstablishmentType()) == false) {
                hrmsEmployeeSaveReq.setEstablishmentType(hrmsEmployeeSaveReq.getEstablishmentType().equals("是") ? "1" : "2");
            }
            if (StringUtils.isBlank(hrmsEmployeeSaveReq.getPoliticalStatus()) == false) {
                hrmsEmployeeSaveReq.setPoliticalStatus(hrmsEmployeeSaveReq.getPoliticalStatus().equals("是") ? "1" : "4");
            }
            Example example = new Example(HrmsEmployee.class);
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            example.and().andEqualTo("employeeNo", hrmsEmployeeSaveReq.getEmployeeNo());
            example.setOrderByClause("  employee_id LIMIT 1");
            HrmsEmployee emp = hrmsEmployeeMapper.selectOneByExample(example);
            if (emp != null) {
                hrmsEmployeeSaveReq.setEmployeeId(emp.getEmployeeId());
                updateEmployee(hrmsEmployeeSaveReq);
            } else {

                addEmployee(hrmsEmployeeSaveReq);
            }
        }
    }


    @Override
    /**
     * @description: 获取员工选择器列表
     * @param: record
     * @return: java.util.List<cn.trasen.basicsbottom.bean.HrmsEmployeeResp>
     * @author: liyuan
     * @createTime: 2021/6/23 14:41
     */
    public List<EmployeeListOutBO> pageSelect(Page page, EmployeeListInBO employeeListInBO) {
        if (!StringUtils.isBlank(employeeListInBO.getOrgId())) {
            List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(employeeListInBO.getOrgId());
            employeeListInBO.setOrgIdList(orgIdNewList);
        }
        if (employeeListInBO.getNoEmployeeStatusList() == null && employeeListInBO.getEmployeeStatusList() == null) {
            List<String> employeeStatusList = new ArrayList<>();
            employeeStatusList.add("4");
            employeeStatusList.add("7");
            employeeStatusList.add("8");
            employeeListInBO.setNoEmployeeStatusList(employeeStatusList);
        }
        List<EmployeeListOutBO> employeeListOutBOList = hrmsEmployeeMapper.list(page, employeeListInBO);

        employeeListOutBOList.forEach(emp -> {
            emp.setGenderText(GenderTypeEnum.getValByKey(emp.getGender()));
            emp.setEnableText(EnableEnum.getValByKey(emp.getIsEnable()));
        });
        return employeeListOutBOList;
    }


    @Override
    @Deprecated
    public List<HrmsEmployeeResp> getEmployeeByCodes(List<String> codes) {
        // TODO Auto-generated method stub
        return hrmsEmployeeMapper.getEmployeeByCodes(codes);
    }

    
    //修改员工工号
    @Override
    @Transactional(readOnly = false)
    @Deprecated
    public void updateJobNumber(ResetJobNumberReq resetJobNumberReq) {
        //查询之前的工号
        Example example = new Example(HrmsEmployee.class);
        example.createCriteria().andEqualTo("employeeId", resetJobNumberReq.getEmployeeId());
        HrmsEmployee hrmsEmployee = hrmsEmployeeMapper.selectOneByExample(example);

        //判断工号是否存在
        HrmsEmployee findByEmployeeCode = findByEmployeeCode(resetJobNumberReq.getEmployeeNo());
        if(null != findByEmployeeCode){
            log.error(resetJobNumberReq.getEmployeeId() +"修改工号错误： 工号已存在" );
            throw new RuntimeException("工号已存在");
        }
        
        //修改员工工号
        try {
            GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
            if("csjkyy".equals(globalSetting.getOrgCode())){
                resetJobNumberReq.setEmpPayroll(resetJobNumberReq.getEmployeeNo());
            }
            hrmsEmployeeMapper.updateHrmsEmpNoByid(resetJobNumberReq);  //修改hrms_employee
            hrmsEmployeeMapper.updateToaEmpNoById(resetJobNumberReq);  //修改toa_employee
            hrmsEmployeeMapper.updateThpsEmpNoByid(resetJobNumberReq); //修改ts_thps.thps_user
            if (Objects.nonNull(hrmsEmployee) && StringUtils.isNotEmpty(hrmsEmployee.getEmployeeNo())){
                //修改转正管理employeeNo
                hrmsEmployeeMapper.updateHrmsEmpBecome(resetJobNumberReq.getEmployeeNo(),hrmsEmployee.getEmployeeNo());
            }
        } catch (Exception e) {
            log.error(resetJobNumberReq.getEmployeeId() +"修改工号错误：" + e.getMessage(),e );
            throw new RuntimeException("修改工号出错");
        }
    }


    @Override
    public List<HrmsEmployeeResp> selectListByDeptCode(List<String> organizationIdList) {
        return hrmsEmployeeMapper.selectListByDeptCode(organizationIdList);
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzXuexi(String employeeId) {
        return hrmsEmployeeMapper.getLyzXuexi(employeeId, UserInfoHolder.getCurrentUserCorpCode());
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzYuanwai(String employeeId) {
        return hrmsEmployeeMapper.getLyzYuanwai(employeeId);
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzYuannei(String employeeId) {
        return hrmsEmployeeMapper.getLyzYuannei(employeeId);
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzZhicheng(String employeeId) {
        return hrmsEmployeeMapper.getLyzZhicheng(employeeId);
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzJinxiu(String employeeId) {
        return hrmsEmployeeMapper.getLyzJinxiu(employeeId);
    }


    @Override
    @Deprecated
    public List<EmployeeDetails> getLyzGuipei(String employeeId) {
        return hrmsEmployeeMapper.getLyzGuipei(employeeId);
    }


    @Override
    @Transactional(readOnly = false)
    public void update(HrmsEmployee hrmsEmployee) {
    	
    	CustomEmployeeBase customEmployeeBase = new CustomEmployeeBase();
    	
    	BeanUtil.copyProperties(hrmsEmployee, customEmployeeBase);
    	
    	customEmployeeBaseService.updateEmployee(customEmployeeBase);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getKhjg(String employeeId) {
        return hrmsEmployeeMapper.getKhjg(employeeId);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getKylw(String employeeId) {
        return hrmsEmployeeMapper.getKylw(employeeId);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getJcjl(String employeeId) {
        return hrmsEmployeeMapper.getJcjl(employeeId);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getJxjl(String employeeId) {
        return hrmsEmployeeMapper.getJxjl(employeeId);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getZyysgfhpx(String employeeId) {
        return hrmsEmployeeMapper.getZyysgfhpx(employeeId);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getGpjl(String employeeId) {
        return hrmsEmployeeMapper.getGpjl(employeeId);
    }

	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public void updateDisable(String employeeId, String disableStatus) {
		hrmsEmployeeMapper.updateDisable(employeeId,disableStatus);
	}

	@Override
	public List<Map<String, String>> getEmpAll() {
		return hrmsEmployeeMapper.getEmpAll();
	}


	@Override
	public List<EmployeeListReq> getEmployeeListReqList(RequestContent requestContent) {
		return hrmsEmployeeMapper.getEmployeeListReqList(requestContent);
	}
	
	
}