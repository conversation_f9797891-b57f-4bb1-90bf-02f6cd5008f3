package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 联系人分类
 * @Date: 2020/1/14 14:56
 * @Param:
 * @return:
 **/
@Table(name = "COMM_LINKMANCLASS")
@Setter
@Getter
public class Linkmanclass {
    /**
     * 表ID
     */
    @Id
    @Column(name = "CLASS_ID")
    @ApiModelProperty(value = "表ID")
    private String classId;

    /**
     * 用户标识(usercode)
     */
    @Column(name = "EMP_ID")
    @ApiModelProperty(value = "用户标识(usercode)")
    private String empId;

    /**
     * 分类类型(0:个人联系人 1:公共联系人)
     */
    @Column(name = "CLASS_TYPE")
    @ApiModelProperty(value = "分类类型(0:个人联系人 1:公共联系人)")
    private Short classType;

    /**
     * 分类名称
     */
    @Column(name = "CLASS_NAME")
    @ApiModelProperty(value = "分类名称")
    private String className;

    /**
     * 分类说明
     */
    @Column(name = "CLASS_DESCRIBE")
    @ApiModelProperty(value = "分类说明")
    private String classDescribe;

    /**
     * 域标识
     */
    @Column(name = "DOMAIN_ID")
    @ApiModelProperty(value = "域标识")
    private String domainId;

    /**
     * 分类父id
     */
    @Column(name = "CLASS_PARENT_ID")
    @ApiModelProperty(value = "分类父id")
    private String classParentId;

    /**
     * 分类等级
     */
    @Column(name = "CLASS_LEVEL")
    @ApiModelProperty(value = "分类等级")
    private String classLevel;

    /**
     * 分类菜单
     */
    @Column(name = "CLASS_ORDER_CODE")
    @ApiModelProperty(value = "分类菜单")
    private String classOrderCode;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}