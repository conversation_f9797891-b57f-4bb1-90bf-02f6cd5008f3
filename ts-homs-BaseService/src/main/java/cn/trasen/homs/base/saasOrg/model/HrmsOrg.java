package cn.trasen.homs.base.saasOrg.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 机构表
 * <AUTHOR>
 * @update 2025-07-12
 *
 */
@Table(name = "comm_org")
@Setter
@Getter
@Accessors(chain = true)
public class HrmsOrg {

	@Id
    @Column(name = "org_id")
    @ApiModelProperty("唯一标识ID")
    private String orgId;

	@Column(name = "org_code")
    @ApiModelProperty(value = "机构编码", required = true)
    private String orgCode;

    @Column(name = "org_name")
    @ApiModelProperty(value = "机构名称", required = true)
    private String orgName;
    
    @Column(name = "parent_code")
    @ApiModelProperty("上级机构编码")
    private String parentCode;
    
    /**
     * 这里不管理院区
     */
    @Column(name = "mar_catg_code")
    @ApiModelProperty("管理类别编码:	WJW-卫计委,YLT-医联体,INTERNET_HOSP-互联网医院,YLJG-医疗机构,AREA-院区,TJ_YQ-体检的院区标识")
    private String marCatgCode;
    
    @Column(name = "mar_catg_name")
    @ApiModelProperty("管理类别名称")
    private String marCatgName;

    @Column(name = "status")
    @ApiModelProperty(value = "状态（1:启用;0:禁用;)", required = true)
    private Integer status;

    @Column(name = "sort")
    @ApiModelProperty("排序")
    private Integer sort;

    @Column(name = "remark")
    @ApiModelProperty("备注")
    private String remark;

    @Column(name = "create_date")
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty("创建人名称")
    private String createUserName;

    @Column(name = "update_date")
    @ApiModelProperty("更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    @ApiModelProperty("更新人员")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty("更新人员名称")
    private String updateUserName;

    @Column(name = "is_deleted")
    @ApiModelProperty("删除标识")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty("查询关键字")
    private String keywords;
    
    @Transient
    @ApiModelProperty("父类ID")
    private String parentId;
    
    @Transient
    @ApiModelProperty("同步标识：Y-是，N-否，默认否，用于是否更新sso的脚本")
    private String sync;
}
