package cn.trasen.homs.base.groupLeader.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader;
import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeaderBase;
import cn.trasen.homs.base.groupLeader.service.HrmsRoleGroupLeaderService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsRoleGroupLeaderController
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsRoleGroupLeaderController")
public class HrmsRoleGroupLeaderController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsRoleGroupLeaderController.class);

	@Autowired
	private HrmsRoleGroupLeaderService hrmsRoleGroupLeaderService;

	/**
	 * @Title saveHrmsRoleGroupLeader
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/rolegroupleader/save")
	public PlatformResult<String> saveHrmsRoleGroupLeader(@RequestBody HrmsRoleGroupLeader record) {
		try {
			hrmsRoleGroupLeaderService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsRoleGroupLeader
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/rolegroupleader/update")
	public PlatformResult<String> updateHrmsRoleGroupLeader(@RequestBody HrmsRoleGroupLeader record) {
		try {
			hrmsRoleGroupLeaderService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsRoleGroupLeaderById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsRoleGroupLeader>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/rolegroupleader/{id}")
	public PlatformResult<HrmsRoleGroupLeader> selectHrmsRoleGroupLeaderById(@PathVariable String id) {
		try {
			HrmsRoleGroupLeader record = hrmsRoleGroupLeaderService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsRoleGroupLeaderById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/rolegroupleader/delete/{id}")
	public PlatformResult<String> deleteHrmsRoleGroupLeaderById(@PathVariable String id) {
		try {
			hrmsRoleGroupLeaderService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsRoleGroupLeaderList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsRoleGroupLeader>
	 * @date 2023��12��4�� ����10:36:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/rolegroupleader/list")
	public DataSet<HrmsRoleGroupLeader> selectHrmsRoleGroupLeaderList(Page page, HrmsRoleGroupLeader record) {
		return hrmsRoleGroupLeaderService.getDataSetList(page, record);
	}

	//获取后台所有权限组成员
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/rolegroupleader/rolegroupleaderAll")
	public DataSet<HrmsRoleGroupLeaderBase> rolegroupleaderAll() {
		List<HrmsRoleGroupLeaderBase> records = hrmsRoleGroupLeaderService.getRolegroupleaderAllList();
		return new DataSet<>(1, 1, 1, records.size(), records);
	}

}
