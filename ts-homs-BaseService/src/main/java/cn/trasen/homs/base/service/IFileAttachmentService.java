package cn.trasen.homs.base.service;

import java.beans.Transient;
import java.io.IOException;
import java.util.List;

import cn.trasen.homs.base.bean.FileAttachmentBusinessReq;
import cn.trasen.homs.base.bean.FileAttachmentByBusinessIdListRes;
import cn.trasen.homs.base.bean.FileAttachmentResp;
import cn.trasen.homs.base.bo.PreviewImageInBO;
import cn.trasen.homs.base.bo.PreviewImageOutBO;
import cn.trasen.homs.base.dto.FileAttachmentSaveInDTO;
import cn.trasen.homs.base.model.FileAttachment;

public interface IFileAttachmentService {



    /**
        * 保存附件
        * @param fileAttachmentSaveInDTO
        * @return void
        * <AUTHOR>
        * @date 2021/12/31 13:41
        */
    FileAttachmentResp upload(FileAttachmentSaveInDTO fileAttachmentSaveInDTO) throws IOException;

    /**
     * @description: 保存附近
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    void save(FileAttachment fileAttachment);

    /**
     * @description: 查询
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    FileAttachment getById(String id,String isdel);

    /**
     * @description: 查询
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    List<FileAttachment> getByIds(List<String> ids);

    /**
    * @description: 关联附件
* @param: fileAttachmentBusinessReq
    * @return: void
    * @author: liyuan
    * @createTime: 2021/6/23 16:27
    */
    void saveBusinessId(FileAttachmentBusinessReq fileAttachmentBusinessReq);

    /**
    * @description: 删除附件
* @param: fileid
    * @return: void
    * @author: liyuan
    * @createTime: 2021/6/23 16:29
    */
    @Transient
    void deleteFileId(String fileid);

    /**
     * @description: 获取附件
     * @param: fileid
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/23 16:29
     */
    @Transient
    List<FileAttachmentResp> getFileAttachmentByBusinessId(String businessId);

    @Transient
    List<FileAttachmentByBusinessIdListRes> listFileAttachmentByBusinessIdList(List<String> businessId);

    void saveAttachmentList(List<FileAttachmentResp> fileAttachment);
	
	/**
     * 
    * @Title: getEmployeeFileAttachmentByBusinessId  
    * @Description: 获取人员档案附件,多个附件
    * @Params: @param businessId
    * @Params: @return      
    * @Return: List<FileAttachmentResp>
    * <AUTHOR>
    * @date:2021年9月6日
    * @Throws
     */
    List<FileAttachmentResp> getFileAttachmentByBusinessIdOrId(String businessId);

    /**
     * 复制businessId相关的文件
     *
     * @param businessId
     * @return void
     * <AUTHOR>
     * @date 2022/1/18 14:34
     */
    List<FileAttachmentResp> copyBusinessIdFiles(String businessId);

    /**
    * 预览图片
    * @param previewImageInBO
    * @return byte[]
    * <AUTHOR>
    * @date 2022/1/21 10:41
    */
    PreviewImageOutBO previewImage(PreviewImageInBO previewImageInBO) throws IOException;
}
