/**
 * @Title: CustomEmpModel.java  
 * @Package: cn.trasen.homs.baseservice.model  
 * @Date: 2021年6月24日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
* @ClassName: CustomEmpModel  
 * @Author: 86189
 * @Date: 2021年6月24日
 */
@Setter
@Getter
public class CustomEmpModel {
	

	@ApiModelProperty(value = "员工id")
	private String employeeId;
	
	@ApiModelProperty(value = "保存字段接收list")
	private List<CustomEmployeeFieldModel> customFileds;
}
