package cn.trasen.homs.base.service;


import cn.hutool.core.date.DateTime;
import cn.trasen.homs.core.bean.GlobalSetting;

public interface GlobalSettingsService {


    /**
     *
     * @return GlobalSetting
     */
    GlobalSetting getGlobalSetting(String isAll);

    /**
     *
     * @Title: getGlobalSettingById
     * @Description: 通过id查询全局配置
     * @param: @param globalSetting
     * @param: @param globalSetting
     * @param: @return
     * @return: GlobalSetting
     * @author: wangchao
     * @date:   2021年6月9日 下午4:57:51
     * @throws
     */
    GlobalSetting getGlobalSettingById(GlobalSetting globalSetting);

    /**
     *
     * @Title: insert
     * @Description: 新增
     * @param: @param globalSetting
     * @return: void
     * @author: wangchao
     * @date:   2021年6月9日 下午5:23:53
     * @throws
     */
    void insert(GlobalSetting globalSetting);

    /**
     *
     * @Title: update
     * @Description: 修改
     * @param: @param globalSetting
     * @return: void
     * @author: wangchao
     * @date:   2021年6月9日 下午5:24:00
     * @throws
     */
    void update(GlobalSetting globalSetting);

    /**
     *
     * @Title: deleted
     * @Description: 删除
     * @param: @param id
     * @return: void
     * @author: wangchao
     * @date:   2021年6月9日 下午6:21:43
     * @throws
     */
    void deleted(String id);

    /**
     * 
     * @MethodName: getSafeGlobalSetting
     * @Description: TODO
     * <AUTHOR>
     * @return GlobalSetting
     * @date 2023-03-08 06:09:36
     */
	GlobalSetting getSafeGlobalSetting();

	void updatePasswordExpireDate(String employeeId);

}
