/**
 * @Title: CommEmployeeFieldGroupService.java  
 * @Package: cn.trasen.homs.base.service  
 * @Date: 2021��6��15��
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.model.HrmsEmployee;

import java.util.List;
import java.util.Map;

/**
* @ClassName: CommEmployeeFieldGroupService  
 * @Author: 86189
 * @Date: 2021��6��15��
 */
public interface CommEmployeeFieldGroupService {
	
	 /**
     * 
    * @Title: insert
    * @Description: 新增
    * @param @param record
    * @param @return    ����
    * @return int    ��������
    * @throws
    * <AUTHOR>
    * @date 2019��7��23�� ����5:33:54
     */
	int insert(CommEmployeeFieldGroup record);
    
    /**
     * 
    * @Title: update
    * @Description: 修改自定义人员档案分组
    * @param @param record
    * @param @return    ����
    * @return int    ��������
    * @throws
    * <AUTHOR>
    * @date 2019��7��23�� ����5:34:01
     */
    int update(CommEmployeeFieldGroup record);
    
    /**
     * 
    * @Title: checkName  
    * @Description: 新增时校验分组名是否已存在
    * @Params: @param record
    * @Params: @return      
    * @Return: boolean
    * <AUTHOR>
    * @date:2021年7月15日
    * @Throws
     */
    public boolean checkName(CommEmployeeFieldGroup record);

    
    /**
     * 
    * @Title: deleted
    * @Description:删除自定义人员档案分组
    * @param @param id
    * @param @return    ����
    * @return int    ��������
    * @throws
    * <AUTHOR>
    * @date 2019��7��23�� ����5:34:08
     */
    int deleted(String id);
    
    
    /**
     * 
    * @Title: getList
    * @Description: 获取自定义人员档案分组列表
    * @param @param record
    * @param @param page
    * @param @return    ����
    * @return List<CommEmployeeFieldGroup>
    * @throws
    * <AUTHOR>
    * @date 2019��7��23�� ����5:34:26
     */
    List<CommEmployeeFieldGroup> getFieldAndJurisdictionListByGroupid();
    
    
	/**
	 * 
	* @Title: findById  
	* @Description: 根据id查询人员档案分组
	* @Params: @param id
	* @Params: @return      
	* @Return: CommEmployeeFieldGroup
	* <AUTHOR>
	* @date:2021年6月15日
	* @Throws
	 */
    CommEmployeeFieldGroup findById(String id);
    
    /**
     * 
    * @Title: updateList  
    * @Description: 根据列表修改自定义人员档案分组
    * @Params: @param records
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月15日
    * @Throws
     */
    String updateList(List<CommEmployeeFieldGroup> records);
    
    /**
     * 
    * @Title: deleteById  
    * @Description: 根据id删除
    * @Params: @param id
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月17日
    * @Throws
     */
    int deleteById(String id);
    
    
    /**
     * 
    * @Title: getList  
    * @Description: 获取所有分组
    * @Params: @return      
    * @Return: List<CommEmployeeFieldGroup>
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    List<CommEmployeeFieldGroup> getDataList();
    
    List<Map<String,String>>  getGroupListTrends (HrmsEmployee record);

    //查询分组的表名
    List<CommEmployeeFieldGroup> findByIdList(List<String> ids);

    List<String> getUnfinishResult(String employeeId,String wfInstanceId,List<String> groupIds);

    List<CustomEmployeeGroup> getAllIsMustFeild(List<String> groupIds);

    List<CommEmployeeFieldGroup> getGroupDataList();
}
