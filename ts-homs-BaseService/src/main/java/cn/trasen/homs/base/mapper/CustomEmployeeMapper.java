/**
 * @Title: CustomEmployeeMapper.java  
 * @Package: cn.trasen.homs.base.mapper  
 * @Date: 2021年6月19日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.mapper;

import cn.trasen.homs.base.bean.CustomEmployeeResp;
import cn.trasen.homs.base.bean.EmployeeStorageData;
import cn.trasen.homs.base.bean.HistoricalListResp;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateDetail;
import cn.trasen.homs.base.model.*;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* @ClassName: CustomEmployeeMapper  
 * @Author: 86189n
 * @Date: 2021年6月19日
 */
@Component
public interface CustomEmployeeMapper extends Mapper<CustomEmployeeFieldModel> {
	
	/**
	 * 
	* @Title: getEmployeePageListByCustom  
	* @Description:  分页获取自定义人员档案列表
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<Map<String,String>>
	* <AUTHOR>
	* @date:2021年6月19日
	* @Throws
	 */
	List<Map<String,String>> getEmployeePageListByCustom(Page page,HrmsEmployee record);
	
	/**
	 * 
	* @Title: findById  
	* @Description: 根据id查询自定义人员档案
	* @Params: @param id
	* @Params: @return      
	* @Return: Map<String,String>
	* <AUTHOR>
	* @date:2021年6月20日
	* @Throws
	 */
	List<Map<String,String>> findByEmployeeInfoId(CustomEmployeeFieldModel record);
	
	/**
	 * 
	* @Title: getGroupJurisdictionListById  
	* @Description: 获取修改需要走流程的分组
	* @Params: @param record
	* @Params: @return      
	* @Return: List<CommEmployeeFieldGroup>
	* <AUTHOR>
	* @date:2021年6月26日
	* @Throws
	 */
	List<CommEmployeeFieldGroup> getGroupJurisdictionListById(CustomEmployeeFieldModel record);
	
	/**
	 * 
	* @Title: getEmployeeFieldJurisdictionList  
	* @Description: 获取编辑需要走流程个人信息的字段 
	* @Params: @param record
	* @Params: @return      
	* @Return: List<CommEmployeeField>
	* <AUTHOR>
	* @date:2021年6月26日
	* @Throws
	 */
	List<CommEmployeeField> getEmployeeFieldJurisdictionList(CustomEmployeeFieldModel record);
	
	/**
	 * 
	* @Title: findEmployeeDetailInfoByEmployeeId  
	* @Description: 根据员工id查询员工明细信息
	* @Params: @param record
	* @Params: @return      
	* @Return: List<Map<String,String>>
	* <AUTHOR>
	* @date:2021年6月28日
	* @Throws
	 */
	List<Map<String,String>> findEmployeeDetailInfoByEmployeeId(CustomEmployeeFieldModel record);
	
	/**
	 * 
	* @Title: getDataWorkflowByNoDoList  
	* @Description: 查询修改待办员工信息
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<HrmsEmployee>
	* <AUTHOR>
	* @date:2021年6月30日
	* @Throws
	 */
	List<CustomEmployeeResp> getDataWorkflowByNoDoList(Page page,CustomEmployeeResp record);
	
	/**
	 * 
	* @Title: getDataWorkflowIDoList  
	* @Description: 查询已办理的员工档案修改信息
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<HrmsEmployee>
	* <AUTHOR>
	* @date:2021年6月30日
	* @Throws
	 */
	List<CustomEmployeeResp> getDataWorkflowIDoList(Page page,CustomEmployeeResp record);
	
	/**
	 * 
	* @Title: getDataWorkflowIApplyList  
	* @Description: 我已完成的修改档案信息
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<HrmsEmployee>
	* <AUTHOR>
	* @date:2021年6月30日
	* @Throws
	 */
	List<CustomEmployeeResp> getDataWorkflowIApplyList(Page page,CustomEmployeeResp record);
	
	/**
	 * 
	* @Title: getEmployeeMessage  
	* @Description: 获取员工信息
	* @Params: @param record
	* @Params: @return      
	* @Return: List<HrmsEmployee>
	* <AUTHOR>
	* @date:2021年7月22日
	* @Throws
	 */
	List<HrmsEmployee> getEmployeeMessage(HrmsEmployee record);

	/**
	 * 经开离职原因
	 * @return
	 */
	List<Map<String, String>> getJklzyy();
	//退休时间
	List<Map<String, String>> getJkTx();

	/**
	 * 技术档案
	 * @param employeeId
	 * @return
	 */
    List<Map<String, String>> getEmployeeJsda(String employeeId);

	String getPersonalIdentityValBayEmployeeId(@Param("employeeId") String employeeId, @Param("ssoOrgCode") String ssoOrgCode);

	Map<String, String> getEmployeeNoByemployeeId(String employeeId);

    void deltedStorage(@Param("employeeId") String employeeId);

	void storage(@Param("content") String content, @Param("employeeId") String employeeId,@Param("id") String id);

	EmployeeStorageData  getStorage(String employeeId);

	Map<String, String> getEmpFlowStatus(String employeeId);

	Map<String, String> getEmployeeTask(String employeeNo);

    List<CustomEmployeeUpdateDetail> getAuditInfo(@Param("wfInstanceId") String wfInstanceId,@Param("employeeId") String employeeId);

    List<Map<String, String>> getEmployeePageListGroup(Page page, HrmsEmployee record);

    List<HistoricalListResp> getHistoricalRecords(@Param("employeeId") String employeeId);

	List<HistoricalListResp> getUnFlowHistoricalRecords(@Param("employeeId") String employeeId);

	List<CommEmployeeFieldGroup> getCompleteProgress();

	List<Map<String, String>> getEmployeePageListReport(Page page, HrmsEmployee record);

    Integer getUnFinishList(@Param("employeeId") String employeeId,@Param("groupId") String groupId,@Param("wfInstanceId") String wfInstanceId);

	List<Map<String, String>> getEmployeeCloum(@Param("tableName") String tableName,@Param("fieldList") List<CustomEmployeeGroup> fieldList,@Param("employeeId") String employeeId);
}
