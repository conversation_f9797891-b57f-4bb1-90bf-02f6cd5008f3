package cn.trasen.homs.base.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.base.bean.PostCategorySaveReq;
import cn.trasen.homs.base.service.IPostCategoryService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/** 
* @description:
* @return: 
* @author: liyuan
* @createTime: 2021/8/6 10:49
*/
@Api(tags = "岗位类别")
@RequestMapping("/postCategory")
@RestController
public class PostCategoryController {

	@Autowired
	private IPostCategoryService postCategoryService;


	@ApiOperation(value = "获取类别列表", notes = "获取类别列表")
	@RequestMapping(value = "/getList", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult getList() {
		return PlatformResult.success(postCategoryService.getList());
	}

	@ApiOperation(value = "新增岗位类别", notes = "新增岗位类别")
	@PostMapping(value = "/add")
	public PlatformResult add(@RequestBody @Validated  PostCategorySaveReq postCategorySaveReq) {
		postCategoryService.add(postCategorySaveReq);
		return PlatformResult.success();
	}


	@ApiOperation(value = "修改岗位类别", notes = "修改岗位类别")
	@PostMapping(value = "/update")
	public PlatformResult update(@RequestBody @Validated  PostCategorySaveReq postCategorySaveReq) {
		postCategoryService.update(postCategorySaveReq);
		return PlatformResult.success();
	}

	@ApiOperation(value = "删除岗位类别", notes = "删除岗位类别")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "postCategoryId", value = "postCategoryId", required = true, dataType = "String")
	})
	@RequestMapping(value = "/del", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult del(@RequestParam("dictTypeId") String dictTypeId,@RequestParam("postCategoryId") String postCategoryId) {
		try{
			postCategoryService.delete(dictTypeId, postCategoryId);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "验证删除", notes = "验证删除")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "postCategoryId", value = "postCategoryId", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyDel", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyDel(@RequestParam("postCategoryId") String postCategoryId) {
		try{
			postCategoryService.verify(postCategoryId);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "验证禁用", notes = "验证禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "postCategoryId", value = "postCategoryId", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyEnable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyEnable(@RequestParam("postCategoryId") String postCategoryId) {
		try{
			postCategoryService.verify(postCategoryId);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "启用禁用", notes = "启用禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "postCategoryId", value = "postCategoryId", required = true, dataType = "String")
			, @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
	})
	@RequestMapping(value = "/enable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult enable(@RequestParam("dictTypeId") String dictTypeId, @RequestParam("postCategoryId") String postCategoryId, @RequestParam("enable") String enable) {
		try{
			postCategoryService.enable(dictTypeId, postCategoryId, enable);
			return PlatformResult.success();
		}catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}
}