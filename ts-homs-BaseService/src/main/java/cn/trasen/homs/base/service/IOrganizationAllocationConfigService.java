package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrganizationAllocationConfigSaveReq;
import cn.trasen.homs.base.bean.OrganizationAllocationConfigSaveResp;

/**
 * <AUTHOR>
 * @createTime 2021/8/5 9:48
 * @description
 */
public interface IOrganizationAllocationConfigService {
    /**
    * @description: 保存床位胚子
* @param: organizationAllocationConfigSaveReqList
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/5 9:58
    */
    @Transactional(rollbackFor = Exception.class)
    void saveConfig(List<OrganizationAllocationConfigSaveReq> organizationAllocationConfigSaveReqList);

    /**
    * @description: 获取配置列表
    * @return: java.util.List<cn.trasen.homs.base.bean.OrganizationAllocationConfigSaveResp>
    * @author: liyuan
    * @createTime: 2021/8/5 10:05
    */
    List<OrganizationAllocationConfigSaveResp> getList();
}
