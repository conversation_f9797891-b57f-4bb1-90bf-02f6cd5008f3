package cn.trasen.homs.base.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import cn.hutool.core.lang.Assert;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.service.HrmsOrganizationService;
import cn.trasen.homs.core.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;

/**   
 * @Title: OrgCacheManager.java 
 * @Package cn.trasen.hrms.cache 
 * @Description: 组织机构缓存
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月13日 下午2:34:02 
 * @version V1.0   
 */
@Slf4j
@Component
public class OrgCacheManager {

	private static final Map<String, HrmsOrganization> ORG_CACHE_MANAGER = new ConcurrentHashMap<String, HrmsOrganization>();

	private static Object lock = new Object();

	/**
	 * @Title: get
	 * @Description: 根据机构ID获取信息
	 * @param orgId
	 * @return
	 * @date 2018年6月1日 上午10:52:22
	 * <AUTHOR>
	 */
	public static HrmsOrganization get(String orgId) {
		Assert.notNull(orgId, "orgId must not be null.");
		HrmsOrganization org = ORG_CACHE_MANAGER.get(orgId);
		if (org == null) {
			synchronized (lock) {
				org = ORG_CACHE_MANAGER.get(orgId);
				if (org == null) {
					HrmsOrganizationService service = SpringContextUtil.getBean(HrmsOrganizationService.class);
					org = service.findById(orgId);
					if (org != null) {
						ORG_CACHE_MANAGER.put(org.getOrganizationId(), org);
					}
				}
			}
		}
		return org;
	}

	public static void set(HrmsOrganization record) {
		ORG_CACHE_MANAGER.put(record.getOrganizationId(), record);
	}

	public static void remove(String orgId) {
		Assert.notNull(orgId, "orgId must not be null.");
		ORG_CACHE_MANAGER.remove(orgId);
	}

}
