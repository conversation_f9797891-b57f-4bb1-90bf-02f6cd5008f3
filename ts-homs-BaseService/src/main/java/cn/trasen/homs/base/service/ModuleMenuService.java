package cn.trasen.homs.base.service;

import cn.trasen.homs.base.bean.ModulePermissionsListRes;

/**
 * <AUTHOR>
 * @date 2021/11/24 17:45
 */
public interface ModuleMenuService {
	/**
	 * 读取我的显示菜单
	 *
	 * @return cn.trasen.BootComm.utils.PlatformResult<cn.trasen.homs.base.bean.ModulePermissionsListRes>
	 * <AUTHOR>
	 * @date 2021/11/24 17:47
	 */
	ModulePermissionsListRes getMyMenu();

	/**
	 * 读取个人设置显示菜单
	 *
	 * @return cn.trasen.homs.base.bean.ModulePermissionsListRes
	 * <AUTHOR>
	 * @date 2021/11/24 17:47
	 */
	ModulePermissionsListRes getMyConfigMenu();

	/**
	 * 获取模块菜单
	 *
	 * @param module
	 * @return cn.trasen.homs.base.bean.ModulePermissionsListRes
	 * <AUTHOR>
	 * @date 2021/11/30 9:31
	 */
	ModulePermissionsListRes getMenu(String module);
}
