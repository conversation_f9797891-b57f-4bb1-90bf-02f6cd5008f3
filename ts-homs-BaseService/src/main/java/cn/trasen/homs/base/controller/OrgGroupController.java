package cn.trasen.homs.base.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.BootComm.utils.Response;
import cn.trasen.homs.base.bean.OrgGroupEmpListReq;
import cn.trasen.homs.base.bean.OrgGroupEmpListRes;
import cn.trasen.homs.base.bean.OrgGroupListReq;
import cn.trasen.homs.base.bean.OrgGroupListRes;
import cn.trasen.homs.base.bean.OrgGroupUpdateSort;
import cn.trasen.homs.base.bean.OrgGroupUserSaveReq;
import cn.trasen.homs.base.bean.TreeGroup;
import cn.trasen.homs.base.bo.OrgGroupEmpInBO;
import cn.trasen.homs.base.bo.OrgGroupInBO;
import cn.trasen.homs.base.model.OrgGroup;
import cn.trasen.homs.base.model.OrgGroupClass;
import cn.trasen.homs.base.model.OrgUserGroup;
import cn.trasen.homs.base.service.OrgGroupClassService;
import cn.trasen.homs.base.service.OrgGroupService;
import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 自定义群组Controller层
 * @Date: 2020/1/13 18:28
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Api(tags = "自定义群组Controller")
@RestController
public class OrgGroupController {

    private static final Logger logger = LoggerFactory.getLogger(OrgGroupController.class);

    @Autowired
    private OrgGroupService orgGroupService;
    
    @Autowired
    private OrgGroupClassService orgGroupClassService;

    /**
     * @Author: Lizhihuo
     * @Description: 查询自定义群组列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    @ApiOperation(value = "自定义群组列表", notes = "自定义群组列表")
    @PostMapping("/employee/orgGroup/list")
    public DataSet<OrgGroup> getDataList(Page page, OrgGroup record) {
        try {
            List<OrgGroup> list = orgGroupService.getDataList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
    
    @ApiOperation(value = "自定义群组列表树", notes = "自定义群组列表树")
    @PostMapping("/employee/orgGroup/getOrgGroupTreeList")
    public PlatformResult<List<TreeModel>> getOrgGroupTreeList(OrgGroup record) {
        try {
            List<TreeModel> list = orgGroupService.getOrgGroupTreeList(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("查询失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 移动端-查询自定义群组信息列表
     * @Date: 2020/4/22 11:12
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.hrm.model.OrgGroup>
     **/
    @ApiOperation(value = "移动端-查询自定义群组信息列表", notes = "移动端-查询自定义群组信息列表")
    @RequestMapping(value = "/employee/orgGroup/getOrgGroupList", method = {RequestMethod.POST, RequestMethod.GET})
    public List<OrgGroupListRes> getOrgGroupList(Page page, OrgGroup record) {
        List<OrgGroupListRes> list = orgGroupService.getOrgGroupList(page, record);
        return list;
    }


    /**
     * 查询自定义群组信息列表
     *
     * @param page
     * @param record
     * @return cn.trasen.BootComm.utils.PlatformResult<java.util.List < cn.trasen.basicsbottom.bean.OrgGroupListRes>>
     * <AUTHOR>
     * @date 2021/12/4 16:28
     */
    @ApiOperation(value = "移动端-查询自定义群组信息列表", notes = "移动端-查询自定义群组信息列表")
    @RequestMapping(value = "/employee/orgGroup/getOrgGroupListPage", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<List<OrgGroupListRes>> getOrgGroupListPage(Page page, OrgGroup record) {
        return PlatformResult.success(orgGroupService.getOrgGroupList(page, record));
    }


    /**
     * @Author: Lizhihuo
     * @Description: 新增自定义群组
     * @Date: 2020/1/13 8:41
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "新增自定义群组", notes = "新增自定义群组")
    @PostMapping("/employee/orgGroup/save")
    @ControllerLog(description = "新增自定义群组")
    public PlatformResult<String> insert(@RequestBody OrgGroup record) {
        OrgGroup orgGroup = orgGroupService.insert(record);
        return PlatformResult.success(orgGroup.getGroupId());

    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组
     * @Date: 2020/1/13 9:23
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "修改自定义群组", notes = "修改自定义群组")
    @PostMapping("/employee/orgGroup/update")
    @ControllerLog(description = "修改自定义群组")
    public PlatformResult<String> update(@RequestBody OrgGroup record) {
        orgGroupService.update(record);
        return PlatformResult.success();
    }


    /**
     * 禁用
     *
     * @param id
     * @return cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     * <AUTHOR>
     * @date 2022/2/8 15:24
     */
    @ApiOperation(value = "禁用", notes = "禁用")
    @PostMapping("/employee/orgGroup/disable")
    public PlatformResult disable(String id) {
        orgGroupService.enable(id, "2");
        return PlatformResult.success();
    }

    /**
     * 启用
     *
     * @param id
     * @return cn.trasen.BootComm.utils.PlatformResult
     * <AUTHOR>
     * @date 2022/2/8 15:25
     */
    @ApiOperation(value = "启用", notes = "启用")
    @PostMapping("/employee/orgGroup/enable")
    public PlatformResult enable(String id) {
        orgGroupService.enable(id, "1");
        return PlatformResult.success();
    }


    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组
     * @Date: 2020/1/13 10:22
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "删除自定义群组", notes = "删除自定义群组")
    @PostMapping("/employee/orgGroup/deletedById")
    @ControllerLog(description = "删除自定义群组")
    public PlatformResult<String> deleteById(@RequestBody OrgGroup record) {
        try {
            orgGroupService.deleted(record.getGroupId());
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @throws
     * @Title: getOrgGroupTree
     * @Description: TODO(描述这个方法的作用)
     * @param: @param groupType
     * @param: @return
     * @return: PlatformResult<List < TreeGroup>>
     * @author: YueC
     * @date: 2020年6月10日 上午11:32:38
     */
    @ApiOperation(value = "自定义群组列表(选人)", notes = "自定义群组列表(选人)")
    @GetMapping("/employee/orgGroup/getOrgGroupTree")
    public PlatformResult<List<TreeGroup>> getOrgGroupTree(String groupType) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("groupType", groupType);
            params.put("currentUserCode", UserInfoHolder.getCurrentUserCode());
            params.put("currentUserOrg", UserInfoHolder.getCurrentUserInfo().getDeptcode());
            List<OrgGroup> list = orgGroupService.getOrgGroupTree(params);
            List<TreeGroup> resultList = new ArrayList<>();
            TreeGroup treeGroup = new TreeGroup();
            
            if ("0".equals(groupType)) {
            	treeGroup.setId("8888");
                treeGroup.setName("系统群组");
            } else {
            	treeGroup.setId("9999");
                treeGroup.setName("个人群组");
            }
            treeGroup.setChkDisabled(true);
            List<TreeGroup> children = new ArrayList<>();
            
            if("0".equals(groupType)){
            	OrgGroupClass orgGroupClass = new OrgGroupClass();
                orgGroupClass.setClassType((short) 0);
         		List<OrgGroupClass> orgGroupClassList = orgGroupClassService.getList(orgGroupClass);
         		
         		for (OrgGroupClass orgGroupClass2 : orgGroupClassList) {
         			TreeGroup tg = new TreeGroup();
                    tg.setId(orgGroupClass2.getId());
                    tg.setChkDisabled(true);
                    tg.setName(orgGroupClass2.getClassName());
                    tg.setDataType("group");
                    
                    List<TreeGroup> children2 = new ArrayList<>();
                    for (OrgGroup orgGroup : list) {
                   	 if(orgGroupClass2.getId().equals(orgGroup.getGroupClassId())){
                   		 TreeGroup tg2 = new TreeGroup();
                   		 tg2.setId(orgGroup.getGroupId());
                            tg2.setChkDisabled(true);
                            tg2.setName(orgGroup.getGroupName());
                            tg2.setDataType("group");
                            children2.add(tg2);
                   	 }
                    }
                    
                    tg.setChildren(children2);
                    children.add(tg);
         		}
            }else{
                 for (OrgGroup orgGroup : list) {
            		 TreeGroup tg = new TreeGroup();
            		 tg.setId(orgGroup.getGroupId());
                     tg.setChkDisabled(true);
                     tg.setName(orgGroup.getGroupName());
                     tg.setDataType("group");
                     children.add(tg);
                 }
            }
           
           
            treeGroup.setChildren(children);

            resultList.add(treeGroup);
            return PlatformResult.success(resultList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询数据失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @throws
     * @Title: getOrgGroupUser
     * @Description: TODO(描述这个方法的作用)
     * @param: @param page
     * @param: @param groupId
     * @param: @return
     * @return: DataSet<Map < String, Object>>
     * @author: YueC
     * @date: 2020年6月10日 上午11:32:33
     */
    @ApiOperation(value = "查询自定义群组人员信息", notes = "查询自定义群组人员信息")
    @GetMapping("/employee/orgGroup/getOrgGroupUser")
    public DataSet<Map<String, Object>> getOrgGroupUser(Page page, HttpServletRequest request, String groupId) {
    	if(ObjectUtils.isEmpty(page.getSidx())){
    		page.setSidx("e.emp_sort");
    		page.setSord("asc");
    	}
        page.setPageNo(Integer.valueOf(request.getParameter("pageNo")));
        page.setPageSize(Integer.valueOf(request.getParameter("pageSize")));
        List<Map<String, Object>> groupUserList = orgGroupService.getOrgGroupUser(page, groupId);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), groupUserList);

    }

    /**
     * @throws
     * @Title: selectGroupIdByUserCode
     * @Description: TODO(描述这个方法的作用)
     * @param: @param userCode
     * @param: @return
     * @return: PlatformResult<List < String>>
     * @author: Yuec
     * @date: 2021年5月14日 上午11:56:32
     */
    @ApiOperation(value = "查询用户所在的群组", notes = "查询用户所在的群组")
    @PostMapping("/employee/orgGroup/selectGroupIdByUserCode")
    public PlatformResult<List<String>> selectGroupIdByUserCode(String userCode) {
        try {
            List<String> list = orgGroupService.selectGroupIdByUserCode(userCode);
            return PlatformResult.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询数据失败，失败原因：" + e.getMessage());
        }
    }


    /**
     * @throws
     * @Title: selectOrgGroupList
     * @Description: TODO(描述这个方法的作用)
     * @param: @return
     * @return: PlatformResult<List < OrgGroup>>
     * @author: Yuec
     * @date: 2021年5月14日 上午11:41:51
     */
    @ApiOperation(value = "自定义群组列表", notes = "自定义群组列表")
    @PostMapping("/employee/orgGroup/selectOrgGroupList")
    public PlatformResult<List<Map<String, String>>> selectOrgGroupList(String groupName) {
        try {
            List<Map<String, String>> list = orgGroupService.selectOrgGroupList(groupName);
            return PlatformResult.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询数据失败，失败原因：" + e.getMessage());
        }
    }


    /**
     * 获取员工列表
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    @ApiOperation(value = "获取员工列表", notes = "获取员工列表")
    @PostMapping("/employee/orgGroup/listOrgGroupEmp")
    public PlatformResult<List<OrgGroupEmpListRes>> listOrgGroupEmp(@RequestBody OrgGroupEmpListReq orgGroupEmpListReq) {
        return PlatformResult.success(BeanUtil.copyToList(orgGroupService.listOrgGroupEmp(BeanUtil.copyProperties(orgGroupEmpListReq, OrgGroupEmpInBO.class)), OrgGroupEmpListRes.class));
    }


    /**
     * 获取员工列表
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    @ApiOperation(value = "获取员工列表分页", notes = "获取员工列表分页")
    @PostMapping("/employee/orgGroup/listPageOrgGroupEmp")
    public DataSet<OrgGroupEmpListRes> listPageOrgGroupEmp(Page page,OrgGroupEmpListReq orgGroupEmpListReq) {
        List<OrgGroupEmpListRes> orgGroupEmpListResList = BeanUtil.copyToList(orgGroupService.listOrgGroupEmp(page, BeanUtil.copyProperties(orgGroupEmpListReq, OrgGroupEmpInBO.class)), OrgGroupEmpListRes.class);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), orgGroupEmpListResList);
    }


    /**
     * 移除员工
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    @ApiOperation(value = "移除员工", notes = "移除员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "组ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "empCode", value = "员工编码", required = true, dataType = "String")

    })
    @PostMapping("/employee/orgGroup/deleteGroupEmp")
    public PlatformResult<List<OrgGroupEmpListRes>> deleteGroupEmp(String groupId, String empCode) {
        orgGroupService.deleteGroupEmp(groupId, empCode);
        return PlatformResult.success();
    }

    @ApiOperation(value = "保存员工", notes = "保存员工")
    @PostMapping("/employee/orgGroup/saveGroupEmp")
    public PlatformResult saveGroupEmp(@RequestBody OrgGroupUserSaveReq orgGroupUserSaveReq) {
        orgGroupService.saveGroupEmp(orgGroupUserSaveReq.getGroupId(), orgGroupUserSaveReq.getEmpCodeList());
        return PlatformResult.success();
    }


    // @GetMapping("/employee/download/template/downloadExportGroupEmp")
    //  public void downloadExportGroupEmp(HttpServletResponse response) throws IOException {
    //   OrgGroupEmpListReq orgGroupEmpListReq = new OrgGroupEmpListReq();
    @ApiOperation(value = "群组人员导出", notes = "群组人员导出")
    @PostMapping("/employee/download/template/downloadExportGroupEmp")
    public void downloadExportGroupEmp(HttpServletResponse response, @RequestBody OrgGroupEmpListReq orgGroupEmpListReq) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("群组人员.xlsx", "UTF-8"));
        Response.Write(response, orgGroupService.downloadExportGroupEmp(BeanUtil.copyProperties(orgGroupEmpListReq, OrgGroupEmpInBO.class)));
    }

    @ApiOperation(value = "当前用户系统群组", notes = "当前用户系统群组")
    @PostMapping("/employee/orgGroup/listCurrentEmpOrgGroup")
    public PlatformResult<List<OrgGroup>> listCurrentEmpOrgGroup(@RequestBody OrgGroupListReq orgGroupListReq) {
        OrgGroupInBO orgGroupInBO = new OrgGroupInBO();
        orgGroupInBO.setGroupName(orgGroupListReq.getGroupName());
        return PlatformResult.success(orgGroupService.listCurrentEmpOrgGroup(orgGroupInBO));
    }

    @ApiOperation(value = "批量修改排序", notes = "批量修改排序")
    @PostMapping("/employee/orgGroup/updateSort")
    public PlatformResult<List<OrgGroup>> updateSort(@RequestBody List<OrgGroupUpdateSort> orgGroupUpdateSortList) {
        orgGroupService.updateSort(BeanUtil.copyToList(orgGroupUpdateSortList, OrgGroupInBO.class));
        return PlatformResult.success();
    }
    
    @ApiOperation(value = "批量修改群组人员排序", notes = "批量修改群组人员排序")
    @PostMapping("/employee/orgGroup/updateUserGroupSort")
    public PlatformResult<String> updateUserGroupSort(@RequestBody List<OrgUserGroup> orgGroupUpdateSortList) {
        orgGroupService.updateUserGroupSort(orgGroupUpdateSortList);
        return PlatformResult.success();
    }
    
    @ApiOperation(value = "同步科室领导到群组", notes = "同步科室领导到群组")
    @PostMapping("/employee/orgGroup/syncLeaderToGroup")
    public PlatformResult<String> syncLeaderToGroup() {
    	try {
    		orgGroupService.syncLeaderToGroup();
    		 return PlatformResult.success();
    	}catch(Exception e) {
    		e.printStackTrace();
    		 return PlatformResult.failure("同步失败，失败原因："  + e.getMessage());
    	}
        
       
    }
}

