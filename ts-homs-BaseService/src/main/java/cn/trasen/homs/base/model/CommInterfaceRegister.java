package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_interface_register")
@Setter
@Getter
public class CommInterfaceRegister {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 应用模块
     */
    @Column(name = "module_name")
    @ApiModelProperty(value = "应用模块")
    private String moduleName;

    /**
     * 数据调用方式 1主动推送  2第三方拉取
     */
    @Column(name = "call_type")
    @ApiModelProperty(value = "数据调用方式 1主动推送  2第三方拉取")
    private String callType;

    /**
     * 接口名称
     */
    @Column(name = "interface_name")
    @ApiModelProperty(value = "接口名称")
    private String interfaceName;

    /**
     * 服务器ip端口
     */
    @Column(name = "interface_ip")
    @ApiModelProperty(value = "服务器ip端口")
    private String interfaceIp;

    /**
     * 接口地址
     */
    @Column(name = "interface_address")
    @ApiModelProperty(value = "接口地址")
    private String interfaceAddress;

    /**
     * 请求方式
     */
    @Column(name = "post_type")
    @ApiModelProperty(value = "请求方式")
    private String postType;

    /**
     * 请求头参数
     */
    @Column(name = "header_params")
    @ApiModelProperty(value = "请求头参数")
    private String headerParams;
    
    @Column(name = "cipher")
    @ApiModelProperty(value = "请求密钥 khel3@hkzlv8!")
    private String cipher;

    /**
     * 状态  1启用  2停用
     */
    @ApiModelProperty(value = "状态 1启用 2停用")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 入参示例
     */
    @Column(name = "params_example")
    @ApiModelProperty(value = "入参示例")
    private String paramsExample;

    /**
     * 出参示例
     */
    @Column(name = "response_example")
    @ApiModelProperty(value = "出参示例")
    private String responseExample;
    
    @Column(name = "is_sys")
    @ApiModelProperty(value = "是否系统级 0否 1是")
    private String isSys;
    
    @Column(name = "platform_org_code")
    @ApiModelProperty(value = "集成平台医院编码")
    private String platformOrgCode;
    
    @Column(name = "platform_org_name")
    @ApiModelProperty(value = "集成平台医院名称")
    private String platformOrgName;
    
    
    @Column(name = "platform_hosp_code")
    @ApiModelProperty(value = "集成平台院区编码")
    private String platformHospCode;
    
    @Column(name = "platform_hosp_name")
    @ApiModelProperty(value = "集成平台院区名称")
    private String platformHospName;
    
    @Column(name = "platform_app_id")
    @ApiModelProperty(value = "集成平台appid")
    private String platformAppId;
    
    @Column(name = "platform_sign")
    @ApiModelProperty(value = "集成平台sign")
    private String platformSign;
    
    @Column(name = "member_dept")
    @ApiModelProperty(value = "是否需要科室  0否 1是")
    private String memberDept;
    
    @Column(name = "platform_type")
    @ApiModelProperty(value = "平台类型 1单机构  2多机构")
    private String platformType;
    
}