package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.Linkmanclass;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 联系人类型Service层
 * @Date: 2020/1/13 18:23
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
public interface LinkmanclassService {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 查询联系人类型列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    List<Linkmanclass> getDataList(Page page, Linkmanclass linkmanclass);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 新增联系人类型
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    int insert(Linkmanclass linkmanclass);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 修改联系人类型
     * @Date: 2020/1/13 9:24
     * @Param:
     * @return: int
     **/
    int update(Linkmanclass linkmanclass);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 删除联系人类型
     * @Date: 2020/1/13 10:25
     * @Param:
     * @return: int
     **/
    int deleted(String id);

}
