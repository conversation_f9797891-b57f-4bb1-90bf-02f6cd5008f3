package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.OrganizationLeaderListReq;
import cn.trasen.homs.base.bean.OrganizationLeaderResp;
import cn.trasen.homs.base.bean.OrganizationLeaderSaveReq;
import cn.trasen.homs.base.dto.ReplaceLeader;
import cn.trasen.homs.base.mapper.OrganizationLeaderMapper;
import cn.trasen.homs.base.mapper.OrganizationMapper;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.model.OrganizationLeader;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationLeaderService;
import cn.trasen.homs.base.service.IOrganizationService;
import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.DeptFeignService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/7/27 17:11
 * @description
 */

@Service
public class OrganizationLeaderService  implements IOrganizationLeaderService {

    @Autowired
    OrganizationLeaderMapper organizationLeaderMapper;


    @Autowired
    IDictItemService dictItemService;


    @Autowired
    HrmsEmployeeService hrmsEmployeeService;


    @Autowired
    DeptFeignService deptFeignService;


    @Autowired
    IOrganizationService organizationService;
    
    @Autowired
    OrganizationMapper organizationMapper;

    @Override
    /**
     * @description: 保存部门领导
     * @param: orgId
     * @param: leaders
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 16:59
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLeaders(String orgId, List<OrganizationLeaderSaveReq> leaders) {
        if (leaders != null) {

            List<Organization> organizationNexts = organizationService.getNextOrg(orgId);


            for (OrganizationLeaderSaveReq organizationLeader : leaders) {
                if (organizationLeader.getSynNextOrg().equals(1)) {
                    for (Organization o : organizationNexts) {

                        Example example = new Example(OrganizationLeader.class);
                        example.createCriteria().andEqualTo("orgId", o.getOrganizationId()).andEqualTo("roleId", organizationLeader.getRoleId());
                        organizationLeaderMapper.deleteByExample(example);

                        organizationLeader.setOrgId(o.getOrganizationId());
                        OrganizationLeader bean = BeanUtils.InitBean(OrganizationLeader.class);
                        BeanUtil.copyProperties(organizationLeader, bean);
                        bean.setEmployeeIds(org.apache.commons.lang.StringUtils.join(organizationLeader.getEmployeeIdList().toArray(), ","));
                        bean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                        organizationLeaderMapper.insertSelective(bean);
                    }
                } else {

                    Example example = new Example(OrganizationLeader.class);
                    example.createCriteria().andEqualTo("orgId", orgId).andEqualTo("roleId", organizationLeader.getRoleId());
                    organizationLeaderMapper.deleteByExample(example);

                    organizationLeader.setOrgId(orgId);
                    OrganizationLeader bean = BeanUtils.InitBean(OrganizationLeader.class);
                    BeanUtil.copyProperties(organizationLeader, bean);
                    bean.setEmployeeIds(org.apache.commons.lang.StringUtils.join(organizationLeader.getEmployeeIdList().toArray(), ","));
                    bean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    organizationLeaderMapper.insertSelective(bean);
                }
            }
            //同步
            Organization organization = organizationMapper.selectByPrimaryKey(orgId);

            // 同步sys
            for (OrganizationLeaderSaveReq organizationLeader : leaders) {
                if (organizationLeader.getSynNextOrg().equals(1)) {
                    ThpsDeptReq deptInfo = new ThpsDeptReq();
                    fullData(deptInfo, organizationLeader);
                    for (Organization o : organizationNexts) {
                        deptInfo.setId(o.getOrganizationId());
                        deptInfo.setDeptcode(o.getCode());
                        deptInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                        deptFeignService.doSaveOrUpdateDept(deptInfo);
                    }
                } else {
                    ThpsDeptReq deptInfo = new ThpsDeptReq();
                    deptInfo.setId(orgId);
                    deptInfo.setDeptcode(organization.getCode());
                    fullData(deptInfo, organizationLeader);
                    deptInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    deptFeignService.doSaveOrUpdateDept(deptInfo);
                }
            }
        }
    }

    /** 
    * @description: 填充数据
* @param: deptInfo
* @param: leaders
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/17 12:43
    */
    private  void fullData(ThpsDeptReq deptInfo,OrganizationLeaderSaveReq organizationLeaderSaveReq) {
        if (organizationLeaderSaveReq.getRoleId().equals("1")) {
            setField("clerkid", "clerkname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("2")) {
            setField("headnurseid", "headnursename", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("3")) {
            setField("directorofdepartmentid", "directorofdepartmentname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("4")) {
            setField("medicaldirectorid", "medicaldirectorname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("5")) {
            setField("departmentheadid", "departmentheadname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("6")) {
            setField("managementleadid", "managementleadname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("7")) {
            setField("directleadershipid", "directleadershipname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        } else if (organizationLeaderSaveReq.getRoleId().equals("8")) {
            setField("departmentephorid", "departmentephorname", deptInfo, organizationLeaderSaveReq.getEmployeeIdList());
        }
    }


    /** 
    * @description: 填充字段
* @param: idFile
* @param: valueFile
* @param: deptInfo
* @param: employeeIdList
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/17 12:54
    */
    private  void setField(String idFile,String valueFile,ThpsDeptReq deptInfo,List<String> employeeIdList) {
        if (CollectionUtils.isEmpty(employeeIdList)) {
            BeanUtil.setFieldValue(deptInfo, idFile, "");
            BeanUtil.setFieldValue(deptInfo, valueFile, "");
        } else {
            List<HrmsEmployeeResp> employeeResps = hrmsEmployeeService.getEmployeeDetailByCodes(employeeIdList);
            List<String> empNameList = employeeResps.stream().map(d -> d.getEmployeeName()).collect(Collectors.toList());
            BeanUtil.setFieldValue(deptInfo, idFile, StringUtils.join(employeeIdList.toArray(), ","));
            BeanUtil.setFieldValue(deptInfo, valueFile, StringUtils.join(empNameList.toArray(), ","));
        }
    }

    @Override
    /**
     * @description: 获取领导列表
     * @param: orgId
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationLeaderResp>
     * @author: liyuan
     * @createTime: 2021/7/28 17:05
     */
    public List<OrganizationLeaderResp> getList(String orgId) {
        Example example = new Example(OrganizationLeader.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("orgId", orgId);
        example.orderBy("sortNo").desc();
        List<OrganizationLeader> organizationLeaderList = organizationLeaderMapper.selectByExample(example);

        Map<String, String> LDJSDict = dictItemService.convertDictMap("LDJS");

        List<OrganizationLeaderResp> organizationLeaderRespList = new ArrayList<>();
        for (OrganizationLeader organizationLeader : organizationLeaderList) {
            OrganizationLeaderResp organizationLeaderResp = new OrganizationLeaderResp();
            BeanUtil.copyProperties(organizationLeader, organizationLeaderResp);
            organizationLeaderResp.setRoleName(LDJSDict.getOrDefault(organizationLeaderResp.getRoleId(), ""));

            if (StringUtils.isBlank(organizationLeader.getEmployeeIds()) == false) {

                organizationLeaderResp.setEmployeeIdList(hrmsEmployeeService.getEmployeeDetailByCodes(new ArrayList<>(Arrays.asList(organizationLeader.getEmployeeIds().split(",")))));

            }

            organizationLeaderRespList.add(organizationLeaderResp);
        }
        return organizationLeaderRespList;
    }


    @Override
    /**
     * @description: 获取领导列表
     * @param: orgId
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationLeaderResp>
     * @author: liyuan
     * @createTime: 2021/7/28 17:05
     */
    public List<OrganizationLeader> getBaseList(OrganizationLeaderListReq organizationLeaderListReq) {
        Example example = new Example(OrganizationLeader.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if(organizationLeaderListReq.getShowEmployeeIdsIsNull()==null)
        {
            organizationLeaderListReq.setShowEmployeeIdsIsNull(true);
        }
        if(organizationLeaderListReq.getShowEmployeeIdsIsNull()==false) {
            criteria.andIsNotNull("employeeIds");
            criteria.andNotEqualTo("employeeIds", "");
        }
        if (organizationLeaderListReq.getShowFramework() != null) {
            criteria.andEqualTo("showFramework", organizationLeaderListReq.getShowFramework());
        }
        example.orderBy("sortNo").desc();
        List<OrganizationLeader> organizationLeaderList = organizationLeaderMapper.selectByExample(example);
        return organizationLeaderList;
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void bacthReplaceLeader(ReplaceLeader replaceLeader) {
		Example example = new Example(OrganizationLeader.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("roleId", replaceLeader.getRoleId());
        criteria.andCondition(" find_in_set("  + replaceLeader.getOldEmployeeCode() + ",employee_ids)");
        if(CollectionUtils.isNotEmpty(replaceLeader.getOrgIds())) {
        	criteria.andIn("orgId", replaceLeader.getOrgIds());
        }
        
        
        List<OrganizationLeader> leaderList = organizationLeaderMapper.selectByExample(example);
        
        for (OrganizationLeader organizationLeader : leaderList) {
        	String employeeIds = organizationLeader.getEmployeeIds();
        	if(StringUtils.isNotBlank(employeeIds)) {
        		List<String> employeeIdsList = new ArrayList<>();
        		String[] employeeIdsArray = employeeIds.split(",");
        		for (int i = 0; i < employeeIdsArray.length; i++) {
					if(employeeIdsArray[i].equals(replaceLeader.getOldEmployeeCode())) {
						employeeIdsList.add(replaceLeader.getNewEmployeeCode());
					}else {
						employeeIdsList.add(employeeIdsArray[i]);
					}
				}
        		
        		organizationLeader.setEmployeeIds(String.join(",", employeeIdsList));
        		
        		organizationLeaderMapper.updateByPrimaryKeySelective(organizationLeader);
        	}
		}
	}
    
    
}