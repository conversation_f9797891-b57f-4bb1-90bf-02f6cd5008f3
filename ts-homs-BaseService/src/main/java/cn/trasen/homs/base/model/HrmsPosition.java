package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import java.util.Date;

/**
 * 职务表
 *
 */
@Table(name = "comm_position")
@Setter
@Getter
public class HrmsPosition {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "position_id")
    @ApiModelProperty(value = "主键ID")
    private String  positionId;

    /**
     * 职务名称
     */
    @Column(name = "position_name")
    @ApiModelProperty(value = "职务名称")
    private String  positionName;

    /**
     * 排序号
     */
    @Column(name = "serial_number")
    @ApiModelProperty(value = "排序号")
    private Integer serialNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String  remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String  enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date    createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String  createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String  createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date    updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String  updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String  updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String  isDeleted;

    @Column(name = "is_enable")
    @ApiModelProperty(value = "启用标识: 0=禁用; 1=启用;")
    private String  isEnable;
    
    @Column(name = "sso_org_code")
    private String  ssoOrgCode;
    
    @Transient
    @ApiModelProperty(value = "是否管理页面：0-否，1-是，用于标识职务的管理维护界面")
    private String  isManagerPage;
}