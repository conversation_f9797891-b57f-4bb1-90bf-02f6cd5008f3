package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class HrmsSalaryLevelWageResp {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * 主键ID
     */
    @Id
    @Column(name = "salary_level_wage_id")
    @ApiModelProperty(value = "主键ID")
    private String salaryLevelWageId;
    
    /**
     * 薪级ID
     */
    @Column(name = "salary_level_id")
    @ApiModelProperty(value = "薪级ID")
    private String salaryLevelId;

    /**
     * 薪级工资
     */
    @Excel(name = "薪级工资")
    @Column(name = "salary_level_wage")
    @ApiModelProperty(value = "薪级工资")
    private BigDecimal salaryLevelWage = new BigDecimal(0);

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 是否启用文本
     */
    @Transient
    @ApiModelProperty(value = "是否启用文本")
    private String isEnableLabel;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
 // ------- 扩展字段 ------- //
 	/**
 	 * 薪级名称
 	 */
    @Excel(name = "薪级名称")
 	@Transient
 	@ApiModelProperty(value = "薪级名称")
 	private String salaryLevelName;

 	/**
 	 * 薪级类别ID
 	 */
 	@Transient
 	@ApiModelProperty(value = "薪级类别ID")
 	private String salaryLevelCategory;

 	/**
 	 * 薪级类别名称
 	 */
 	@Excel(name = "薪级类别")
 	@Transient
 	@ApiModelProperty(value = "薪级类别名称")
	private String salaryLevelCategoryName;
 	
 	/**
     * 等级
     */
 	@Excel(name = "等级")
 	@Transient
    @ApiModelProperty(value = "等级")
    private String grade;
}