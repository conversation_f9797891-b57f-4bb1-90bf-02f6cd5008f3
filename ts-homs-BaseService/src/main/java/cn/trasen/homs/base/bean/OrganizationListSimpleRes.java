package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationListSimpleRes {

    @ApiModelProperty(value = "主键ID")
    private String organizationId;

    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private String code;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String name;
    
    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;
    

    /**
     * 父类名称
     */
    @ApiModelProperty(value = "父类名称")
    private String parentName;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;


    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlagLable;
    /**
     * 员工人数
     */
    @ApiModelProperty(value = "员工人数")
    private Integer employeeNum;

    @ApiModelProperty(value = "层级")
    private Integer orgLevel;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer seqNo;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnable;
    
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;



    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnableLable;

}