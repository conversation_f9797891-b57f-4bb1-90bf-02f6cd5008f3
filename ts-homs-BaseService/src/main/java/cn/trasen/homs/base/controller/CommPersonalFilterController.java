package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommPersonalFilter;
import cn.trasen.homs.base.service.CommPersonalFilterService;
import cn.trasen.homs.core.entity.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@Api(tags = "筛选器Controller")
public class CommPersonalFilterController {
	
	@Autowired
	private CommPersonalFilterService commPersonalFilterService;
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    新增筛选器
	 * @author: jiang<PERSON>qiu 
	 * @date:   2020年3月6日 下午3:08:04
	 */
	@ApiOperation(value = "新增筛选器", notes = "新增筛选器")
	@PostMapping("/personalFilter/save")
	public Result save(@RequestBody CommPersonalFilter record){
		Result result = new Result();
		try{
			commPersonalFilterService.insert(record);
			result.setSuccess(true);
			result.setMessage("保存成功");
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    修改筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:08:14
	 */
	@ApiOperation(value = "修改筛选器", notes = "修改筛选器")
	@PostMapping("/personalFilter/udpate")
	public Result update(@RequestBody CommPersonalFilter record){
		Result result = new Result();
		try{
			commPersonalFilterService.update(record);
			result.setSuccess(true);
			result.setMessage("操作成功");
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    复制筛选器  
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:08:23
	 */
	@ApiOperation(value = "复制筛选器", notes = "复制筛选器")
	@PostMapping("/personalFilter/copy")
	public Result copy(@RequestBody CommPersonalFilter record){
		Result result = new Result();
		try{
			commPersonalFilterService.copy(record);
			result.setSuccess(true);
			result.setMessage("保存成功");
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    获取当前登录人筛选器列表
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:08:31
	 */
	@ApiOperation(value = "获取筛选器列表", notes = "获取筛选器列表")
	@PostMapping("/personalFilter/getList")
	public Result getList(CommPersonalFilter record){
		Result result = new Result();
		try{
			List<CommPersonalFilter> list = commPersonalFilterService.getList(record);
			result.setObject(list);
			result.setSuccess(true);
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    根据id查询筛选器
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:10:26
	 */
	@ApiOperation(value = "根据id查询筛选器", notes = "根据id查询筛选器")
	@PostMapping("/personalFilter/findById/{id}")
	public Result findById(@PathVariable String id){
		Result result = new Result();
		 
		try{
			result.setObject(commPersonalFilterService.findById(id));
			result.setSuccess(true);
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
	
	/**
	 * 
	 * 
	 * @Title:  CommPersonalFilterController.java   
	 * @Package cn.trasen.sh.controller   
	 * @Description:    根据id删除筛选器   
	 * @author: jiangyaqiu 
	 * @date:   2020年3月6日 下午3:10:36
	 */
	@ApiOperation(value = "根据id删除筛选器", notes = "根据id删除筛选器")
	@PostMapping("/personalFilter/deleteById/{id}")
	public Result deletedById(@PathVariable String id){
		Result result = new Result();
		 
		try{
			commPersonalFilterService.deleted(id);
			result.setSuccess(true);
			result.setMessage("操作成功");
		}catch(Exception e){
			e.printStackTrace();
			result.setMessage("系统异常，请联系管理员……");
			result.setSuccess(false);
		}
		return result;
	}
}
