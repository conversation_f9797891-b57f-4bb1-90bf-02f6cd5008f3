package cn.trasen.homs.base.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommModesUses;
import cn.trasen.homs.base.service.CommModesUsesService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommModesUsesController
 * @Description TODO
 * @date 2024��12��18�� ����5:19:08
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "运营平台模块使用统计")
public class CommModesUsesController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommModesUsesController.class);

	@Autowired
	private CommModesUsesService commModesUsesService;

	/**
	 * @Title saveCommModesUses
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/modesUses/save")
	public PlatformResult<String> saveCommModesUses(@RequestBody CommModesUses record) {
		try {
			commModesUsesService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommModesUses
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/modesUses/update")
	public PlatformResult<String> updateCommModesUses(@RequestBody CommModesUses record) {
		try {
			commModesUsesService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommModesUsesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommModesUses>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/modesUses/{id}")
	public PlatformResult<CommModesUses> selectCommModesUsesById(@PathVariable String id) {
		try {
			CommModesUses record = commModesUsesService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommModesUsesById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/modesUses/delete/{id}")
	public PlatformResult<String> deleteCommModesUsesById(@PathVariable String id) {
		try {
			commModesUsesService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommModesUsesList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommModesUses>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/modesUses/list")
	public DataSet<CommModesUses> selectCommModesUsesList(Page page, CommModesUses record) {
		return commModesUsesService.getDataSetList(page, record);
	}
}
