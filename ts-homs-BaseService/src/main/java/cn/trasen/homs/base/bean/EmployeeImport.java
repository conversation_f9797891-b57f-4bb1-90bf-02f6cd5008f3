package cn.trasen.homs.base.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/9 9:19
 */
@Data
public class EmployeeImport {

    @Excel(name = "科室")
    private String orgName;

    @Excel(name = "姓名")
    private String employeeName;

    @Excel(name = "工号")
    private String employeeNo;

    @Excel(name = "发薪号")
    private String empPayroll;

    @Excel(name = "性别")
    private String gender;

    @Excel(name = "出生日期")
    private String birthday;

    @Excel(name = "在编")
    private String establishmentType;

    @Excel(name = "党员")
    private String politicalStatus;

    @Excel(name = "身份证")
    private String identityNumber;

    @Excel(name = "来院时间")
    private String entryDate;

    @Excel(name = "电话")
    private String phoneNumber;
    
    @Excel(name = "员工id")
    private String employeeId;

    /**
     * 出生日期
     */
    private Date birthdayDate;

    /**
     * 来院时间
     */
    private Date entryDateDate;
}
