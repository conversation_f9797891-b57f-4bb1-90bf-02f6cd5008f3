package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeField;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeFieldHead;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeFieldHeadMapper extends Mapper<CustomEmployeeFieldHead> {

	List<CustomEmployeeField> selectByMyHeadField(@Param("setType")String setType,@Param("userCode")String userCode, @Param("ssoOrgCode")String ssoOrgCode);
	
	List<CustomEmployeeGroup> selectByMyExportGroup(@Param("currentUserCode")String currentUserCode,@Param("setValue")String setValue,
													@Param("archivesType")String archivesType,@Param("ssoOrgCode")String ssoOrgCode,
													@Param("setType")String setType);

	List<CustomEmployeeField> getListByType(@Param("setType")String setType, @Param("userCode")String userCode, @Param("ssoOrgCode")String ssoOrgCode);

}