package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.base.dao.CommPersonalFilterMapper;
import cn.trasen.homs.base.model.CommPersonalFilter;
import cn.trasen.homs.base.service.CommPersonalFilterService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommPersonalFilterServiceImpl implements CommPersonalFilterService{
    
    @Autowired
    private CommPersonalFilterMapper commPersonalFilterMapper;
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterService.java   
     * @Package cn.trasen.sh.service   
     * @Description:    新增筛选器
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午2:37:05
     */
    @Transactional(readOnly = false)
    public int insert(CommPersonalFilter record) {
        
        record.setUpdateDate(new Date());
        record.setCreateDate(new Date());
        record.setIsDeleted(Contants.IS_DELETED_FALSE);
        record.setCreateUser(UserInfoHolder.getCurrentUserCode());
        record.setCreateUserName(UserInfoHolder.getCurrentUserName());
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        record.setId(ApplicationUtils.GUID32());
        
        return commPersonalFilterMapper.insert(record);
    }
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterService.java   
     * @Package cn.trasen.sh.service   
     * @Description:    修改筛选器
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午2:37:13
     */
    @Transactional(readOnly = false)
    public int update(CommPersonalFilter record) {
        Assert.hasText(record.getId(), "id不能为空.");
        record.setUpdateDate(new Date());
        record.setIsDeleted(Contants.IS_DELETED_FALSE);
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        
        return commPersonalFilterMapper.updateByPrimaryKeySelective(record);
        
    }
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterService.java   
     * @Package cn.trasen.sh.service   
     * @Description:    删除筛选器 
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午2:37:21
     */
    @Transactional(readOnly = false)
    public int deleted(String id) {
        Assert.hasText(id, "id不能为空.");
        
        CommPersonalFilter record = findById(id);
        
        record.setIsDeleted(Contants.IS_DELETED_TURE);
        
        record.setUpdateDate(new Date());
        
        record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        
        record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        
        return commPersonalFilterMapper.updateByPrimaryKeySelective(record);
    }
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterService.java   
     * @Package cn.trasen.sh.service   
     * @Description:    根据id查询筛选器
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午2:37:31
     */
    public CommPersonalFilter findById(String id) {
        
        
        return  commPersonalFilterMapper.selectByPrimaryKey(id);
        
    }
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterService.java   
     * @Package cn.trasen.sh.service   
     * @Description:    复制筛选器
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午2:37:40
     */
    @Transactional(readOnly = false)
    public int copy(CommPersonalFilter record) {
        
        Assert.hasText(record.getId(), "id不能为空.");
        
        CommPersonalFilter original = findById(record.getId());
        
        original.setFilterName(record.getFilterName());
        
        original.setId(ApplicationUtils.GUID32());
        
        return commPersonalFilterMapper.insert(original);
        
    }
    
    /**
     * 
     * 
     * @Title:  CommPersonalFilterServiceImpl.java  
     * @Package cn.trasen.sh.service.impl   
     * @Description:    TODO(用一句话描述该文件做什么)   
     * @author: jiangyaqiu 
     * @date:   2020年3月6日 下午3:00:54
     */
    public List<CommPersonalFilter> getList(CommPersonalFilter record) {
        
        String userCode = UserInfoHolder.getCurrentUserCode();
        
        Example example = new Example(CommPersonalFilter.class);
        
        example.createCriteria()
                .andEqualTo("isDeleted",Contants.IS_DELETED_FALSE)
                //根据当前登录账号机构编码过滤查询数据
                .andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode())
                .andEqualTo("createUser",userCode)
                .andEqualTo("menuId",record.getMenuId());
        
        return commPersonalFilterMapper.selectByExample(example);
    }
}
