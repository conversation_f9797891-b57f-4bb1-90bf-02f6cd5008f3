package cn.trasen.homs.base.utils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 一些常用量值的封装
 * @Date: 2020/1/13 13:51
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
public class Utils {

    public static final Short STATUS_ENABLED = 0; //启用

    public static final Short STATUS_PROHIBIT = -1; //禁用

    //性别
    public static final Short SEX_TYPE_ZERO = 0; //男

    public static final Short SEX_TYPE_ONE = 1; //女

    //员工状态
    public static final Short EMP_STATUS_ONE = 1; //正常

    public static final Short EMP_STATUS_TWO = 2; //停用

    public static final Short EMP_STATUS_THREE = 3; //病假

    public static final Short EMP_STATUS_FOUR = 4; //病假

    public static final Short EMP_STATUS_FIVE = 5; //离职

    public static final Short EMP_STATUS_SIX = 6; //年假

    public static final Short EMP_STATUS_SEVEN = 7; //退休

    public static final Short EMP_STATUS_EIGHT = 8; //调休

    public static final Short EMP_STATUS_NINE = 9; //婚丧假

    public static final Short EMP_STATUS_TEN = 10; //产假护理假

    //婚姻状况
    public static final Short EMP_IS_MARRIAGE_ONE = 1; //已婚

    public static final Short EMP_IS_MARRIAGE_TWO = 2; //未婚

    public static final Short EMP_IS_MARRIAGE_THREE = 3; //保密

    public static final Short IS_STATUS_ON = 1; //是

    public static final Short IS_STATUS_ZERO = 0; //否


    /**
     * 将List<Object>转换为List<Map<String,Object>>
     *
     * @param list
     * @return
     */
    public static List<Map<String, Object>> convertListMap(List<Object> list) {
        List<Map<String, Object>> maps = new ArrayList<>();
        for (Object obj : list) {
            Class c = obj.getClass();
            Field[] f = c.getDeclaredFields();
            Map<String, Object> map = new HashMap<>();
            for (Field fie : f) {
                try {
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            //获取父类的私有属性
            for (Field fie : c.getSuperclass().getDeclaredFields()) {
                try {
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            maps.add(map);
        }
        return maps;
    }

    public static boolean checkDateStrOk(String date, String fmtTpl) {
        if (fmtTpl == null || fmtTpl.equals("")) {
            fmtTpl = "yyyy-MM-dd HH:mm:ss";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(fmtTpl);
            java.util.Date dd = sdf.parse(date);
            if (!date.equals(sdf.format(dd))) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }
    
    /**
     * 获取分隔符分割后的最后一个元素
     * @param str       输入字符串
     * @param delimiter 分隔符
     * @return 最后一个元素（若分隔符不存在或字符串为空，返回原字符串或空）
     */
    public static String getLastElement(String str, String delimiter) {
        if (str == null || str.isEmpty()) return "";
        int lastIndex = str.lastIndexOf(delimiter);
        if (lastIndex == -1) return str;
        return str.substring(lastIndex + delimiter.length());
    }
    
}
