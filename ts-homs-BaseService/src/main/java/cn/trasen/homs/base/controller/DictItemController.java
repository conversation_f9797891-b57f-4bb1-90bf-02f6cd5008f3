package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import cn.hutool.core.lang.Dict;

import cn.trasen.homs.base.model.DictType;
import cn.trasen.homs.base.service.IDictTypeService;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.OrganizationLeaderResp;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationLeaderService;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 字典项目Controller层
 * @Date: 2020/4/29 10:45
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Api(tags = "字典项目Controller")
@RestController
public class DictItemController {

    private static final Logger logger = LoggerFactory.getLogger(DictItemController.class);

    @Autowired
    private IDictItemService dictItemService;

    @Autowired
    private IOrganizationLeaderService iOrganizationLeaderService;
    @Autowired
    private IDictTypeService iDictTypeService;

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增字典项目</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日 10:38 </p>
     */
    @ApiOperation(value = "新增字典项目", notes = "新增字典项目")
    @PostMapping("/dictItem/save")
    public PlatformResult<String> insert(@RequestBody DictItem entity) {
        try {
            int count = dictItemService.insert(entity);
            if (count == 0) {//存在重复的
                return PlatformResult.failure("项目编码不能重复");
            } else {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改字典项目</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午4:48:58 </p>
     */
    @ApiOperation(value = "修改字典项目", notes = "修改字典项目")
    @PostMapping("/dictItem/update")
    public PlatformResult<String> update(@RequestBody DictItem entity) {
        try {
            dictItemService.update(entity);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: deleteById</p>
     * <p> @Description: 删除字典项目</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午4:49:12 </p>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/dictItem/deletedById")
    public PlatformResult<String> deleteById(@RequestBody DictItem entity) {
        try {
            dictItemService.deleted(entity);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 启用
     * @Date: 2020/5/16 10:51
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "启用", notes = "启用")
    @PostMapping("/dictItem/dictItemEnable")
    public PlatformResult<String> dictItemEnable(@RequestBody DictItem entity) {
        try {
            entity.setIsDeleted(Contants.IS_DELETED_FALSE);
            DictItem dictItem = dictItemService.getDictItemById(entity);

            if (dictItem == null) {
                return PlatformResult.failure("字典类型为禁用状态、不能启用字典项目！");
            } else {
                List<DictItem> itemList = dictItemService.getDictItemByTypeCode(dictItem.getItemCode(), dictItem.getSsoOrgCode());
                if (CollectionUtils.isNotEmpty(itemList) && itemList.size() > 0){
                    return PlatformResult.failure("相同字典项目编码已存在启用数据！");
                }
                dictItemService.dictItemEnable(entity);
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 获取字典项目列表</p>
     * <p> @Return: DataSet<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午4:49:35 </p>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/dictItem/list")
    public DataSet<DictItem> getDataList(Page page, DictItem entity) {
        try {
            List<DictItem> list = dictItemService.getDataList(page, entity);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 通过ID查询字典项目
     * @Date: 2020/4/29 16:11
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.system.model.DictItem>
     **/
    @ApiOperation(value = "通过ID查询字典项目", notes = "通过ID查询字典项目")
    @GetMapping("/dictItem/getDictItemById")
    public PlatformResult<DictItem> getDictItemById(String id) {
        DictItem entity = new DictItem();
        entity.setId(id);
        try {
            DictItem dictItem = dictItemService.getDictItemById(entity);
            return PlatformResult.success(dictItem);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @throws
     * @Title: getDictItemByTypeCode
     * @Description: TODO(描述这个方法的作用)
     * @param: @param typeCode
     * @param: @return
     * @return: PlatformResult<List < DictItem>>
     * @author: 龙涌
     * @date: 2025年8月15日
     */
    @ApiOperation(value = "根据字典编码查询字典项", notes = "根据字典编码查询字典项")
    @GetMapping("/dictItem/getDictItemByTypeCode")
    public PlatformResult<List<DictItemResp>> getDictItemByTypeCode(String typeCode) {
        List<DictItem> list = dictItemService.getDictItemByTypeCode(typeCode);

        List<DictItemResp> result = new ArrayList<DictItemResp>();

        if (list != null && list.size() > 0) {
            for (DictItem dictItem : list) {
                DictItemResp newBean = new DictItemResp();
                BeanUtils.copyProperties(dictItem, newBean);
                result.add(newBean);
            }
        }
        return PlatformResult.success(result);
    }

    /**
     * @throws
     * @Title: getDictItemByTypeCodeList
     * @Description: 根据多个字典编码查询
     * @param: @param typeCode
     * @param: @return
     * @return: PlatformResult<List < DictItem>>
     * @author: YueC
     * @date: 2020年7月30日 下午2:11:41
     */
    @ApiOperation(value = "根据多个字典编码查询字典项", notes = "根据多个字典编码查询字典项")
    @GetMapping("/dictItem/getDictItemByTypeCodeList")
    public PlatformResult<List<DictItemResp>> getDictItemByTypeCode(List<String> typeCode) {
        List<DictItem> list = dictItemService.getDictItemByTypeCodeList(typeCode);

        List<DictItemResp> result = new ArrayList<DictItemResp>();

        if (list != null && list.size() > 0) {
            for (DictItem dictItem : list) {
                DictItemResp newBean = new DictItemResp();
                BeanUtils.copyProperties(dictItem, newBean);
                result.add(newBean);
            }
        }
        return PlatformResult.success(result);
    }

    /**
     * @throws
     * @Title: getDictItemByTypeCode
     * @Description: 根据字典编码+机构编码查询字典项
     * @param: @param typeCode
     * @param: @return
     * @return: PlatformResult<List < DictItem>>
     * @author: 龙涌
     * @date: 2025年8月5日
     */
    @ApiOperation(value = "根据字典编码查询字典项", notes = "根据字典编码查询字典项")
    @GetMapping("/dictItem/getSsoOrgCodeDictItemByTypeCode/{typeCode}/{ssoOrgCode}")
    public PlatformResult<List<DictItemResp>> getSsoOrgCodeDictItemByTypeCode(@PathVariable("typeCode") String typeCode, @PathVariable("ssoOrgCode") String ssoOrgCode) {
        List<DictItem> list = dictItemService.getDictItemByTypeCode(typeCode, ssoOrgCode);
        List<DictItemResp> result = new ArrayList<DictItemResp>();
        if (list != null && list.size() > 0) {
            for (DictItem dictItem : list) {
                DictItemResp newBean = new DictItemResp();
                BeanUtils.copyProperties(dictItem, newBean);
                result.add(newBean);
            }
        }
        return PlatformResult.success(result);
    }
    
    @ApiOperation(value = "根据字典编码查询字典项", notes = "根据字典编码查询字典项")
    @GetMapping("/dictItem/getDictItemByTypeCodeAndSsoOrgCode")
    public PlatformResult<List<DictItemResp>> getDictItemByTypeCodeAndSsoOrgCode(String typeCode, String ssoOrgCode) {
        return getSsoOrgCodeDictItemByTypeCode(typeCode, ssoOrgCode);
    }

    @ApiOperation(value = "根据字典编码设置字典项", notes = "根据字典编码设置字典项")
    @PostMapping("/dictItem/setDictItemByTypeCode")
    public PlatformResult setDictItemByTypeCode(@RequestParam String typeCode, @RequestParam String itemCode, @RequestParam String itemName, @RequestParam String itemValue, @RequestParam String remark) {

        DictItem dictItem = new DictItem();
        dictItem.setDictTypeCode(typeCode);
        dictItem.setItemCode(itemCode);
        dictItem.setItemNameValue(itemValue);
        dictItem.setItemName(itemName);
        dictItem.setRemark(remark);

        try {
            dictItemService.setDictItemByTypeCode(dictItem);
            return PlatformResult.success();

        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());

        }


    }

    @ApiOperation(value = "根据字典项目编码和字典值查询字典配置", notes = "根据字典项目编码和字典值查询字典配置")
    @GetMapping("/dictItem/getDictItemByDictTypeIdAndItemNameValue")
    public PlatformResult<DictItemResp> getDictItemByDictTypeIdAndItemNameValue(String dictTypeId, String itemNameValue) {
        try {
            DictItem dictItem = dictItemService.getDictItemByDictTypeIdAndItemNameValue(dictTypeId, itemNameValue, UserInfoHolder.getCurrentUserCorpCode());

            DictItemResp newBean = new DictItemResp();
            BeanUtils.copyProperties(dictItem, newBean);
            return PlatformResult.success(newBean);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    @ApiOperation(value = "根据字典项目编码和字典值查询字典配置查公共库", notes = "根据字典项目编码和字典值查询字典配置查公共库")
    @GetMapping("/dictItem/getDictItemByDictTypeIdAndItemNameValuePublic")
    public PlatformResult<DictItemResp> getDictItemByDictTypeIdAndItemNameValuePublic(String dictTypeId, String itemNameValue, String ssoOrgCode) {
        try {
            DictItem dictItem;
            if (StringUtils.isNotEmpty(ssoOrgCode)){
                dictItem = dictItemService.getDictItemByDictTypeIdAndItemNameValue(dictTypeId, itemNameValue, ssoOrgCode);
            }else {
                dictItem = dictItemService.getDictItemByDictTypeIdAndItemNameValue(dictTypeId, itemNameValue, UserInfoHolder.getCurrentUserCorpCode());
            }

            DictItemResp newBean = new DictItemResp();
            BeanUtils.copyProperties(dictItem, newBean);
            return PlatformResult.success(newBean);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * -- =============================================
     * -- 功能描述: 根据传入的typeCode 获取字典树
     * -- 作者: GW
     * -- 创建时间: 2024年11月13日
     * -- @return
     * -- =============================================
     */
    @ApiOperation(value = "获取字典树", notes = "获取字典树")
    @PostMapping("/dictItem/getDictItemTreeByCode")
    public PlatformResult<List<Map<String, Object>>> getDictItemTreeByCode(String typeCode) {
        try {
            return PlatformResult.success(dictItemService.getDictItemTreeByCode(typeCode));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "查询科室领导带姓名", notes = "查询科室领导带姓名")
    @GetMapping("/dictItem/getLeaderList")
    public PlatformResult<List<DictItem>> getLeaderList(String orgId) {

        if (StringUtils.isBlank(orgId)) {
            return PlatformResult.success(Collections.EMPTY_LIST);
        }

        List<DictItem> list = dictItemService.getDictItemByTypeCode("LDJS");

        List<OrganizationLeaderResp> leaderList = iOrganizationLeaderService.getList(orgId);

        for (DictItem dictItem : list) {

            StringBuffer names = new StringBuffer();

            for (OrganizationLeaderResp organizationLeaderResp : leaderList) {
                if (dictItem.getItemNameValue().equals(organizationLeaderResp.getRoleId())) {
                    if (CollectionUtils.isNotEmpty(organizationLeaderResp.getEmployeeIdList())) {
                        for (HrmsEmployeeResp hrmsEmployeeResp : organizationLeaderResp.getEmployeeIdList()) {
                            names.append(hrmsEmployeeResp.getEmployeeName()).append(",");
                        }
                    }
                }
            }

            if (names.length() > 0) {
                names.deleteCharAt(names.length() - 1);
                dictItem.setItemName(dictItem.getItemName() + "(" + names.toString() + ")");
            }

        }

        return PlatformResult.success(list);
    }
}
