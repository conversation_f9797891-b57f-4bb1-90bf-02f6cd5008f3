package cn.trasen.homs.base.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.extra.servlet.ServletUtil;
import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.RequestContent;
import cn.trasen.homs.base.model.CommInterfaceRegister;
import cn.trasen.homs.base.service.CommInterfaceRegisterService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommInterfaceRegisterController
 * @Description TODO
 * @date 2024��8��22�� ����7:02:57
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CommInterfaceRegisterController")
public class CommInterfaceRegisterController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommInterfaceRegisterController.class);

	@Autowired
	private CommInterfaceRegisterService commInterfaceRegisterService;

	/**
	 * @Title saveCommInterfaceRegister
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/interfaceRegister/save")
	public PlatformResult<String> saveCommInterfaceRegister(@RequestBody CommInterfaceRegister record) {
		try {
			commInterfaceRegisterService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommInterfaceRegister
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/interfaceRegister/update")
	public PlatformResult<String> updateCommInterfaceRegister(@RequestBody CommInterfaceRegister record) {
		try {
			commInterfaceRegisterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommInterfaceRegisterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommInterfaceRegister>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/interfaceRegister/{id}")
	public PlatformResult<CommInterfaceRegister> selectCommInterfaceRegisterById(@PathVariable String id) {
		try {
			CommInterfaceRegister record = commInterfaceRegisterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommInterfaceRegisterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/interfaceRegister/delete/{id}")
	public PlatformResult<String> deleteCommInterfaceRegisterById(@PathVariable String id) {
		try {
			commInterfaceRegisterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommInterfaceRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommInterfaceRegister>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/interfaceRegister/list")
	public DataSet<CommInterfaceRegister> selectCommInterfaceRegisterList(Page page, CommInterfaceRegister record) {
		return commInterfaceRegisterService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @param request
	 * @param requestContent
	 * @return
	 */
    @ApiOperation(value = "查询组织机构数据（接口登记接口）", notes = "查询组织机构数据（接口登记接口）")
    @PostMapping(value = "/api/ts-homs/interfaceRegister/getOrganization")
    public PlatformResult<List<OrganizationListSimpleRes>> getOrganization(HttpServletRequest request,@RequestBody RequestContent requestContent) {
    	
    	 try {
    		 	String clientIP = ServletUtil.getClientIP(request, null);
    		 	requestContent.setIpAddress(clientIP);
    		 	List<OrganizationListSimpleRes> list = commInterfaceRegisterService.getOrganization(requestContent);
    	    	return PlatformResult.success(list);
    	 } catch (Exception e) {
             e.printStackTrace();
             return PlatformResult.failure("查询科室数据失败,失败原因：" + e.getMessage());
         }
    }
    
    @ApiOperation(value = "查询人员数据（接口登记接口）", notes = "查询人员数据（接口登记接口）")
    @PostMapping(value = "/api/ts-homs/interfaceRegister/getEmployee")
    public PlatformResult<List<EmployeeListReq>> getEmployee(HttpServletRequest request,@RequestBody RequestContent requestContent) {
    	
    	 try {
    		 	String clientIP = ServletUtil.getClientIP(request, null);
    		 	requestContent.setIpAddress(clientIP);
    		 	List<EmployeeListReq> list = commInterfaceRegisterService.getEmployee(requestContent);
    	    	return PlatformResult.success(list);
    	 } catch (Exception e) {
             e.printStackTrace();
             return PlatformResult.failure("查询科室数据失败,失败原因：" + e.getMessage());
         }
    }
    
    @ApiOperation(value = "获取人员签名图片（接口登记接口）", notes = "获取人员签名图片（接口登记接口）")
    @PostMapping(value = "/api/ts-homs/interfaceRegister/getSignatureImg")
    public PlatformResult<List<Map<String,String>>> getSignatureImg(HttpServletRequest request,@RequestBody RequestContent requestContent) {
    	
    	 try {
    		 	String clientIP = ServletUtil.getClientIP(request, null);
    		 	requestContent.setIpAddress(clientIP);
    		 	List<Map<String,String>> list = commInterfaceRegisterService.getSignatureImg(requestContent);
    	    	return PlatformResult.success(list);
    	 } catch (Exception e) {
             e.printStackTrace();
             return PlatformResult.failure("查询科室数据失败,失败原因：" + e.getMessage());
         }
    }
    
    @ApiOperation(value = "北海妇幼同步人员信息", notes = "北海妇幼同步人员信息")
    @PostMapping(value = "/syncplatform/bhfyempsync")
    public PlatformResult<String> bhfyempsync() {
        try {
        	String str = commInterfaceRegisterService.bhfyempsync();
            return PlatformResult.success(str);
        } catch (Exception e) {
        	e.printStackTrace();
        }
        return PlatformResult.failure();
    }
}
