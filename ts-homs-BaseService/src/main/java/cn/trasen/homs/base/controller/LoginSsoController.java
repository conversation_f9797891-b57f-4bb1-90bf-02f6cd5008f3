package cn.trasen.homs.base.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.LoginSso;
import cn.trasen.homs.base.service.LoginSsoService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 外部系统 Controller层
 * @Date: 2020/4/3 15:32
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Api(tags = "外部系统Controller")
@RestController
public class LoginSsoController {

    private static final Logger logger = LoggerFactory.getLogger(LoginSsoController.class);

    @Autowired
    private LoginSsoService loginSsoService;

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增外部系统</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020/3/23 10:38 </p>
     */
    @ApiOperation(value = "新增外部系统", notes = "新增外部系统")
    @PostMapping("/loginSso/save")
    public PlatformResult<String> insert(@RequestBody LoginSso loginSso) {
        try {
            if (loginSsoService.insert(loginSso) > 0) {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改外部系统</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  下午4:48:58 </p>
     */
    @ApiOperation(value = "修改外部系统", notes = "修改外部系统")
    @PostMapping("/loginSso/update")
    public PlatformResult<String> update(@RequestBody LoginSso loginSso) {
        try {
            if (loginSsoService.update(loginSso) > 0) {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: deleteById</p>
     * <p> @Description: 删除外部系统</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  下午4:49:12 </p>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/loginSso/deletedById")
    public PlatformResult<String> deleteById(@RequestBody LoginSso loginSso) {
        try {
            if (loginSsoService.deleted(loginSso) > 0) {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
        return PlatformResult.failure();
    }


    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 获取外部系统列表</p>
     * <p> @Return: DataSet<documentChannel></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  下午4:49:35 </p>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/loginSso/list")
    public DataSet<LoginSso> getDataList(Page page, LoginSso loginSso) {
    	try {
	        List<LoginSso> list = loginSsoService.getDataList(page, loginSso);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }
    
    
    @ApiOperation(value = "批量更新", notes = "批量更新")
	@PostMapping("/loginSso/batchUpdate")
	public PlatformResult<String> batchUpdate(@RequestBody List<LoginSso> record) {
		try {
			loginSsoService.batchUpdate(record);
			return PlatformResult.success(null, "批量更新成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("修改失败,失败原因:"+e.getMessage());
		}
	}

}

