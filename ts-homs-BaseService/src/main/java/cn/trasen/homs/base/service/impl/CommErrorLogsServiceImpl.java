package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.mapper.CommErrorLogsMapper;
import cn.trasen.homs.base.model.CommErrorLogs;
import cn.trasen.homs.base.service.CommErrorLogsService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommErrorLogsServiceImpl
 * @Description TODO
 * @date 2024��5��15�� ����2:15:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommErrorLogsServiceImpl implements CommErrorLogsService {

	@Autowired
	private CommErrorLogsMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommErrorLogs record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommErrorLogs record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CommErrorLogs record = new CommErrorLogs();
		record.setId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CommErrorLogs selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommErrorLogs> getDataSetList(Page page, CommErrorLogs record) {
		List<CommErrorLogs> records = mapper.selectPageList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
