package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.RequestContent;
import cn.trasen.homs.base.model.CommInterfaceRegister;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommInterfaceRegisterService
 * @Description TODO
 * @date 2024��8��22�� ����7:02:57
 * <AUTHOR>
 * @version 1.0
 */
public interface CommInterfaceRegisterService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	Integer save(CommInterfaceRegister record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	Integer update(CommInterfaceRegister record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommInterfaceRegister
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	CommInterfaceRegister selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommInterfaceRegister>
	 * @date 2024��8��22�� ����7:02:57
	 * <AUTHOR>
	 */
	DataSet<CommInterfaceRegister> getDataSetList(Page page, CommInterfaceRegister record);

	/**
	 * 
	 * @param requestBody
	 * @return
	 */
	List<OrganizationListSimpleRes> getOrganization(RequestContent requestBody);

	/**
	 * 
	 * @param requestContent
	 * @return
	 */
	List<EmployeeListReq> getEmployee(RequestContent requestContent);
	
	void addSyncOrg(String orgId);
	
	void updateSyncOrg(String orgId);
	
	void addSyncEmp(String empId);

	void updateSyncEmp(String empId);

	String bhfyempsync();

	List<Map<String, String>> getSignatureImg(RequestContent requestContent);
}
