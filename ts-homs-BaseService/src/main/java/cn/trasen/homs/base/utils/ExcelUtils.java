package cn.trasen.homs.base.utils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.common.IOUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import cn.trasen.BootComm.excel.utils.ExcelData;
import cn.trasen.BootComm.excel.utils.ExcelWrite;
import cn.trasen.homs.base.model.HrmsEmployee;

/**
 * @ClassName: ExcelUtils
 * @Description: TODO
 * @Author: hezz#trasen.cn
 * @Date: 2019/7/18 8:29
 */
public class ExcelUtils {

    private transient static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

       

    public static String convertTemplatePath(String path) {
        Resource resource = new ClassPathResource(path);
        // 将模版文件写入到系统临时目录
        String folder = System.getProperty("java.io.tmpdir");
        System.out.println(System.getProperty("java.io.tmpdir"));
        File tempFile = new File(folder + File.separator + path);
        // System.out.println("文件路径：" + tempFile.getPath());
        // 文件存在时 不再写入
        if (tempFile.exists()) {
            if(!tempFile.delete()){
            	logger.info("文件删除失败");
            }
        }
        File parentFile = tempFile.getParentFile();
        // 判断父文件夹是否存在
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        try ( BufferedInputStream bufferedInputStream = new BufferedInputStream(resource.getInputStream());
        		FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
        		){
            byte[] buffer = new byte[10240];
            int len = 0;
            while ((len = bufferedInputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String filepath = tempFile.getPath();
        return filepath;
    }

    
    
    public static void exportByChooseFields(HrmsEmployee hrmsEmployee, HttpServletResponse response,List<Map<String, String>> list,String name, String templateUrl) {
        // 创建excel导出
        OutputStream out2 = null;
        try {
            ExcelData data = new ExcelData();

            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

            // 查询页面所选的导出字段
            Map<String, String> fieldMap = new LinkedHashMap<String, String>();
            
            if(StringUtils.isNotBlank(hrmsEmployee.getExportFields())) {
            	
            	String[] strarr = hrmsEmployee.getExportFields().split(",");
            	
            	if(null!=strarr && strarr.length>0) {
            		
            		for(String fieldName : strarr) {
            			
            			if(StringUtils.isNotBlank(fieldName) && fieldName.contains("=")) {
            				
            				String[] field = fieldName.split("=");
            				
            				if(null!=field && field.length>0) { 
            					
            					fieldMap.put(field[0], field[1]);
            				}
            			} 
            		}
            	}
            }

            // 设置Excel表头 导出的字段value
            List<String> titlesValue = new ArrayList<>();
            // 导出的字段名称
            List<String> exportFiled = new ArrayList<>();
            // 解析模板Excel
            for (String key : fieldMap.keySet()) {
                titlesValue.add(key);
                exportFiled.add(fieldMap.get(key));
            }

            data.setTitles(titlesValue);
            
            List<String[]> datas = new ArrayList<String[]>();
            
            for (Map<String, String> ot : list) {
            	
                String[] d = new String[titlesValue.size()];
                //遍历所选的字段
                for (int index = 0; index < exportFiled.size(); index++) {
                    
                	if(null!=ot.get(exportFiled.get(index))) {
                		//由于查询出来的是一个map mybatis对于返回值为map时,没有强制去转换类型,所以map的泛型其实是没起作用的
                		try {
                			Object objot = ot.get(exportFiled.get(index));
							d[index] = objot.toString();
						} catch (Exception e) {
							e.printStackTrace();
							d[index] = ot.get(exportFiled.get(index));
						}
                		
                		
                		
                	}else {
                		
                		 d[index] = "";
                	}
                	
                }
                datas.add(d);
            }
            name = java.net.URLEncoder.encode(name, "UTF8");
            response.setContentType(CONTENT_TYPE);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name);

            data.setDatas(datas);
            ExcelWrite inst = ExcelWrite.getInst();
            out2 = response.getOutputStream();
            inst.write(data, out2);
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }finally {
        	try {
				out2.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
//            IOUtil.close(out2);
        }
    }
}
