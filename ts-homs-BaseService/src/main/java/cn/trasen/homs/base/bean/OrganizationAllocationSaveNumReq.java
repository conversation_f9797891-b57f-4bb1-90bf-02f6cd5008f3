package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 13:28
 * @description
 */

@Data
public class OrganizationAllocationSaveNumReq {
    @ApiModelProperty(value = "机构Id")
    String orgId;

    @ApiModelProperty(value = "床位数量")
    Integer bedNum;


    @ApiModelProperty(value = "岗位定编人数")
    List<PersonalIdentityNum> PersonalIdentityNumList;

    @Data
    public static class PersonalIdentityNum
    {
        @ApiModelProperty(value = "岗位Id 比如 personalIdentity_4")
        String personalIdentityCode;
        @ApiModelProperty(value = "岗位定编人数")
        Integer num;

    }

}