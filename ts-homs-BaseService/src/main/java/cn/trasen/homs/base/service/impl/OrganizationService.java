package cn.trasen.homs.base.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.google.common.collect.Maps;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.trasen.homs.base.bean.EmployeeBean;
import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.OrgChildrenEmplListReq;
import cn.trasen.homs.base.bean.OrgChildrenEmplListRes;
import cn.trasen.homs.base.bean.OrganizationChildrenListResp;
import cn.trasen.homs.base.bean.OrganizationFrameworkResp;
import cn.trasen.homs.base.bean.OrganizationImport;
import cn.trasen.homs.base.bean.OrganizationLeaderListReq;
import cn.trasen.homs.base.bean.OrganizationLeaderResp;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.bean.OrganizationListResp;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.OrganizationMergeReq;
import cn.trasen.homs.base.bean.OrganizationMoveReq;
import cn.trasen.homs.base.bean.OrganizationReq;
import cn.trasen.homs.base.bean.OrganizationSaveReq;
import cn.trasen.homs.base.bean.OrganizationSplitReq;
import cn.trasen.homs.base.bo.OrgEmpInBO;
import cn.trasen.homs.base.bo.OrgEmpOutBO;
import cn.trasen.homs.base.dto.ReplaceLeader;
import cn.trasen.homs.base.mapper.CommOrganizationContactsMapper;
import cn.trasen.homs.base.mapper.OrganizationMapper;
import cn.trasen.homs.base.mapper.ToaEmployeeMapper;
import cn.trasen.homs.base.model.CommOrganizationContacts;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.model.OrganizationAllocation;
import cn.trasen.homs.base.model.OrganizationLeader;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationAllocationService;
import cn.trasen.homs.base.service.IOrganizationLeaderService;
import cn.trasen.homs.base.service.IOrganizationService;
import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.bean.ThpsUser;
//import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.OAEmployeeFeignService;
import cn.trasen.homs.feign.sso.DeptFeignService;
import lombok.Data;
import lombok.extern.log4j.Log4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;


/**
 * @description:组织架构
 * @return:
 * @author: liyuan
 * @createTime: 2021/7/26 11:12
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Log4j
public class OrganizationService implements IOrganizationService {


    @Autowired
    HrmsEmployeeService hrmsEmployeeService;


    @Autowired
    OrganizationMapper organizationMapper;

    @Autowired
    IDictItemService dictItemService;


    @Autowired
    DeptFeignService deptFeignService;


    @Autowired
    IOrganizationLeaderService organizationLeaderService;

    @Autowired
    IOrganizationAllocationService organizationAllocationService;

    @Autowired
    OAEmployeeFeignService oaEmployeeFeignService;

    @Autowired
    ToaEmployeeMapper toaEmployeeMapper;

    @Autowired
    CommOrganizationContactsMapper commOrganizationContactsMapper;

    @Data
    class InsideClass {
        Map<String, String> zzlxDict = null;
        Map<String, String> ldjsDict = null;

        List<Organization> organizationsAll = null;
        List<HrmsEmployee> hrmsEmployeeList = null;
        Map<String, Integer> OrgEmployeeNum = new HashMap<>();
        List<OrganizationLeader> organizationLeaderList = null;

        public void init() {
            OrganizationLeaderListReq organizationLeaderListReq = new OrganizationLeaderListReq();
            init(organizationLeaderListReq);
        }


        public void init(OrganizationLeaderListReq organizationLeaderListReq) {
            zzlxDict = dictItemService.convertDictMap("zzlx");
            ldjsDict = dictItemService.convertDictMap("LDJS");
            Example example = new Example(Organization.class);
            example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            organizationsAll = organizationMapper.selectByExample(example);
            hrmsEmployeeList = hrmsEmployeeService.getEmployeeBaseList(new EmployeeListReq());
            organizationLeaderList = organizationLeaderService.getBaseList(organizationLeaderListReq);
        }

    }


    @Override
    /**
     * @description: 获取组织架构图
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationFrameworkResp>
     * @author: liyuan
     * @createTime: 2021/8/5 10:39
     */
    public List<OrganizationFrameworkResp> getFramework(OrganizationListReq organizationListReq) {

        Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        criteria.andEqualTo("isEnable", EnableEnum.Y.getKey());
        if (!StringUtils.isBlank(organizationListReq.getName())) {
            criteria.andLike("name", organizationListReq.getName());
        }

        List<Organization> organizations = organizationMapper.selectByExample(example);


        List<OrganizationFrameworkResp> organizationFrameworkRespArrayList = new ArrayList<>();


        InsideClass insideClass = new InsideClass();
        OrganizationLeaderListReq organizationLeaderListReq = new OrganizationLeaderListReq();
        organizationLeaderListReq.setShowFramework(1);
        organizationLeaderListReq.setShowEmployeeIdsIsNull(false);
        insideClass.init(organizationLeaderListReq);


        List<OrganizationAllocation> organizationAllocationList = organizationAllocationService.getBaseList();

        for (Organization g : organizations) {
            OrganizationFrameworkResp organizationFrameworkResp = new OrganizationFrameworkResp();
            BeanUtil.copyProperties(g, organizationFrameworkResp);

            organizationFrameworkResp.setEmployeeNum(getEmployeeNum(getNextOrgIds(organizationFrameworkResp.getOrganizationId(), insideClass.getOrganizationsAll()), insideClass.getHrmsEmployeeList(), insideClass.getOrgEmployeeNum()));

            if (StringUtils.isBlank(organizationFrameworkResp.getOrgFlag())) {
                organizationFrameworkResp.setOrgFlag("0");
            }

            organizationFrameworkResp.setOrgFlagLable(insideClass.getZzlxDict().getOrDefault(organizationFrameworkResp.getOrgFlag(), "其他"));

            organizationFrameworkResp.setLeaders(
                    getLeadersList(organizationFrameworkResp.getOrganizationId(), insideClass.getOrganizationLeaderList(), insideClass.getLdjsDict())

                    //	organizationLeaderService.getList(organizationFrameworkResp.getOrganizationId())


            );

            organizationFrameworkResp.setAllocationNum(0);
            for (OrganizationAllocation organizationAllocation : organizationAllocationList) {
                if (organizationFrameworkResp.getOrganizationId().equals(organizationAllocation.getOrgId())) {
                    organizationFrameworkResp.setAllocationNum(organizationFrameworkResp.getAllocationNum() + organizationAllocation.getNum());
                }
            }


            organizationFrameworkResp.setContrast(organizationFrameworkResp.getEmployeeNum() - organizationFrameworkResp.getAllocationNum());

            organizationFrameworkRespArrayList.add(organizationFrameworkResp);
        }


        List<OrganizationFrameworkResp> organizationFrameworkRespList = new ArrayList<>();
        for (OrganizationFrameworkResp o : organizationFrameworkRespArrayList) {
            if (StringUtils.isBlank(o.getParentId())) {
                AddChildrenListResp(o, organizationFrameworkRespArrayList);
                organizationFrameworkRespList.add(o);
            }
        }

        //return organizationChildrenListRespNew;

        return organizationFrameworkRespList;
    }


    @Override
    /**
     * @description: 返回机构数列表
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationChildrenListResp>
     * @author: liyuan
     * @createTime: 2021/7/26 11:25
     */
    public List<OrganizationChildrenListResp> getChildrenList(OrganizationListReq organizationListReq) {

        Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (!StringUtils.isBlank(organizationListReq.getName())) {
            criteria.andLike("name", organizationListReq.getName());
        }

        if (!StringUtils.isBlank(organizationListReq.getIsEnable())) {
            criteria.andEqualTo("isEnable", organizationListReq.getIsEnable());
        }
        example.orderBy("seqNo").asc().orderBy("createDate").desc();
        List<Organization> organizations = organizationMapper.selectByExample(example);


        List<OrganizationChildrenListResp> organizationChildrenListRespList = new ArrayList<>();


        InsideClass insideClass = new InsideClass();
        insideClass.init();

        for (Organization g : organizations) {
            if (g.getParentId() == null) {
                g.setParentId("");
            }
            OrganizationChildrenListResp organizationChildrenListResp = new OrganizationChildrenListResp();
            BeanUtil.copyProperties(g, organizationChildrenListResp);

            organizationChildrenListResp.setEmployeeNum(getEmployeeNum(getNextOrgIds(organizationChildrenListResp.getOrganizationId(), insideClass.getOrganizationsAll()), insideClass.getHrmsEmployeeList(), insideClass.getOrgEmployeeNum()));

            if (StringUtils.isBlank(organizationChildrenListResp.getOrgFlag())) {
                organizationChildrenListResp.setOrgFlag("0");
            }

            organizationChildrenListResp.setOrgFlagLable(insideClass.getZzlxDict().getOrDefault(organizationChildrenListResp.getOrgFlag(), "其他"));

            organizationChildrenListResp.setIsEnableLable(EnableEnum.getValByKey(organizationChildrenListResp.getIsEnable()));


            //	organizationChildrenListResp.setLeaders(organizationLeaderService.getList(organizationChildrenListResp.getOrganizationId()));
            organizationChildrenListResp.setLeaders(
                    getLeadersList(organizationChildrenListResp.getOrganizationId(), insideClass.getOrganizationLeaderList(), insideClass.getLdjsDict()));

            organizationChildrenListRespList.add(organizationChildrenListResp);
        }

        if (!StringUtils.isBlank(organizationListReq.getName())) {
            return organizationChildrenListRespList;
        }

        List<OrganizationChildrenListResp> organizationChildrenListRespNew = new ArrayList<>();
        for (OrganizationChildrenListResp organizationChildrenListResp : organizationChildrenListRespList) {
            if (StringUtils.isBlank(organizationChildrenListResp.getParentId())) {
                AddChildrenListResp(organizationChildrenListResp, organizationChildrenListRespList);
                organizationChildrenListRespNew.add(organizationChildrenListResp);
            }
        }

        return organizationChildrenListRespNew;

    }

    public List<Organization> getChildrenList(OrganizationReq organizationReq) {
        // 先通过 name 查询出目标组织
        Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        if (!StringUtils.isBlank(organizationReq.getEqName())) {
            criteria.andLike("name", "%" + organizationReq.getEqName() + "%");
        }
        //根据当前账号机构编码过滤
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.orderBy("seqNo").asc().orderBy("createDate").desc();
        List<Organization> organizations = organizationMapper.selectByExample(example);


        List<Organization> res = new ArrayList<>();

        if (organizations.size() > 0) {
            // 先把自己本身塞进去
            res.addAll(organizations);
            for (Organization org : organizations) {
                Example exampleSon = new Example(Organization.class);
                Criteria criteriaSon = exampleSon.createCriteria();
                criteriaSon.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
                criteriaSon.andLike("treeIds", "%" + org.getOrganizationId() + "%");
                //根据当前账号机构编码过滤
                criteriaSon.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                List<Organization> organizationsSon = organizationMapper.selectByExample(exampleSon);
                if (organizationsSon.size() > 0) {
                    // 把所有子集塞进去
                    res.addAll(organizationsSon);
                }
            }
        }
        return res;
    }


    @Override
    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */
    public List<Organization> getBaseList(OrganizationListReq organizationListReq) {
        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();


        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (!StringUtils.isBlank(organizationListReq.getName())) {
            criteria.andLike("name", "%" + organizationListReq.getName() + "%");
        }
        if (!StringUtils.isBlank(organizationListReq.getParentId())) {
            Example.Criteria criteriaParent = example.createCriteria();
            criteriaParent.orEqualTo("organizationId", organizationListReq.getParentId()).orEqualTo("parentId", organizationListReq.getParentId());
            example.and(criteriaParent);
        }
        if (!StringUtils.isBlank(organizationListReq.getEqParentId())) {
            criteria.andEqualTo("parentId", organizationListReq.getEqParentId());
        }
        return organizationMapper.selectByExample(example);
    }


    @Override
    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    public List<OrganizationListSimpleRes> getSimpleList(OrganizationListReq organizationListReq) {
        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (!StringUtils.isBlank(organizationListReq.getEqName())) {
            criteria.andEqualTo("name", organizationListReq.getEqName());
        }
        if (!StringUtils.isBlank(organizationListReq.getTel())) {
            criteria.andLike("tel", "%" + organizationListReq.getTel() + "%");
        }
        if (!StringUtils.isBlank(organizationListReq.getIsEnable())) {
            criteria.andEqualTo("isEnable", organizationListReq.getIsEnable());
        }
        List<Organization> organizationList = organizationMapper.selectByExample(example);
        List<OrganizationListSimpleRes> organizationListSimpleResList = BeanUtil.copyToList(organizationList, OrganizationListSimpleRes.class);
        EmployeeListReq employeeListReq = new EmployeeListReq();
        List<HrmsEmployee> hrmsEmployees = hrmsEmployeeService.getEmployeeBaseList(employeeListReq);

        organizationListSimpleResList.forEach(org -> {
            if (org.getEmployeeNum() == null) {
                org.setEmployeeNum(0);
            } else {
                hrmsEmployees.forEach(emp -> {
                    if (emp.getOrgId().equals(org.getOrganizationId())) {
                        org.setEmployeeNum(org.getEmployeeNum() + 1);
                    }
                });
            }
        });
        return organizationListSimpleResList;
    }

    @Override
    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    public DataSet<OrganizationListSimpleRes> pageSimple(Page page, OrganizationListReq organizationListReq) {
        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (!StringUtils.isBlank(organizationListReq.getEqName())) {
            criteria.andLike("name", "%" + organizationListReq.getEqName() + "%");
        }
        if (!StringUtils.isBlank(organizationListReq.getTel())) {
            criteria.andLike("tel", "%" + organizationListReq.getTel() + "%");
        }
        if (!StringUtils.isBlank(organizationListReq.getIsEnable())) {
            criteria.andEqualTo("isEnable", organizationListReq.getIsEnable());
        }
        List<Organization> organizationList = organizationMapper.selectByExampleAndRowBounds(example, page);
        List<OrganizationListSimpleRes> organizationListSimpleResList = BeanUtil.copyToList(organizationList, OrganizationListSimpleRes.class);
        EmployeeListReq employeeListReq = new EmployeeListReq();
        List<HrmsEmployee> hrmsEmployees = hrmsEmployeeService.getEmployeeBaseList(employeeListReq);

        organizationListSimpleResList.forEach(org -> {
            if (org.getEmployeeNum() == null) {
                org.setEmployeeNum(0);
            }
            hrmsEmployees.forEach(emp -> {
                if (emp.getOrgId().equals(org.getOrganizationId())) {
                    org.setEmployeeNum(org.getEmployeeNum() + 1);
                    return;
                }
            });
        });
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), organizationListSimpleResList);
    }

    @Override
    /**
     * @description: 获取基础信息
     * @param: organizationListReq
     * @return: cn.trasen.basicsbottom.model.Organization
     * @author: liyuan
     * @createTime: 2021/8/4 16:58
     */
    public Organization getBase(OrganizationReq organizationReq) {
        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();


        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if(ObjectUtils.isEmpty(organizationReq.getSsoOrgCode())){
        	criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        }

        if (!StringUtils.isBlank(organizationReq.getEqName())) {
            criteria.andEqualTo("name", organizationReq.getEqName());
        }
        
        if (!StringUtils.isBlank(organizationReq.getEqCode())) {
            criteria.andEqualTo("code", organizationReq.getEqCode());
        }

        List<Organization> organizations = organizationMapper.selectByExample(example);
        if (organizations.size() > 0) {
            return organizations.get(0);
        }
        return null;
    }


    @Override
    /**
     * 根据ID批量获取组织结构
     * @param idList
     * @return java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * <AUTHOR>
     * @date 2021/10/25 19:26
     */
    public List<OrganizationListResp> getListByIds(List<String> idList) {
        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        criteria.andIn("organizationId", idList);

        List<Organization> organizations = organizationMapper.selectByExample(example);

        List<OrganizationListResp> organizationListRespList = new ArrayList<>();

        organizationListRespList = BeanUtil.copyToList(organizations, OrganizationListResp.class);

        return organizationListRespList;
    }


    @Override
    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */

    public DataSet<OrganizationListResp> getAllPageList(Page page, OrganizationListReq organizationListReq) {
      /*  Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        if (StringUtils.isBlank(organizationListReq.getName()) == false) {
            criteria.andLike("name", "%" + organizationListReq.getName() + "%");
        }
        if (StringUtils.isBlank(organizationListReq.getParentId()) == false) {
            Example.Criteria criteriaParent = example.createCriteria();
            criteriaParent.orEqualTo("organizationId", organizationListReq.getParentId()).orEqualTo("parentId", organizationListReq.getParentId());
            example.and(criteriaParent);
        }

        example.orderBy("orgLevel").asc().orderBy("seqNo").asc().orderBy("createDate").desc();*/

        organizationListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        List<Organization> organizations = organizationMapper.getAllPageList(page, organizationListReq);


        List<OrganizationListResp> organizationListResps = new ArrayList<>();


        InsideClass insideClass = new InsideClass();
        insideClass.init();
        for (Organization g : organizations) {
            OrganizationListResp organizationListResp = new OrganizationListResp();
            BeanUtil.copyProperties(g, organizationListResp);

            fullData(organizationListResp, insideClass);

            organizationListResps.add(organizationListResp);


        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), organizationListResps);
    }


    @Override
    /**
     * @description: 获取组织机构分页列表
     * @param: page
     * @param: organizationListReq
     * @return: java.util.List<cn.trasen.basicsbottom.bean.OrganizationListResp>
     * @author: liyuan
     * @createTime: 2021/7/28 11:34
     */
    public OrganizationListResp getAll(String orgId) {

        Organization organizations = organizationMapper.selectByPrimaryKey(orgId);

        OrganizationListResp organizationListResp = new OrganizationListResp();
        BeanUtil.copyProperties(organizations, organizationListResp);

        InsideClass insideClass = new InsideClass();
        insideClass.init();

        fullData(organizationListResp, insideClass);

        return organizationListResp;
    }

//	Map<String, String> zzlxDict=null;
//	List<Organization> organizationsAll=null;
//	List<HrmsEmployee> hrmsEmployeeList=null;

    private void fullData(OrganizationListResp organizationListResp, InsideClass insideClass) {

        organizationListResp.setEmployeeNum(getEmployeeNum(getNextOrgIds(organizationListResp.getOrganizationId(), insideClass.getOrganizationsAll()), insideClass.getHrmsEmployeeList(), insideClass.getOrgEmployeeNum()));

        if (StringUtils.isBlank(organizationListResp.getOrgFlag())) {
            organizationListResp.setOrgFlag("0");
        }

        organizationListResp.setOrgFlagLable(insideClass.getZzlxDict().getOrDefault(organizationListResp.getOrgFlag(), "其他"));

        organizationListResp.setIsEnableLable(EnableEnum.getValByKey(organizationListResp.getIsEnable()));

        organizationListResp.setHaveChildren(isChildren(organizationListResp.getOrganizationId(), insideClass.getOrganizationsAll()));

        organizationListResp.setParentName(getOrgName(organizationListResp.getParentId(), insideClass.getOrganizationsAll()));
//		Organization parentOrg = organizationMapper.selectByPrimaryKey(organizationListResp.getParentId());
//		if (parentOrg != null) {
//			organizationListResp.setParentName(parentOrg.getName());
//
//		}


        organizationListResp.setLeaders(
                getLeadersList(organizationListResp.getOrganizationId(), insideClass.getOrganizationLeaderList(), insideClass.getLdjsDict()));


        //organizationListResp.setLeaders(organizationLeaderService.getList(organizationListResp.getOrganizationId()));

    }

    private void AddChildrenListResp(OrganizationFrameworkResp topOrganizationFrameworkResp,
                                     List<OrganizationFrameworkResp> organizationFrameworkRespList) {
        for (OrganizationFrameworkResp d : organizationFrameworkRespList) {
            if (d.getParentId().equals(topOrganizationFrameworkResp.getOrganizationId())) {
                if (topOrganizationFrameworkResp.getChildren() == null) {
                    topOrganizationFrameworkResp.setChildren(new ArrayList<>());
                }
                topOrganizationFrameworkResp.getChildren().add(d);
                AddChildrenListResp(d, organizationFrameworkRespList);
            }
        }
    }

    private void AddChildrenListResp(OrganizationChildrenListResp topOrganizationChildrenListResp,
                                     List<OrganizationChildrenListResp> organizationChildrenListResps) {
        for (OrganizationChildrenListResp d : organizationChildrenListResps) {
            if (d.getParentId().equals(topOrganizationChildrenListResp.getOrganizationId())) {
                if (topOrganizationChildrenListResp.getChildren() == null) {
                    topOrganizationChildrenListResp.setChildren(new ArrayList<>());
                }
                topOrganizationChildrenListResp.getChildren().add(d);
                AddChildrenListResp(d, organizationChildrenListResps);
            }
        }
    }

    /**
     * @description: 获取下级所有的机构ID
     * @param: orgId
     * @param: organizationList
     * @return: java.util.List<java.lang.String>
     * @author: liyuan
     * @createTime: 2021/7/26 14:18
     */
    private List<String> getNextOrgIds(String orgId, List<Organization> organizationList) {
        List<String> orgIdList = new ArrayList<>();
        for (Organization organization : organizationList) {
            if (("," + organization.getTreeIds() + ",").contains("," + orgId + ",")) {
                orgIdList.add(organization.getOrganizationId());
            }
        }
        return orgIdList;
    }


    /**
     * @description: 获取下级所有组织机构
     * @param: orgId
     * @param: organizationList
     * @return: java.util.List<cn.trasen.basicsbottom.model.Organization>
     * @author: liyuan
     * @createTime: 2021/7/27 15:58
     */
    private List<Organization> getNextOrg(String orgId, List<Organization> organizationList) {
        List<Organization> orgIdList = new ArrayList<>();
        for (Organization organization : organizationList) {
            if (("," + organization.getTreeIds() + ",").contains("," + orgId + ",")) {
                orgIdList.add(organization);
            }
        }
        return orgIdList;
    }


    @Override
    /**
     * @description: 获取下级所有组织机构
     * @param: orgId
     * @param: organizationList
     * @return: java.util.List<cn.trasen.basicsbottom.model.Organization>
     * @author: liyuan
     * @createTime: 2021/7/27 15:58
     */
    public List<Organization> getNextOrg(String orgId) {
        List<Organization> orgIdList = new ArrayList<>();
        OrganizationListReq organizationListReq = new OrganizationListReq();
        List<Organization> organizationList = getBaseList(organizationListReq);
        for (Organization organization : organizationList) {
            if (("," + organization.getTreeIds() + ",").contains("," + orgId + ",")) {
                orgIdList.add(organization);
            }
        }
        return orgIdList;
    }

    /**
     * @description: 获取机构下员工数量
     * @param: orgIds
     * @param: employeeBaseList
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/7/27 9:17
     */
    private Integer getEmployeeNum(List<String> orgIds, List<HrmsEmployee> employeeBaseList, Map<String, Integer> OrgEmployeeNum) {
        Integer employeeNum = 0;
        for (String orgId : orgIds) {
            if (OrgEmployeeNum.containsKey(orgId)) {
                employeeNum = employeeNum + OrgEmployeeNum.getOrDefault(orgId, 0);
            } else {
                Integer cNum = 0;
                for (HrmsEmployee hrmsEmployee : employeeBaseList) {
                    if (orgId.equals(hrmsEmployee.getOrgId())) {
                        cNum++;
                    }
                }
                employeeNum = employeeNum + cNum;
                OrgEmployeeNum.put(orgId, cNum);
            }
        }
        return employeeNum;
    }


    private List<OrganizationLeaderResp> getLeadersList(String orgId, List<OrganizationLeader> AllOrganizationLeaderList, Map<String, String> LDJSDict) {
        List<OrganizationLeaderResp> organizationLeaderRespList = new ArrayList<>();
        List<OrganizationLeader> organizationLeaderList = new ArrayList<>();
        for (OrganizationLeader o : AllOrganizationLeaderList) {
            if (o.getOrgId().equals(orgId)) {
                organizationLeaderList.add(o);
            }
        }
        for (OrganizationLeader organizationLeader : organizationLeaderList) {
            OrganizationLeaderResp organizationLeaderResp = new OrganizationLeaderResp();
            BeanUtil.copyProperties(organizationLeader, organizationLeaderResp);
            organizationLeaderResp.setRoleName(LDJSDict.getOrDefault(organizationLeaderResp.getRoleId(), ""));
            if (!StringUtils.isBlank(organizationLeader.getEmployeeIds())) {
                organizationLeaderResp.setEmployeeIdList(hrmsEmployeeService.getEmployeeDetailByCodes(new ArrayList<>(Arrays.asList(organizationLeader.getEmployeeIds().split(",")))));
            }
            organizationLeaderRespList.add(organizationLeaderResp);
        }
        //按照领导角色排序
        List<DictItem> ldjsList = dictItemService.getDictItemByTypeCode("LDJS");
        Collections.reverse(ldjsList);
        List<OrganizationLeaderResp> reList = new ArrayList<>();
        for (DictItem item : ldjsList) {
            for (OrganizationLeaderResp resp : organizationLeaderRespList) {
                if(StrUtil.equals(item.getItemName(),resp.getRoleName())){
                    reList.add(resp);
                }
            }
        }
        return reList;
    }


    @Transactional(rollbackFor = Exception.class)
    public Organization add(OrganizationSaveReq organizationAddReq, boolean syn) {
        OrganizationReq organizationReq = new OrganizationReq();
        organizationReq.setEqName(organizationAddReq.getName());
        Organization organizationOld = getBase(organizationReq);
        if (organizationOld != null) {
            throw new BusinessException("组织机构名已经存在不可以添加");
        }
        Organization organization = new Organization();
        organization.setCreateDate(new Date());
        organization.setUpdateDate(organization.getCreateDate());
        if (StringUtils.isBlank(organizationAddReq.getOrganizationId())) {
            String organizationId = String.valueOf(IdWork.id.nextId());
            organization.setOrganizationId(organizationId.substring(organizationId.length() - 8));
        }else {
        	organization.setOrganizationId(organizationAddReq.getOrganizationId());
        }
        organization.setCreateUser(UserInfoHolder.getCurrentUserCode());
        organization.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        organization.setIsDeleted(Contants.IS_DELETED_FALSE);
        organization.setIsEnable(EnableEnum.Y.getKey());
        organization.setName(organizationAddReq.getName());
        organization.setParentId(organizationAddReq.getParentId());
        organization.setOrgFlag(organizationAddReq.getOrgFlag());
        organization.setRemark(organizationAddReq.getRemark());
        if(!ObjectUtils.isEmpty(organization.getCode())){
        	organization.setCode(organization.getOrganizationId());
        }
        organization.setTel(organizationAddReq.getTel());
        organization.setCustomCode(organizationAddReq.getCustomCode());
        organization.setOrgType(organizationAddReq.getOrgType());

        if (StringUtils.isNotBlank(organization.getParentId())) { // 树ID
            Organization parentOrg = organizationMapper.selectByPrimaryKey(organization.getParentId());
            organization.setTreeIds(parentOrg.getTreeIds() + "," + organization.getOrganizationId());
        } else {
            organization.setTreeIds(organization.getOrganizationId());
        }
        organization.setOrgLevel(organization.getTreeIds().split(",").length);
        if(ObjectUtil.isEmpty(organization.getSsoOrgCode())){
        	organization.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        }
        organizationMapper.insertSelective(organization);

        organizationLeaderService.saveLeaders(organization.getOrganizationId(), organizationAddReq.getLeaders());

        if (syn) {
            synSystemDept(organization);
        }
        return organization;
    }

    @Override
    /**
     * @description: 新增
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization add(OrganizationSaveReq organizationAddReq) {
        return add(organizationAddReq, true);
    }


    @Override
    /**
     * @description: 修改
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(OrganizationSaveReq organizationAddReq) {
        Organization oldOrganization = organizationMapper.selectByPrimaryKey(organizationAddReq.getOrganizationId());
        Organization organizationUpdate = new Organization();
        organizationUpdate.setUpdateDate(new Date());
        organizationUpdate.setOrganizationId(organizationAddReq.getOrganizationId());
        organizationUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        organizationUpdate.setTel(organizationAddReq.getTel());
        if (!StringUtils.isBlank(organizationAddReq.getName())) {
            organizationUpdate.setName(organizationAddReq.getName());
        }
        if (!StringUtils.isBlank(organizationAddReq.getParentId())) {
            organizationUpdate.setParentId(organizationAddReq.getParentId());
        }
        if (!StringUtils.isBlank(organizationAddReq.getOrgFlag())) {
            organizationUpdate.setOrgFlag(organizationAddReq.getOrgFlag());
        }
        if (!StringUtils.isBlank(organizationAddReq.getRemark())) {
            organizationUpdate.setRemark(organizationAddReq.getRemark());
        }
        if (StringUtils.isNotBlank(organizationUpdate.getParentId())) { // 树ID
            Organization parentOrg = organizationMapper.selectByPrimaryKey(organizationUpdate.getParentId());
            organizationUpdate.setTreeIds(parentOrg.getTreeIds() + "," + organizationUpdate.getOrganizationId());
        } else {
            organizationUpdate.setTreeIds(organizationUpdate.getOrganizationId());
        }
        if (!StringUtils.isBlank(organizationAddReq.getDoctorSta())) {
            organizationUpdate.setDoctorSta(organizationAddReq.getDoctorSta());
        }

        if (!StringUtils.isBlank(organizationAddReq.getDoctorThree())) {
            organizationUpdate.setDoctorThree(organizationAddReq.getDoctorThree());
        }

        if (!StringUtils.isBlank(organizationAddReq.getNurseSta())) {
            organizationUpdate.setNurseSta(organizationAddReq.getNurseSta());
        }

        if (!StringUtils.isBlank(organizationAddReq.getNurseThree())) {
            organizationUpdate.setNurseThree(organizationAddReq.getNurseThree());
        }

        if (!StringUtils.isBlank(organizationAddReq.getCustomCode())) {
            organizationUpdate.setCustomCode(organizationAddReq.getCustomCode());
        }
        organizationUpdate.setOrgLevel(organizationUpdate.getTreeIds().split(",").length);
        organizationUpdate.setOrgType(organizationAddReq.getOrgType());
        
        if(StringUtils.isNotBlank(organizationAddReq.getSort())) {
        	 organizationUpdate.setSort(Integer.valueOf(organizationAddReq.getSort()));
        }
       
        organizationMapper.updateByPrimaryKeySelective(organizationUpdate);

        if (!oldOrganization.getParentId().equals(organizationUpdate.getParentId())) {
            updateOrgTreeIds(oldOrganization.getOrganizationId());
        }

        organizationLeaderService.saveLeaders(organizationUpdate.getOrganizationId(), organizationAddReq.getLeaders());

        synSystemDept(organizationUpdate);

        //更新人员排序
        if (CollectionUtils.isNotEmpty(organizationAddReq.getHrmsEmployeeList())) {

            List<HrmsEmployeeResp> hrmsEmployeeList = organizationAddReq.getHrmsEmployeeList();
            for (HrmsEmployeeResp hrmsEmployeeResp : hrmsEmployeeList) {
                HrmsEmployee hrmsEmployee = new HrmsEmployee();
                hrmsEmployee.setEmployeeId(hrmsEmployeeResp.getEmployeeId());
                hrmsEmployee.setEmpSort(hrmsEmployeeResp.getEmpSort());
                hrmsEmployeeService.update(hrmsEmployee);
            }

        }
    }

    @Override
    /**
     * @description: 删除
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String orgId) {
        delete(orgId, true);
    }

    @Override
    /**
     * @description: 删除
     * @param: organizationAddReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:46
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String orgId, boolean isVerify) {

        if (isVerify) {
            verifyEnable(orgId);
        }
        Organization organizationUpdate = new Organization();
        organizationUpdate.setOrganizationId(orgId);
        organizationUpdate.setUpdateDate(new Date());
        organizationUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        organizationUpdate.setIsDeleted(Contants.IS_DELETED_TURE);
        organizationMapper.updateByPrimaryKeySelective(organizationUpdate);

        synSystemDept(organizationUpdate);
    }


    @Override
    /**
     * @description: 合并
     * @param: organizationMergeReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:06
     */
    @Transactional(rollbackFor = Exception.class)
    public void merge(OrganizationMergeReq organizationMergeReq) {

        if (organizationMergeReq.getMergeOrganizationId() == null) {
            throw new BusinessException("合并组织不能为空！");
        }
        if (organizationMergeReq.getMergeOrganizationId().size() < 2) {
            throw new BusinessException("合并组织必须大于2个以上！");

        }

        EmployeeListReq record = new EmployeeListReq();


        OrganizationSaveReq organizationAddReq = new OrganizationSaveReq();
        BeanUtil.copyProperties(organizationMergeReq, organizationAddReq);
        organizationAddReq.setLeaders(null);
        Organization organization = add(organizationAddReq);

        List<String> employeeIdList = new ArrayList<>();
        for (String s : organizationMergeReq.getMergeOrganizationId()) {
            record.setEqOrgId(s);
            List<HrmsEmployee> hrmsEmployees = hrmsEmployeeService.getEmployeeBaseList(record);
            employeeIdList.addAll(hrmsEmployees.stream().map(emp -> emp.getEmployeeId()).collect(Collectors.toList()));


            Example example = new Example(Organization.class);
            Criteria criteria = example.createCriteria();

            criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

            criteria.andEqualTo("parentId", s);

            List<Organization> newOrganizations = organizationMapper.selectByExample(example);
            for (Organization o : newOrganizations) {
                OrganizationSaveReq organizationUpdate = new OrganizationSaveReq();
                organizationUpdate.setOrganizationId(o.getOrganizationId());
                organizationUpdate.setParentId(organization.getOrganizationId());
                update(organizationUpdate);
            }
        }
        for (String s : organizationMergeReq.getMergeOrganizationId()) {
            delete(s, false);
        }

        organizationLeaderService.saveLeaders(organization.getOrganizationId(), organizationMergeReq.getLeaders());
        hrmsEmployeeService.updateUserOrg(employeeIdList, organization.getOrganizationId());
        //	throw new BusinessException("xxxxxx");
    }


    @Override
    /**
     * @description: 迁移
     * @param: organizationMoveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:15
     */
    @Transactional(rollbackFor = Exception.class)
    public void move(OrganizationMoveReq organizationMoveReq) {
        OrganizationSaveReq organizationAddReq = new OrganizationSaveReq();
        organizationAddReq.setOrganizationId(organizationMoveReq.getOrganizationId());
        organizationAddReq.setParentId(organizationMoveReq.getParentId());
        update(organizationAddReq);
    }

    @Override
    /**
     * @description: 拆分
     * @param: organizationSplitReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 13:42
     */
    @Transactional(rollbackFor = Exception.class)
    public void split(OrganizationSplitReq organizationSplitReq) {
        if (organizationSplitReq.getNewOrganizationList() == null) {
            throw new BusinessException("拆分组织信息不正确！");

        }
        if (organizationSplitReq.getNewOrganizationList().size() < 1) {
            throw new BusinessException("拆分组织必须大于二！");
        }


        Organization oldOrganization = organizationMapper.selectByPrimaryKey(organizationSplitReq.getOldOrganization().getOrganizationId());

        Example example = new Example(Organization.class);

        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentId", oldOrganization.getOrganizationId());
        if (organizationMapper.selectByExample(example).size() > 0) {
            throw new BusinessException("拆分组织必须是最后一级组织！");
        }

        for (OrganizationSplitReq.Organization o : organizationSplitReq.getNewOrganizationList()) {
            hrmsEmployeeService.updateUserOrg(o.getEmployeeIds(), o.getOrganizationId());
        }
    }

    @Override
    /**
     * @description: 禁用停用
     * @param: orgId
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 8:42
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String orgId, String enable) {

        if (enable.equals(EnableEnum.N.getKey())) {
            verifyEnable(orgId);
        }

        Organization organizationUpdate = new Organization();
        organizationUpdate.setOrganizationId(orgId);
        organizationUpdate.setUpdateDate(new Date());
        organizationUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        organizationUpdate.setIsEnable(enable);
        organizationMapper.updateByPrimaryKeySelective(organizationUpdate);
    }

    /**
     * @description: 是否存在下级陌路
     * @param: orgId
     * @return: boolean
     * @author: liyuan
     * @createTime: 2021/7/28 14:32
     */
    private boolean isChildren(String orgId) {
        Organization oldOrganization = organizationMapper.selectByPrimaryKey(orgId);
        Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentId", oldOrganization.getOrganizationId());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("isEnable", EnableEnum.Y.getKey());
        if (organizationMapper.selectByExample(example).size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * @description: 是否存在下级陌路
     * @param: orgId
     * @return: boolean
     * @author: liyuan
     * @createTime: 2021/7/28 14:32
     */
    private boolean isChildren(String orgId, List<Organization> organizationList) {

        for (Organization organization : organizationList) {
            if (orgId.equals(organization.getParentId())) {
                return true;
            }
        }
        return false;
    }


    /**
     * @description: 获取机构名称
     * @param: orgId
     * @param: organizationList
     * @return: java.lang.String
     * @author: liyuan
     * @createTime: 2021/8/5 11:10
     */
    private String getOrgName(String orgId, List<Organization> organizationList) {

        if (orgId == null) {
            return null;
        }
        for (Organization organization : organizationList) {
            if (orgId.equals(organization.getOrganizationId())) {
                return organization.getName();
            }
        }
        return null;
    }


    @Override
    /**
     * @description: 验证组织下有子级或人员
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 14:18
     */
    public void verifyEnable(String orgId) {
//		Organization oldOrganization = organizationMapper.selectByPrimaryKey(orgId);
//		Example example = new Example(Organization.class);
//		Criteria criteria = example.createCriteria();
//		criteria.andEqualTo("parentId", oldOrganization.getOrganizationId());
        if (isChildren(orgId)) {
            throw new BusinessException("组织下有子级或人员，不能删除或禁用！");
        }
        EmployeeListReq employeeListReq = new EmployeeListReq();
        employeeListReq.setEqOrgId(orgId);
        List<HrmsEmployee> hrmsEmployees = hrmsEmployeeService.getEmployeeBaseList(employeeListReq);
        if (hrmsEmployees.size() > 0) {
            throw new BusinessException("组织下有子级或人员，不能删除或禁用！");
        }
    }


    @Override
    /**
     * @description: 验证组织下有子级或人员
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/28 14:18
     */
    public void verifyEnable(String orgId, String msg) {

        try {
            verifyEnable(orgId);
        } catch (BusinessException businessException) {
            throw new BusinessException(msg);
        }
    }


    /**
     * @description: 修改相关组织机构的TreeIds
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 16:01
     */
    private void updateOrgTreeIds(String orgId) {
        Example example = new Example(Organization.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        List<Organization> organizations = organizationMapper.selectByExample(example);
        List<Organization> organizationsSortAscList = organizations.stream().sorted(Comparator.comparing(Organization::getOrgLevel)).collect(Collectors.toList());
        List<Organization> organizationsNextList = getNextOrg(orgId, organizationsSortAscList);

        String newParentId = organizationsNextList.get(0).getParentId();
        for (Organization organization : organizationsNextList) {
            if (newParentId.equals(organization.getOrganizationId())) {
                throw new BusinessException("不可以转移到下级");
            }
        }

        for (Organization organization : organizationsNextList) {
            if (organization.getOrganizationId().equals(orgId)) {
                continue;
            }
            Organization parentOrg = null;
            for (Organization o : organizationsNextList) {
                if (organization.getParentId().equals(o.getOrganizationId())) {
                    parentOrg = o;
                }
            }
            if (parentOrg != null) {
                Organization organizationUpdate = new Organization();
                if (StringUtils.isNotBlank(organization.getParentId())) { // 树ID
                    organizationUpdate.setTreeIds(parentOrg.getTreeIds() + "," + organization.getOrganizationId());
                } else {
                    organizationUpdate.setTreeIds(organization.getOrganizationId());
                }
                organizationUpdate.setOrgLevel(organizationUpdate.getTreeIds().split(",").length);
                organizationUpdate.setOrganizationId(organization.getOrganizationId());
                organization.setOrgLevel(organizationUpdate.getOrgLevel());
                organization.setTreeIds(organizationUpdate.getTreeIds());
                organizationMapper.updateByPrimaryKeySelective(organizationUpdate);
            }
        }
    }

    /**
     * @description: 同步打sso
     * @param: organization
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:59
     */
    private void synSystemDept(Organization organization) {
        // 同步sys
        ThpsDeptReq deptInfo = new ThpsDeptReq();
        deptInfo.setId(organization.getOrganizationId());
        deptInfo.setDeptcode(organization.getOrganizationId());
        if (!StringUtils.isBlank(organization.getName())) {
            deptInfo.setDeptname(organization.getName());
        }

        deptInfo.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());


        if (!StringUtils.isBlank(organization.getParentId())) {
            deptInfo.setPdcode(organization.getParentId());
        }
        if (!StringUtils.isBlank(organization.getOrgFlag())) {
            deptInfo.setOrgFlag(organization.getOrgFlag());
        }
        if (!StringUtils.isBlank(organization.getIsDeleted())) {
            if (organization.getIsDeleted().equals(Contants.IS_DELETED_TURE)) {
                deptInfo.setStatus(0);
            }
        }
        deptFeignService.doSaveOrUpdateDept(deptInfo);

        //同步到企业微信
        ThpsDeptReq thpsDeptReq = new ThpsDeptReq();
        BeanUtil.copyProperties(deptInfo, thpsDeptReq);

        oaEmployeeFeignService.syncDeptToWeixin(thpsDeptReq);
    }


    @Override
    /**
     * @description: 同步打sso
     * @param: organization
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/27 15:59
     */
    public void synAllSystemDept() {
        OrganizationListReq organizationListReq = new OrganizationListReq();
        
        List<Organization> organizationList = getBaseList(organizationListReq);

        for (Organization organization : organizationList) {
            // 同步sys
            ThpsDeptReq deptInfo = new ThpsDeptReq();
            deptInfo.setId(organization.getOrganizationId());
            deptInfo.setDeptcode(organization.getOrganizationId());
            if (!StringUtils.isBlank(organization.getName())) {
                deptInfo.setDeptname(organization.getName());
            }

            deptInfo.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());


            if (!StringUtils.isBlank(organization.getParentId())) {
                deptInfo.setPdcode(organization.getParentId());
            }
            if (!StringUtils.isBlank(organization.getOrgFlag())) {
                deptInfo.setOrgFlag(organization.getOrgFlag());
            }
            if (!StringUtils.isBlank(organization.getIsDeleted())) {
                if (organization.getIsDeleted().equals(Contants.IS_DELETED_TURE)) {
                    deptInfo.setStatus(0);
                }
            }
            deptFeignService.doSaveOrUpdateDept(deptInfo);
        }
    }

    @Override
    /**
     * @description: 导入数据
     * @param: organizationImports
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     * @author: liyuan
     * @createTime: 2021/7/29 14:44
     */
    @Transactional(rollbackFor = Exception.class)
    public PlatformResult excelImportOrganization(List<OrganizationImport> organizationImports, String type) {

        Map<Integer, String> errorMap = Maps.newLinkedHashMap();

        if (organizationImports == null) {
            throw new BusinessException("错误信息：模板无数据！");
        }

        List<DictItem> zzlxList = dictItemService.getDictItemByTypeCode("zzlx");
        Map<String, String> zzlxMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(zzlxList)) {
            for (DictItem d : zzlxList) {
                zzlxMap.put(d.getItemName(), d.getItemNameValue());
            }
        }
        List<Organization> organizations = new ArrayList<>();

        int i = 0;

        for (OrganizationImport organizationImport : organizationImports) {
            i++;
            if (StringUtils.isBlank(organizationImport.getName())) {
                setErrorMap(errorMap, i, "组织名称不能为空");
                //throw new BusinessException("错误信息：第" + i + "行组织名称不能为空！");
            } else {
                if (organizationImport.getName().length() > 50) {
                    setErrorMap(errorMap, i, "组织机构名称不能大于50个字");
                    //throw new BusinessException("错误信息：第" + i + "行组织名称不能为空！");
                }
            }
            if (StringUtils.isBlank(organizationImport.getParentName())) {
                setErrorMap(errorMap, i, "上级组织名称不能为空");
                //throw new BusinessException("错误信息：第" + i + "行上级组织名称不能为空！");
            }
            if (StringUtils.isBlank(organizationImport.getOrgFlag())) {
                setErrorMap(errorMap, i, "组织类型不能为空");
                //errorList.add("错误信息：第" + i + "组织类型不能为空！");
            } else {
                if (StringUtils.isBlank(zzlxMap.getOrDefault(organizationImport.getOrgFlag(), ""))) {
                    setErrorMap(errorMap, i, "组织类型不正确");
                    //errorList.add("错误信息：第" + i + "组织类型不正确！");
                }
            }
        }

        if (errorMap.size() > 0) {
            String error = "";
            for (Map.Entry<Integer, String> s : errorMap.entrySet()) {
                error = error + "错误信息： 第" + s.getKey() + "行 " + s.getValue() + "</br>";
            }
            throw new BusinessException(error);
        }

        if (type.equals("2")) {
            organizationMapper.deleteAll();
            // 跟着删除所有的通讯录数据
            commOrganizationContactsMapper.deleteAll();

            OrganizationSaveReq organizationAddReq = new OrganizationSaveReq();
            organizationAddReq.setName(organizationImports.get(0).getParentName());
            organizationAddReq.setParentId(null);
            organizationAddReq.setRemark(organizationImports.get(0).getRemark());
            organizationAddReq.setOrgFlag(zzlxMap.getOrDefault(organizationImports.get(0).getOrgFlag(), ""));
            organizations.add(add(organizationAddReq, false));
        }
        i = 0;
        for (OrganizationImport organizationImport : organizationImports) {
            i++;
            Example example = new Example(Organization.class);
            example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE).andEqualTo("name", organizationImport.getParentName());
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            List<Organization> parentOrganizations = organizationMapper.selectByExample(example);
            if (parentOrganizations.size() < 1) {
                setErrorMap(errorMap, i, "上级组织不存在");
                continue;
            }

            example = new Example(Organization.class);
            example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE).andEqualTo("name", organizationImport.getName());
            example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            List<Organization> organization = organizationMapper.selectByExample(example);
            if (organization.size() > 0) {
                setErrorMap(errorMap, i, "组织名称已存在");
                continue;
            }
            OrganizationSaveReq organizationAddReq = new OrganizationSaveReq();
            
            log.info("Orgid:" + organizationImport.getOrgid());
            if(StringUtils.isNotBlank(organizationImport.getOrgid())) {
           	   organizationAddReq.setOrganizationId(organizationImport.getOrgid());
            }
            
            organizationAddReq.setName(organizationImport.getName());
            organizationAddReq.setParentId(parentOrganizations.get(0).getOrganizationId());
            organizationAddReq.setRemark(organizationImport.getRemark());
            organizationAddReq.setOrgFlag(zzlxMap.getOrDefault(organizationImport.getOrgFlag(), ""));
            organizations.add(add(organizationAddReq, false));
        }


        if (errorMap.entrySet().size() < 1) {
            for (Organization o : organizations) {
                synSystemDept(o);
                // 同步到通讯录 不管是新增还是修改，先查询是否有记录，有则修改，无则添加
                syncOrganization2ContactsSingleTime("add", o, null);
            }
            return PlatformResult.success("", "导入数量：" + organizationImports.size());
        } else {
            String error = "";
            for (Map.Entry<Integer, String> s : errorMap.entrySet()) {
                error = error + "错误信息： 第" + s.getKey() + "行 " + s.getValue() + "</br>";
            }
            throw new BusinessException(error);
        }
    }

    /**
     * @description: 错误复制格式化
     * @param: errorMap
     * @param: rowId
     * @param: error
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/23 11:07
     */
    private void setErrorMap(Map<Integer, String> errorMap, Integer rowId, String error) {
        if (errorMap.containsKey(rowId)) {
            errorMap.put(rowId, errorMap.get(rowId) + "," + error);
        } else {
            errorMap.put(rowId, error);
        }
    }


    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<OrganizationSaveReq> organizationSaveReqs) {
        for (OrganizationSaveReq organizationSaveReq : organizationSaveReqs) {
            Organization organizationUpdate = new Organization();
            organizationUpdate.setOrganizationId(organizationSaveReq.getOrganizationId());
            organizationUpdate.setUpdateDate(new Date());
            organizationUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            organizationUpdate.setSeqNo(organizationSaveReq.getSortNo());
            organizationMapper.updateByPrimaryKeySelective(organizationUpdate);
        }
    }


    @Override
    /**
     * 获取子机构和员工信息
     *
     * @param orgEmplListReq
     * @return cn.trasen.basicsbottom.bean.OrgChildrenEmplListRes
     * <AUTHOR>
     * @date 2021/12/9 11:42
     */
    public OrgChildrenEmplListRes getOrgChildrenEmplList(OrgChildrenEmplListReq orgEmplListReq) {
        OrgChildrenEmplListRes orgChildrenEmplListRes = new OrgChildrenEmplListRes();
        OrganizationListReq organizationListReq = new OrganizationListReq();
        organizationListReq.setEqParentId(orgEmplListReq.getOrgId());
        List<Organization> organizationList = getBaseList(organizationListReq);
        orgChildrenEmplListRes.setOrgId(orgEmplListReq.getOrgId());
        List<OrgChildrenEmplListRes.Org> orgList = new ArrayList<>();
        for (Organization o : organizationList) {
            OrgChildrenEmplListRes.Org org = new OrgChildrenEmplListRes.Org();
            org.setOrgId(o.getOrganizationId());
            org.setOrgCode(o.getCode());
            org.setOrgName(o.getName());
            orgList.add(org);
        }
        orgChildrenEmplListRes.setOrgList(orgList);

        EmployeeListReq record = new EmployeeListReq();
        record.setEqOrgId(orgEmplListReq.getOrgId());
        List<HrmsEmployee> hrmsEmployees = hrmsEmployeeService.getEmployeeBaseList(record);
        List<OrgChildrenEmplListRes.EMP> empList = new ArrayList<>();
        for (HrmsEmployee e : hrmsEmployees) {
            OrgChildrenEmplListRes.EMP emp = new OrgChildrenEmplListRes.EMP();
            emp.setEmpId(e.getEmployeeId());
            emp.setEmpCode(e.getEmployeeNo());
            emp.setEmpName(e.getEmployeeName());
            emp.setAvatar(e.getAvatar());
            empList.add(emp);
        }
        orgChildrenEmplListRes.setEmpList(empList);
        return orgChildrenEmplListRes;
    }

    @Override
    /**
     * 获取组织机构和员工列表
     * @param orgEmpInBO
     * @return java.util.List<cn.trasen.basicsbottom.bo.OrgEmpOutBO>
     * <AUTHOR>
     * @date 2022/2/14 10:17
     */
    public OrgEmpOutBO getOrgEmp(OrgEmpInBO orgEmpInBO) {
        if (StringUtils.isBlank(orgEmpInBO.getParentId()) && StringUtils.isBlank(orgEmpInBO.getName())) {
            return new OrgEmpOutBO();
        }
        List<Organization> organizationAllList = getBaseList(new OrganizationListReq());
        OrgEmpOutBO orgEmpOutBO = new OrgEmpOutBO();
        if (!StringUtils.isBlank(orgEmpInBO.getParentId())) {
            organizationAllList.forEach(o -> {
                if (orgEmpInBO.getParentId().equals("0")) {
                    if (StringUtils.isBlank(o.getParentId())) {
                        orgEmpOutBO.setOrgId(o.getOrganizationId());
                        orgEmpOutBO.setOrgName(o.getName());
                        orgEmpOutBO.setOrgCode(o.getCode());
                        return;
                    }
                } else {
                    if (o.getOrganizationId().equals(orgEmpInBO.getParentId())) {
                        orgEmpOutBO.setOrgId(o.getOrganizationId());
                        orgEmpOutBO.setOrgName(o.getName());
                        orgEmpOutBO.setOrgCode(o.getCode());
                        return;
                    }
                }
            });
        }

        OrganizationListReq organizationListReq = new OrganizationListReq();
        if (!StringUtils.isBlank(orgEmpInBO.getName())) {
            organizationListReq.setName(orgEmpInBO.getName());
        }
        if (!StringUtils.isBlank(orgEmpOutBO.getOrgId())) {
            organizationListReq.setEqParentId(orgEmpOutBO.getOrgId());
        }

        List<Organization> organizationList = getBaseList(organizationListReq);

        List<OrgEmpOutBO.Org> orgList = new ArrayList<>();

        organizationList.forEach(o -> {
            OrgEmpOutBO.Org org = new OrgEmpOutBO.Org();
            org.setOrgId(o.getOrganizationId());
            org.setOrgName(o.getName());
            org.setOrgCode(o.getCode());


            List<OrgEmpOutBO.Org> children = new ArrayList<>();
            organizationAllList.forEach(oall -> {
                if (oall.getParentId().equals(o.getOrganizationId())) {
                    OrgEmpOutBO.Org childrenOrg = new OrgEmpOutBO.Org();
                    childrenOrg.setOrgId(oall.getOrganizationId());
                    childrenOrg.setOrgName(oall.getName());
                    childrenOrg.setOrgCode(oall.getCode());
                    children.add(childrenOrg);
                }
            });
            if (!CollectionUtils.isEmpty(children)) {
                org.setChildren(children);
            }
            orgList.add(org);
        });
        orgEmpOutBO.setOrgList(orgList);

        EmployeeListReq employeeListReq = new EmployeeListReq();
        if (!StringUtils.isBlank(orgEmpInBO.getName())) {
            employeeListReq.setEmployeeName(orgEmpInBO.getName());
        }
        if (!StringUtils.isBlank(orgEmpOutBO.getOrgId())) {
            employeeListReq.setEqOrgId(orgEmpOutBO.getOrgId());
        } else {
            if (StringUtils.isBlank(orgEmpInBO.getName())) {
                orgEmpOutBO.setEmpList(new ArrayList<>());
                return orgEmpOutBO;
            }
        }
        List<HrmsEmployeeResp> hrmsEmployees = hrmsEmployeeService.getEmployeeList(employeeListReq);
        List<EmployeeBean> employeeBeanList = new ArrayList<>();
        hrmsEmployees.forEach(emp -> {
            EmployeeBean employeeBean = new EmployeeBean();
            employeeBean.setEmpId(emp.getEmployeeId());
            employeeBean.setEmpName(emp.getEmployeeName());
            employeeBean.setAvatar(emp.getAvatar());
            employeeBean.setGender(emp.getGender());
            employeeBean.setGenderText(emp.getGenderText());
            employeeBean.setEmpMobile(emp.getPhoneNumber());
            employeeBean.setIsEnable(emp.getIsEnable());
            employeeBean.setOrgId(emp.getOrgId());
            employeeBean.setOrgName(emp.getOrgName());
            employeeBean.setPersonalIdentity(emp.getPersonalIdentity());
            employeeBean.setPersonalIdentityName(emp.getPersonalIdentityName());
            employeeBean.setPositionId(emp.getPositionId());
            employeeBean.setPositionName(emp.getPositionName());
            employeeBean.setEmployeeStatusName(emp.getEmployeeStatusName());
            employeeBeanList.add(employeeBean);

        });
        orgEmpOutBO.setEmpList(employeeBeanList);

        return orgEmpOutBO;
    }

    //获取所有组织机构
    @Override
    public List<Organization> getAllOrgList(Page page, Organization organizatio) {
        Example example = new Example(Organization.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if(!organizatio.isAllData()){
        	criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        }

        if (!StringUtils.isBlank(organizatio.getCode())) {
            String[] split = organizatio.getCode().split(",");
            List<String> asList = Arrays.asList(split);
            criteria.andIn("organizationId", asList);
        } else {
            if (!StringUtils.isBlank(organizatio.getName())) {
                criteria.andLike("name", "%" + organizatio.getName() + "%");
            }
        }
        
        if(StrUtil.isNotBlank(organizatio.getOrgType())) {
        	String[] orgTypes = organizatio.getOrgType().split(",");
        	criteria.andIn("orgType", ListUtil.toList(orgTypes));
        }
        
        example.orderBy("sort").asc().orderBy("orgLevel").asc().orderBy("seqNo").asc().orderBy("createDate").desc();
        List<Organization> organizations = organizationMapper.selectByExampleAndRowBounds(example, page);
        return organizations;
    }

    /**
     * 获取当前登录人所属科室
     *
     * @return
     */
    public Organization getEmpBelongingHospital() {
        //当前登录人
        String userCode = UserInfoHolder.getCurrentUserCode();

        HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeCode(userCode);

        if (null != hrmsEmployee) {

            String orgId = hrmsEmployee.getOrgId();

            Organization organization = organizationMapper.selectByPrimaryKey(orgId);

            List<String> orgIds = new ArrayList<>();

            if (null != organization) {

                if (StringUtils.isNotBlank(organization.getTreeIds())) {

                    String orgArr[] = organization.getTreeIds().split(",");

                    orgIds.addAll(Arrays.asList(orgArr));


                }

                orgIds.add(organization.getOrganizationId());

                Example example = new Example(Organization.class);

                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

                example.and().andIn("organizationId", orgIds);

                List<Organization> list = organizationMapper.selectByExample(example);

                for (Organization item : list) {

                    if (null != item.getOrgFlag() && "1".equals(item.getOrgFlag())) {

                        return item;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取所有医院
     *
     * @param record
     * @return
     */
    public List<Organization> getAllHospital(OrganizationListResp record) {

        Example example = new Example(Organization.class);

        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        example.and().andEqualTo("orgFlag", "1");

        return organizationMapper.selectByExample(example);
    }

    @Override
    public void syncOrganization2ContactsSingleTime(String act, Organization record, Organization origin) {

    	 if (record == null) {
             return;
         }

         String orgName = "";
         if (origin == null) {
             orgName = record.getName();
         } else {
             orgName = origin.getName();
         }

         // 检查是否存在记录，没有的话则新增
         Example e = new Example(CommOrganizationContacts.class);
         e.createCriteria()
                 .andEqualTo("isDeleted", "N")
                 .andEqualTo("name", orgName)
                 .andEqualTo("orgId", record.getOrganizationId());
         e.orderBy("createDate");

         List<CommOrganizationContacts> existList = commOrganizationContactsMapper.selectByExample(e);
         CommOrganizationContacts exist = null;

         if (existList.size() > 0) {
             // 找到名称
             exist = existList.get(0);
         }

         CommOrganizationContacts c = new CommOrganizationContacts();
         c.setOrgId(record.getOrganizationId());
         c.setName(record.getName());
         c.setIsDeleted("N");

         switch (act) {
             case "add":
             case "edit":
                 // 维护操作信息
                 ThpsUser user = UserInfoHolder.getCurrentUserInfo();

                 String tel = "";
                 if (record.getTel() != null) {
                     tel = record.getTel();
                 }

                 if (exist == null) {
                     c.setTel(tel);
                     c.setId(IdGeneraterUtils.nextId());
                     c.setCreateDate(new Date());
                     c.setUpdateDate(new Date());
                     c.setIsDeleted("N");

                     if (user != null) {
                         c.setCreateUser(user.getUsercode());
                         c.setCreateUserName(user.getUsername());
                         c.setUpdateUser(user.getUsercode());
                         c.setUpdateUserName(user.getUsername());
                     }
                     // 新增
                     commOrganizationContactsMapper.insert(c);

                 } else {
                     // 修改
                     if (user != null) {
                         exist.setTel(tel);
                         exist.setName(record.getName());
                         exist.setUpdateUser(user.getUsercode());
                         exist.setUpdateUserName(user.getUsername());
                     }
                     commOrganizationContactsMapper.updateByPrimaryKey(exist);
                 }

                 break;
             case "delete":
                 Example example = new Example(CommOrganizationContacts.class);
                 example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                 example.and().andEqualTo("orgId", record.getOrganizationId());
                 commOrganizationContactsMapper.deleteByExample(example);
         }
    }

    @Override
    public Organization selectOneById(String id) {
        return organizationMapper.selectByPrimaryKey(id);
    }


    @Override
    public List<OrganizationListResp> getChildrenResp(List<OrganizationListResp> list, String pid) {
        List<OrganizationListResp> reList = new ArrayList<>();
        for (OrganizationListResp o : list) {
            if(StrUtil.equals(o.getParentId(),pid)){
                reList.add(o);
            }
        }
        return reList;
    }


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void bacthReplaceLeader(ReplaceLeader replaceLeader) {
		organizationLeaderService.bacthReplaceLeader(replaceLeader);
	}
    
    
    
}