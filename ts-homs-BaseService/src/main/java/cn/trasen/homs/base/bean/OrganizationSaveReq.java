package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationSaveReq {
    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 20, message = "名称不能超过20个字符")
    private String name;
    
    @ApiModelProperty(value = "组织机构编码")
    private String code;


    @ApiModelProperty(value = "主键ID")
    private String organizationId;


    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "部门领导")
    private List<OrganizationLeaderSaveReq> leaders;

    @ApiModelProperty(value = "排序")
    private  Integer sortNo;

    @ApiModelProperty(value = "电话号码")
    private  String tel;
    
	/**
	 * 三甲医生标准人数
	 */
	@ApiModelProperty(value = "三甲医生标准人数")
	private String doctorSta;
	
	/**
	 * 三甲护士标准人数
	 */
	@ApiModelProperty(value = "三甲护士标准人数")
	private String nurseSta;
	
	/**
	 * 三甲医床比
	 */
	@ApiModelProperty(value = "三甲医床比")
	private String doctorThree;
	
	/**
	 * 三甲护床比
	 */
	@ApiModelProperty(value = "三甲护床比")
	private String nurseThree;
	
	@Transient
	private List<HrmsEmployeeResp> hrmsEmployeeList;
	
    private String orgCode;
    
    private String ssoOrgCode;
    
    private String ssoOrgName;

    private String customCode;
    
    private String orgType;
    
    private String sort;
}