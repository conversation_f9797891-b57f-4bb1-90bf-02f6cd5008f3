package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.cache.OrgCacheManager;
import cn.trasen.homs.base.contants.CommonContants;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeBaseMapper;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.mapper.HrmsOrganizationMapper;
import cn.trasen.homs.base.model.HrmsEmployeeOperateRecord;
import cn.trasen.homs.base.model.HrmsOrgOperateRecord;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.model.HrmsPersonnelTransaction;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.properties.BasicsBottomAppConfigProperties;
import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.service.HrmsOrgService;
import cn.trasen.homs.base.service.HrmsEmployeeOperateRecordService;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.HrmsOrgOperateRecordService;
import cn.trasen.homs.base.service.HrmsOrganizationService;
import cn.trasen.homs.base.service.HrmsPersonnelTransactionService;
import cn.trasen.homs.base.utils.MyCommTree;
import cn.trasen.homs.base.utils.UserPermissionManager;
import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.MyTreeModel;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.DeptFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tk.mybatis.mapper.util.StringUtil;

/**
 * @Title: HrmsOrganizationServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 组织机构 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年4月9日 下午2:37:54 
 * @version V1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsOrganizationServiceImpl implements HrmsOrganizationService {

    @Autowired
    HrmsOrganizationMapper hrmsOrganizationMapper;
    @Autowired
    DeptFeignService deptFeignService;
    @Autowired
    HrmsEmployeeService hrmsEmployeeService;

    @Autowired
    SystemUserFeignService userService;

    @Autowired
    BasicsBottomAppConfigProperties basicsBottomAppConfigProperties;

    @Autowired
    HrmsOrgOperateRecordService hrmsOrgOperateRecordService;
    @Autowired
    HrmsEmployeeOperateRecordService hrmsEmployeeOperateRecordService;

    @Autowired
    HrmsPersonnelTransactionService hrmsPersonnelTransactionService;
    
    @Autowired
    CustomEmployeeBaseService customEmployeeBaseService;
    
    @Autowired
	private CustomEmployeeBaseMapper customEmployeeBaseMapper;
    
    @Autowired
    private HrmsOrgService hrmsOrgService;

    /**
     * @Title: insert
     * @Description: 新增组织机构
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年4月28日 上午11:55:17
     */
    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> insert(HrmsOrganization entity) {
        if (!validateData(entity)) {
            return PlatformResult.failure("组织机构名称不是唯一,请校验.");
        }

        String organizationId = String.valueOf(IdWork.id.nextId());
        entity.setOrganizationId(organizationId);
        entity.setCode(organizationId);
        entity.setCreateDate(new Date());
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        HrmsOrganization parentOrg = null;
        if (StringUtils.isNotBlank(entity.getParentId())) { // 树ID
            parentOrg = hrmsOrganizationMapper.selectByPrimaryKey(entity.getParentId());
            entity.setTreeIds(parentOrg.getTreeIds() + "," + entity.getOrganizationId());
        } else {
            entity.setTreeIds(entity.getOrganizationId());
        }
        if (StringUtils.isBlank(entity.getParentId()) || CommonContants.ORG_BASE_PID.equals(entity.getParentId())) { // 机构等级
            entity.setOrgLevel(1);
        } else {
            if (parentOrg != null) {
                int level = parentOrg.getOrgLevel().intValue();
                level = level + 1;
                entity.setOrgLevel(level);
            }
        }

        // 同步sys
        ThpsDeptReq deptInfo = new ThpsDeptReq();
        deptInfo.setId(entity.getOrganizationId());
        deptInfo.setDeptcode(entity.getCode());
        deptInfo.setDeptname(entity.getName());
        deptInfo.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
        if (parentOrg != null) {
            deptInfo.setPdcode(parentOrg.getCode());
        }
        deptInfo.setOrgFlag(entity.getOrgFlag());
        deptFeignService.doSaveOrUpdateDept(deptInfo);

        if (hrmsOrganizationMapper.insertSelective(entity) > 0) {
            HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(entity.getOrganizationId());
            if (org != null) {
                OrgCacheManager.set(org);
            }
            return PlatformResult.success();
        }
        return PlatformResult.failure();
    }

    /**
     * @Title: update
     * @Description: 修改组织机构
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年4月28日 下午4:42:32
     */
    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> update(HrmsOrganization entity) {
        Assert.hasText(entity.getOrganizationId(), "organizationId must be not null.");

        if (!validateData(entity)) {
            return PlatformResult.failure("组织机构名称不是唯一,请校验.");
        }

        HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(entity.getOrganizationId());
        if (org == null) {
            return PlatformResult.failure("记录不存在,请联系系统管理员");
        }

        HrmsOrganization parentOrg = null;
        if (StringUtils.isNotBlank(entity.getParentId())) { // 选择了上级机构
            parentOrg = hrmsOrganizationMapper.selectByPrimaryKey(entity.getParentId());
            org.setTreeIds(parentOrg.getTreeIds() + "," + org.getOrganizationId());
        } else {
            org.setTreeIds(org.getOrganizationId());
        }
        if (entity.getParentId() == null || CommonContants.ORG_BASE_PID.equals(entity.getParentId())) {
            org.setOrgLevel(1);
        } else {
            if (parentOrg != null) {
                int level = parentOrg.getOrgLevel().intValue();
                level = level + 1;
                org.setOrgLevel(level);
            }
        }
        org.setName(entity.getName());
        org.setUpdateDate(new Date());
        org.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        org.setParentId(entity.getParentId());
        org.setSeqNo(entity.getSeqNo());
        org.setRemark(entity.getRemark());



        // 同步sys
        ThpsDeptReq deptInfo = new ThpsDeptReq();
        deptInfo.setId(entity.getOrganizationId());
        deptInfo.setDeptcode(entity.getOrganizationId());
        deptInfo.setDeptname(entity.getName());
        deptInfo.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
        if (parentOrg != null) {
            deptInfo.setPdcode(parentOrg.getCode());
        }
        deptInfo.setOrgFlag(entity.getOrgFlag());
        deptFeignService.doSaveOrUpdateDept(deptInfo);

        if (hrmsOrganizationMapper.updateByPrimaryKey(org) > 0) {
            OrgCacheManager.set(org);
            return PlatformResult.success();
        }
        return PlatformResult.failure();
    }

    /**
     * @Title: validateData
     * @Description: TODO
     * @param entity
     * @Return boolean
     * <AUTHOR>
     * @date 2020年5月7日 上午11:25:30
     */
    private boolean validateData(HrmsOrganization entity) {
        Example example = new Example(HrmsOrganization.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);

        // 校验机构名称是否重复
        if (StringUtils.isNotBlank(entity.getName())) {
            example.and().andEqualTo("name", entity.getName());
            if (StringUtils.isNotBlank(entity.getParentId())) {
                example.and().andEqualTo("parentId", entity.getParentId());
            }
            if (StringUtils.isNotBlank(entity.getOrganizationId())) {
                example.and().andNotEqualTo("organizationId", entity.getOrganizationId());
            }
            return hrmsOrganizationMapper.selectByExample(example).size() == 0;
        }
        return true;
    }

    /**
     * @Title: updateEnableStatus
     * @Description: 修改组织机构启用/停用状态
     * @param entity
     * @Return int
     * <AUTHOR>
     * @date 2020年4月30日 下午5:18:51
     */
    @Override
    @Transactional(readOnly = false)
    public int updateEnableStatus(HrmsOrganization entity) {
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateDate(new Date());
        return hrmsOrganizationMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * @Title: mergeOrganization
     * @Description: 组织机构合并
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年6月23日 下午2:05:19
     */
    //@Override
    @Transactional(readOnly = false)
    public PlatformResult<String> mergeOrganization(HrmsOrganization entity) {
        List<HrmsOrganization> operateOrgs = entity.getOperateOrgs(); // 被合并的机构集合

        if (StringUtils.isBlank(entity.getOrganizationId())) { // 未选择合并的机构
            return PlatformResult.failure("请选择新的机构名称");
        }
        if (CollectionUtils.isEmpty(entity.getOperateOrgs())) {
            return PlatformResult.failure("请选择需要合并的机构");
        }
        for (HrmsOrganization org : operateOrgs) { // 判断合并的机构是否存在子机构
            List<HrmsOrganization> childOrgs = hrmsOrganizationMapper.findListByParentId(org.getOrganizationId());
            if (CollectionUtils.isNotEmpty(childOrgs)) {
                return PlatformResult.failure("【" + org.getName() + "】" + "存在子机构不能被合并");
            }
        }

        List<HrmsOrgOperateRecord> insertOrgOperateRecords = Lists.newArrayList(); // 插入的机构操作记录
        List<HrmsEmployeeOperateRecord> insertEmployeeOperateRecords = Lists.newArrayList(); // 插入的员工操作记录
        String batchNumber = DateUtil.format(new Date(),"YYYYMMddHHmmss");// 批次号




        List<HrmsEmployeeResp> empList = new ArrayList<HrmsEmployeeResp>();

        for (HrmsOrganization org : operateOrgs) {
            // 将被合并的机构删除表示改成已删除
            if (entity.getOrganizationId().equals(org.getOrganizationId())) {
                if (StringUtils.isNotBlank(entity.getParentId())) { // 上级组织
                    HrmsOrganization parentOrg = hrmsOrganizationMapper.selectByPrimaryKey(entity.getParentId());
                    org.setTreeIds(parentOrg.getTreeIds() + "," + entity.getOrganizationId());
                    org.setOrgLevel(parentOrg.getOrgLevel() == null ? null : parentOrg.getOrgLevel().intValue() + 1);
                } else {
                    org.setTreeIds(entity.getOrganizationId());
                    org.setOrgLevel(1);
                }
            } else {
                org.setIsDeleted(Contants.IS_DELETED_TURE); // 被合并的机构状态改为已删除
                org.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                org.setUpdateDate(new Date());
                // 将该科室下的人员更新到新科室下
                EmployeeListReq employeeListReq = new EmployeeListReq();
                employeeListReq.setOrgId(org.getOrganizationId());
                List<HrmsEmployeeResp> currOrgEmployees = hrmsEmployeeService.getList(employeeListReq); // 查询当前机构下的员工列表
                empList.addAll(currOrgEmployees);
                if (CollectionUtils.isNotEmpty(currOrgEmployees)) {
                    for (HrmsEmployeeResp employee : currOrgEmployees) {

                    	CustomEmployeeBase emp = new CustomEmployeeBase();

                        BeanUtil.copyProperties(employee,emp);

                        employee.setOrgId(entity.getOrganizationId());
                        
                        customEmployeeBaseService.update(emp);
                        
                        HrmsEmployeeOperateRecord employeeOperateRecord = new HrmsEmployeeOperateRecord();
                        employeeOperateRecord.setEmployeeId(employee.getEmployeeId());
                        employeeOperateRecord.setOperateRecordId(String.valueOf(IdWork.id.nextId()));
                        employeeOperateRecord.setBatchNumber(batchNumber);
                        employeeOperateRecord.setOldOrgId(org.getOrganizationId());
                        employeeOperateRecord.setOldOrgName(org.getName());
                        employeeOperateRecord.setNewOrgId(entity.getOrganizationId());
                        employeeOperateRecord.setNewOrgName(entity.getName());
                        employeeOperateRecord.setCreateDate(new Date());
                        employeeOperateRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
                        employeeOperateRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
                        employeeOperateRecord.setEmployeeNo(employee.getEmployeeNo());
                        employeeOperateRecord.setEmployeeName(employee.getEmployeeName());
                        employeeOperateRecord.setOperateType(CommonContants.ORG_OPERATE_TYPE_1);
                        employeeOperateRecord.setIsDeleted(Contants.IS_DELETED_FALSE);
                        if (UserInfoHolder.getCurrentUserInfo() != null) {
                            employeeOperateRecord.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                            employeeOperateRecord.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                        }
                        insertEmployeeOperateRecords.add(employeeOperateRecord);
                    }
                }
            }
            if (hrmsOrganizationMapper.updateByPrimaryKeySelective(org) > 0) {
                OrgCacheManager.set(org);
            }

            HrmsOrgOperateRecord record = new HrmsOrgOperateRecord();
            record.setOperateRecordId(String.valueOf(IdWork.id.nextId()));
            record.setBatchNumber(batchNumber);
            record.setOldOrgId(org.getOrganizationId());
            record.setOldOrgName(org.getName());
            record.setNewOrgId(entity.getOrganizationId());
            record.setNewOrgName(entity.getName());
            record.setCreateDate(new Date());
            record.setCreateUser(UserInfoHolder.getCurrentUserCode());
            record.setCreateUserName(UserInfoHolder.getCurrentUserName());
            record.setOperateType(CommonContants.ORG_OPERATE_TYPE_1);
            record.setIsDeleted(Contants.IS_DELETED_FALSE);
            if (UserInfoHolder.getCurrentUserInfo() != null) {
                record.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                record.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
            }
            insertOrgOperateRecords.add(record);
        }
        // 保存科室合并记录
        if (CollectionUtils.isNotEmpty(insertOrgOperateRecords)) {
            hrmsOrgOperateRecordService.batchInsert(insertOrgOperateRecords);
        }
        // 保存员工操作记录
        if (CollectionUtils.isNotEmpty(insertEmployeeOperateRecords)) {
            hrmsEmployeeOperateRecordService.batchInsert(insertEmployeeOperateRecords);
        }

        //保存人员异动记录

        List<HrmsPersonnelTransaction> list = new ArrayList<>();

        if(insertEmployeeOperateRecords != null && insertEmployeeOperateRecords.size() >0) {
            for (HrmsEmployeeOperateRecord bean : insertEmployeeOperateRecords) {
//              HrmsPersonnelTransaction  hpt = new HrmsPersonnelTransaction(employee.getEmployeeNo(),
//                      employee.getEmployeeName(), employeeId, oldOrgId, oldOrgName,personnelChangeInfo.getNewOrgId(),
//                      personnelChangeInfo.getNewOrgName(),DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()), "人员调动", "是",
//                      hrmsPersonnelTransactionService.getBatchNumber(),null, null, null);
                HrmsPersonnelTransaction  hpt = new HrmsPersonnelTransaction();
                BeanUtil.copyProperties(bean, hpt);
                hpt.setCause("合并科室");
                hpt.setExecute("是");
                hpt.setBatchNumber(hrmsPersonnelTransactionService.getBatchNumber());
                hpt.setEffectiveDate(DateUtil.format(new Date(),"yyyy-MM-dd"));
                list.add(hpt);
            }
        }


//      hrmsPersonnelTransactionService.insert(hpt);

        hrmsPersonnelTransactionService.batchInsert(list); //批量添加人员异动记录


        return PlatformResult.success();
    }

    /**
     * @Title: findById
     * @Description: 根据机构ID查询组织机构
     * @param organizationId
     * @Return HrmsOrganization
     * <AUTHOR>
     * @date 2020年4月13日 下午3:41:18
     */
    @Override
    public HrmsOrganization findById(String organizationId) {
        Assert.hasText(organizationId, "organizationId must be not null.");
        HrmsOrganization record = new HrmsOrganization();
        record.setOrganizationId(organizationId);
        return hrmsOrganizationMapper.selectByPrimaryKey(record);
    }

    /**
     * @Title: getDataSetList
     * @Description: 分页查询组织机构列表
     * @param page
     * @param record
     * @Return DataSet<HrmsOrganization>
     * <AUTHOR>
     * @date 2020年4月13日 下午3:40:56
     */
    @Override
    public DataSet<HrmsOrganization> getDataSetList(Page page, HrmsOrganization record) {
//      UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();

        Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause(" seq_no asc");
        Criteria createCriteria = example.createCriteria();
        Criteria createCriteria1 = example.createCriteria();
        createCriteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        // 点击树形节点查询子公司
        if (StringUtils.isNotBlank(record.getOrganizationId())) {
            createCriteria.andLike("treeIds", "%" + record.getOrganizationId() + "%");
        } else {// 默认加载集团下公司
            // example.and().andLike("treeIds",Contants.ORG_PARENTID +"%");
        }
        if (StringUtils.isNotBlank(record.getName())) {
            createCriteria.andLike("name", "%" + record.getName() + "%");
        }
//      if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
//          example.and().andIn("organizationId", userDataPermissionVo.getOrgCodeList());
//      }
//      if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
//          example.and().andEqualTo("organizationId", "-1");
//      }

        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if(!UserInfoHolder.ISADMIN()) { // 是否管理员   
            if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
                String _temp = orgRang.replaceAll("\\(","").replaceAll("\\)", "").replaceAll("\\'", "");
                List<String> asList = Arrays.asList( _temp.split(","));
                createCriteria1.orIn("organizationId", asList);
                createCriteria1.orIsNull("parentId");
                createCriteria1.orEqualTo("parentId", "");
                example.and(createCriteria1);
            }
        }
        
        createCriteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        List<HrmsOrganization> records = hrmsOrganizationMapper.selectByExampleAndRowBounds(example, page);
        HrmsOrganization parentOrg = null;
        for (HrmsOrganization org : records) {
            if (StringUtils.isNotBlank(org.getParentId())) {
                parentOrg = OrgCacheManager.get(org.getParentId());
                if (parentOrg != null) {
                    org.setParentName(parentOrg.getName());
                }
            }
        }
        return new DataSet<HrmsOrganization>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    /**
     * @Title: getOrgTree
     * @Description: 获取组织机构树
     * @param level 查询层级
     * @Return List<TreeModel>
     * <AUTHOR>
     * @date 2020年4月13日 下午4:14:25
     */
    @Override
    public List<TreeModel> getOrgTree(Integer level) {
        UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();

        Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause("seq_no ASC");
        example.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("isEnable", EnableEnum.Y.getKey());
        //根据当前登录账号机构编码过滤查询数据
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
            userDataPermissionVo.getOrgCodeList().add("TS-JT");
            example.and().andIn("organizationId", userDataPermissionVo.getOrgCodeList());
        }
        if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
//          example.and().andEqualTo("organizationId", "TS-JT");
            example.or().andLike("treeIds", "%ZZSFYBJY%");
        }
        if(level>0)
        {
            example.and().andCondition(" LENGTH(tree_ids)-LENGTH(REPLACE(tree_ids,',',''))+1 <= ", level);
        }

        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(example);
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

    /**
     * @Title: getOrgTree
     * @Description: 获取组织机构树
     * @Return List<TreeModel>
     * <AUTHOR>
     * @date 2020年4月13日 下午4:14:25
     */
    @Override
    public List<TreeModel> getOrgTree() {
        return getOrgTree(0);
    }

    /**
     * @Title: getOrgAllList
     * @Description: 获取所有组织机构
     * @Return List<HrmsOrganization>
     * <AUTHOR>
     * @date 2020年4月13日 下午5:27:57
     */
    @Override
    public List<HrmsOrganization> getOrgAllList() {
        Example example = new Example(HrmsOrganization.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        return hrmsOrganizationMapper.selectByExample(example);
    }

    /**
     * @Title: excelImportOrganization
     * @Description: excel导入组织机构
     * @param list
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年6月16日 下午3:13:33
     */
    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> excelImportOrganization(List<HrmsOrganization> list) {
        if (CollectionUtils.isEmpty(list)) {
            return PlatformResult.failure("导入内容为空");
        }
        if (list.stream().anyMatch(item -> StringUtils.isBlank(item.getName()))) {
            return PlatformResult.failure("导入失败，机构名称不能为空");
        }
        if (list.stream().anyMatch(item -> item.getOrgLevel() == null)) {
            return PlatformResult.failure("导入失败，机构等级不能为空");
        }
        // 按上级机构和名称分组，判断同一机构下是否有存在相同的名称
        Map<String, Map<String, Long>> maps = list.stream().collect(Collectors.groupingBy(HrmsOrganization::getParentName, Collectors.groupingBy(HrmsOrganization::getName, Collectors.counting())));
        if (maps != null && maps.size() > 0) {
            for (Map.Entry<String, Map<String, Long>> entry : maps.entrySet()) {
                Map<String, Long> childMap = entry.getValue();
                for (Map.Entry<String, Long> childEntry : childMap.entrySet()) {
                    if (childEntry.getValue().intValue() > 1) {
                        return PlatformResult.failure("导入失败，机构【" + entry.getKey() + "】中存在相同机构名称");
                    }
                }
            }
        }

        Example example = new Example(HrmsOrganization.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("orgLevel", "1");
        HrmsOrganization topOrg = hrmsOrganizationMapper.selectOneByExample(example);
        if (topOrg == null) {
            return PlatformResult.failure("导入失败，请先添加一级机构");
        }

        int level = 2;
        int cnt = 0;
        Map<String, HrmsOrganization> organizationMap = Maps.newHashMap(); // 机构名称Key-机构对象map集合
        do {
            cnt = 0;
            for (HrmsOrganization organization : list) {
                if (organization.getOrgLevel() == level) {
                    String organizationId = String.valueOf(IdWork.id.nextId());
                    organization.setOrganizationId(organizationId);
                    organization.setCode(organizationId);
                    organization.setCreateDate(new Date());
                    organization.setIsDeleted(Contants.IS_DELETED_FALSE);
                    organization.setIsEnable(CommonContants.IS_ENABLE_TRUE);
                    organization.setCreateUser(UserInfoHolder.getCurrentUserCode());
                    if (level == 2) {
                        organization.setParentId(topOrg.getOrganizationId());
                        organization.setTreeIds(topOrg.getTreeIds() + "," + organizationId);
                    } else {
                        HrmsOrganization parent = organizationMap.get(organization.getParentName());
                        if (parent != null) {
                            organization.setParentId(parent.getOrganizationId());
                            organization.setTreeIds(parent.getTreeIds() + "," + organizationId);
                        }
                    }
                    organizationMap.put(organization.getName(), organization);
                    // 同步sys
                    insertThpsDept(organization);
                    cnt++;
                }
            }
            level++;
        } while (cnt > 0);

        int insertCnt = 0;
        if (organizationMap != null && organizationMap.size() > 0) {
            List<HrmsOrganization> insertList = Lists.newArrayList();
            for (Map.Entry<String, HrmsOrganization> entry : organizationMap.entrySet()) {
                insertList.add(entry.getValue());
            }
            insertCnt = insertList.size();
            hrmsOrganizationMapper.batchInsert(insertList);
        }
        return PlatformResult.success(String.valueOf(insertCnt));
    }

    /**
     * @Title: insertThpsDept
     * @Description: 组织机构同步System
     * @param organization
     * @Return void
     * <AUTHOR>
     * @date 2020年6月19日 上午9:40:11
     */
    private void insertThpsDept(HrmsOrganization organization) {
        ThpsDeptReq deptInfo = new ThpsDeptReq();
        deptInfo.setId(organization.getOrganizationId());
        deptInfo.setDeptcode(organization.getCode());
        deptInfo.setDeptname(organization.getName());
        deptInfo.setPdcode(organization.getParentId());
        deptInfo.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
        deptFeignService.doSaveOrUpdateDept(deptInfo);
    }


    public List<TreeModel> getOrgTreeAndEmployeeAll() {
        Example examples = new Example(HrmsOrganization.class);
        examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        examples.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        examples.setOrderByClause("seq_no ASC");
        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(examples);
        List<TreeModel> trees = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<>();

            // 人员类型
            Map<String, String> employeeUserData = new HashMap<>();
            employeeUserData.put("type", "1");

            // 机构类型
            Map<String, String> orgUserData = new HashMap<>();
            orgUserData.put("type", "2");
            Map<String, String> nodeIds = new HashMap<>();
            for(HrmsOrganization item : orgLists) {
                nodeIds.put(item.getOrganizationId(), item.getOrganizationId());

                EmployeeListReq employee = new EmployeeListReq();
                employee.setOrgId(item.getOrganizationId());
                List<HrmsEmployeeResp> employeeList=hrmsEmployeeService.getList(employee);

                if(null!=employeeList && employeeList.size()>0) {
                    for(HrmsEmployeeResp emp : employeeList) {
                        TreeModel empnode = new TreeModel();
                        empnode.setId(emp.getEmployeeId());
                        empnode.setPid(item.getOrganizationId());
                        empnode.setName(emp.getEmployeeName());
                        empnode.setCode(emp.getEmployeeNo());
                        empnode.setUserData(employeeUserData);
                        nodes.add(empnode);
                    }

                }

                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if(StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                }else {
                    node.setPid(item.getParentId());
                }
                node.setCode(item.getCode());
                node.setUserData(orgUserData);
                node.setName(item.getName());
                nodes.add(node);
            }

            // 遍历父级节点
            orgLists.forEach(item ->{
                String[] ids = item.getTreeIds().split(",");
                if(ids != null) {
                    Stream.of(ids).forEach(key -> {
                        if(!nodeIds.containsKey(key)) {
                            nodeIds.put(key, key);
                            HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(key);
                            if(org != null) {
                                TreeModel node = new TreeModel();
                                node.setId(org.getOrganizationId());
                                if(StringUtils.isBlank(org.getParentId())) {
                                    node.setPid("");
                                }else {
                                    node.setPid(org.getParentId());
                                }
                                node.setName(org.getName());
                                nodes.add(node);
                            }
                        }
                    });
                }
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }


    public List<TreeModel> getOrgTree2() {
        return getOrgTree2(0);
    }
    @Override
    public List<TreeModel> getOrgTree2(Integer level) {

//      UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();

        Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause("seq_no ASC");

        Criteria createCriteria = example.createCriteria();
        createCriteria.andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
        Criteria createCriteria1 = example.createCriteria();
        createCriteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        createCriteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

/*      if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
            userDataPermissionVo.getOrgCodeList().add("TS-JT");
            example.and().andIn("organizationId", userDataPermissionVo.getOrgCodeList());
        }*/
/*      if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
            example.and().andEqualTo("organizationId", "TS-JT");
            example.or().andLike("treeIds", "%ZZSFYBJY%");
        }*/
        createCriteria.andEqualTo("isEnable", "1");

        //权限判断
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if(!UserInfoHolder.ISADMIN()) { // 是否管理员

            List<String> asList = new ArrayList<>();

            //本科室及上级科室
            List<HrmsOrganization> parentOrgList = getParentOrgList(UserInfoHolder.getCurrentUserInfo().getDeptcode());

            if(CollectionUtils.isNotEmpty(parentOrgList)) {
                for (HrmsOrganization hrmsOrganization : parentOrgList) {
                    if(StringUtils.isNotBlank(hrmsOrganization.getOrganizationId())) {
                        asList.add(hrmsOrganization.getOrganizationId());
                    }
                }
            }

            if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据

                String _temp = orgRang.replaceAll("\\(","").replaceAll("\\)", "").replaceAll("\\'", "");

                List<String> orgRangList = Arrays.asList( _temp.split(","));

                if(CollectionUtils.isNotEmpty(orgRangList)) {
                    asList.addAll(orgRangList);
                }
            }

            createCriteria1.orIn("organizationId", asList);
            createCriteria1.orIsNull("parentId");
            createCriteria1.orEqualTo("parentId", "");
            example.and(createCriteria1);
        }

        if(level>0)
        {
            example.and().andCondition(" LENGTH(tree_ids)-LENGTH(REPLACE(tree_ids,',',''))+1 <= ", level);
        }

        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(example);
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }


	@Override
	public List<TreeModel> getTreeWithMapping(String syscode) {
		Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause("seq_no ASC");

        Criteria createCriteria = example.createCriteria();
        createCriteria.andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
        createCriteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        createCriteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        createCriteria.andEqualTo("isEnable", "1");
        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(example);
        //将已经映射的科室返回，已经映射的科室在返回的树形结构中用isEnabled=Y表示
        if(!orgLists.isEmpty() && !StringUtils.isEmpty(syscode)){
        	List<String> deptIdMappingList = hrmsOrganizationMapper.getMappingDeptIdsList(syscode);
        	if(!orgLists.isEmpty()){
        		for(HrmsOrganization org : orgLists){
        			if(deptIdMappingList.contains(org.getOrganizationId())){
        				org.setIsMapped("Y");
        				org.setName(org.getName() + " (已映射)");
        			}
        		}
        	}
        }
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setIsEnable(item.getIsMapped());
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
	}
    
    @Override
    public List<TreeModel> getTreeScduDept(Integer level) {

//      UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();

        Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause("seq_no ASC");

        Criteria createCriteria = example.createCriteria();
        createCriteria.andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
        Criteria createCriteria1 = example.createCriteria();
        createCriteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        createCriteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

/*      if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
            userDataPermissionVo.getOrgCodeList().add("TS-JT");
            example.and().andIn("organizationId", userDataPermissionVo.getOrgCodeList());
        }*/
/*      if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
            example.and().andEqualTo("organizationId", "TS-JT");
            example.or().andLike("treeIds", "%ZZSFYBJY%");
        }*/
        createCriteria.andEqualTo("isEnable", "1");

        //权限判断
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if(!UserInfoHolder.ISADMIN()) { // 是否管理员   
        	
        	Example example2 = new Example(HrmsOrganization.class);
            example2.setOrderByClause("seq_no ASC");

            Criteria createCriteria2 = example2.createCriteria();
            createCriteria2.andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
            createCriteria2.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            createCriteria2.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            createCriteria2.andEqualTo("isEnable", "1");
            createCriteria2.andEqualTo("hzFlag", "1");//允许会诊
           
            
        	List<String> asList = new ArrayList<>();
        	//添加本科室
        	asList.add(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        	
            if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
            	
                String _temp = orgRang.replaceAll("\\(","").replaceAll("\\)", "").replaceAll("\\'", "");
                
                List<String> orgRangList = Arrays.asList( _temp.split(","));
                
                if(CollectionUtils.isNotEmpty(orgRangList)) {
                	asList.addAll(orgRangList);
                }
            }
            createCriteria2.andIn("organizationId", asList);
            List<HrmsOrganization> orgLists2 = hrmsOrganizationMapper.selectByExample(example2);
            
            
            List<String> asList2 = new ArrayList<>();
          //本科室及上级科室,不查询，直接取treeIds字段
            if (CollectionUtils.isNotEmpty(orgLists2)) {
            	for(HrmsOrganization hrmsOrganization : orgLists2 ){
            		//本科室及上级科室id
            		String treeIds = hrmsOrganization.getTreeIds();
            		 if(StringUtils.isNotBlank(treeIds)){
                        String[] treeIdArray = treeIds.split(",");
                        asList2.addAll(Arrays.asList(treeIdArray));
            		 }
            	}
            	createCriteria.andIn("organizationId", asList2);
            }else {
            	createCriteria.andEqualTo("organizationId", "-1");//查询无科室
            }
            
        }else {
        	 Example example2 = new Example(HrmsOrganization.class);
             example2.setOrderByClause("seq_no ASC");

             Criteria createCriteria2 = example2.createCriteria();
             createCriteria2.andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
             createCriteria2.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
             createCriteria2.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
             createCriteria2.andEqualTo("isEnable", "1");
             createCriteria2.andEqualTo("hzFlag", "1");//允许会诊
             List<HrmsOrganization> orgLists2 = hrmsOrganizationMapper.selectByExample(example2);
             
             List<String> asList = new ArrayList<>();
         	
         	//本科室及上级科室,不查询，直接取treeIds字段
             if (CollectionUtils.isNotEmpty(orgLists2)) {
             	for(HrmsOrganization hrmsOrganization : orgLists2 ){
             		//本科室及上级科室
             		/*
                     List<HrmsOrganization> parentOrgList = getParentOrgList(hrmsOrganization.getOrganizationId());
                     if(CollectionUtils.isNotEmpty(parentOrgList)) {
                     	 for (HrmsOrganization hrmsOrganization2 : parentOrgList) {
                     		 if(StringUtils.isNotBlank(hrmsOrganization2.getOrganizationId())) {
                     			 asList.add(hrmsOrganization2.getOrganizationId());
                     		 }
                     	 }
                     }*/
             		//本科室及上级科室id
             		String treeIds = hrmsOrganization.getTreeIds();
             		 if(StringUtils.isNotBlank(treeIds)){
                         String[] treeIdArray = treeIds.split(",");
                         asList.addAll(Arrays.asList(treeIdArray));
             		 }
             	}
             	 createCriteria.andIn("organizationId", asList);
             }else {
            	 createCriteria.andEqualTo("organizationId", "-1");//查询无科室
             }
            
        }

        if(level>0)
        {
            example.and().andCondition(" LENGTH(tree_ids)-LENGTH(REPLACE(tree_ids,',',''))+1 <= ", level);
        }
        //createCriteria.andEqualTo("hzFlag", "1").orEqualTo("parentId", "");//会诊排班科室
//        createCriteria1.orIsNull("parentId");
//        createCriteria1.orEqualTo("parentId", "");
        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(example);
        
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.stream().forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }
    
    private List<HrmsOrganization> getParentOrgList(String organizationId) {
    	HrmsOrganization hrmsOrganization = hrmsOrganizationMapper.selectByPrimaryKey(organizationId);

        String treeIds = hrmsOrganization.getTreeIds();

        List<HrmsOrganization> orgLists = new ArrayList<>();
        if(StringUtils.isNotBlank(treeIds)){
            String[] treeIdArray = treeIds.split(",");
            Example examples = new Example(HrmsOrganization.class);
            examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            //根据当前登录账号机构编码过滤查询数据
            examples.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            examples.and().andIn("organizationId", Arrays.asList(treeIdArray));
            examples.setOrderByClause("seq_no ASC");
            orgLists = hrmsOrganizationMapper.selectByExample(examples);
        }
        
        return orgLists;
    }

    @Override
    public List<MyTreeModel> getOrgTree3(Integer level,boolean isTmpEmp,String employeeStatus,String archivesType, boolean isAllData) {
        Map<String,Object> parMap = new HashMap<>();
        //权限判断是否所有权限
        Boolean isALL = UserInfoHolder.ISALL();
        boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");
        boolean isGroupLeader = UserInfoHolder.getRight("GROUP_LEADER");
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = UserInfoHolder.getOrgRang();
        System.out.println("科室权限orgRang：" + orgRang);
        // 是否管理员
        if(!UserInfoHolder.ISADMIN() && !isALL && ! isInfoAdmin &&  !isGroupLeader && !isAllData) {
            //查询组织范围数据
            if(!StringUtil.isEmpty(orgRang)) {
                String orgTemp = orgRang.replaceAll("\\(","").replaceAll("\\)", "").replaceAll("\\'", "");
                List<String> asList = Arrays.asList( orgTemp.split(","));
                List<String> mapList = new ArrayList<String>(asList);
               
                //if(asList.contains("SELF")) {  //默认查询自己的组织结构
                HrmsOrganization selectByPrimaryKey = hrmsOrganizationMapper.selectByPrimaryKey(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                List<String> split =  Arrays.asList( selectByPrimaryKey.getTreeIds().split(","));
                List<String> splitList = new ArrayList<String>(split);
                mapList.addAll(splitList);
                //}
                //有下属权限 查询有下属权限的科室
              //  boolean isSub = UserInfoHolder.getRight("IS_SUB");  //下属管理权限
                //if(isSub){
                    List<String>  hrmsOrganizationAndNextList = getHrmsOrganizationAndNextList(thpsUser.getDeptId());
                    mapList.addAll(hrmsOrganizationAndNextList);
              //  }
                //根据当前登录账号机构编码过滤查询数据
                //科主任可以查看自己管理的科室
                List<String> orgIdList = customEmployeeBaseMapper.selectManageDept(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());
                if(CollectionUtils.isNotEmpty(orgIdList)) {
                	mapList.addAll(orgIdList);
                }
                
                mapList.add("23405568"); //省人医使用的  后续要去掉
                
                parMap.put("asList", mapList);
            }
        }

        if(level>0){
            parMap.put("level", level );
        }else {
            parMap.put("level", "" );
        }
        //如果是不是显示全部数据，则查看当前登录人所在机构数据
        if(!isAllData){
        	parMap.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        }
        parMap.put("archivesType", archivesType);
        parMap.put("employeeStatus", employeeStatus);

        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.getOrgTree(parMap);
        if(isTmpEmp){
            orgLists = hrmsOrganizationMapper.getOrgTreeByTmpEmp(parMap);
        }
        
        List<MyTreeModel> trees = new LinkedList<MyTreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
        	// 转换为树节点列表
        	List<MyTreeModel> nodes = new LinkedList<MyTreeModel>();
        	orgLists.stream().forEach(item -> {
        		MyTreeModel node = new MyTreeModel();
        		node.setId(item.getOrganizationId());
        		if (StringUtils.isBlank(item.getParentId())) {
        			node.setPid("");
        		} else {
        			node.setPid(item.getParentId());
					node.setIcon("dept");
        		}
        		node.setName(item.getName());
        		node.setCode(item.getCode());
        		node.setpNumber(item.getPnumber());
        		node.setSsoOrgCode(item.getSsoOrgCode());
        		nodes.add(node);
        	});
        	MyCommTree commTree = new MyCommTree();
        	trees = commTree.CommTreeList(nodes);
        }
        /**
         * 适配多机构显示，查询全部数据则根据机构 层级渲染树，否则按照原来的逻辑
         * <AUTHOR>
         * @update 2025-07-15
         */
        if(isAllData){
        	List<MyTreeModel> orgTrees = new LinkedList<MyTreeModel>();
        	List<MyTreeModel> nodesOrg = new LinkedList<MyTreeModel>();
        	HrmsOrg record = new HrmsOrg();
        	record.setStatus(1);
        	List<HrmsOrg> list = hrmsOrgService.selectList(record);
        	list.stream().forEach(item -> {
        		MyTreeModel node = new MyTreeModel();
        		node.setId(item.getOrgId());
        		if (StringUtils.isBlank(item.getParentId())) {
        			node.setPid("");
        		} else {
        			node.setPid(item.getParentId());
        		}
        		node.setIcon("org");
        		node.setName(item.getOrgName());
        		node.setCode(item.getOrgCode());
        		node.setSsoOrgCode(item.getOrgCode());
        		nodesOrg.add(node);
        	});
        	MyCommTree commTree = new MyCommTree();
        	orgTrees = commTree.CommTreeList(nodesOrg);
        	
        	//机构id和科室id的映射关系，用于后续替换
        	Map<String, String> idMapping = new HashMap<>();
        	for(HrmsOrg orgVo : list){
        		for(MyTreeModel deptVo : trees){
        			if(orgVo.getOrgCode().equals(deptVo.getSsoOrgCode())){
        				idMapping.put(orgVo.getOrgId(), deptVo.getId());
        			}
        		}
        	}
        	for(MyTreeModel vo : orgTrees){
        		renderChildern(vo, trees, idMapping);
        	}
            return orgTrees;
        }
        return trees;
    }
    
    /**
     * 根据机构替换科室数据
     * @param org
     * @param deptTrees
     * @param idMapping
     */
    private void renderChildern(MyTreeModel org, List<MyTreeModel> deptTrees, Map<String, String> idMapping){
    	if("org".equals(org.getIcon()) && (org.getChildren() == null || org.getChildren().size() == 0)){
    		//替换当前节点ID
    		for(MyTreeModel dept : deptTrees){
    			if(dept.getSsoOrgCode().equals(org.getSsoOrgCode())){
    				org.setId(dept.getId());
    				org.setIcon("dept");
    				org.setName(dept.getName());
    				org.setCode(dept.getCode());
    				org.setSsoOrgCode(dept.getSsoOrgCode());
    				org.setpNumber(dept.getpNumber());
    				if(null != dept.getChildren() && dept.getChildren().size() > 0){
    					if(null == org.getChildren()){
    						org.setChildren(dept.getChildren());
    					} else {
    						org.getChildren().addAll(dept.getChildren());
    					}
    				}
    				if(!ObjectUtils.isEmpty(org.getPid()) && idMapping.containsKey(org.getPid())){
    					org.setPid(idMapping.get(org.getPid()));
    				}
    			}
    		}
    	} else if("org".equals(org.getIcon())) {
    		for(MyTreeModel node : org.getChildren()){
    			renderChildern(node, deptTrees, idMapping);
    		}
    		//当前节点也要替换
    		for(MyTreeModel dept : deptTrees){
    			if(dept.getSsoOrgCode().equals(org.getSsoOrgCode())){
    				org.setId(dept.getId());
    				org.setIcon("dept");
    				org.setName(dept.getName());
    				org.setCode(dept.getCode());
    				org.setSsoOrgCode(dept.getSsoOrgCode());
    				org.setpNumber(dept.getpNumber());
    				if(null != dept.getChildren() && dept.getChildren().size() > 0){
    					if(null == org.getChildren()){
    						org.setChildren(dept.getChildren());
    					} else {
    						org.getChildren().addAll(dept.getChildren());
    					}
    				}
    				if(!ObjectUtils.isEmpty(org.getPid()) && idMapping.containsKey(org.getPid())){
    					org.setPid(idMapping.get(org.getPid()));
    				}
    			}
    		}
    	}
    }

    /**
     * <p> @Title: getHrmsOrganizationList</p>
     * <p> @Description:获取指定组织</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年3月22日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */
    public List<HrmsOrganization> getHrmsOrganizationList(List<String> orgIdList)
    {

        if(orgIdList.size()<1)
        {
            return new ArrayList<HrmsOrganization>();
        }
        Example examples = new Example(HrmsOrganization.class);
        examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        examples.setOrderByClause("seq_no ASC");
        examples.and().andNotEqualTo("organizationId", basicsBottomAppConfigProperties.getYgtwqtsydwCode());
        List<HrmsOrganization> hrmsOrganizations= hrmsOrganizationMapper.selectByExample(examples);

        List<HrmsOrganization> hrmsOrganizationsNew=new ArrayList<HrmsOrganization>();

        for (HrmsOrganization hrmsOrganization : hrmsOrganizations) {
            for (String s : orgIdList) {
                if(hrmsOrganization.getOrganizationId().equals(s))
                {
                    hrmsOrganizationsNew.add(hrmsOrganization);
                    break;
                }
            }
        }
        return hrmsOrganizationsNew;
    }


    /**
     * <p> @Title: getHrmsOrganizationList</p>
     * <p> @Description:获取指定组织以及下层所有组织</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年3月22日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */
    public List<String> getHrmsOrganizationAndNextList(String orgIds) {
        List<String> orgIdList = new ArrayList<String>();
        if (!StringUtils.isBlank(orgIds)) {
            for (String s : orgIds.split(",")) {
                if (!StringUtils.isBlank(s) && (!s.equals("ZZSFYBJY"))) {
                    orgIdList.add(s);
                }
            }

        } else {

            if (UserInfoHolder.ISADMIN()) {
                orgIdList = new ArrayList<String>();
            } else {

                for (String s : UserInfoHolder.getOrgRang().replace("'", "").split(",")) {
                    if (!StringUtils.isBlank(s) && (!s.equals("ZZSFYBJY"))) {
                        orgIdList.add(s);
                    }
                }
            }

        }

        List<String> orgIdNewList = new ArrayList<String>();
        if (orgIdList.size() > 0) {
            List<HrmsOrganization> hrmsOrganizations = getHrmsOrganizationAndNextList(orgIdList);
            for (HrmsOrganization hrmsOrganization : hrmsOrganizations) {
                orgIdNewList.add(hrmsOrganization.getOrganizationId());
            }
        }


        if (orgIdNewList.size() > 0) {
            orgIdNewList = orgIdNewList.stream().distinct().collect(Collectors.toList());
        }
        if (!StringUtils.isBlank(orgIds)) {
            if (orgIds.contains("ZZSFYBJY")) {
                if (UserInfoHolder.ISADMIN()) {
                    orgIdNewList.add("ZZSFYBJY");
                }
            }
        }

        if (orgIdNewList.size() < 1) {
            if (!UserInfoHolder.ISADMIN()) {
                orgIdNewList.add("0000");
            }
        }
        return orgIdNewList;
    }
    /**
     * <p> @Title: getHrmsOrganizationList</p>
     * <p> @Description:获取指定组织以及下层所有组织</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年3月22日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */
    public List<HrmsOrganization> getHrmsOrganizationAndNextList(List<String> orgIdList)
    {

        if(orgIdList.size()<1)
        {
            return new ArrayList<HrmsOrganization>();
        }

        Example examples = new Example(HrmsOrganization.class);
        examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
        examples.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        examples.setOrderByClause("seq_no ASC");
        List<HrmsOrganization> hrmsOrganizations= hrmsOrganizationMapper.selectByExample(examples);

        List<HrmsOrganization> hrmsOrganizationsNew=new ArrayList<HrmsOrganization>();

        for (HrmsOrganization hrmsOrganization : hrmsOrganizations) {
            for (String s : orgIdList) {
                if((","+hrmsOrganization.getTreeIds()+",").contains(","+s+","))
                {
                    hrmsOrganizationsNew.add(hrmsOrganization);
                    break;
                }
            }
        }
        //   System.out.println(hrmsOrganizationsNew.size()+"--------------------------------------");
        return hrmsOrganizationsNew;
    }



    /**
     * 获取指定组织以及上层所有组织
     * @param orgIdList
     * @return java.util.List<cn.trasen.basicsbottom.model.HrmsOrganization>
     * <AUTHOR>
     * @date 2022/2/10 9:09
     */
    public List<HrmsOrganization> getHrmsOrganizationAndUpList(List<String> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return new ArrayList<HrmsOrganization>();
        }
        Example examples = new Example(HrmsOrganization.class);
        examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        examples.setOrderByClause("seq_no ASC");
        List<HrmsOrganization> hrmsOrganizations = hrmsOrganizationMapper.selectByExample(examples);
        List<HrmsOrganization> hrmsOrganizationsNew = new ArrayList<HrmsOrganization>();
        for (HrmsOrganization hrmsOrganization : hrmsOrganizations) {
            for (String s : orgIdList) {
                if (hrmsOrganization.getOrganizationId().equals(s)) {
                    if (StringUtils.isBlank(hrmsOrganization.getParentId())) {
                        hrmsOrganizationsNew.add(hrmsOrganization);
                    }
                }
                else
                {

                }
            }
        }
        return hrmsOrganizationsNew;
    }

    @Override
    public List<TreeModel> selectUpOrganizationTree(String organizationId) {

        HrmsOrganization hrmsOrganization = hrmsOrganizationMapper.selectByPrimaryKey(organizationId);

        String treeIds = hrmsOrganization.getTreeIds();

        List<HrmsOrganization> orgLists = new ArrayList<>();
        if(StringUtils.isNotBlank(treeIds)){
            String[] treeIdArray = treeIds.split(",");
            Example examples = new Example(HrmsOrganization.class);
            examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            examples.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            examples.and().andIn("organizationId", Arrays.asList(treeIdArray));
            examples.setOrderByClause("seq_no ASC");
            orgLists = hrmsOrganizationMapper.selectByExample(examples);
        }


        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.stream().forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

    @Override
    public List<TreeModel> selectDownOrganizationTree(String organizationId) {

        String childs = hrmsOrganizationMapper.selectChildList(organizationId);
        List<HrmsOrganization> orgLists = new ArrayList<>();
        if(StringUtils.isNotBlank(childs)){
            String[] treeIdArray = childs.split(",");
            List<String> childsList = Arrays.asList(treeIdArray);

            Example examples = new Example(HrmsOrganization.class);
            examples.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            examples.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
            examples.and().andIn("organizationId", childsList);
            examples.setOrderByClause("seq_no ASC");
            orgLists = hrmsOrganizationMapper.selectByExample(examples);
        }

        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.stream().forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if(item.getOrganizationId().equals(organizationId)){
                    node.setPid("");
                }else{
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

    @Override
    public List<TreeModel> selectOrganizationTree(String organizationId) {
        HrmsOrganization hrmsOrganization = hrmsOrganizationMapper.selectByPrimaryKey(organizationId);
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (null != hrmsOrganization) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            TreeModel node = new TreeModel();
            node.setId(hrmsOrganization.getOrganizationId());
            node.setPid("");
            node.setName(hrmsOrganization.getName());
            node.setCode(hrmsOrganization.getCode());
            nodes.add(node);
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

    @Override
    public List<ThpsUser> selectUserListByDeptCode(List<String> organizationIdList) {
        List<ThpsUser> users = new ArrayList<>();

        List<HrmsEmployeeResp> list = hrmsEmployeeService.selectListByDeptCode(organizationIdList);
        for (HrmsEmployeeResp hrmsEmployee : list) {
            ThpsUser user = new ThpsUser();
            user.setId(hrmsEmployee.getEmployeeId());
            user.setUsercode(hrmsEmployee.getEmployeeNo());
            user.setUsername(hrmsEmployee.getEmployeeName());
            user.setSex(hrmsEmployee.getGender());
            user.setDeptcode(hrmsEmployee.getOrgId());
            user.setDeptname(hrmsEmployee.getOrgName());
            users.add(user);
        }
        return users;
    }

    @Override
    public List<HrmsEmployeeResp> selectEmpListByDeptCode(List<String> organizationIdList) {
        return hrmsEmployeeService.selectListByDeptCode(organizationIdList);
    }

    @Override
    public List<TreeModel> selectOrgOrganizationTree(String ssoOrgCode) {

        Example example = new Example(HrmsOrganization.class);
        example.setOrderByClause("seq_no ASC");
        example.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("isEnable", EnableEnum.Y.getKey());
        example.and().andEqualTo("ssoOrgCode", ssoOrgCode);

        List<HrmsOrganization> orgLists = hrmsOrganizationMapper.selectByExample(example);
        List<TreeModel> trees = new LinkedList<TreeModel>();
        if (CollectionUtils.isNotEmpty(orgLists)) {
            List<TreeModel> nodes = new LinkedList<TreeModel>();
            orgLists.stream().forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getOrganizationId());
                if (StringUtils.isBlank(item.getParentId())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getParentId());
                }
                node.setName(item.getName());
                node.setCode(item.getCode());
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

	@Override
	public HrmsOrganization getOrganizationByName(String orgName) {
		
		Example example = new Example(Organization.class);
        example.and().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("isEnable", EnableEnum.Y.getKey());
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("name", orgName);
        List<HrmsOrganization> list = hrmsOrganizationMapper.selectByExample(example);
        
        if(CollectionUtils.isEmpty(list)) {
        	Assert.isTrue(false, orgName +  "在系统中不存在，请检查导入科室数据！");
        }
        
		return list.get(0);
	}

	@Override
	public List<String> getChildOrgIdsList(String orgId) {
		return hrmsOrganizationMapper.getChildOrgIdsList(orgId);
	}
    
    

}
