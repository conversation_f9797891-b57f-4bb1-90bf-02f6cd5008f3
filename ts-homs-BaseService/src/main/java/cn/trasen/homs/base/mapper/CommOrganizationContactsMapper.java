package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.model.CommOrganizationContacts;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface CommOrganizationContactsMapper extends Mapper<CommOrganizationContacts> {

    List<CommOrganizationContacts> getDataSetList(@Param("p") Page page,
                                                  @Param("id") Set<String> id,
                                                  @Param("keyword") String keyword,
                                                  @Param("ssoOrgCode") String ssoOrgCode);

    int batchInsert(List<CommOrganizationContacts> list);

    /*
     * @param :
     * @return int
     * <AUTHOR>
     * @description 删除所有通讯录数据
     * @date 2023/12/5 08:53
     */
    int deleteAll();

    List<CommOrganizationContacts> getExternalDataSetList(@Param("p")Page page,
                                                          @Param("keyword")String keyword,
                                                          @Param("ssoOrgCode") String ssoOrgCode);
}