//package cn.trasen.homs.base.model;
//
//
//import java.util.Date;
//
//import javax.persistence.Column;
//import javax.persistence.Id;
//import javax.persistence.Table;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Getter;
//import lombok.Setter;
//
//@Table(name = "TOA_SYS_SETTING")
//@Setter
//@Getter
//public class GlobalSetting {
//
//    /**
//     * ID
//     */
//    @Id
//    @Column(name = "ID")
//    @ApiModelProperty(value = "ID")
//    private String id;
//
//    /**
//     * 医院logo
//     */
//    @Column(name = "HOSPITAL_LOGO")
//    @ApiModelProperty(value = "医院logo")
//    private String hospitalLogo;
//
//    /**
//     * 顶部导航logo
//     */
//    @Column(name = "TOP_LOGO")
//    @ApiModelProperty(value = "顶部导航logo")
//    private String topLogo;
//
//    /**
//     * 登录页背景logo
//     */
//    @Column(name = "LOGIN_PAGE_BACKGROUND")
//    @ApiModelProperty(value = "登录页背景")
//    private String loginPageBackground;
//
//    /**
//     * 登录导航logo
//     */
//    @Column(name = "LOGIN_TOP_LOGO")
//    @ApiModelProperty(value = "登录页顶部logo")
//    private String loginTopLogo;
//
//
//    /**
//     * 网站名称
//     */
//    @Column(name = "web_title")
//    @ApiModelProperty(value = "网站名称")
//    private String webTitle;
//
//    @Column(name = "login_title")
//    @ApiModelProperty(value = "登录页标题")
//    private String loginTitle;
//
//    /**
//     * 备案号
//     */
//    @Column(name = "RECORD_NUMBER")
//    @ApiModelProperty(value = "备案号")
//    private String recordNumber;
//
//    /**
//     * 备案链接地址
//     */
//    @Column(name = "RECORD_LINK_URL")
//    @ApiModelProperty(value = "备案链接地址")
//    private String recordLinkUrl;
//
//    /**
//     * 版权内容
//     */
//    @Column(name = "ORIGINAL_CONTENT")
//    @ApiModelProperty(value = "版权内容")
//    private String originalContent;
//
//    /**
//     * 主题颜色
//     */
//    @Column(name = "THEME_COLOR")
//    @ApiModelProperty(value = "主题颜色")
//    private String themeColor;
//
//    /**
//     * 创建人ID
//     */
//    @Column(name = "CREATE_USER")
//    @ApiModelProperty(value = "创建人ID")
//    private String createUser;
//
//    /**
//     * 创建人姓名
//     */
//    @Column(name = "CREATE_USER_NAME")
//    @ApiModelProperty(value = "创建人姓名")
//    private String createUserName;
//
//    /**
//     * 创建时间
//     */
//    @Column(name = "CREATE_DATE")
//    @ApiModelProperty(value = "创建时间")
//    private Date createDate;
//
//    /**
//     * 更新人ID
//     */
//    @Column(name = "UPDATE_USER")
//    @ApiModelProperty(value = "更新人ID")
//    private String updateUser;
//
//    /**
//     * 更新人姓名
//     */
//    @Column(name = "UPDATE_USER_NAME")
//    @ApiModelProperty(value = "更新人姓名")
//    private String updateUserName;
//
//    /**
//     * 更新人姓名
//     */
//    @Column(name = "UPDATE_DATE")
//    @ApiModelProperty(value = "更新时间")
//    private Date updateDate;
//
//    /**
//     * 是否删除 N 正常   Y 删除
//     */
//    @Column(name = "IS_DELETED")
//    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
//    private String isDeleted;
//
//    /**
//     * 机构编码
//     */
//    @Column(name = "ORG_CODE")
//    @ApiModelProperty(value = "机构编码")
//    private String orgCode;
//
//    /**
//     * 水印内容 多个逗号隔开
//     */
//    @Column(name = "watermark_list")
//    @ApiModelProperty(value = "水印内容 多个逗号隔开")
//    private String watermarkList;
//    
//    /**
//     * 水印文本
//     */
//    @Column(name = "watermark_text")
//    @ApiModelProperty(value = "水印文本")
//    private String watermarkText;
//    
//    /**
//     * 初始密码
//     */
//    @Column(name = "password_preset")
//    @ApiModelProperty(value = "初始密码")
//    private String passwordPreset;
//    
//    /**
//     * 密码长度
//     */
//    @Column(name = "password_length")
//    @ApiModelProperty(value = "密码长度")
//    private Integer passwordLength;
//    
//    /**
//     * 密码规则   1必须包含(A-Z)   2 必须包含(a-z) 3 必须包含(0-9) 必须包含(特殊字符)  多个规则逗号隔开
//     */
//    @Column(name = "password_rule")
//    @ApiModelProperty(value = "密码规则   1必须包含(A-Z)   2 必须包含(a-z) 3 必须包含(0-9) 必须包含(特殊字符)  多个规则逗号隔开")
//    private String passwordRule;
//    
//    /**
//     * 是否开启强制修改密码  0不开启  1开启
//     */
//    @Column(name = "remind_password")
//    @ApiModelProperty(value = "是否开启强制修改密码  0不开启  1开启")
//    private Integer remindPassword;
//    
//    /**
//     * 是否开启强制设置签章  0不开启  1开启
//     */
//    @Column(name = "remind_signature")
//    @ApiModelProperty(value = "是否开启强制设置签章  0不开启  1开启")
//    private Integer remindSignature;
//    
//    @Column(name = "mobile_platform")
//    @ApiModelProperty(value = "平台类型   1企业微信  2钉钉")
//    private Integer mobilePlatform;
//    
//    @Column(name = "salary_type")
//    @ApiModelProperty(value = "财务薪资导入类型  1 基本工资   2绩效工资 ")
//    private String salaryType;
//    
//    @Column(name = "forget_pwd")
//    @ApiModelProperty(value = "登录页忘记密码开关  0不开启  1开启")
//    private Integer forgetPwd;
//    
//    @Column(name = "remember_pwd")
//    @ApiModelProperty(value = "登录页记住密码开关  0不开启  1开启")
//    private Integer rememberPwd;
//    
//    @Column(name = "verify_code")
//    @ApiModelProperty(value = "登录页验证码开关 0不开启  1开启")
//    private Integer verifyCode;
//    
//    @Column(name = "im_switch")
//    @ApiModelProperty(value = "IM开关 0不开启  1开启")
//    private Integer imSwitch;
//    
//    @Column(name = "worksheet_switch")
//    @ApiModelProperty(value = "工单开关  0不开启  1开启")
//    private Integer worksheetSwitch;
//    
//    @Column(name = "account_login")
//    @ApiModelProperty(value = "账号登录 0不开启  1开启")
//    private Integer accountLogin;
//    
//    @Column(name = "lockscreen")
//    @ApiModelProperty(value = "锁屏 0不开启  1开启")
//    private Integer lockscreen;
//    
//    @Column(name = "anonymous_box")
//    @ApiModelProperty(value = "匿名意见箱 0不开启  1开启")
//    private Integer anonymousBox;
//    
//    @Column(name = "parttime_switch")  
//    @ApiModelProperty(value = "可自行设置兼职科室的开关 0不开启  1开启")
//    private Integer parttimeSwitch;
//    
//    @Column(name = "SMS_CODE")  
//    @ApiModelProperty(value = "短信验证码登录开关 0不开启  1开启")
//    private Integer SMSCode;
//    
//    @Column(name = "verify_code_type")
//    @ApiModelProperty(value = "验证码类型   1随机数  2企业微信")
//    private Integer verifyCodeType;
//    
//    @Column(name = "sso_org_code")
//    private String ssoOrgCode;
//    
//    @Column(name = "sso_org_name")
//    private String ssoOrgName;
//}
