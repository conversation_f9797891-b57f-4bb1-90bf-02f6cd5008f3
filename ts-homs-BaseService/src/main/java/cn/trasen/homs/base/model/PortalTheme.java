package cn.trasen.homs.base.model;


import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_portal_theme")
@Setter
@Getter
public class PortalTheme {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 是否默认  0否  1是
     */
    @Column(name = "is_default")
    @ApiModelProperty(value = "是否默认  0否  1是")
    private Integer isDefault;
    
    /**
     * 是否固定高度  0否  1是
     */
    @Column(name = "fixed_height")
    @ApiModelProperty(value = "是否固定高度  0否  1是")
    private Integer fixedHeight;

    /**
     * 创建人编号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人编号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 是否全部人员  0否  1是
     */
    @Column(name = "all_reader")
    @ApiModelProperty(value = "是否全部人员  0否  1是")
    private Integer allReader;
    
    /**
     * 可查看人名称
     */
    @Column(name = "reader_name")
    @ApiModelProperty(value = "可查看人名称")
    private String readerName;

    /**
     * 可查看人id
     */
    @Column(name = "reader_user")
    @ApiModelProperty(value = "可查看人id")
    private String readerUser;

    /**
     * 可查看人部门名称
     */
    @Column(name = "reader_org_name")
    @ApiModelProperty(value = "可查看部门名称")
    private String readerOrgName;
    
    /**
     * 可查看部门
     */
    @Column(name = "reader_org")
    @ApiModelProperty(value = "可查看部门")
    private String readerOrg;
    
    /**
     * 可查看角色名称
     */
    @Column(name = "reader_role_name")
    @ApiModelProperty(value = "可查看角色名称")
    private String readerRoleName;
    
    /**
     * 可查看角色
     */
    @Column(name = "reader_role")
    @ApiModelProperty(value = "可查看角色")
    private String readerRole;
    
    /**
     * 可查看群组名称
     */
    @Column(name = "reader_group_name")
    @ApiModelProperty(value = "可查看群组名称")
    private String readerGroupName;
    
    /**
     * 可查看群组
     */
    @Column(name = "reader_group")
    @ApiModelProperty(value = "可查看群组")
    private String readerGroup;
    
    
    /**
     * 是否查询子元素
     */
    @Transient
    @ApiModelProperty(value = "是否查询子元素 Y是  N否")
    private String isFindChild;
    
    /**
     * 是否查询授权数据
     */
    @Transient
    @ApiModelProperty(value = "是否查询授权数据 Y是  N否")
    private String authorization;
    
    @Transient
    @ApiModelProperty(value = "是否需要默认标示 Y是  N否")
    private String isDefaultFlag;
    
    @Transient
    @ApiModelProperty(value = "默认ID")
    private String defaultId;
    
    
    @Transient
    private String userCode;
    
    @Transient
    private String deptCode;
    
    @Transient
    private List<String> groupIds;
    
    @Transient
    private List<String> roleIds;
    
    /**
     * 元素列表JSON字符串
     */
    @Transient
    @ApiModelProperty(value = "元素列表")
    private List<PortalElement> portalElement;
    
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}
