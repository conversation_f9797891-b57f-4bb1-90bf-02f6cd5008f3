package cn.trasen.homs.base.controller;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.base.bean.OrganizationContactsImport;
import cn.trasen.homs.base.bean.OrganizationContactsImportResp;
import cn.trasen.homs.base.bean.OrganizationReq;
import cn.trasen.homs.base.model.CommOrganizationContacts;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.service.CommOrganizationContactsService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.IOrganizationService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommOrganizationContactsController
 * @Description TODO
 * @date 2023年11月20日 上午11:43:39
 */
@RequestMapping("/organizationContacts")
@RestController
@Api(tags = "CommOrganizationContactsController")
public class CommOrganizationContactsController {

    private transient static final Logger logger = LoggerFactory.getLogger(CommOrganizationContactsController.class);

    @Autowired
    IOrganizationService organizationService;

    @Autowired
    private CommOrganizationContactsService commOrganizationContactsService;
    
    @Autowired
    private GlobalSettingsService globalSettingsService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveCommOrganizationContacts
     * @Description 新增
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/save")
    public PlatformResult<String> saveCommOrganizationContacts(@RequestBody CommOrganizationContacts record) {

    	GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
    	
        try {
              if(2 == globalSetting.getOrgContactType()) {
            	  commOrganizationContactsService.save(record);
              }else {
            	  Boolean has = commOrganizationContactsService.checkExist(record);
                  if (has) {
                      return PlatformResult.failure("数据重复了，提交失败");
                  }
                  commOrganizationContactsService.save(record);
                  commOrganizationContactsService.syncContacts2OrganizationSingleTime("add", record);
              }
          
              return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateCommOrganizationContacts
     * @Description 编辑
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/update")
    public PlatformResult<String> updateCommOrganizationContacts(@RequestBody CommOrganizationContacts record) {
    	
    	GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
    	
        try {
        	if(2 == globalSetting.getOrgContactType()) {
        		commOrganizationContactsService.update(record);
            }else {
            	Boolean has = commOrganizationContactsService.checkExist(record);
                if (has) {
                    return PlatformResult.failure("数据重复了，提交失败");
                }
                commOrganizationContactsService.update(record);
                commOrganizationContactsService.syncContacts2OrganizationSingleTime("edit", record);
            }
           
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<CommOrganizationContacts>
     * @Title selectCommOrganizationContactsById
     * @Description 根据ID查询
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/detail/{id}")
    public PlatformResult<CommOrganizationContacts> selectCommOrganizationContactsById(@PathVariable String id) {
        try {
            CommOrganizationContacts record = commOrganizationContactsService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteCommOrganizationContactsById
     * @Description 根据ID删除
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/delete/{id}")
    public PlatformResult<String> deleteCommOrganizationContactsById(@PathVariable String id) {
    	
    	GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
    	
        try {
        	if(2 == globalSetting.getOrgContactType()) {
        		commOrganizationContactsService.deleteById(id);
        	}else {
        		CommOrganizationContacts record = commOrganizationContactsService.selectById(id);
                commOrganizationContactsService.deleteById(id);
                if(StringUtils.isNotBlank(record.getOrgId())) {
                	commOrganizationContactsService.syncContacts2OrganizationSingleTime("delete", record);
                }
        	}
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<CommOrganizationContacts>
     * @Title selectCommOrganizationContactsList
     * @Description 查询列表
     * @date 2023年11月20日 上午11:43:39
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/list")
    public DataSet<CommOrganizationContacts> selectCommOrganizationContactsList(Page page, CommOrganizationContacts record) {
        // 检查是否传科室名称否则给空数据
        commOrganizationContactsService.syncOrganization2Contacts();

        Set<String> id = new HashSet<>();
        if (record.getOrgName() != null && !record.getOrgName().equals("")) {
            // 根据科室名称查询科室
            OrganizationReq orgReq = new OrganizationReq();
            orgReq.setEqName(record.getOrgName().trim());
            // 这里支持模糊查询
            List<Organization> orgList = organizationService.getChildrenList(orgReq);
            if (orgList.size() > 0) {
                for (Organization org : orgList) {
                    id.add(org.getOrganizationId());
                }
            } else {
                id = null;
            }
        }

        return commOrganizationContactsService.getDataSetList(page, id, record.getOrgName());
    }

    @GetMapping(value = "/template/import/download")
    @ApiOperation(value = "下载通讯录导入模板", notes = "下载通讯录导入模板")
    public void downloadImportContactsTemplate(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "通讯录导入模板.xls";
            String template = "template/importContacts.xls";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {
        List<OrganizationContactsImport> organizationImports = (List<OrganizationContactsImport>) ImportExcelUtil.getExcelDatas(file, OrganizationContactsImport.class);
        PlatformResult resp = new PlatformResult();
        try {
        	 OrganizationContactsImportResp importResp = commOrganizationContactsService.excelImport(organizationImports);
             resp.setMessage("信息导入成功，新增：" + importResp.getInserts() + "条，更新数据：" + importResp.getUpdates() + "条");
             resp.setSuccess(true);
             resp.setObject(importResp);
             return resp;
        } catch (Exception e) {
            e.printStackTrace();
            resp.setSuccess(false);
            resp.setMessage(e.getMessage());
            return resp;
        }
       
    }
}
