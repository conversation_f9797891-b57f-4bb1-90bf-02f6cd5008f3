package cn.trasen.homs.base.utils;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.base.dao.CommEmployeeFieldGroupMapper;
import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.model.CustomEmpModel;
import cn.trasen.homs.base.model.CustomEmployeeFieldModel;
import cn.trasen.homs.base.service.JdbcService;
import cn.trasen.homs.base.service.impl.MySqlJdbcService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class JdbcTemplateUtil {

	@Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
	private CommEmployeeFieldGroupMapper commEmployeeFieldGroupMapper;
    /**
     * 
    * @Title: getDataSource
    * @Description: 获取当前数据库类型
    * @param @return    参数
    * @return String    返回类型
    * @throws
    * <AUTHOR>
    * @date 2019年7月24日 下午10:16:19
     */
    public String getDataSourceName() {
        Connection connection = null;
        try {
            connection = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData md = connection.getMetaData();
            return md.getDatabaseProductName();
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }finally {
            if(null!=connection) {
            	try {
                    connection.close(); 
                } catch (SQLException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
        return null;
    }
    
    /**
     * 
    * @Title: getJdbcService
    * @Description: 获取数据库类型接口
    * @param @return    参数
    * @return JdbcService    返回类型
     * @throws IllegalAccessException 
     * @throws InstantiationException 
    * @throws
    * <AUTHOR>
    * @date 2019年8月2日 上午10:53:19
     */
    private JdbcService getJdbcService(){
    	
        JdbcService jdbcService = new MySqlJdbcService();
        
        /*String datasourceName = getDataSourceName();
        
       if("Oracle".equals(datasourceName)) {
    	   
    	   jdbcService = new OracleJdbcService();
       }*/
       
       return jdbcService;
    }
    
    
    public Map<String,Object> executeQuery(String sql) {
    	
    	return jdbcTemplate.queryForMap(sql);
    }
    
    
    
    public void addFieldNew(CommEmployeeField field,String tableName) {
        
		 String sql  = getJdbcService().addFieldNew(field, tableName);
	        
         if(StringUtils.isNotBlank(sql)) {
            
            jdbcTemplate.execute(sql);
            
         }
       
    }
    
    public String insert(List<CustomEmployeeFieldModel> records) {
        
        String employeeId = ApplicationUtils.GUID32();
        
        List<CommEmployeeField> allEmployeeFields = new ArrayList<CommEmployeeField>();
        
        for(CustomEmployeeFieldModel customEmployee : records) {
        	
        	//个人信息
        	List<CommEmployeeField> fields = customEmployee.getFields();
        	
        	String groupId = customEmployee.getGroupId();
        	
        	//明细表
        	List<List<CommEmployeeField>> detailFields = customEmployee.getDetailFields();
        	
        	if(CollectionUtils.isNotEmpty(fields)) { //添加个人信息
                
                allEmployeeFields.addAll(fields);
        	}
        	
        	if(CollectionUtils.isNotEmpty(detailFields)) {//增加明细信息
        		
        		CommEmployeeField commEmployeeField = new CommEmployeeField();
        		
        		commEmployeeField.setFieldName("employee_id");
        		
        		commEmployeeField.setValue(employeeId);
        		
        		for(List<CommEmployeeField> detailFieldList : detailFields) {
        			
        			if(CollectionUtils.isNotEmpty(detailFieldList)) {
        				
        				CommEmployeeFieldGroup commEmployeeFieldGroup  = commEmployeeFieldGroupMapper.selectByPrimaryKey(groupId);
        				
        				String tableName = commEmployeeFieldGroup.getTableName();
        				 
        				detailFieldList.add(commEmployeeField);
        				
        				String id = ApplicationUtils.GUID32();
            			
            			String sql  = getJdbcService().insertEmployeeDetail(detailFieldList,tableName,id);
            			
            			if(StringUtils.isNotBlank(sql)) {
                            
                            jdbcTemplate.execute(sql);
                        }
        			}
        		}
        	}
        }
        
        if(CollectionUtils.isNotEmpty(allEmployeeFields)) { //添加个人信息
    		
    		String tableName = "hrms_employee";
    		
    		String sql  = getJdbcService().insertEmployee(allEmployeeFields,tableName,employeeId);
            
            if(StringUtils.isNotBlank(sql)) {
                
                jdbcTemplate.execute(sql);
            }
    	}

        return  employeeId;
    }
    
    public void update(CustomEmpModel record) {
    	
    	String employeeId = record.getEmployeeId();
    	
    	if(CollectionUtils.isNotEmpty(record.getCustomFileds())) {
    		
    		for(CustomEmployeeFieldModel item : record.getCustomFileds()) {
    			
    			String groupId = item.getGroupId();
    			
    			//个人信息
        		List<CommEmployeeField> fields = item.getFields();
        		
        		//明细表
            	List<List<CommEmployeeField>> detailFields = item.getDetailFields();
        		
        		if(CollectionUtils.isNotEmpty(fields)) {
        			
        			String tableName = "hrms_employee";
        			
        			String sql  = getJdbcService().updateEmployee(fields,tableName,employeeId);
                    
                    if(StringUtils.isNotBlank(sql)) {
                        
                        jdbcTemplate.execute(sql);
                    }
        		}
        		
        		if(CollectionUtils.isNotEmpty(detailFields)) {//增加明细信息
            		
            		CommEmployeeField commEmployeeField = new CommEmployeeField();
            		
            		commEmployeeField.setFieldName("employee_id");
            		
            		commEmployeeField.setValue(employeeId);
            		
            		CommEmployeeFieldGroup commEmployeeFieldGroup  = commEmployeeFieldGroupMapper.selectByPrimaryKey(groupId);
            		
            		String tableName = commEmployeeFieldGroup.getTableName();

    				//先根据员工id,删除数据
    				deleteEmployeeDetailByEmployeeId(employeeId, tableName,commEmployeeFieldGroup);
            		
            		for(List<CommEmployeeField> detailFieldList : detailFields) {
            			
            			if(CollectionUtils.isNotEmpty(detailFieldList)) {
            				 
            				detailFieldList.add(commEmployeeField);
            				
                			String sql  = getJdbcService().insertEmployeeDetail(detailFieldList,tableName,employeeId);
                			
                			if(StringUtils.isNotBlank(sql)) {
                                
                                jdbcTemplate.execute(sql);
                            }
            			}
            		}
            	}
    			
    		}
    	}
    	
    }
    
    /**
     * 
    * @Title: updateEmployeeInfo  
    * @Description: 修改员工个人信息
    * @Params: @param record      
    * @Return: void
    * <AUTHOR>
    * @date:2021年6月30日
    * @Throws
     */
    public void updateEmployeeInfo(List<CommEmployeeField> fields,String tabelName,String employeeId) {
    	
    	if(CollectionUtils.isNotEmpty(fields)) {

			String tableName = "hrms_employee";
			
			String sql  = getJdbcService().updateEmployee(fields,tableName,employeeId);
            
            if(StringUtils.isNotBlank(sql)) {
                
                jdbcTemplate.execute(sql);
            }
		}
    	
    }
    
    /**
     * 
    * @Title: updateEmpDetail  
    * @Description: 修改员工明细表信息
    * @Params: @param detailFields
    * @Params: @param tabelName
    * @Params: @param employeeId      
    * @Return: void
    * <AUTHOR>
    * @date:2021年6月26日
    * @Throws
     */
    public void updateEmpDetail(List<List<CommEmployeeField>> detailFields,String tabelName,String employeeId,CommEmployeeFieldGroup commEmployeeFieldGroup) {
    	
    	CommEmployeeField commEmployeeField = new CommEmployeeField();
		
		commEmployeeField.setFieldName("employee_id");
		
		commEmployeeField.setValue(employeeId);
		
	/*	* 注释  改为从前端控制 是否有新增按钮
		if("hrms_out_record".equals(tabelName)){
			return;
		}*/
		
		deleteEmployeeDetailByEmployeeId(employeeId, tabelName,commEmployeeFieldGroup);
		
		if(CollectionUtils.isNotEmpty(detailFields)) {
			
			for(List<CommEmployeeField> detailFieldList : detailFields) {
				
				if(CollectionUtils.isNotEmpty(detailFieldList)) {
					 
					detailFieldList.add(commEmployeeField);
					
	    			String sql  = getJdbcService().insertEmployeeDetail(detailFieldList,tabelName,employeeId);
	    			
	    			System.out.println("更新人员信息执行的新增sql：" + sql);
	    			
	    			if(StringUtils.isNotBlank(sql)) {
	                    
	                    jdbcTemplate.execute(sql);
	                }
				}
			}
		}
    }
    
    public void deleteEmployeeDetailByEmployeeId(String employeeId,String tableName,CommEmployeeFieldGroup commEmployeeFieldGroup) {

		if(!"hrms_employee".equals(tableName)){ //是主表 不执行删除操作
			String sql  = getJdbcService().deleteEmployeeDetailByEmployeeId(employeeId, tableName,commEmployeeFieldGroup);

			System.out.println("更新人员信息执行的删除sql：" + sql);

			if(StringUtils.isNotBlank(sql)) {

				jdbcTemplate.execute(sql);
			}
		}

    }
    
    public void createTable(CommEmployeeFieldGroup group) {
        
        String sql  = getJdbcService().createTable(group);
        
        if(StringUtils.isNotBlank(sql)) {
            
            jdbcTemplate.execute(sql);
            
        }
    }
    
   /**
    * 
   * @Title: getEmployeeDetailInfoByShow  
   * @Description: 根据设置显示的字段,查询员工明细表业务数据
   * @Params: @param employeeId
   * @Params: @param fields
   * @Params: @return      
   * @Return: List<Map<String,Object>>
   * <AUTHOR>
   * @date:2021年6月28日
   * @Throws
    */
    public List<Map<String,Object>> getEmployeeDetailInfoByShow(String employeeId,List<CommEmployeeField> fields,String tableName) {
    	
    	String sql  = getJdbcService().getEmployeeDetailInfoByShow(employeeId,fields,tableName);
    	
    	return jdbcTemplate.queryForList(sql);
    }

	public void updateFieldLength(String tableName, String fieldName, Integer fieldLength,String showName) {
		
		String sql  = getJdbcService().updateFieldLength(tableName,fieldName,fieldLength,showName);
        
        if(StringUtils.isNotBlank(sql)) {
           
           jdbcTemplate.execute(sql);
           
        }
	}
}
