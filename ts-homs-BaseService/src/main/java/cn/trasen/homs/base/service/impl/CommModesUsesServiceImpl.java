package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.dao.CommModesUsesMapper;
import cn.trasen.homs.base.model.CommModesUses;
import cn.trasen.homs.base.service.CommModesUsesService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommModesUsesServiceImpl
 * @Description TODO
 * @date 2024��12��18�� ����5:19:08
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommModesUsesServiceImpl implements CommModesUsesService {

	@Autowired
	private CommModesUsesMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CommModesUses record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setAccessTime(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUserId(user.getId());
			record.setUserName(user.getUsername());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CommModesUses record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.deleteByPrimaryKey(id);
	}

	@Override
	public CommModesUses selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CommModesUses> getDataSetList(Page page, CommModesUses record) {
		Example example = new Example(CommModesUses.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CommModesUses> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
