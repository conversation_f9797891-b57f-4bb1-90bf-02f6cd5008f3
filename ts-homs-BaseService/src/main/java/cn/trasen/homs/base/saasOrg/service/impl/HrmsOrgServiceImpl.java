package cn.trasen.homs.base.saasOrg.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.homs.base.service.IDictTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeFieldService;
import cn.trasen.homs.base.saasOrg.dao.HrmsOrgMapper;
import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.service.HrmsOrgService;
import cn.trasen.homs.base.saasOrg.vo.HrmsOrgTreeVo;
import cn.trasen.homs.base.saasOrg.vo.OrgData;
import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.bean.ThpsOrgResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.HrmClientService;
import cn.trasen.homs.feign.sso.OrgFeignService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsOrgServiceImpl
 * @Description 机构表服务实现类
 * @date 2025-07-12 11:30:00
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsOrgServiceImpl implements HrmsOrgService {

	@Autowired
	private HrmsOrgMapper mapper;
	
	@Autowired
	private CustomEmployeeFieldService customEmployeeFieldService;
	
	@Autowired
	private OrgFeignService orgFeignService;
	
	@Autowired
	private HrmClientService hrmClientService;

	@Autowired
	private IDictTypeService dictTypeService;
	
	@Override
	@Transactional(readOnly = false)
	public Integer saveOrUpdate(HrmsOrg record) {
		//机构编码重复性校验
		Example example = new Example(HrmsOrg.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("orgCode", record.getOrgCode());
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		List<HrmsOrg> list = mapper.selectByExample(example);

		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if(null != list && list.size() > 0){
			//机构编码存在就更新，否则就新增
			HrmsOrg org = list.get(0);
			BeanUtils.copyProperties(record, org);
			if(ObjectUtils.isEmpty(record.getParentCode())){
				org.setParentCode("0");
			}
			org.setUpdateDate(new Date());
			if (user != null) {
				org.setUpdateUser(user.getUsercode());
				org.setUpdateUserName(user.getUsername());
			}
			mapper.updateByPrimaryKeySelective(org);
			
			//更新机构相关的数据
			OrgData orgData = new OrgData();
			orgData.setOrgCode(org.getOrgCode());
			orgData.setOrgName(org.getOrgName());
			if(null != record.getStatus()){
				orgData.setOrgStatus(record.getStatus());
			} else {
				orgData.setOrgStatus(org.getStatus());
			}
			orgData.setSync(record.getSync());
			updateOrgData(orgData);
			//TODO 推送给第三方
			
			return 1;
		}
		//机构名称重复性校验
//		example = new Example(HrmsOrg.class);
//		criteria = example.createCriteria();
//		criteria.andEqualTo("orgName", record.getOrgName());
//		criteria.andEqualTo("isDeleted", "N");
//		if(!ObjectUtil.isEmpty(record.getOrgId())){
//			criteria.andNotEqualTo("orgId", record.getOrgId());
//		}
//		list = mapper.selectByExample(example);
//		if(null != list && list.size() > 0){
//			throw new BusinessException("已经存在该【" + record.getOrgName() +"】机构名称，请重新输入！");
//		}
		if(record.getStatus() == null){
			record.setStatus(1);
		}
		Date now = new Date();
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		record.setCreateDate(now);
		record.setUpdateDate(now);
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}

		if (ObjectUtils.isEmpty(record.getOrgId())) {
			record.setOrgId(IdGeneraterUtils.nextId());
		}
		mapper.insertSelective(record);
		OrgData orgData = new OrgData();
		orgData.setOrgId(record.getOrgId());
		orgData.setOrgCode(record.getOrgCode());
		orgData.setOrgName(record.getOrgName());
		orgData.setWxPrantId("1");
		orgData.setUserCode(record.getOrgCode() + "_admin");
		orgData.setUserName(record.getOrgName() + "管理员");
		orgData.setParentCode(record.getParentCode());
		orgData.setSync(record.getSync());
		orgData.setOrgStatus(record.getStatus());
		//添加人事需要添加基础数据
		addOrgData(orgData);
		//TODO 推送给第三方
		
		
		return 1;
	}

	@Override
	@Transactional(readOnly = false)
	public Integer deleteByIdOrgCode(String orgId, String orgCode) {
		Example example = new Example(HrmsOrg.class);
		Example.Criteria criteria = example.createCriteria();
		if(!ObjectUtil.isEmpty(orgId)){
			criteria.andEqualTo("orgId", orgId);
		}
		if(!ObjectUtil.isEmpty(orgCode)){
			criteria.andEqualTo("orgCode", orgCode);
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		List<HrmsOrg> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			//校验有下级子机构不能删除
			HrmsOrg org = list.get(0);
			Example example1 = new Example(HrmsOrg.class);
			Example.Criteria criteria1 = example1.createCriteria();
			criteria1.andEqualTo("parentCode", org.getOrgCode());
			List<HrmsOrg> childrenList = mapper.selectByExample(example1);
			if(CollUtil.isNotEmpty(childrenList)){
				throw new BusinessException("当前机构下有子机构，不能删除！");
			}
			//删除机构相关的数据
			OrgData orgData = new OrgData();
			orgData.setOrgCode(list.get(0).getOrgCode());
			deleteOrgData(orgData);
			//TODO 推送给第三方
			
			
			
			HrmsOrg record = new HrmsOrg();
			record.setIsDeleted(Contants.IS_DELETED_TURE);
			mapper.updateByExampleSelective(record, example);
		}
		
		
		
		return 1;
	}

	@Override
	public List<HrmsOrg> selectList(HrmsOrg record) {
		return mapper.selectList(record);
	}

	@Override
	public List<HrmsOrgTreeVo> selectOrgTree(String orgCode, String orgId) {
		List<HrmsOrgTreeVo> tree = new ArrayList<HrmsOrgTreeVo>();
		HrmsOrg record = new HrmsOrg();
		List<HrmsOrg> list = mapper.selectList(record);
		if(CollUtil.isNotEmpty(list)){
			//先找到根节点机构
			for(HrmsOrg org : list){
				if(ObjectUtils.isEmpty(org.getParentCode()) || "0".equals(org.getParentCode())){
					HrmsOrgTreeVo vo = new HrmsOrgTreeVo();
					vo.setId(org.getOrgCode());
					vo.setName(org.getOrgName());
					vo.setPid(null);
					vo.setMarCatgCode(org.getMarCatgCode());
					vo.setMarCatgName(org.getMarCatgName());
					tree.add(vo);
				}
			}
			//渲染子机构
			if(tree.size() > 0){
				for(HrmsOrgTreeVo treeNode : tree){
					renderChildren(treeNode, list);
				}
			}
		}
		if(!ObjectUtils.isEmpty(orgCode)){
			for(HrmsOrgTreeVo vo : tree){
				if(orgCode.equals(vo.getId())){
					List<HrmsOrgTreeVo> resultTree = new ArrayList<HrmsOrgTreeVo>();
					resultTree.add(vo);
					return resultTree;
				}
			}
		}
		return tree;
	}
	
	/**
	 * 渲染子机构列表
	 * @param parent
	 * @param list
	 */
	private void renderChildren(HrmsOrgTreeVo parent, List<HrmsOrg> list){
		List<HrmsOrgTreeVo> children = new ArrayList<HrmsOrgTreeVo>();
		for(HrmsOrg org : list){
			if(!ObjectUtil.isEmpty(org.getParentCode()) && parent.getId().equals(org.getParentCode())){
				HrmsOrgTreeVo vo = new HrmsOrgTreeVo();
				vo.setId(org.getOrgCode());
				vo.setName(org.getOrgName());
				vo.setPid(org.getParentCode());
				vo.setMarCatgCode(org.getMarCatgCode());
				vo.setMarCatgName(org.getMarCatgName());
				children.add(vo);
			}
		}
		if(children.size() > 0){
			for(HrmsOrgTreeVo treeNode : children){
				renderChildren(treeNode, list);
			}
		}
		parent.setChildren(children);
	}

	@Override
	@Transactional(readOnly = false)
	public void syncSsoOrg() {
		//获取sso的机构列表，同步更新到人事的机构表中-sso返回的只有启用的数据，无院区数据
		PlatformResult<List<ThpsOrgResp>> resp = orgFeignService.queryAllOrgList(null);
		if(resp.isSuccess() && resp.getObject().size() > 0){
			for(ThpsOrgResp org : resp.getObject()){
				HrmsOrg record = new HrmsOrg();
				record.setOrgId(org.getOrgId());
				record.setOrgCode(org.getOrgCode());
				record.setOrgName(org.getOrgName());
				record.setMarCatgCode(org.getMarCatgCode());
				record.setMarCatgName(org.getMarCatgName());
				record.setParentCode(org.getParentCode());
				record.setStatus(1);
				record.setSync("Y");
				saveOrUpdate(record);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void addOrgData(OrgData orgData) {
    	String deptId =String.valueOf(IdWork.id.nextId());
    	deptId =  deptId.substring(deptId.length() - 8);
    	orgData.setDeptId(deptId);
    	orgData.setUserId(String.valueOf(IdWork.id.nextId()));
    	orgData.setOrgMapId(orgData.getUserId());
    	orgData.setSettingId(String.valueOf(IdWork.id.nextId()));
    	orgData.setThemeId(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId1(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId2(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId3(String.valueOf(IdWork.id.nextId()));
    	
		mapper.initAddOrgData(orgData);
		
		/**
		 * 同步机构到微信
		 */
		ThpsDeptReq thpsDeptReq = new ThpsDeptReq();
		thpsDeptReq.setId(orgData.getDeptId());
		thpsDeptReq.setDeptname(orgData.getOrgName());
		thpsDeptReq.setPdcode(orgData.getWxPrantId());
		thpsDeptReq.setStatus(1);
		hrmClientService.syncDeptToWeixin(thpsDeptReq);
		
		//人事档案相关信息
		customEmployeeFieldService.initializationOfBasicData(orgData.getOrgCode());
		//新机构系统字典
		dictTypeService.dictTypeData(orgData.getOrgCode());
	}

	@Override
	@Transactional(readOnly = false)
	public void updateOrgData(OrgData orgData) {
		mapper.updateOrgData(orgData);
		/**
		 * 同步机构到微信
		 */
		ThpsDeptReq thpsDeptReq = new ThpsDeptReq();
		thpsDeptReq.setId(orgData.getDeptId());
		thpsDeptReq.setDeptname(orgData.getOrgName());
		thpsDeptReq.setPdcode(orgData.getWxPrantId());
		thpsDeptReq.setStatus(orgData.getOrgStatus());
		hrmClientService.syncDeptToWeixin(thpsDeptReq);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteOrgData(OrgData orgData) {
		mapper.deleteOrgData(orgData);
		/**
		 * 同步机构到微信
		 */
		ThpsDeptReq thpsDeptReq = new ThpsDeptReq();
		thpsDeptReq.setId(orgData.getDeptId());
		thpsDeptReq.setDeptname(orgData.getOrgName());
		thpsDeptReq.setPdcode(orgData.getWxPrantId());
		thpsDeptReq.setStatus(0);
		hrmClientService.syncDeptToWeixin(thpsDeptReq);
	}

	@Override
	public HrmsOrg detailByIdOrgCode(String orgId, String orgCode) {
		Example example = new Example(HrmsOrg.class);
		Example.Criteria criteria = example.createCriteria();
		if(!ObjectUtil.isEmpty(orgId)){
			criteria.andEqualTo("orgId", orgId);
		}
		if(!ObjectUtil.isEmpty(orgCode)){
			criteria.andEqualTo("orgCode", orgCode);
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		List<HrmsOrg> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<HrmsOrg> selectChildren(String orgCode) {
		List<HrmsOrg> result = new ArrayList<HrmsOrg>();
		if(ObjectUtils.isEmpty(orgCode)){
			return result;
		}
		HrmsOrg record = new HrmsOrg();
		List<HrmsOrg> list = mapper.selectList(record);
		if(CollUtil.isNotEmpty(list)){
			HrmsOrg parent = list.get(0);
			result.add(parent);
			List<HrmsOrg> children = new ArrayList<HrmsOrg>();
			//先找到根节点机构
			for(HrmsOrg org : list){
				if(orgCode.equals(org.getParentCode())){
					children.add(org);
				}
			}
			//渲染子机构
			if(children.size() > 0){
				for(HrmsOrg node : children){
					addChildren(node.getOrgCode(), list, result);
				}
			}
		}
		return result;
	}
	
	/**
	 * 渲染子机构列表
	 * @param parent
	 * @param list
	 */
	private void addChildren(String orgCode, List<HrmsOrg> list, List<HrmsOrg> result){
		List<HrmsOrg> children = new ArrayList<HrmsOrg>();
		for(HrmsOrg org : list){
			if(orgCode.equals(org.getParentCode())){
				children.add(org);
			}
		}
		if(children.size() > 0){
			for(HrmsOrg node : children){
				addChildren(node.getOrgCode(), list, result);
			}
		}
	}

}
