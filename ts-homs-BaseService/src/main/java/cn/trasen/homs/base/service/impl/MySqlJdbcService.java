package cn.trasen.homs.base.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.base.model.CommEmployeeField;
import cn.trasen.homs.base.model.CommEmployeeFieldGroup;
import cn.trasen.homs.base.service.JdbcService;
import cn.trasen.homs.core.utils.UserInfoHolder;

public class MySqlJdbcService implements JdbcService{
    
    
    public String addFieldNew(CommEmployeeField field,String tableName) {
        
         String addSql  ="alter table " + tableName + " add  " + field.getFieldName();
         
         if(null != field.getFieldLength()) {
        	 addSql += " varchar(" + field.getFieldLength() + ") DEFAULT NULL COMMENT '" + field.getShowName() + "'";
         }else {
        	 addSql += " varchar(255) DEFAULT NULL COMMENT '" + field.getShowName() + "'";
         }
         
         return addSql;
        
    }
    
    
	public String updateFieldLength(String tableName, String fieldName, Integer fieldLength,String showName) {
		
		 String sql  ="alter table " + tableName + " MODIFY COLUMN " + fieldName;
		 
		 sql += " varchar(" + fieldLength + ") DEFAULT NULL COMMENT '" + showName + "'";
         
         return sql;
	}


	/**
     * 
    * @Title: insert  
    * @Description: 新增数据
    * @Params: @param records
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月23日
    * @Throws
     */
    public String  insertEmployee(List<CommEmployeeField> records,String tableName,String id) {
        
        StringBuffer  sql = new StringBuffer();
        
        sql.append("INSERT INTO "+tableName+" (");
        
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        
        String fieldStr = "employee_id,IS_DELETED,CREATE_DATE,CREATE_USER,CREATE_USER_NAME,SSO_ORG_CODE,"; //要插入的字段
        
        String valueStr = "'"+id+"','N','"+date+"','"+UserInfoHolder.getCurrentUserCode()+"',"+"'"+UserInfoHolder.getCurrentUserName()+"','"+ UserInfoHolder.getCurrentUserCorpCode() + "',";//要插入的values
        
        for(CommEmployeeField field : records) {
            
            if(StringUtils.isNotBlank(field.getValue()) && StringUtils.isNoneBlank(field.getFieldName())) {
                
                String value = field.getValue();
                
                if("employee_name".equals(field.getFieldName())) {
                    
                    fieldStr += field.getFieldName()+",";
                    
                    valueStr += "'"+value+"',";
                    
                    String jianpin = PinYinUtil.converterToFirstSpell(value);
                    
                    fieldStr += "name_spell,";
                    
                    valueStr += "'"+jianpin+"',";
                    
                }else {
                    
                     fieldStr += field.getFieldName()+",";
                     
                     valueStr += "'"+value+"',";
                }
                
            }
            
        }
        sql.append(fieldStr.substring(0, fieldStr.length()-1)+" )");
        
        sql.append(" VALUES (");
        
        sql.append(valueStr.substring(0, valueStr.length()-1)+" )");
        
        return sql.toString();
    }
    
    /**
     * 
    * @Title: insertEmployeeDetail  
    * @Description: 新增员工明细信息
    * @Params: @param records
    * @Params: @param tableName
    * @Params: @param id
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    public String  insertEmployeeDetail(List<CommEmployeeField> records,String tableName,String employeeId) {
        
        StringBuffer  sql = new StringBuffer();
        
        String id = ApplicationUtils.GUID32();
        
        sql.append("INSERT INTO "+tableName+" (");
        
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        
        String fieldStr = "id,IS_DELETED,CREATE_DATE,CREATE_USER,CREATE_USER_NAME,"; //要插入的字段
        
        String valueStr = "'"+id+"','N','"+date+"','"+UserInfoHolder.getCurrentUserCode()+"',"+"'"+UserInfoHolder.getCurrentUserName()+"',";//要插入的values
        
        for(CommEmployeeField field : records) {
            
            if(StringUtils.isNotBlank(field.getValue()) && StringUtils.isNoneBlank(field.getFieldName())) {
                
                fieldStr += field.getFieldName()+",";
                
                valueStr += "'"+field.getValue()+"',";
                
            }
            
        }
        sql.append(fieldStr.substring(0, fieldStr.length()-1)+" )");
        
        sql.append(" VALUES (");
        
        sql.append(valueStr.substring(0, valueStr.length()-1)+" )");
        
        return sql.toString();
        
    }
    
    /**
     * 
    * @Title: updateEmployee  
    * @Description: 修改人员档案
    * @Params: @param records
    * @Params: @param tableName
    * @Params: @param id
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月23日
    * @Throws
     */
    public String  updateEmployee(List<CommEmployeeField> records,String tableName,String employeeId) {
        
        StringBuffer  sql = new StringBuffer();
        
        sql.append("UPDATE "+tableName+" SET ");
        
        String updateField = "";
        
        for(CommEmployeeField field : records) {
            
            if(StringUtils.isNotBlank(field.getFieldName())) {
                
                String fieldName = field.getFieldName();
                
                String value = field.getValue();
                
                if("employee_name".equals(fieldName)) {
                    
                    String jianpin = PinYinUtil.converterToFirstSpell(value);
                    
                    updateField += " name_spell ='"+jianpin+"' ,";
                }
                
                if(StringUtils.isNotBlank(field.getValue())) {
                    
                    updateField += fieldName+" = '"+field.getValue()+"' ,";
                
                }else {
                    updateField += fieldName+" = NULL ,";
                }
            }
        }
        
        sql.append(updateField.substring(0,updateField.length()-1));
        
        sql.append(" WHERE employee_id='"+employeeId+"'");
        
        
        return sql.toString();
    }
    
    /**
     * 
    * @Title: deleteEmployeeDetailByEmployeeId  
    * @Description: 根据员工id删除明细表数据
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月25日
    * @Throws
     */
    public String  deleteEmployeeDetailByEmployeeId(String employeeId,String tableName,CommEmployeeFieldGroup commEmployeeFieldGroup) {
        
        StringBuffer  sql = new StringBuffer();
        
        sql.append("UPDATE "+tableName+" SET is_deleted='Y' where employee_id = '"+employeeId+"' ");
        
        
        String defaultCondition = commEmployeeFieldGroup.getDefaultCondition();
        
        String conditionSql = "";
        
        if(StringUtils.isNotBlank(defaultCondition)) {
            
            conditionSql = " and ";
            String [] conditionArr = defaultCondition.split(":");
            
            String condition = conditionArr[0];
            
            String value = conditionArr[1];
            
            if(value.contains(",")) {
                
                String[] values = value.split(",");
                
                conditionSql += condition+"   in(";
                
                for(int i = 0; i<values.length;i++) {
                    
                    if(i==values.length-1) {
                        
                        conditionSql +="'"+values[i]+"'"+"";
                    }else {
                        conditionSql +="'"+values[i]+"'"+",";
                    }
                }
                conditionSql +=")";
            }else {
                
                conditionSql += condition+"='"+value+"'";
            }
            
        }
        
        sql.append(conditionSql);
        
        return  sql.toString();
    }
    
    public String createTable(CommEmployeeFieldGroup group) {
        
        return getMySQLCreateTableSQL(group);
    }
    
    private String getMySQLCreateTableSQL(CommEmployeeFieldGroup group) {
        
        
        String tableName = group.getTableName();
        
        StringBuffer sql = new StringBuffer();
        
        sql.append("create table "+tableName+" ( "+"\n");
        
        String pk="ID";
        
        String defaultSql = "ID  VARCHAR (36) not null  comment '唯一标识',\n"
                + "employee_id  VARCHAR (50)  comment '员工id',\n"
                + "CREATE_DATE  DATETIME  comment '创建日期',\n"
                + "CREATE_USER  VARCHAR (50) comment '创建人',\n"
                +"CREATE_USER_NAME  VARCHAR (255) comment '创建人姓名',\n"
                +"UPDATE_DATE  DATETIME  comment '更新时间',\n"
                +"UPDATE_USER  VARCHAR (50) comment '更新人',\n"
                +"UPDATE_USER_NAME  VARCHAR (255) comment '更新人名称',\n"
                +"IS_DELETED  CHAR (1) comment '删除标识',\n"
                + "WORKFLOW_ID  VARCHAR (255) comment '工作流id',\n"
                + "SSO_ORG_CODE  VARCHAR (50) comment '机构编码',\n"
                + "SSO_ORG_NAME  VARCHAR (50) comment '机构名称',\n";
        
        sql.append(defaultSql);
        
        
        
        StringBuffer fieldSql = new StringBuffer();
        
        if(StringUtils.isNotBlank(pk)) {
            fieldSql.append("primary key ("+pk+")");
        }else {
            fieldSql.substring(0,sql.length()-1);
        }
        fieldSql.append("\n");
        fieldSql.append(");\n");
        
        sql.append(fieldSql);
        
        if(StringUtils.isNotBlank(group.getGroupName())) {
            sql.append("alter table "+group.getTableName()+" comment'"+group.getGroupName()+"';");
        }
        return sql.toString();
    }
    
    /**
     * 
    * @Title: getEmployeeDetailInfoByShow  
    * @Description: 根据设置显示的字段,查询员工明细表业务数据
    * @Params: @param employeeId
    * @Params: @param fields
    * @Params: @param tableName
    * @Params: @return      
    * @Return: String
    * <AUTHOR>
    * @date:2021年6月28日
    * @Throws
     */
    public String getEmployeeDetailInfoByShow(String employeeId,List<CommEmployeeField> fields,String tableName) {
        
        StringBuffer sql = new StringBuffer();
        
        sql.append("select ");
        
        String fieldStr = "";
        
        for(CommEmployeeField item : fields) {
        
            fieldStr += item.getFieldName()+",";
        }
        sql.append(fieldStr.substring(0, fieldStr.length()-1)+" ");
        
        sql.append(" from "+tableName+" where 1=1 and is_deleted = 'N' and employee_id = '"+employeeId+"'");
        
        return sql.toString();
        
    }
}