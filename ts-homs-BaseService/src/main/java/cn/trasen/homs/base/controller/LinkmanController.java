package cn.trasen.homs.base.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.LinkmanResp;
import cn.trasen.homs.base.model.Linkman;
import cn.trasen.homs.base.service.LinkmanService;
import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 联系人Controller层
 * @Date: 2020/1/13 18:14
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Api(tags = "联系人Controller")
@RestController
public class LinkmanController {

    private static final Logger logger = LoggerFactory.getLogger(LinkmanController.class);

    @Autowired
    private LinkmanService linkmanService;

    /**
     * @Author: Lizhihuo
     * @Description: 查询个人联系人列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    @ApiOperation(value = "个人联系人列表", notes = "个人联系人列表")
    @PostMapping("/employee/linkman/list")
    public DataSet<Linkman> getDataList(Page page, Linkman record) {
	    try {
    		List<Linkman> list = linkmanService.getDataList(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 内部联系人列表
     * @Date: 2020/3/6 10:55
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.hrm.model.Linkman>
     **/
    @ApiOperation(value = "内部联系人列表", notes = "内部联系人列表")
    @PostMapping("/employee/linkman/innerLinkManlist")
    public DataSet<LinkmanResp> innerLinkManlist(Page page, EmployeeListReq employeeReq) {
	    try {
            if(!StringUtils.isBlank(employeeReq.getOrgName()))
            {
                employeeReq.setLikeOrgName(employeeReq.getOrgName());
                employeeReq.setOrgName(null);
            }
    		List<LinkmanResp> list = linkmanService.innerLinkManlist(page, employeeReq);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增个人联系人
     * @Date: 2020/1/13 8:41
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "新增个人联系人", notes = "新增个人联系人")
    @PostMapping("/employee/linkman/save")
    @ControllerLog(description="新增个人联系人")
    public PlatformResult<String> insert(@RequestBody Linkman record) {
        try {
            linkmanService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改个人联系人
     * @Date: 2020/1/13 9:23
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "修改个人联系人", notes = "修改个人联系人")
    @PostMapping("/employee/linkman/update")
    @ControllerLog(description="修改个人联系人")
    public PlatformResult<String> update(@RequestBody Linkman record) {
        try {
            linkmanService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除个人联系人
     * @Date: 2020/1/13 10:22
     * @Param:
     * @return: cn.trasen.BootComm.Utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "删除个人联系人", notes = "删除个人联系人")
    @PostMapping("/employee/linkman/deletedById")
    @ControllerLog(description="删除个人联系人")
    public PlatformResult<String> deleteById(@RequestBody Linkman record) {
        try {
            linkmanService.deleted(record.getId());
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }






}
