package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 11:12
 * @description
 */

@Data
public class JobtitleBasicListResp {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "id")
    private String jobtitleBasicId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String jobtitleBasicName;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称")
    private String className;


    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别id")
    private String classId;


    /**
     * 等级名称
     */
    @ApiModelProperty(value = "等级名称")
    private String levelName;

    /**
     * 等级名称
     */
    @ApiModelProperty(value = "等级id")
    private String levelId;

    @ApiModelProperty(value = "类型")
    private Integer jobtitleBasicGrade;


    @ApiModelProperty(value = "类型名称")
    private String jobtitleBasicGradeLable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnable;



    @ApiModelProperty(value = "是否启用: Y=1; N=0;")
    private String isEnableLable;


    @ApiModelProperty(value = "treeIds")
    private String treeIds;


    @ApiModelProperty(value = "升级")
    List<JobtitleBasicUpgradeListResp> jobtitleBasicUpgradeListRespList;

    @ApiModelProperty(value = "排序")
    private String sortNo;
}
