package cn.trasen.homs.base.customEmployee.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import tk.mybatis.mapper.common.Mapper;

public interface CustomEmployeeGroupMapper extends Mapper<CustomEmployeeGroup> {

	/**
	 * 获取所有分组信息
	 * @return
	 */
	List<CustomEmployeeGroup> getGroupList(@Param("groupId")String groupId);

	/**
	 * 获取所有分组信息
	 * @return
	 */
	List<CustomEmployeeGroup> getGroupList(@Param("groupId")String groupId, @Param("archivesType")String archivesType, @Param("ssoOrgCode")String ssoOrgCode);

	/**
	 * 获取引用档案信息
	 * @return
	 */
	List<CustomEmployeeGroup> getQuotedGroupListBy(Page page, CustomEmployeeGroup record);

	/**
	 * 获取权限分组信息
	 * @param record
	 * @return
	 */
	List<CustomEmployeeGroup> getGroupListByAuthority(CustomEmployeeGroup record);

	void executeSql(@Param("sql")String sql);

	/**
	 *员工档案编辑新增用户角色关系
	 * @param id
	 * @param employeeId
	 * @param roleId
	 * @return
	 */
	int insertSSOUSerRole(@Param("id")String id,@Param("employeeId")String employeeId,@Param("roleId")String roleId);

	/**
	 *员工档案编辑删除用户角色关系
	 * @param employeeId
	 * @param roleId
	 * @return
	 */
	int delSSOUSerRole(@Param("employeeId")String employeeId,@Param("roleId")String roleId);

	List<Map<String, Object>> querySql(@Param("sql")String sql);

	String selectPersonalIdentity(String currentUserCode);

	List<Map<String, String>> getGroupDataList(CustomEmployeeInfo record);

	String selectSalaryLevelName(String value);
}