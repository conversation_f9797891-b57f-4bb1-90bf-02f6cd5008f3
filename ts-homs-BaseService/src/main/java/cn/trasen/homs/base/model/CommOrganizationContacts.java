package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "comm_organization_contacts")
@Setter
@Getter
public class CommOrganizationContacts {

    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    /**
     * 机构名称
     */
    @Transient
    @ApiModelProperty(value = "机构名称")
    private String orgName;
    
    @Column(name = "external_org_name")
    @ApiModelProperty(value = "自定义机构名称")
    private String externalOrgName;


    /**
     * 联系部门（人）
     */
    @ApiModelProperty(value = "联系部门（人）")
    private String name;

    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String tel;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
}