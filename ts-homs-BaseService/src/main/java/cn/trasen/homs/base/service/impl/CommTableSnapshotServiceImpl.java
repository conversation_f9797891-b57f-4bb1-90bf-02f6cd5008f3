package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


import cn.trasen.homs.base.mapper.CommTableSnapshotMapper;
import cn.trasen.homs.base.model.CommTableSnapshot;
import cn.trasen.homs.base.service.CommTableSnapshotService;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommTableSnapshotServiceImpl
 * @Description TODO
 * @date 2024年6月6日 上午9:05:30
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CommTableSnapshotServiceImpl implements CommTableSnapshotService {

    @Autowired
    private CommTableSnapshotMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(CommTableSnapshot record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
        }

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(CommTableSnapshot record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        CommTableSnapshot record = new CommTableSnapshot();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CommTableSnapshot selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<CommTableSnapshot> getDataSetList(Page page, CommTableSnapshot record) {
        Example example = new Example(CommTableSnapshot.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (record.getTableName() != null) {
            criteria.andEqualTo("tableName", record.getTableName());
        }

        if (record.getRowPkValue() != null) {
            criteria.andEqualTo("rowPkValue", record.getRowPkValue());
        }

        example.setOrderByClause("create_date desc");
        List<CommTableSnapshot> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void asyncSaveTableSnapshot(String tableName, String rowPkValue, Object o, Object n) {

        if (StringUtils.isEmpty(tableName)) {
            return;
        }

        if (StringUtils.isEmpty(rowPkValue)) {
            return;
        }

        CommTableSnapshot c = new CommTableSnapshot();
        // 对object 进行 json 转化
        ObjectMapper objectMapper = new ObjectMapper();
        String oJson = "";
        String nJson = "";
        c.setRowPkValue(rowPkValue);
        c.setTableName(tableName);
        try {
            if (o != null) {
                oJson = objectMapper.writeValueAsString(o);
            }

            if (n != null) {
                nJson = objectMapper.writeValueAsString(n);
            }

            c.setRowJsonOld(oJson);
            c.setRowJsonNew(nJson);

            save(c);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
