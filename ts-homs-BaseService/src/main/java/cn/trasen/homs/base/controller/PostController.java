package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.base.bean.PostListReq;
import cn.trasen.homs.base.bean.PostSaveReq;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * @description:
 * @return:
 * @author: liyuan
 * @createTime: 2021/8/6 10:49
 */
@Api(tags = "岗位等级")
@RequestMapping("/post")
@RestController
@Slf4j
public class PostController {

    @Autowired
    private IPostService postService;


    @ApiOperation(value = "获取级别列表只显示显示状态", notes = "获取级别列表只显示显示状态")
    @PostMapping(value = "/getShowList")
    public PlatformResult getShowList(@RequestBody PostListReq postSaveReq) {
        postSaveReq.setIsEnable(EnableEnum.Y.getKey());
        return PlatformResult.success(postService.getList(postSaveReq));
    }

    @ApiOperation(value = "获取级别列表", notes = "获取级别列表")
    @PostMapping(value = "/getList")
    public PlatformResult getList(@RequestBody PostListReq postSaveReq) {
        return PlatformResult.success(postService.getList(postSaveReq));
    }


    @ApiOperation(value = "获取级别列表分页", notes = "获取级别列表分页")
    @PostMapping(value = "/getPageList")
    public DataSet getPageList(PostListReq postListReq, Page page) {
        return postService.getPageList(postListReq, page);
    }

    @ApiOperation(value = "新增岗位级别", notes = "新增岗位级别")
    @PostMapping(value = "/add")
    public PlatformResult add(@RequestBody @Validated PostSaveReq postSaveReq) {
        try {
        	 postService.add(postSaveReq);
             return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "修改岗位级别", notes = "修改岗位级别")
    @PostMapping(value = "/update")
    public PlatformResult update(@RequestBody @Validated PostSaveReq postSaveReq) {
        try {
        	 //verify
            String isEnable = postSaveReq.getIsEnable();
            String postId = postSaveReq.getPostId();

            if (isEnable != null && postId != null && isEnable.equals(EnableEnum.N.getKey())){
                postService.verify(postId);
            }

            postService.update(postSaveReq);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "修改排序", notes = "修改排序")
    @PostMapping(value = "/updateSort")
    public PlatformResult updateSort(@RequestBody List<PostSaveReq> postSaveReqList) {
        try {
        	postService.updateSort(postSaveReqList);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "删除岗位级别", notes = "删除岗位级别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postId", value = "postId", required = true, dataType = "String")
    })
    @RequestMapping(value = "/del", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult del(@RequestParam("postId") String postId) {
        try {
           postService.delete(postId);
           return PlatformResult.success();
       } catch (Exception e) {
           log.error(e.getMessage(), e);
           return PlatformResult.failure(e.getMessage());
       }
    }


    @ApiOperation(value = "验证删除", notes = "验证删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postId", value = "postId", required = true, dataType = "String")
    })
    @RequestMapping(value = "/verifyDel", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult verifyDel(@RequestParam("postId") String postId) {
        try {
        	 postService.verify(postId);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "验证禁用", notes = "验证禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postId", value = "postId", required = true, dataType = "String")
    })
    @RequestMapping(value = "/verifyEnable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult verifyEnable(@RequestParam("postId") String postId) {
        try {
            postService.verify(postId);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "启用禁用", notes = "启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postId", value = "postId", required = true, dataType = "String")
            , @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
    })
    @RequestMapping(value = "/enable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult enable(@RequestParam("postId") String postId, @RequestParam("enable") String enable) {
        try {
            if (enable.equals(EnableEnum.N.getKey())) {
                postService.verify(postId);
            }
            postService.enable(postId, enable);
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量启用禁用", notes = "批量启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postId", value = "多个用逗号分开", required = true, dataType = "String")
            , @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
    })
    @RequestMapping(value = "/batchEnable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult batchEnable(@RequestParam("postId") String postId, @RequestParam("enable") String enable) {
        try {

            // 把逗号分隔的postId 转成数组 这里数据量不到 直接loop 另外一个问题是因为要增加操作日志 这样比较方便
            // 如果性能差 则再优化 先不过度设计
            String[] postIds = postId.split(",");
            int index = 1;
            StringBuilder sb = new StringBuilder();
            for (String id : postIds) {
                try {
                    postService.verify(id);
                } catch (Exception e) {
                    // 把错误收集起来，全部反馈
                    sb.append(index).append(",");
                }
                index++;
            }
            if (sb.length() > 0) {
                return PlatformResult.failure("第" + sb.toString() + "条岗位等级已被使用，不能禁用和删除");
            }

            for (String id : postIds) {
                postService.enable(id, enable);
            }
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "岗位等级下拉框", notes = "岗位等级下拉框")
    @GetMapping(value = "/getPostLevel")
    public PlatformResult<List<Map<String, Object>>> getPostLevel() {
        List<Map<String, Object>> list = postService.getPostLevel();
        return PlatformResult.success(list);
    }
}