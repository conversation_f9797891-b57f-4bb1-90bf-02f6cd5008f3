package cn.trasen.homs.base.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.PortalElementCustom;
import cn.trasen.homs.base.service.PortalElementCustomService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PortalElementCustomController
 * @Description TODO
 * @date 2024��9��21�� ����10:52:59
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PortalElementCustomController")
public class PortalElementCustomController {

	private transient static final Logger logger = LoggerFactory.getLogger(PortalElementCustomController.class);

	@Autowired
	private PortalElementCustomService portalElementCustomService;
	
	
	@ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/portalElementCustom/savePortalElementCustom")
    public PlatformResult<String> savePortalElementCustom(@RequestBody List<PortalElementCustom> records) {
        try {
        	portalElementCustomService.savePortalElementCustom(records);
            return PlatformResult.success();
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }
	
	@ApiOperation(value = "还原", notes = "还原")
    @PostMapping("/api/portalElementCustom/restore")
    public PlatformResult<String> restore(String themeId) {
        try {
        	portalElementCustomService.restore(themeId);
            return PlatformResult.success();
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }

	/**
	 * @Title savePortalElementCustom
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/portalElementCustom/save")
	public PlatformResult<String> save(@RequestBody PortalElementCustom record) {
		try {
			portalElementCustomService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePortalElementCustom
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/portalElementCustom/update")
	public PlatformResult<String> updatePortalElementCustom(@RequestBody PortalElementCustom record) {
		try {
			portalElementCustomService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPortalElementCustomById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PortalElementCustom>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/portalElementCustom/{id}")
	public PlatformResult<PortalElementCustom> selectPortalElementCustomById(@PathVariable String id) {
		try {
			PortalElementCustom record = portalElementCustomService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePortalElementCustomById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/portalElementCustom/delete/{id}")
	public PlatformResult<String> deletePortalElementCustomById(@PathVariable String id) {
		try {
			portalElementCustomService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPortalElementCustomList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PortalElementCustom>
	 * @date 2024��9��21�� ����10:52:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/portalElementCustom/list")
	public DataSet<PortalElementCustom> selectPortalElementCustomList(Page page, PortalElementCustom record) {
		return portalElementCustomService.getDataSetList(page, record);
	}
}
