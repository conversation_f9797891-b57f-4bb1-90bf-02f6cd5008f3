package cn.trasen.homs.base.groupLeader.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.groupLeader.mapper.HrmsRoleGroupLeaderMapper;
import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeader;
import cn.trasen.homs.base.groupLeader.model.HrmsRoleGroupLeaderBase;
import cn.trasen.homs.base.groupLeader.service.HrmsRoleGroupLeaderService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

/**
 * @ClassName HrmsRoleGroupLeaderServiceImpl
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsRoleGroupLeaderServiceImpl implements HrmsRoleGroupLeaderService {

	@Autowired
	private HrmsRoleGroupLeaderMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsRoleGroupLeader record) {

		if(!StringUtil.isEmpty(record.getUsercode())){
			List<String> usercode = Arrays.asList(record.getUsercode().split(","));
			List<String> username = Arrays.asList(record.getUsername().split(","));
			for (int j = 0; j < usercode.size(); j++) {
				if (!StringUtil.isEmpty(record.getGroupCode())) {
					List<String> groupCode = Arrays.asList(record.getGroupCode().split(","));
					List<String> groupName = Arrays.asList(record.getGroupCodeName().split(","));
					for (int i = 0; i < groupCode.size(); i++) {
						HrmsRoleGroupLeader _newBean = new HrmsRoleGroupLeader();
						_newBean.setUsercode(usercode.get(j));
						_newBean.setUsername(username.get(j));
						_newBean.setGroupCode(groupCode.get(i));
						_newBean.setGroupCodeName(groupName.get(i));
						_newBean.setId(String.valueOf(IdWork.id.nextId()));
						_newBean.setCreateDate(new Date());
						_newBean.setUpdateDate(new Date());
						_newBean.setIsDeleted("N");
						ThpsUser user = UserInfoHolder.getCurrentUserInfo();
						if (user != null) {
							_newBean.setCreateUser(user.getUsercode());
							_newBean.setCreateUserName(user.getUsername());
							_newBean.setUpdateUser(user.getUsercode());
							_newBean.setUpdateUserName(user.getUsername());
						}
						//如果已存在就跳过
						Example example = new Example(HrmsRoleGroupLeader.class);
						Example.Criteria criteria = example.createCriteria();
						criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
						criteria.andEqualTo("usercode", _newBean.getUsercode());
						criteria.andEqualTo("groupCode", _newBean.getGroupCode());
						List<HrmsRoleGroupLeader> list = mapper.selectByExample(example);
						if (list == null || list.size() == 0) {
							mapper.insertSelective(_newBean);
						}
					}

				}
			}
			return usercode.size();
		}
		return 0;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsRoleGroupLeader record) {
		//根据usercode 先删除原来的 重新添加
		HrmsRoleGroupLeader del = new HrmsRoleGroupLeader();
		del.setUsercode(record.getUsercode());
		mapper.delete(del);
		save(record);
		return 1;
	}
	
	

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsRoleGroupLeader record = new HrmsRoleGroupLeader();
		record.setUsercode(id);
		return mapper.delete(record);  //根据usercode 全部干掉
	}

	@Override
	public HrmsRoleGroupLeader selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsRoleGroupLeader> getDataSetList(Page page, HrmsRoleGroupLeader record) {
		List<HrmsRoleGroupLeader> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsRoleGroupLeaderBase> getRolegroupleaderAllList() {
		return mapper.getRolegroupleaderAllList() ;
	}

	@Override
	public List<String> getRoleByUserCode(String usercode) {
		Example example = new Example(HrmsRoleGroupLeader.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("usercode", usercode);
		List<HrmsRoleGroupLeader> list = mapper.selectByExample(example);
		if (list != null && list.size() > 0){
			List<String> listArray = list.stream().map(HrmsRoleGroupLeader::getGroupCode).collect(Collectors.toList());
			return listArray;
		}
		return null;
	}
}
