package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.bo.IntroductionPublicBO;
import cn.trasen.homs.base.dto.BelongingSystemDTO;
import cn.trasen.homs.base.model.DictType;
import cn.trasen.homs.base.vo.BelongingSystemVO;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 字典类型Service层
 * @Date: 2020/4/29 10:43
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface IDictTypeService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int insert(DictType entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int update(DictType entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int deleted(DictType entity);
    
    /**
     * @Author: Lizhihuo
     * @Description: 启用
     * @Date: 2020/5/16 11:13
     * @Param: 
     * @return: int
     **/
    int dictTypeEnable(DictType entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    List<DictType> getDataList(Page page, DictType entity);


    /**
     * @Description: 根据条件获取系统下拉
     * <AUTHOR>
     * @Date    2025/7/28
     **/
    List<BelongingSystemVO> getBelongingSystem(BelongingSystemDTO dto);

    /**
     * @Description: 公共库引入
     * <AUTHOR>
     * @Date    2025/7/28
     **/
    String addIntroductionPublic(IntroductionPublicBO bo);

    Boolean dictTypeData(String ssoOrgCode);

    DictType getInfoByTypeCode(String typeCode);

}
