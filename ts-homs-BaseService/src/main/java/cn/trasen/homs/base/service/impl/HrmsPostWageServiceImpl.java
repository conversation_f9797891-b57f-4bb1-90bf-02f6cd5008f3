package cn.trasen.homs.base.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.base.bean.PostListReq;
import cn.trasen.homs.base.bean.PostListResp;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.service.CommTableSnapshotService;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.core.exception.BusinessException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import cn.trasen.homs.base.contants.DictContants;
import cn.trasen.homs.base.mapper.HrmsPostWageMapper;
import cn.trasen.homs.base.model.HrmsPostWage;
import cn.trasen.homs.base.service.HrmsPostWageService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**   
 * @Title: HrmsPostWageServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 岗位工资 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月1日 下午10:00:49 
 * @version V1.0   
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPostWageServiceImpl implements HrmsPostWageService {

    @Resource
    HrmsPostWageMapper hrmsPostWageMapper;

    @Autowired
    IDictItemService hrmsDictInfoService;

    @Autowired
    private CommTableSnapshotService commTableSnapshotService;

    @Autowired
    private IPostService postService;

    /**
     * @Title: insert
     * @Description: 新增岗位工资
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int insert(HrmsPostWage entity) {
        Example example = new Example(HrmsPostWage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("postId", entity.getPostId());
        if(StringUtils.isNotBlank(entity.getPolicyStandardId())) {
            example.and().andEqualTo("policyStandardId", entity.getPolicyStandardId());
        }
        List<HrmsPostWage> list = hrmsPostWageMapper.selectByExample(example);
        if(list != null && list.size() > 0) {
            throw new RuntimeException("已设置相同岗位工资配置");
        }
        entity.setPostWageId(String.valueOf(IdWork.id.nextId()));
        entity.setIsEnable(entity.getIsEnable());
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDate(new Date());
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        hrmsPostWageMapper.insert(entity);
        // 增加处理快照
        this.fillDescField(entity);
        commTableSnapshotService.asyncSaveTableSnapshot("comm_post_wage",entity.getPostWageId(),null,entity);
        return 1;
    }

    /**
     * @Title: update
     * @Description: 更新岗位工资
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int update(HrmsPostWage entity) {
        HrmsPostWage postWage = hrmsPostWageMapper.selectByPrimaryKey(entity.getPostWageId());
        postWage.setPostName(entity.getPostName());
        postWage.setPostCategory(entity.getPostCategory());
        postWage.setPostCategoryName(entity.getPostCategoryName());
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        entity.setUpdateDate(new Date());
        hrmsPostWageMapper.updateByPrimaryKeySelective(entity);
        //增加处理快照
        this.fillDescField(postWage);
        this.fillDescField(entity);
        commTableSnapshotService.asyncSaveTableSnapshot("comm_post_wage",
                entity.getPostWageId(),postWage,entity);
        return 1;
    }

    /**
     * @Title: deleted
     * @Description: 删除岗位工资
     * @Param: id
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        HrmsPostWage postWage = hrmsPostWageMapper.selectByPrimaryKey(id);
        if (postWage != null) {
            postWage.setIsDeleted(Contants.IS_DELETED_TURE);
        }
        return hrmsPostWageMapper.updateByPrimaryKeySelective(postWage);
    }

    /**
     * @Title: getDataList
     * @Description: 获取岗位工资列表
     * @Param: page
     * @param entity
     * @Return: List<HrmsPostWage>
     * <AUTHOR>
     */
    @Override
    public List<HrmsPostWage> getDataList(Page page, HrmsPostWage entity) {
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        //获取最新标准对应的岗位工资列表
        List<HrmsPostWage> list = hrmsPostWageMapper.getList(page, entity);
        //获取所有标准数据
        Example example = new Example(HrmsPostWage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsPostWage> policyStandardPostWages = hrmsPostWageMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            //填充政策标准数据
            if(CollUtil.isNotEmpty(policyStandardPostWages)){
                Map<String, List<HrmsPostWage>> collect = policyStandardPostWages.stream().filter(vo-> StringUtils.isNotBlank(vo.getPostId()))
                        .collect(Collectors.groupingBy(HrmsPostWage::getPostId));
                list.forEach(item ->{
                    item.setPolicyStandardPostWages(collect.get(item.getPostId()));
                });
            }
            Map<String, String> postCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.POST_CATEGORY); // 岗位类别字典集合
            if (postCategoryMap != null && postCategoryMap.size() > 0) {
                for (HrmsPostWage wage : list) {

                    wage.setPostCategoryName(postCategoryMap.get(wage.getPostCategory())); // 岗位类别名称
                    this.fillDescFieldText(wage);
                }
            }
        }
        return list;
    }

    /**
     * 查询岗位工资设置列表
     * @param entity
     * @return
     */
    @Override
    public List<HrmsPostWage> getPostWagesSettingList(HrmsPostWage entity){
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        return hrmsPostWageMapper.getPostWagesSettingList(entity);
    }

    /**
     * 批量保存岗位工资数据
     * @param entity
     * @return
     */
    @Transactional(readOnly = false)
    @Override
    public void postWagesSettingBatchSave(HrmsPostWage entity){
        Assert.hasText(entity.getPostCategory(),"岗位类别不能为空");
        Assert.hasText(entity.getPolicyStandardId(),"政策标准不能为空");
        if(CollUtil.isEmpty(entity.getPolicyStandardPostWages())){
            throw new BusinessException("请至少保存一条数据");
        }
        //获取上个版本政策标准的岗位工资
        List<Map<String,Object>> policyStandardList = hrmsPostWageMapper.getPolicyStandardList(UserInfoHolder.getCurrentUserCorpCode());
        String oldPolicyStandardId = "";
        List<HrmsPostWage> oldData = null;
        if(CollUtil.isNotEmpty(policyStandardList)) {
            for (int i = 0; i < policyStandardList.size(); i++) {
                if(entity.getPolicyStandardId().equals(policyStandardList.get(i).get("policy_standard_id")) && i<policyStandardList.size()-1){
                    oldPolicyStandardId = Convert.toStr(policyStandardList.get(i+1).get("policy_standard_id"));
                }
            }
            if(StringUtils.isNotBlank(oldPolicyStandardId)){
                HrmsPostWage serachOldPostWage = new HrmsPostWage();
                serachOldPostWage.setPolicyStandardId(oldPolicyStandardId);
                serachOldPostWage.setPolicyStandardId(oldPolicyStandardId);
                oldData = getPostWagesSettingList(serachOldPostWage);
            }
        }
        List<HrmsPostWage> tempdata  = null;
        for (HrmsPostWage postWage : entity.getPolicyStandardPostWages()) {
            postWage.setPolicyStandardId(entity.getPolicyStandardId());
            postWage.setPostCategory(entity.getPostCategory());
            if(CollUtil.isNotEmpty(oldData)){
                tempdata = oldData.stream().filter(vo->vo.getPostId().equals(postWage.getPostId())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(tempdata)){
                    if("true".equals(entity.getIsUseOldPerformanceWage()) && postWage.getPerformanceWage() == null){
                        postWage.setPerformanceWage(tempdata.get(0).getPerformanceWage());
                    }
                    if("true".equals(entity.getIsUseOldAwardWage()) && postWage.getAwardWage() == null){
                        postWage.setAwardWage(tempdata.get(0).getAwardWage());
                    }
                }
            }
            if(StringUtils.isNotBlank(postWage.getPostWageId())){
                update(postWage);
            }else{
                postWage.setIsEnable("1");
                insert(postWage);
            }
        }
    }

    @Override
    public HrmsPostWage getDataByPostWageId(HrmsPostWage hrmsPostWage) {
        Example example = new Example(HrmsPostWage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("postId",hrmsPostWage.getPostId());
        criteria.andEqualTo("ssoOrgCode",UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("isEnable","1");
        if(StringUtils.isNotBlank(hrmsPostWage.getPolicyStandardId())){
            criteria.andEqualTo("policyStandardId",hrmsPostWage.getPolicyStandardId());
        }
        HrmsPostWage postWage = hrmsPostWageMapper.selectOneByExample(example);
        return postWage;
    }

    @Override
    @Transactional(readOnly = false)
    public void batchEnable(List<String> postWageIds, String enable) {
        Example example = new Example(HrmsPostWage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("postWageId",postWageIds);
        HrmsPostWage postWage = new HrmsPostWage();
        postWage.setIsEnable(enable);
        for (String postWageId : postWageIds) {
            HrmsPostWage oldWage = hrmsPostWageMapper.selectByPrimaryKey(postWageId);
            HrmsPostWage newWage = BeanUtil.copyProperties(oldWage, HrmsPostWage.class);
            newWage.setIsEnable(enable);
            //增加处理快照
            this.fillDescField(oldWage);
            this.fillDescField(newWage);
            commTableSnapshotService.asyncSaveTableSnapshot("comm_post_wage",
                    postWageId,oldWage,newWage);
        }
        hrmsPostWageMapper.updateByExampleSelective(postWage,example);
    }

    /**
     *
     * @Title importData
     * @Description 导入
     * @return Map
     * @date 2024��10��8�� ����3:11:08
     * <AUTHOR>
     */
    @Transactional(readOnly = false)
    @Override
    public Map<String,Object> importData(List<HrmsPostWage> list){
        log.info("导入的数据:" + list);
        if(CollectionUtil.isEmpty(list)){
            throw new BusinessException("请至少添加一条数据");
        }
        //岗位类别
        Map<String, String> postCategoryConvertDictMap = convertPostCategoryDictMap(DictContants.POST_CATEGORY);
        Map<String, String> postMap = convertPostMap();
        //获取政策标准列表
        Map<String, String> policyStandardMap = convertPolicyStandardMap();

        StringBuffer errorMsg =  new StringBuffer();
        int sucessNum = 0;
        for (HrmsPostWage postWage : list) {
            if (StringUtils.isNotBlank(postWage.getPostCategoryName())) {//岗位类别
                postWage.setPostCategory(postCategoryConvertDictMap.get(postWage.getPostCategoryName().trim()));
            }
            if (StringUtils.isNotBlank(postWage.getPostName())) {//岗位类别
                postWage.setPostId(postMap.get(postWage.getPostName().trim()+"@"+ postWage.getPostCategoryName().trim()));
            }
            if (StringUtils.isNotBlank(postWage.getPolicyStandardName())) {//政策标准
                postWage.setPolicyStandardId(policyStandardMap.get(postWage.getPolicyStandardName().trim()));
            }
            postWage.setIsDeleted("N");
            postWage.setIsEnable("1");
            try {
                sucessNum += insert(postWage);
            }catch (Exception e){
                errorMsg.append("岗位："+postWage.getPostName()+"[类别："+postWage.getPostCategoryName()+"]导入失败："+e.getMessage()).append(".\n");
                e.printStackTrace();
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("successNum",sucessNum);
        map.put("errorNum",list.size() - sucessNum);
        map.put("errorMsg",errorMsg.toString());
        return map;
    }
    /**
     * @Title: batchInsert
     * @Description: 批量插入
     * @param list
     * @Return int
     * <AUTHOR>
     * @date 2020年6月8日 下午3:17:03
     */
    @Override
    public int batchInsert(List<HrmsPostWage> list) {
        return hrmsPostWageMapper.batchInsert(list);
    }

    private void fillDescField(HrmsPostWage record) {
        // 填充isEnable
        if (StringUtils.isNotBlank(record.getIsEnable())) {
            if(StrUtil.equals("1",record.getIsEnable())){
                record.setIsEnable("启用");
            }else if(StrUtil.equals("2",record.getIsEnable()))
                record.setIsEnable("禁用");
        }
    }

    private void fillDescFieldText(HrmsPostWage record) {
        // 填充isEnable
        if (StringUtils.isNotBlank(record.getIsEnable())) {
            if(StrUtil.equals("1",record.getIsEnable())){
                record.setIsEnableLabel("启用");
            }else if(StrUtil.equals("2",record.getIsEnable()))
                record.setIsEnableLabel("禁用");
        }
    }

    //岗位类别
    private Map<String, String> convertPostCategoryDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItem> dictItemList = hrmsDictInfoService.getDictItemByTypeCode(dictType);
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItem d : dictItemList) {
                map.put(d.getItemName(),d.getItemNameValue());
            }
        }
        return map;
    }

    //岗位等级
    private Map<String, String> convertPostMap() {
        Map<String, String> map = Maps.newHashMap();
        List<PostListResp> postList = postService.getList(new PostListReq());
        if (CollectionUtils.isNotEmpty(postList)) {
            for (PostListResp d : postList) {
                map.put(d.getPostName()+"@"+d.getPostCategoryName(),d.getPostId());
            }
        }
        return map;
    }

    //政策标准
    private Map<String, String> convertPolicyStandardMap() {
        Map<String, String> map = Maps.newHashMap();
        List<Map<String,Object>> policyStandardList = hrmsPostWageMapper.getPolicyStandardList(UserInfoHolder.getCurrentUserCorpCode());
        if (CollectionUtils.isNotEmpty(policyStandardList)) {
            for (Map<String,Object> d : policyStandardList) {
                map.put(Convert.toStr(d.get("policy_standard_name")),Convert.toStr(d.get("policy_standard_id")));
            }
        }
        return map;
    }
}
