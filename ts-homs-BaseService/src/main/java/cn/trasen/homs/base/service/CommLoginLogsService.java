package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommLoginLogsService
 * @Description TODO
 * @date 2023��9��20�� ����10:30:27
 * <AUTHOR>
 * @version 1.0
 */
public interface CommLoginLogsService {

    /**
     * @Title save
     * @Description 新增
     * @param record
     * @return Integer
     * @date 2023��9��20�� ����10:30:27
     * <AUTHOR>
     */
    Integer save(CommLoginLogs record,String orgCode);

    /**
     * @Title update
     * @Description 修改
     * @param record
     * @return Integer
     * @date 2023��9��20�� ����10:30:27
     * <AUTHOR>
     */
    Integer update(CommLoginLogs record);

    /**
     * 
     * @Title deleteById
     * @Description 根据ID删除
     * @param id
     * @return Integer
     * @date 2023��9��20�� ����10:30:27
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @Title selectById
     * @Description 根据ID查询
     * @return CommLoginLogs
     * @date 2023��9��20�� ����10:30:27
     * <AUTHOR>
     */
    CommLoginLogs selectById(String id);

    /**
     * @Title getDataSetList
     * @Description 分页查询
     * @param page
     * @param record
     * @return DataSet<CommLoginLogs>
     * @date 2023��9��20�� ����10:30:27
     * <AUTHOR>
     */
    DataSet<CommLoginLogs> getDataSetList(Page page, CommLoginLogs record);

    /**
     * 
     * @MethodName: updateByUserCode
     * @Description: TODO
     * <AUTHOR>
     * @param record void
     * @date 2023-09-23 11:48:06
     */
    void updateByUserCode(CommLoginLogs record);

    /**
     * 
     * @MethodName: updateDisable
     * @Description: TODO
     * <AUTHOR>
     * @param employeeId
     * @param status void
     * @date 2023-09-23 03:05:15
     */
    void updateDisable(String employeeId, String status);

    /**
     * @param loginDate: 登录日期
     * @param loginType: 登录方式 空表示不区别
     * @return Integer
     * <AUTHOR>
     * @description 统计从当前时间起,不同的登录方式的登录次数
     * @date 2023/11/16 14:20
     */
    Integer getLogins(String loginDate ,String loginType,String queryDate);

    List<CommLoginLogs> getLoginList(String loginDate,String queryDate);

    String selectOpenIdByUserCode(String usercode);

    String selectOrgCodeByUserCode(String usercode);

    String getUserOnlineListCacheKey();

    List<Map<String, String>> getMultipleOrg(String orgCode,String userCode);

	String getUserCodeByOldCode(String usercode);

}
