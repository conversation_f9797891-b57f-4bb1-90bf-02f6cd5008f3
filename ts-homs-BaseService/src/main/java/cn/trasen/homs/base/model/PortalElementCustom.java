package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_portal_element_custom")
@Setter
@Getter
public class PortalElementCustom {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 模板id
     */
    @Column(name = "theme_id")
    @ApiModelProperty(value = "模板id")
    private String themeId;

    /**
     * 元素id
     */
    @Column(name = "element_id")
    @ApiModelProperty(value = "元素id")
    private String elementId;

    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private Integer height;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Integer width;

    /**
     * 最小高度
     */
    @Column(name = "min_height")
    @ApiModelProperty(value = "最小高度")
    private Integer minHeight;

    /**
     * 最小宽度
     */
    @Column(name = "min_width")
    @ApiModelProperty(value = "最小宽度")
    private Integer minWidth;

    /**
     * 最大高度
     */
    @Column(name = "max_height")
    @ApiModelProperty(value = "最大高度")
    private Integer maxHeight;

    /**
     * 最大宽度
     */
    @Column(name = "max_width")
    @ApiModelProperty(value = "最大宽度")
    private Integer maxWidth;

    /**
     * x坐标
     */
    @ApiModelProperty(value = "x坐标")
    private Integer xpos;

    /**
     * y坐标
     */
    @ApiModelProperty(value = "y坐标")
    private Integer ypos;

    /**
     * 元素是否拖拽  0否  1是
     */
    @Column(name = "is_draggable")
    @ApiModelProperty(value = "元素是否拖拽  0否  1是")
    private Integer isDraggable;

    /**
     * 元素是否缩放  0否 1是
     */
    @Column(name = "is_resizable")
    @ApiModelProperty(value = "元素是否缩放  0否 1是")
    private Integer isResizable;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建部门编码")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}