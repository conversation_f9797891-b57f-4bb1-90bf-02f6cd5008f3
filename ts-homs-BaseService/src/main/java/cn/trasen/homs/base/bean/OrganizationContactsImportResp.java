package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: package cn.trasen.homs.baseservice.bean;
 * @className: OrganizationContactsImportResp
 * @author: chenbin
 * @description: TODO
 * @date: 2023/11/21 16:35
 * @version: 1.0
 */

@Data
public class OrganizationContactsImportResp {
    @ApiModelProperty(value = "更新数量")
    Integer updates;
    @ApiModelProperty(value = "插入数量")
    Integer inserts;
}
