package cn.trasen.homs.base.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: OrgDTO
 * @Description: 集成平台同步科室数据DTO
 * <AUTHOR>
 * @date 2022年12月15日 下午3:57:56
 * 
 */
@Getter
@Setter
public class OrgDTO {
	private String deptId;  //科室ID
	private String sysCode;  //系统编码
	private String sysName;  //系统名称
	private String orgCode;  //医院编码   必填
	private String orgName;  //医院名称   必填
	private String hospCode;  //院区编码
	private String hospName;  //院区编码
	private String createUserId; //创建人id
	private String updateUserId; //更新人id
	private String createDate;  //创建时间
	private String updateDate;  //更新时间
	private String deptCode;  //科室编码 必填
	private String name;//科室名称 必填
	private String parentCode;  //父ID
	private String parentName;  //父ID
	private String typeCode;  //科室类别编码
	private String typeName;  //科室类别名称
	private String address;  //科室地址
	private String dutyScope;  //业务范围
	private String pyCode; //拼音码
	private String wbCode; //五笔码
	
	private String enabled;  //状态  必填  Y-有效 N-无效
	private Integer orderNum;  //排序号 
	private Integer isFact;  //是否实际科室  0、否，1、是
	private String isWard; //是否病区  0-否 1-是
	private String isDelete;  //删除状态  必填 1已删除  0 未删除
	private String layer;  //科室层级
	private String dutyPhone;  //值班电话
	private String introduce;  //科室简介
	private String remark; //备注
	private String leaderId;  //主管领导ID
	private String leaderName;  //主管领导ID
	private String headDeptId;  //科室主任ID
	private String headDeptName;  //科室主任名称
	private String headNurseId;  //护士长ID
	private String headNurseName;  //护士长名称
}
