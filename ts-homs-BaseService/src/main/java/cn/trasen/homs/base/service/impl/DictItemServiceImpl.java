package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import cn.trasen.homs.base.service.IDictTypeService;
import cn.trasen.homs.core.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.BootComm.utils.RedisUtil;
import cn.trasen.homs.base.bean.DictItemListReq;
import cn.trasen.homs.base.mapper.DictItemMapper;
import cn.trasen.homs.base.mapper.DictTypeMapper;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.DictType;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 字典项目Impl层
 * @Date: 2020/4/29 10:47
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
@Slf4j
public class DictItemServiceImpl implements IDictItemService {

    @Autowired
    private DictItemMapper dictItemMapper;

    @Autowired
    private DictTypeMapper dictTypeMapper;

    @Autowired
    private IDictTypeService idictTypeService;

    @Resource
    private RedisUtil redisUtil;


    final static String redisDictKey = "dictKey:";
    final static String redisDictALLKey = "dictKey:ALL";

    final static String REDISDICTALL = "dictKey:*";

    /**
     * @Author: Lizhihuo
     * @Description: 增加
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(DictItem entity) {
        //通过dicTypeId查出dictType属于哪个机构
        String ssoOrgCode = dictTypeMapper.selectByPrimaryKey(entity.getDicTypeId()).getSsoOrgCode();
        if (StringUtils.isNotEmpty(entity.getSsoOrgCode())){
            ssoOrgCode = entity.getSsoOrgCode();
        }
        int count;
        //判断是否重复
        Example example = new Example(DictItem.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isEmpty(entity.getIsDeleted())) {
            criteria.andEqualTo("isDeleted", Contants.IS_DELETED_TURE);
        }
        criteria.andEqualTo("itemCode", entity.getItemCode());
        criteria.andEqualTo("dicTypeId", entity.getDicTypeId());
        criteria.andEqualTo("ssoOrgCode", ssoOrgCode);
        List<DictItem> dictItemList = dictItemMapper.selectByExample(example);
        if (dictItemList.size() == 0) {
            String versionId = String.valueOf(IdWork.id.nextId());
            entity.setId(versionId);
            if (StringUtils.isEmpty(entity.getIsDeleted())){
                entity.setIsDeleted(Contants.IS_DELETED_FALSE);
            }
            entity.setIsEnable(EnableEnum.Y.getKey());

            entity.setCreateDate(new Date());
            entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
            entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
            entity.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
            entity.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
            entity.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
            entity.setSsoOrgCode(ssoOrgCode);
            //dossierCategory.setOrgCode(UserInfoHolder.getCurrentUserInfo().getOrgRang());
            count = dictItemMapper.insertSelective(entity);
        } else {
            log.error("新增失败，机构编号:"+entity.getSsoOrgCode()+",字典项目编码："+entity.getItemCode());
            count = 0;
        }
        redisUtil.del(redisDictKey + entity.getDictTypeCode() + ssoOrgCode);
        redisUtil.del(redisDictALLKey);
        // 写入缓存
//        if (count > 0) {
//            List<DictItem> list = dictItemMapper.getDictItemByDicTypeId(entity.getDicTypeId());
//            if (StringUtils.isNoneBlank(entity.getDictTypeCode())) {
//                redisUtil.del(redisDictKey + entity.getDictTypeCode());
//                redisUtil.del(redisDictALLKey);
////
////                System.out.println("删除缓存中的key=========>" + entity.getDictTypeCode());
//                redisUtil.set(redisDictKey+entity.getDictTypeCode(), JSON.toJSON(list).toString());
//            }
//        }
        return count;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(DictItem entity) {

//        DictItem dictItem = dictItemMapper.selectByPrimaryKey(entity.getId());


        //先删除redis缓存
//        if (StringUtils.isNoneBlank(entity.getDictTypeCode())) {
//            redisUtil.del(redisDictKey + entity.getDictTypeCode());
//            System.out.println("删除缓存中的key=========>" + entity.getDictTypeCode());
//        }
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        //重新写入redis缓存
        // List<DictItem> itemList = dictItemMapper.getDictItemByDicTypeId(entity.getDicTypeId());
        // redisUtil.set(redisDictKey+entity.getDictTypeCode(), JSON.toJSON(itemList).toString());

        int exeRow = dictItemMapper.updateByPrimaryKeySelective(entity);
        redisUtil.del(redisDictKey + entity.getDictTypeCode() + entity.getSsoOrgCode());
        redisUtil.del(redisDictALLKey);
        return exeRow;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(DictItem entity) {
        DictItem dictItem = dictItemMapper.getDictItemById(entity);

        Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("dicTypeId", dictItem.getDicTypeId());
        List<DictItem> dictItemList = dictItemMapper.selectByExample(example);

        if (dictItemList != null) {
            for (DictItem item : dictItemList) {
                if (item.getId().equals(entity.getId())) {
                    dictItemList.remove(item);
                    break;
                }
            }
        }

        dictItem.setIsDeleted(Contants.IS_DELETED_TURE);
        int exeRow = dictItemMapper.updateByPrimaryKeySelective(dictItem);
        redisUtil.del(redisDictKey + dictItem.getDictTypeCode() + dictItem.getSsoOrgCode());
        redisUtil.del(redisDictALLKey);
        return exeRow;
    }


    /**
     * @description: 删除
     * @return:
     * @author: liyuan
     * @createTime: 2021/8/6 16:13
     */
    @Override
    @Transactional(readOnly = false)
    public void delete(String typeId, String itemCode) {


        DictItem dictItem = new DictItem();
        dictItem.setIsDeleted(Contants.IS_DELETED_TURE);

        Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("dicTypeId", typeId).andEqualTo("itemCode", itemCode);
        dictItemMapper.updateByExampleSelective(dictItem, example);


        List<DictItem> dictItemList = dictItemMapper.selectByExample(example);
        DictItem _dictItem = dictItemList.get(0);
        _dictItem.setIsDeleted(null);
        DictItem item = dictItemMapper.getDictItemById(_dictItem);
        if (item != null) {
            redisUtil.del(redisDictKey + item.getDictTypeCode() + item.getSsoOrgCode());
        }
        redisUtil.del(redisDictALLKey);
    }


    /**
     * @description: 禁用启用
     * @return:
     * @author: liyuan
     * @createTime: 2021/8/6 16:13
     */
    @Override
    @Transactional(readOnly = false)
    public void enable(String typeId, String itemCode, String enable) {

        DictItem dictItem = new DictItem();
        dictItem.setIsEnable(enable);
        Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("dicTypeId", typeId).andEqualTo("itemCode", itemCode);
        dictItemMapper.updateByExampleSelective(dictItem, example);

        List<DictItem> dictItemList = dictItemMapper.selectByExample(example);
        DictItem item = dictItemMapper.getDictItemById(dictItemList.get(0));
        if (item != null) {
            redisUtil.del(redisDictKey + item.getDictTypeCode() + item.getSsoOrgCode());
        }
        redisUtil.del(redisDictALLKey);
    }


    /**
     * @Author: Lizhihuo
     * @Description: 启用
     * @Date: 2020/5/16 10:53
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int dictItemEnable(DictItem entity) {
        DictItem dictItem = dictItemMapper.getDictItemById(entity);

        dictItem.setIsDeleted(Contants.IS_DELETED_FALSE);//启用
        int count = dictItemMapper.updateByPrimaryKeySelective(dictItem);

        redisUtil.del(redisDictKey + dictItem.getDictTypeCode() + dictItem.getSsoOrgCode());
        redisUtil.del(redisDictALLKey);

//        if (count > 0) {
////            Example example = new Example(DictItem.class);
////            example.createCriteria().andEqualTo("dicTypeId", dictItem.getDicTypeId());
////            List<DictItem> dictItemList = dictItemMapper.selectByExample(example);
////            if (StringUtils.isNoneBlank(dictItem.getDictTypeCode())) {
////                redisUtil.del(redisDictKey + entity.getDictTypeCode());
////                redisUtil.del(redisDictALLKey);
////            }
//        }
        return count;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询
     * @Date: 2020/4/29 15:03
     * @Param:
     * @return: int
     **/
    @Override
    public List<DictItem> getDataList(Page page, DictItem entity) {
        Example example = new Example(DictItem.class);
        example.createCriteria()
                .andEqualTo("dicTypeId", entity.getDicTypeId());
        //根据当前登录账号机构编码过滤查询数据
//                .andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        //example.and().andEqualTo("dicTypeId", entity.getDicTypeId());
        example.and().andEqualTo("isEnable", "1");
        //example.and().andEqualTo("isDeleted", "N");
        if (StringUtils.isNotEmpty(entity.getFuzzyScreening())){
            Example.Criteria criteria = example.createCriteria();
            criteria.andLike("itemName", "%" + entity.getFuzzyScreening() + "%")
            .orLike("itemCode", "%" + entity.getFuzzyScreening() + "%");
            example.and(criteria);
        }else {
            if (StringUtils.isNotBlank(entity.getItemName())) {
                example.and().andLike("itemName", "%" + entity.getItemName() + "%");
            }
            if (StringUtils.isNotBlank(entity.getItemCode())) {
                example.and().andLike("itemCode", "%" + entity.getItemCode() + "%");
            }
        }
        return dictItemMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 通过ID查询字典项目
     * @Date: 2020/4/29 16:15
     * @Param:
     * @return: cn.trasen.system.model.DictItem
     **/
    @Override
    public DictItem getDictItemById(DictItem entity) {
        return dictItemMapper.getDictItemById(entity);
    }


    /**
     * @description: 显示隐藏的数据
     * @param: typeCode
     * @return: java.util.List<cn.trasen.basicsbottom.model.DictItem>
     * @author: liyuan
     * @createTime: 2021/8/10 12:49
     */
    @Override
    public List<DictItem> getDictItemByTypeCodeAll(String typeCode) {
        DictItemListReq dictItemListReq = new DictItemListReq();
        dictItemListReq.setTypeCode(typeCode);
        dictItemListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return dictItemMapper.getDictItemByTypeCode(dictItemListReq);
    }


    /**
     * @description: 获取字典列表
     * @param: dictItemListReq
     * @return: java.util.List<cn.trasen.basicsbottom.model.DictItem>
     * @author: liyuan
     * @createTime: 2021/8/17 14:13
     */
    @Override
    public List<DictItem> getDictItemByTypeCode(DictItemListReq dictItemListReq) {
        return dictItemMapper.getDictItemByTypeCode(dictItemListReq);
    }

    @Override
    public DataSet<DictItem> getDictItemByTypeCode(Page page, DictItemListReq dictItemListReq) {
        List<DictItem> dictItemList = dictItemMapper.getDictItemByTypeCode(page, dictItemListReq);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), dictItemList);

    }

    @Override
    public List<DictItem> getPublicDictItemBydicTypeId(String dicTypeId){
        Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("ssoOrgCode", "*PUBLIC*")
                .andEqualTo("dicTypeId", dicTypeId);
        return dictItemMapper.selectByExample(example);
    }

    @Override
    public List<DictItem> getDictItemByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return new ArrayList<>();
        }
        /*Object v = redisUtil.get(redisDictKey + typeCode);
        boolean updateRedis = false;
        if (v == null) {
            updateRedis = true;
        } else if (StringUtils.isBlank(v.toString())) {
            updateRedis = true;
        }
        if (updateRedis) {
            List<DictItem> dictItemList = dictItemMapper.getDictItemByTypeCodeShow(typeCode);
            v = JSON.toJSON(dictItemList).toString();
            redisUtil.set(redisDictKey + typeCode, v);
        }*/
        String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
        //如果未登录则取公共库字典
        if (UserInfoHolder.getCurrentUserInfo() == null){
            ssoOrgCode = "*PUBLIC*";
        }
        List<DictItem> dictItemList = dictItemMapper.getDictItemByTypeCodeShow(Lists.newArrayList(typeCode), ssoOrgCode);
        Object v = JSON.toJSON(dictItemList).toString();
        return JSON.parseArray(v.toString(), DictItem.class);
    }

    @Override
    public List<DictItem> getDictItemByTypeCodeList(List<String> typeCode) {
        if (CollectionUtils.isEmpty(typeCode)) {
            return new ArrayList<>();
        }
        List<DictItem> dictItemList = dictItemMapper.getDictItemByTypeCodeShow(typeCode, UserInfoHolder.getCurrentUserCorpCode());
        Object v = JSON.toJSON(dictItemList).toString();
        return JSON.parseArray(v.toString(), DictItem.class);
    }

    @Transactional(readOnly = false)
    @Override
    public void setDictItemByTypeCode(DictItem record) {
        if (StringUtil.isEmpty(record.getDictTypeCode())) {
            throw new RuntimeException("字典类型编码不能为空");
        }
        DictType dictType = idictTypeService.getInfoByTypeCode(record.getDictTypeCode());
        if (dictType == null) {
            throw new RuntimeException("字典类型不存在,请先添加字典类型");
        }

        // 检测当前的字典项是否已存在
        Example example = new Example(DictItem.class);
        example.createCriteria().andEqualTo("dicTypeId", dictType.getId())
                .andEqualTo("itemCode", record.getItemCode());
        List<DictItem> list = dictItemMapper.selectByExample(example);

        if (!list.isEmpty()) {
            // 存在则修改
            DictItem exist = list.get(0);
            exist.setItemNameValue(record.getItemNameValue());
            exist.setItemName(record.getItemName());
            exist.setRemark(record.getRemark());
            update(exist);
        } else {
            // 不存在则新增
            record.setDicTypeId(dictType.getId());
            record.setIsEnable(EnableEnum.Y.getKey());
            record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            record.setSsoOrgName(UserInfoHolder.getCurrentUserName());
            record.setSysCode(dictType.getSysCode());
            record.setSort((short) 1);
            insert(record);
        }
    }


    @Override
    public List<DictItem> getDictItemByTypeCode(String typeCode, String ssoOrgCode) {
        if (StringUtils.isBlank(typeCode)) {
            return new ArrayList<>();
        }
        List<DictItem> dictItemList = dictItemMapper.getDictItemByTypeCodeShow(Lists.newArrayList(typeCode), ssoOrgCode);
        Object v = JSON.toJSON(dictItemList).toString();
        return JSON.parseArray(v.toString(), DictItem.class);
    }


    /**
     * @Title: convertDictMap
     * @Description: 查询字典列表并转换成Map形式
     * @Param: dictType 数据字典类型
     * <AUTHOR>
     * @date 2020年3月26日 下午3:18:24
     */
    @Override
    public Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItem> list = getDictItemByTypeCode(dictType);
        if (CollectionUtils.isNotEmpty(list)) {
            for (DictItem d : list) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }


    /**
     * @Title: getDictMap
     * @Description: 获取所有字典值
     * @Params: @return
     * @Return: Map<String, List < Map < String, String>>>
     * <AUTHOR>
     * @date:2021年7月1日
     * @Throws
     */
    public Map<String, Map<String, String>> getDictMap() {

        Object v = redisUtil.get(redisDictALLKey);
        boolean updateRedis = false;
        if (v == null) {
            updateRedis = true;
        } else if (StringUtils.isBlank(v.toString())) {
            updateRedis = true;
        }
        if (updateRedis) {
            Example example = new Example(DictType.class);
            example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

            List<DictType> list = dictTypeMapper.selectByExample(example);

            Example example2 = new Example(DictItem.class);

            example2.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

            example2.orderBy("dicTypeId").asc();

            List<DictItem> itemList = dictItemMapper.selectByExample(example2);

            Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();

            for (DictType dictType : list) {

                Map<String, String> detailMap = new HashMap<>();

                for (DictItem item : itemList) {

                 /*   if (dictType.getTypeCode().equals(item.getDicTypeId())) {

                        detailMap.put(item.getItemCode(), item.getItemName());
                    }*/
                    if (dictType.getTypeCode().equals(item.getDicTypeId()) || dictType.getId().equals(item.getDicTypeId())) {

                        detailMap.put(item.getItemCode(), item.getItemName());
                    }
                }
                map.put(dictType.getTypeCode(), detailMap);
            }

            v = JSON.toJSON(map).toString();
            redisUtil.set(redisDictALLKey, v);


        }
        return JSON.parseObject(v.toString(), new TypeReference<Map<String, Map<String, String>>>() {
        });
    }

    @Override
    public DictItem getDictItemByDictTypeIdAndItemNameValue(String dictTypeId, String itemNameValue, String ssoOrgCode) {
        return dictItemMapper.getDictItemByDictTypeIdAndItemNameValue(dictTypeId, itemNameValue, ssoOrgCode);
    }


    /**
     * @Description: 获取字典树
     * <AUTHOR>
     * @Date 2025/7/28
     **/

    @Override
    public List<Map<String, Object>> getDictItemTreeByCode(String typeCode) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        Map<String, Object> b = new HashMap<String, Object>();
        Example example = new Example(DictType.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("typeCode", typeCode);
		List<DictType> list = dictTypeMapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			b.put("id", list.get(0).getId());
			b.put("pid", null);
			b.put("code", null);
			b.put("name", "全部");
			DictItemListReq dictItemListReq = new DictItemListReq();
			dictItemListReq.setTypeCode(typeCode);
			dictItemListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			List<DictItem> dicts = dictItemMapper.getDictItemByTypeCode(dictItemListReq);
			if(CollUtil.isNotEmpty(dicts)){
				List<Map<String,String>> children = new ArrayList<Map<String,String>>();
				dicts.forEach(i -> {
					Map<String,String> m = new HashMap<String,String>();
					m.put("id", i.getId());
					m.put("pid", i.getDicTypeId());
					m.put("code", i.getItemCode());
					m.put("name", i.getItemName());
					children.add(m);
				});
				b.put("children", children);
			}
		}
		result.add(b);
		return result;
	}
}