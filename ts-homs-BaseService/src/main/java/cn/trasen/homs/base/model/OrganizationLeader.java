package cn.trasen.homs.base.model;

import javax.persistence.Column;
import javax.persistence.Table;

import cn.trasen.homs.core.model.BaseBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/7/27 17:07
 * @description
 */
@Table(name = "comm_organization_leader")
@Data
public class OrganizationLeader extends BaseBean {

	String orgId;

	String roleId;
	String employeeIds;

	// 同步下级部门1同步0不同步
	String synNextOrg;

	// 是否显示组织架构图 1显示0不显示
	String showFramework;
	String sortNo;
    
    @Column(name = "sso_org_code")
    String ssoOrgCode;

}