package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Table(name = "comm_post_wage")
@Setter
@Getter
public class HrmsPostWage {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "post_wage_id")
    @ApiModelProperty(value = "主键ID")
    private String     postWageId;

    /**
     * 岗位ID
     */
    @Column(name = "post_id")
    @ApiModelProperty(value = "岗位ID")
    private String     postId;

    /**
     * 政策标准id
     */
    @Column(name = "policy_standard_id")
    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;

    /**
     * 政策标准名称
     */
    @Transient
    @ApiModelProperty(value = "政策标准名称")
    @Excel(name = "政策标准")
    private String policyStandardName;

    /**
     * 岗位工资
     */
    @Excel(name = "岗位工资")
    @Column(name = "post_wage")
    @ApiModelProperty(value = "岗位工资")
    private BigDecimal postWage;

    /**
     * 基础性绩效
     */
    @Excel(name = "基础性绩效")
    @Column(name = "performance_wage")
    @ApiModelProperty(value = "基础性绩效")
    private BigDecimal performanceWage;

    @Excel(name = "奖励性绩效")
    @Column(name = "award_wage")
    @ApiModelProperty(value = "奖励性绩效")
    private BigDecimal awardWage;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String     isEnable;

    /**
     * 是否启用文本
     */
    @Transient
    @ApiModelProperty(value = "是否启用文本")
    private String isEnableLabel;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String     remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String     enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date       createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String     createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String     createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date       updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String     updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String     updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String     isDeleted;

    // ------- 扩展字段 ------- //
    /**
     * 岗位名称
     */
    @Excel(name = "岗位等级")
    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String  postName;

    /**
     * 岗位类别ID
     */
    @Transient
    @ApiModelProperty(value = "岗位类别ID")
    private String     postCategory;

    /**
     * 岗位类别名称
     */
    @Excel(name = "岗位类别")
    @Transient
    @ApiModelProperty(value = "岗位类别名称")
    private String     postCategoryName;

    @Column(name = "sso_org_code")
    private String     ssoOrgCode;

    @Column(name = "sso_org_name")
    private String     ssoOrgName;

    /**
     * 政策标准对应的岗位工资配置
     */
    @Transient
    List<HrmsPostWage> policyStandardPostWages = new ArrayList<>();

    /**
     * 基础性绩效为空时使用上个政策标准数据 true-是
     */
    @Transient
   private String isUseOldPerformanceWage;

    /**
     * 基础性绩效为空时使用上个政策标准数据 true-是
     */
    @Transient
   private String isUseOldAwardWage;
}