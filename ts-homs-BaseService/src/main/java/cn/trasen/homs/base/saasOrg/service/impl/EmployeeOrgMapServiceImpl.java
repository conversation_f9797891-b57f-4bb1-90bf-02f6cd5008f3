package cn.trasen.homs.base.saasOrg.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeBaseMapper;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.saasOrg.dao.EmployeeOrgMapMapper;
import cn.trasen.homs.base.saasOrg.model.EmployeeOrgMap;
import cn.trasen.homs.base.saasOrg.service.EmployeeOrgMapService;
import cn.trasen.homs.base.saasOrg.vo.AddEmployeeOrgMapVo;
import cn.trasen.homs.base.saasOrg.vo.SsoOrgVo;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName EmployeeOrgMapServiceImpl
 * @Description 员工机构映射表服务实现类
 * @date 2025-06-28 11:30:00
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class EmployeeOrgMapServiceImpl implements EmployeeOrgMapService {

	@Autowired
	private EmployeeOrgMapMapper mapper;

	@Resource
	private CustomEmployeeBaseMapper customEmployeeBaseMapper;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;

	@Override
	@Transactional(readOnly = false)
	public Integer batchSave(AddEmployeeOrgMapVo record) {
		List<String> employeeIdList = record.getEmployeeIdList();
		List<SsoOrgVo> ssoOrgCodeList = record.getSsoOrgCodeList();
		if(CollUtil.isEmpty(employeeIdList) || CollUtil.isEmpty(ssoOrgCodeList)){
			throw new BusinessException("授权员工或机构不能为空！");
		}
		for(String employeeId : employeeIdList){
			for(SsoOrgVo ssoOrgVo : ssoOrgCodeList){
				//先判断该用户是否已经存在，不存在则不新增,存在则更新授权为“启用”
				Example example = new Example(EmployeeOrgMap.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
				criteria.andEqualTo("employeeId", employeeId);
				criteria.andEqualTo("ssoOrgCode", ssoOrgVo.getSsoOrgCode());
				List<EmployeeOrgMap> list = mapper.selectByExample(example);
				EmployeeOrgMap map = null;
				if(CollUtil.isNotEmpty(list)){
					map = list.get(0);
					map.setStatus(1);
					map.setUpdateDate(new Date());
					map.setUpdateUser(UserInfoHolder.getCurrentUserCode());
					map.setUpdateUserName(UserInfoHolder.getCurrentUserName());
					mapper.updateByPrimaryKeySelective(map);
				} else {
					map = new EmployeeOrgMap();
					map.setId(IdGeneraterUtils.nextId());
					map.setEmployeeId(employeeId);
					map.setSsoOrgCode(ssoOrgVo.getSsoOrgCode());
					map.setSsoOrgName(ssoOrgVo.getSsoOrgName());
					map.setStatus(1);
					map.setIsDefault(Contants.IS_DELETED_FALSE);
					map.setIsDeleted(Contants.IS_DELETED_FALSE);
					map.setCreateDate(new Date());
					map.setCreateUser(UserInfoHolder.getCurrentUserCode());
					map.setCreateUserName(UserInfoHolder.getCurrentUserName());
					map.setUpdateDate(new Date());
					map.setUpdateUser(UserInfoHolder.getCurrentUserCode());
					map.setUpdateUserName(UserInfoHolder.getCurrentUserName());
					//有效时间为当前时间
					map.setEffectiveDate(new Date());
					//TODO 默认科室？
					mapper.insertSelective(map);
				}
				//同步用户到平台SSO
				syncUserInfo(map);
			}
		}
		
		return 1;
	}
	
	/**
	 * 将用户信息同步到平台
	 * @param map
	 */
	private void syncUserInfo(EmployeeOrgMap map){
		CustomEmployeeBase customEmployeeBase = customEmployeeBaseMapper.selectByPrimaryKey(map.getEmployeeId());
		ThpsUserReq thpsUser = new ThpsUserReq();
        thpsUser.setId(map.getEmployeeId());
        thpsUser.setUsercode(customEmployeeBase.getEmployeeNo());
        thpsUser.setOldusercode(customEmployeeBase.getEmployeeNo());
        thpsUser.setUsername(customEmployeeBase.getEmployeeName());
        
//        thpsUser.setStatus(map.getStatus());
        //将多机构的授权状态传递到映射表的状态中
        thpsUser.setOrgUserMapStatus(map.getStatus());
        
        thpsUser.setMobileNo(customEmployeeBase.getPhoneNumber());
        thpsUser.setSex(customEmployeeBase.getGender());
        thpsUser.setDeptcode(map.getDefaultDeptCode());
        thpsUser.setCorpcode(map.getSsoOrgCode());
        
		log.info("---同步用户：" + JSON.toJSONString(thpsUser));
		
        // 权限系统同步
		try {
			PlatformResult<String> saveOrUpdate = systemUserFeignService.saveOrUpdate(thpsUser);
			if(saveOrUpdate.isSuccess()){
				log.info("----同步用户返回信息" + saveOrUpdate.getMessage() + "---同步用户返回：" + saveOrUpdate.getObject());
			}
		} catch (Exception e) {
			log.error("----同步用户返回信息失败，报错信息：" +e.getMessage(), e);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public Integer update(EmployeeOrgMap record) {
		EmployeeOrgMap oldRecord = mapper.selectByPrimaryKey(record.getId());
		BeanUtil.copyProperties(record, oldRecord);
		oldRecord.setUpdateDate(new Date());
		oldRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		oldRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		mapper.updateByPrimaryKeySelective(oldRecord);
		//同步到平台
		syncUserInfo(oldRecord);
		return 1;
	}

	@Override
	@Transactional(readOnly = false)
	public Integer updateStatus(String id, Integer status) {
		EmployeeOrgMap record = mapper.selectByPrimaryKey(id);
		record.setStatus(status);
		mapper.updateByPrimaryKeySelective(record);
		//同步到平台
		syncUserInfo(record);
		return 1;
	}

	@Override
	public List<EmployeeOrgMap> selectByEmployeeId(String employeeId) {
		return mapper.selectByEmployeeId(employeeId);
	}

	@Override
	public DataSet<EmployeeOrgMap> selectList(Page page, EmployeeOrgMap record) {
		List<EmployeeOrgMap> records = mapper.selectList(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public Integer saveOrUpdate(EmployeeOrgMap record) {
		Example example = new Example(EmployeeOrgMap.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("ssoOrgCode", record.getSsoOrgCode());
		List<EmployeeOrgMap> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			record.setId(list.get(0).getId());
			record.setUpdateDate(new Date());
			record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			/**
			 * 根据当前所属机构的禁用，停用-则关联停用其他机构的授权，启用-则只关联启用当前机构的授权
			 */
			if(null != record.getStatus() && record.getStatus() == 1){
				//启用
				syncUserInfo(record);
				mapper.updateByPrimaryKeySelective(record);
			} else {
				//停用
				List<EmployeeOrgMap> empMapList = mapper.selectByEmployeeId(record.getEmployeeId());
				for(EmployeeOrgMap orgMap : empMapList){
					if(null != orgMap.getStatus() && orgMap.getStatus() == 1){
						//同步用户到平台SSO
						orgMap.setStatus(0);
						syncUserInfo(orgMap);
					}
				}
				EmployeeOrgMap update = new EmployeeOrgMap();
				update.setStatus(0);
				Example example1 = new Example(EmployeeOrgMap.class);
				Example.Criteria criteria1 = example1.createCriteria();
				criteria1.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
				criteria1.andEqualTo("employeeId", record.getEmployeeId());
				mapper.updateByExampleSelective(update, example1);
			}
		} else {
			if(!ObjectUtils.isEmpty(record.getEmployeeId())){
				record.setId(record.getEmployeeId());
			}
			record.setIsDefault(Contants.IS_DELETED_FALSE);
			record.setIsDeleted(Contants.IS_DELETED_FALSE);
			record.setCreateDate(new Date());
			record.setCreateUser(UserInfoHolder.getCurrentUserCode());
			record.setCreateUserName(UserInfoHolder.getCurrentUserName());
			record.setUpdateDate(new Date());
			record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			//有效时间为当前时间
			record.setEffectiveDate(new Date());
			mapper.insertSelective(record);
		}
		return 1;
	}

}
