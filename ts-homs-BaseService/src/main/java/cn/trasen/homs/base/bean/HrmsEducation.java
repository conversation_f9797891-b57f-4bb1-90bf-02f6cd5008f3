package cn.trasen.homs.base.bean;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsEducation {
	
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    @ApiModelProperty(value = "起止时间")
    private Date startTime;
    
    @ApiModelProperty(value = "起止时间")
    private Date endTime;
    
    @ApiModelProperty(value = "毕业院校")
    private String schoolName;

    @ApiModelProperty(value = "专业名称")
    private String professional;

    @ApiModelProperty(value = "学历类型")
    private String educationType;

    @ApiModelProperty(value = "学历编号")
    private String certificateNumber;
    
    @ApiModelProperty(value = "学制")
    private String schoolSystem;

    @ApiModelProperty(value = "是否最高学历")
    private String highestLevel;
    
    @ApiModelProperty(value = "学位编号")
    private String degreeNumber;
    
    @ApiModelProperty(value = "是否全日制")
    private String learnWay;
    
    @ApiModelProperty(value = "附件")
    private String xlfj;
    
}