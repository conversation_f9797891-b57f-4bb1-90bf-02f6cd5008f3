package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.mapper.SysLogsMapper;
import cn.trasen.homs.base.model.SysLogs;
import cn.trasen.homs.base.service.SysLogsService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;


@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class SysLogsServiceImpl implements SysLogsService {

	@Autowired
	private SysLogsMapper sysLogsMapper;
	
	@Override
	public List<SysLogs> getDataList(Page page, SysLogs sysLogs) {
        return sysLogsMapper.getDataList(sysLogs, page);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(SysLogs sysLogs) {
		//sysLogs.setId(String.valueOf(IdWork.id.nextId()));
		sysLogsMapper.insert(sysLogs);
	}

}
