package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.base.bean.OrganizationAllocationConfigSaveReq;
import cn.trasen.homs.base.bean.OrganizationAllocationSaveNumReq;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationAllocationConfigService;
import cn.trasen.homs.base.service.IOrganizationAllocationService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 10:26
 * @description
 */

@Api(tags = "人力规划")
@RequestMapping("/organizationAllocation")
@RestController
public class OrganizationAllocationController {


    @Autowired
    IOrganizationAllocationService organizationAllocationService;

    @Autowired
    IDictItemService dictItemService;


    @Autowired
    IOrganizationAllocationConfigService organizationAllocationConfigService;


    @ApiOperation(value = "获取列表", notes = "获取列表")
    @PostMapping(value = "/getList")
    public PlatformResult getList(@RequestBody OrganizationListReq organizationListReq) {
        return PlatformResult.success(organizationAllocationService.getList(organizationListReq));
    }

    @ApiOperation(value = "保存编制", notes = "保存编制")
    @PostMapping(value = "/saveNum")
    public PlatformResult saveNum(@RequestBody OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq) {
        organizationAllocationService.saveNum(organizationAllocationSaveNumReq);
        return PlatformResult.success();
    }


    @GetMapping(value = "/download/template/importOrganizationAllocation")
    @ApiOperation(value = "下载机构模板导入模板", notes = "下载机构模板导入模板")
    public void importOrganizationAllocation(HttpServletResponse response, HttpServletRequest request) throws Exception {
        List<DictItem> dictItemList = dictItemService.getDictItemByTypeCode("personal_identity");
        String fileName = "人力规划模板";
        List<String> headList = new ArrayList<>();
        headList.add("组织机构");
        headList.add("床位");
        for (DictItem dictItem : dictItemList) {
            headList.add(dictItem.getItemName());
        }
        List<String> fieldList = new ArrayList<>();

        List<Map<String, Object>> dataList = new ArrayList<>();

        ExportUtil.createExcel("", fileName, headList, fieldList, dataList, response, request);
    }


    @ApiOperation(value = "导入", notes = "导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "type 1:增量 2全量", required = true, dataType = "String")
    })
    @RequestMapping(value = "/import", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file, String type) {
        List<Map<String, Object>> virtualOrgEmployeeImport = (List<Map<String, Object>>) ImportExcelUtil.getExcelDatas(file, Map.class);
        return organizationAllocationService.excelImportNum(virtualOrgEmployeeImport, type);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @RequestMapping(value = "/export", method = {RequestMethod.POST, RequestMethod.GET})
    public void export(OrganizationListReq organizationListReq, HttpServletResponse response, HttpServletRequest request) throws Exception {

        //  @GetMapping(value = "/export")
        //   public void export(HttpServletResponse response, HttpServletRequest request) throws Exception {
        //      OrganizationListReq organizationListReq = new OrganizationListReq();
        Map<String, Object> mapData = organizationAllocationService.getList(organizationListReq);

        Map<String, Object> headMap = (Map<String, Object>) mapData.get("head");
        List<Map<String, Object>> bodyData = (List<Map<String, Object>>) mapData.get("body");

        String excelName = "定岗定编列表";
        List<String> headList = new ArrayList<>();
        List<String> fieldList = new ArrayList<>();


        for (Map.Entry<String, Object> entry : headMap.entrySet()) {
            if (entry.getKey().equals("OrgId") == false) {
                headList.add(entry.getValue().toString());
                fieldList.add(entry.getKey());
            }
        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> map;
        for (Map<String, Object> o : bodyData) {
            o.remove("OrgId");
            dataList.add(o);
        }
        ExportUtil.createExcel(excelName, headList, fieldList, dataList, response, request);
    }


    @ApiOperation(value = "保存人床比", notes = "保存人床比")
    @PostMapping(value = "/saveConfig")
    public PlatformResult saveConfig(@RequestBody List<OrganizationAllocationConfigSaveReq> organizationAllocationConfigSaveReqList) {
        organizationAllocationConfigService.saveConfig(organizationAllocationConfigSaveReqList);
        return PlatformResult.success();
    }

    @ApiOperation(value = "获取人床比列表", notes = "获取人床比列表")
    @PostMapping(value = "/getConfigList")
    public PlatformResult getConfigList() {
        return PlatformResult.success(organizationAllocationConfigService.getList());
    }
}