package cn.trasen.homs.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Table(name = "comm_jobtitle_basic")
@Setter
@Getter
public class HrmsJobtitleBasic {
	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "jobtitle_basic_id")
	@ApiModelProperty(value = "主键ID")
	private String jobtitleBasicId;

	/**
	 * 名称
	 */
	@Column(name = "jobtitle_basic_name")
	@ApiModelProperty(value = "名称")
	private String jobtitleBasicName;

	/**
	 * 等级
	 */
	@Column(name = "jobtitle_basic_grade")
	@ApiModelProperty(value = "等级")
	private Integer jobtitleBasicGrade;

	/**
	 * 父级ID
	 */
	@Column(name = "jobtitle_basic_pid")
	@ApiModelProperty(value = "父级ID")
	private String jobtitleBasicPid;
	
	/**
	 * 分类名称
	 */
	@Column(name = "classification_name")
	@ApiModelProperty(value = "分类名称")
	private String classificationName;
	
	/**
	 * 树ID
	 */
	@Column(name = "tree_ids")
	@ApiModelProperty(value = "树ID")
	private String treeIds;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------ //
	/**
	 * 父级名称
	 */
	@Transient
	@ApiModelProperty(value = "父级名称")
	private String jobtitleBasicPName;
	
	/**
	 * 职称类别ID
	 */
	@Transient
	@ApiModelProperty(value = "职称类别ID")
	private String jobtitleCategoryId;

	/**
	 * 职称类别名称
	 */
	@Transient
	@ApiModelProperty(value = "职称类别名称")
	private String jobtitleCategoryName;
	
	/**
	 * 创建者姓名
	 */
	@Column(name = "jobtitle_salary")
	@ApiModelProperty(value = "职称级别工资")
	private double jobtitleSalary;
	
	
	@Column(name = "jobtitle_upgrade_time")
	@ApiModelProperty(value = "职称升级年限")
	private Integer jobtitleUpgradeTime;
	
	
	
	
	//益阳妇幼字段
	
	/**
	 * 职称工资
	 */
	@Column(name = "base_zcgz")
	@ApiModelProperty(value = "职称工资")
	private double baseZcgz;
	
	/**
	 * 院龄工资
	 */
	@Column(name = "base_ylgz")
	@ApiModelProperty(value = "院龄工资")
	private double baseYlgz;


    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;


	/**
	 * 排序小到大
	 */
	private  int sortNo;
}