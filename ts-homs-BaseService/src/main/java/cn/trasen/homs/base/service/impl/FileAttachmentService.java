package cn.trasen.homs.base.service.impl;

import java.beans.Transient;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.trasen.homs.base.bean.FileAttachmentBusinessReq;
import cn.trasen.homs.base.bean.FileAttachmentByBusinessIdListRes;
import cn.trasen.homs.base.bean.FileAttachmentResp;
import cn.trasen.homs.base.bo.PreviewImageInBO;
import cn.trasen.homs.base.bo.PreviewImageOutBO;
import cn.trasen.homs.base.dto.FileAttachmentSaveInDTO;
import cn.trasen.homs.base.mapper.FileAttachmentMapper;
import cn.trasen.homs.base.model.FileAttachment;
import cn.trasen.homs.base.properties.BasicsBottomAppConfigProperties;
import cn.trasen.homs.base.service.IFileAttachmentService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/6/21 10:13
 * @description
 */

@Service
@Slf4j
public class FileAttachmentService implements IFileAttachmentService {

    @Autowired
    FileAttachmentMapper fileAttachmentMapper;

    @Autowired
    BasicsBottomAppConfigProperties basicsBottomAppConfigProperties;
    
    @Autowired
    GlobalSettingsFeignService globalSettingsFeignService;
    
    @Value("${spring.servlet.multipart.location:${user.dir}/tmp}")
    String uploadDirectory;

    @Override
    /**
     * 保存附件
     * @param fileAttachmentSaveInDTO
     * @return void
     * <AUTHOR>
     * @date 2021/12/31 13:41
     */
    public FileAttachmentResp upload(FileAttachmentSaveInDTO fileAttachmentSaveInDTO) throws IOException {
        if (fileAttachmentSaveInDTO.getFile().isEmpty()) {
            throw new BusinessException("上传失败，请选择文件");
        }
        
        PlatformResult<GlobalSetting> globalSettingResult = globalSettingsFeignService.getGlobalSetting("Y");
    	GlobalSetting globalSetting = globalSettingResult.getObject();
    	
    	Integer allowFileSize = globalSetting.getAllowFileSize();  //允许上传的最大文件大小
    	String allowFileExtension = globalSetting.getAllowFileExtension();   //允许上传的文件后缀名称
    	List<String> allowFileExtensionList = null;
    	if(StringUtils.isNotBlank(allowFileExtension)) {
    		String[] allowFileExtensions = allowFileExtension.split(",");
    		allowFileExtensionList = Arrays.asList(allowFileExtensions);
    	}

        String originalFilename = fileAttachmentSaveInDTO.getFile().getOriginalFilename();
        Long fileSize = fileAttachmentSaveInDTO.getFile().getSize();
        String fileExtension = FileUtil.getSuffix(originalFilename);

        if (CollectionUtils.isNotEmpty(allowFileExtensionList) && !allowFileExtensionList.contains(fileExtension.toLowerCase())) {
        	 throw new BusinessException("上传失败，不允许的文件类型");
        }
        
        if (fileSize > allowFileSize * 1024 * 1024) {
            throw new BusinessException("上传失败，文件最大上传大小为:" + allowFileSize + "M,请重新上传！");
        }

        String fileName = IdWork.id.nextId() + "." + fileExtension;
        String pathName = DateUtil.format(new Date(), "yyyyMMdd");
        if (StringUtils.isBlank(fileAttachmentSaveInDTO.getModuleName()) == false) {
           // pathName = fileAttachmentSaveInDTO.getModuleName() + File.separator + pathName;
            pathName = fileAttachmentSaveInDTO.getModuleName() + "/" + pathName;
        }
        
        String savePath;
        String realPath;
        FileAttachment fileAttachment;
        if (fileAttachmentSaveInDTO.getOperationType() == null) {
            fileAttachmentSaveInDTO.setOperationType(1);
        }
        if (fileAttachmentSaveInDTO.getOperationType().equals(2)) {
            fileAttachment = BeanUtils.addInitAnonymousBean(FileAttachment.class);
            fileAttachment.setCreateUserName("admin");
        } else {
            fileAttachment = BeanUtils.addInitBean(FileAttachment.class);
            fileAttachment.setCreateUserName(UserInfoHolder.getCurrentUserName());
        }
        if ("public".equals(fileAttachmentSaveInDTO.getScope())) {
            fileAttachment.setScope(2);
            savePath = basicsBottomAppConfigProperties.getAttachment().getSaveOpenPath();
           // realPath = basicsBottomAppConfigProperties.getAttachment().getRealResPath() + pathName + File.separator + fileName;
            realPath = basicsBottomAppConfigProperties.getAttachment().getRealResPath() + pathName + "/" + fileName;
        } else {
            savePath = basicsBottomAppConfigProperties.getAttachment().getSavePath();
            fileAttachment.setScope(1);
           // realPath = basicsBottomAppConfigProperties.getAttachment().getRealPath() + "fileAttachment"+File.separator+"downloadFile"+File.separator + fileAttachment.getId();
            realPath = basicsBottomAppConfigProperties.getAttachment().getRealPath() + "fileAttachment/downloadFile/" + fileAttachment.getId();
        }

//        String filePath = savePath + pathName + File.separator + fileName;
//        String pathNames = savePath + pathName;
//        Long fileSize = fileAttachmentSaveInDTO.getFile().getSize();
//        FileUtil.mkdir(pathNames);
//        //因dest指向的是相对路径，需要改变为绝对路径
//        Path path = Paths.get(filePath);
//        fileAttachmentSaveInDTO.getFile().transferTo(path);
        
        String filePath = savePath + pathName + "/" + fileName;
       
        FileUtil.mkdir(savePath + pathName);
        Path path = Paths.get(filePath);
        fileAttachmentSaveInDTO.getFile().transferTo(path);

        fileAttachment.setFileSize(fileSize);
        fileAttachment.setFileExtension(fileExtension);
        fileAttachment.setFilePath(filePath);
        fileAttachment.setModuleName(fileAttachmentSaveInDTO.getModuleName());
        fileAttachment.setOriginalName(originalFilename);
        fileAttachment.setRealPath(realPath);
        fileAttachment.setBusinessId(fileAttachmentSaveInDTO.getBusinessId());
        fileAttachment.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        fileAttachmentMapper.insertSelective(fileAttachment);

        FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
        fileAttachmentResp.setFileName(FileUtil.getName(filePath));
        fileAttachmentResp.setFileExtension(fileExtension);
        fileAttachmentResp.setFileSize(fileSize);
        fileAttachmentResp.setBusinessId(fileAttachmentSaveInDTO.getBusinessId());
        fileAttachmentResp.setModuleName(fileAttachment.getModuleName());
        fileAttachmentResp.setOriginalName(fileAttachment.getOriginalName());
        fileAttachmentResp.setRealPath(fileAttachment.getRealPath());
        fileAttachmentResp.setId(fileAttachment.getId());
        return fileAttachmentResp;
    }

    /**
     * @description: 保存附近
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    @Override
    @Transient
    public void save(FileAttachment fileAttachment) {
        fileAttachment.setCreateUserName(UserInfoHolder.getCurrentUserName());
        fileAttachment.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        fileAttachmentMapper.insertSelective(fileAttachment);
    }


    /**
     * @description: 查询
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    @Override
    public FileAttachment getById(String id,String isdel) {
        FileAttachment fileAttachment = fileAttachmentMapper.selectByPrimaryKey(id);
        if(null == fileAttachment){
              Example example = new Example(FileAttachment.class);
              
              Example.Criteria criteria = example.createCriteria();
      		  criteria.andEqualTo("businessId", id);
      		  if("no".equals(isdel)) {}else {
      			 criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
      		  }
              
              List<FileAttachment> fileAttachments = fileAttachmentMapper.selectByExample(example);
              if(CollectionUtils.isNotEmpty(fileAttachments)) {
            	  return fileAttachments.get(0);
              }else {
            	  return null;
              }
        }
        return fileAttachment;
    }

    /**
     * @description: 查询
     * @param: fileAttachment
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/21 10:17
     */
    @Override
    public List<FileAttachment> getByIds(List<String> ids) {
        Example example = new Example(FileAttachment.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        return fileAttachmentMapper.selectByExample(example);
    }


    /**
     * @description: 关联附件
     * @param: fileAttachmentBusinessReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/23 16:27
     */
    @Override
    @Transient
    public void saveBusinessId(FileAttachmentBusinessReq fileAttachmentBusinessReq) {
        for (String fileid : fileAttachmentBusinessReq.getFileIds()) {
            Example example = new Example(FileAttachment.class);
            FileAttachment fileAttachment = new FileAttachment();
            fileAttachment.setBusinessId(fileAttachmentBusinessReq.getBusinessId());
            example.createCriteria().andEqualTo("id", fileid);
            fileAttachmentMapper.updateByExampleSelective(fileAttachment, example);
        }
    }

    /**
     * @description: 删除附件
     * @param: fileid
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/23 16:29
     */
    @Override
    @Transient
    public void deleteFileId(String fileid) {
        Example example = new Example(FileAttachment.class);
        FileAttachment fileAttachment = new FileAttachment();
        fileAttachment.setIsDeleted(Contants.IS_DELETED_TURE);
        example.createCriteria().andEqualTo("id", fileid);
        fileAttachmentMapper.updateByExampleSelective(fileAttachment, example);

    }


    /**
     * @description: 获取附件
     * @param: fileid
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/23 16:29
     */
    @Override
    @Transient
    public List<FileAttachmentResp> getFileAttachmentByBusinessId(String businessId) {
        
    	
    	if(StringUtils.isBlank(businessId)){
            return new ArrayList<FileAttachmentResp>();
        }else {
        	 Example example = new Example(FileAttachment.class);

             example.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

             if (businessId.contains(",")) {

                 List<Object> ids = Arrays.asList(businessId.split(","));

                 example.and().andIn("id", ids).orIn("businessId", ids);
             } else {

                 example.and().andEqualTo("businessId", businessId).orEqualTo("id", businessId);
             }
             
             List<FileAttachment> fileAttachments = fileAttachmentMapper.selectByExample(example);
             List<FileAttachmentResp> fileAttachmentResps = new ArrayList<>();
             for (FileAttachment fileAttachment : fileAttachments) {
                 FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
                 BeanUtil.copyProperties(fileAttachment, fileAttachmentResp);
                 fileAttachmentResp.setFileName(fileAttachment.getOriginalName());
                 fileAttachmentResps.add(fileAttachmentResp);
             }
             return fileAttachmentResps;
             
        }
        
//        Example example = new Example(FileAttachment.class);
//        example.createCriteria()
//                .andEqualTo("businessId", businessId)
//                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
//        List<FileAttachment> fileAttachments = fileAttachmentServiceMapper.selectByExample(example);
//        List<FileAttachmentResp> fileAttachmentResps = new ArrayList<>();
//        for (FileAttachment fileAttachment : fileAttachments) {
//            FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
//            BeanUtil.copyProperties(fileAttachment, fileAttachmentResp);
//            fileAttachmentResp.setFileName(fileAttachment.getOriginalName());
//            fileAttachmentResps.add(fileAttachmentResp);
//        }
//        return fileAttachmentResps;
    }


    /**
     * @description: 批量获取Business附件信息
     * @param: fileid
     * @return: void
     * @author: liyuan
     * @createTime: 2021/6/23 16:29
     */
    @Override
    public List<FileAttachmentByBusinessIdListRes> listFileAttachmentByBusinessIdList(List<String> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(FileAttachment.class);
        example.createCriteria()
                .andIn("businessId", businessIdList)
                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        List<FileAttachment> fileAttachments = fileAttachmentMapper.selectByExample(example);

        List<FileAttachmentByBusinessIdListRes> fileAttachmentBusinessOutBOList = new ArrayList<>();
        businessIdList.forEach(b -> {
            FileAttachmentByBusinessIdListRes fileAttachmentBusinessOutBO = new FileAttachmentByBusinessIdListRes();
            fileAttachmentBusinessOutBO.setBusinessId(b);
            List<FileAttachmentResp> fileAttachmentOutBOList = new ArrayList<>();
            fileAttachments.forEach(fileAttachment -> {
                if(fileAttachment.getBusinessId().equals(b)) {
                    FileAttachmentResp fileAttachmentOutBO = BeanUtil.copyProperties(fileAttachment, FileAttachmentResp.class);
                    fileAttachmentOutBO.setFileName(fileAttachmentOutBO.getOriginalName());
                    fileAttachmentOutBOList.add(fileAttachmentOutBO);
                }
            });
            fileAttachmentBusinessOutBO.setFileAttachmentRespList(fileAttachmentOutBOList);
            fileAttachmentBusinessOutBOList.add(fileAttachmentBusinessOutBO);
        });
        return fileAttachmentBusinessOutBOList;
    }



    /**
     * 获取基础数据
     *
     * @param businessId
     * @return java.util.List<cn.trasen.basicsbottom.model.FileAttachment>
     * <AUTHOR>
     * @date 2022/1/18 14:38
     */
    public List<FileAttachment> listBaseByBusinessId(String businessId) {
        Example example = new Example(FileAttachment.class);
        example.createCriteria()
                .andEqualTo("businessId", businessId)
                .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        List<FileAttachment> fileAttachments = fileAttachmentMapper.selectByExample(example);

        return fileAttachments;
    }

    @Override
    public void saveAttachmentList(List<FileAttachmentResp> fileAttachment) {
        List<FileAttachment> fileAttachments = new ArrayList<>();
        for (FileAttachmentResp file : fileAttachment) {
            FileAttachment fileAtt = new FileAttachment();
            BeanUtil.copyProperties(file, fileAtt);
            fileAttachments.add(fileAtt);
        }
        for (FileAttachment fileAttachment2 : fileAttachments) {
            fileAttachment2.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            fileAttachmentMapper.insertSelective(fileAttachment2);
        }
    }

    /**
     * @Title: getEmployeeFileAttachmentByBusinessId
     * @Description: 获取人员档案附件, 多个附件
     * @Params: @param businessId
     * @Params: @return
     * @Return: List<FileAttachmentResp>
     * <AUTHOR>
     * @date:2021年9月6日
     * @Throws
     */
    public List<FileAttachmentResp> getFileAttachmentByBusinessIdOrId(String businessId) {

        if (StringUtils.isNotBlank(businessId)) {

            Example example = new Example(FileAttachment.class);

            example.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);

            if (businessId.contains(",")) {

                List<Object> ids = Arrays.asList(businessId.split(","));

                example.and().andIn("id", ids).orIn("businessId", ids);
            } else {

                example.and().andEqualTo("businessId", businessId).orEqualTo("id", businessId);
            }

            List<FileAttachment> fileAttachments = fileAttachmentMapper.selectByExample(example);
            List<FileAttachmentResp> fileAttachmentResps = new ArrayList<>();
            for (FileAttachment fileAttachment : fileAttachments) {
                FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
                BeanUtil.copyProperties(fileAttachment, fileAttachmentResp);
                fileAttachmentResps.add(fileAttachmentResp);
            }
            return fileAttachmentResps;
        }

        return null;
    }


    @Override
    /**
     * 复制businessId相关的文件
     *
     * @param businessId
     * @return void
     * <AUTHOR>
     * @date 2022/1/18 14:34
     */
    public List<FileAttachmentResp> copyBusinessIdFiles(String businessId) {
        List<FileAttachment> fileAttachmentResp = listBaseByBusinessId(businessId);
        String businessIdNew = String.valueOf(IdWork.id.nextId());
        fileAttachmentResp.forEach(f -> {
            f.setId(String.valueOf(IdWork.id.nextId()));
            f.setBusinessId(businessIdNew);
            BeanUtils.updateInitBean(f);
            fileAttachmentMapper.insertSelective(f);
        });
        List<FileAttachmentResp> fileAttachmentRespList = getFileAttachmentByBusinessId(businessIdNew);
        return fileAttachmentRespList;
    }


    @Override
    /**
    * 预览图片
    * @param previewImageInBO
    * @return byte[]
    * <AUTHOR>
    * @date 2022/1/21 10:41
    */
    public PreviewImageOutBO previewImage(PreviewImageInBO previewImageInBO) throws IOException {
        FileAttachment fileAttachment=getById(previewImageInBO.getFileId(),null);
        if (fileAttachment == null) {
            return null;
        }

        if (FileUtil.exist(fileAttachment.getFilePath())==false) {
            return null;
        }

        byte[] fileByte = FileUtil.readBytes(fileAttachment.getFilePath());
        if (fileByte == null) {
            return null;
        }
        if(!StringUtils.isBlank(previewImageInBO.getSize()))
        {
            ByteArrayOutputStream outputStream = null;
            ByteArrayInputStream toStream = null;
            try {
                outputStream = new ByteArrayOutputStream();
                toStream = IoUtil.toStream(fileByte);
                String[] x=previewImageInBO.getSize().split("x");
                Thumbnails.of(toStream).size(Integer.parseInt(x[0]), Integer.parseInt(x[1])).toOutputStream(outputStream);
                fileByte= outputStream.toByteArray();
            } finally {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (toStream != null) {
                    toStream.close();
                }
            }
        }


        PreviewImageOutBO previewFileResp = new PreviewImageOutBO();
        previewFileResp.setFileExtension(fileAttachment.getFileExtension());
        previewFileResp.setFileByte(fileByte);

        return previewFileResp;
    }
}