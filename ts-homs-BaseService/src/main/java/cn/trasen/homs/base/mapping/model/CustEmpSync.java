package cn.trasen.homs.base.mapping.model;

import javax.persistence.Column;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 业务系统员工同步表
 * @date 2025-04-28 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "cust_emp_sync")
@Setter
@Getter
public class CustEmpSync {

    @Column(name = "syscode")
    @ApiModelProperty(value = "所属业务系统编码")
    private String syscode;

    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    @Column(name = "phone_number")
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;
    
    @Column(name = "bus_employee_id")
    @ApiModelProperty(value = "员工ID")
    private String busEmployeeId;

    @Column(name = "bus_employee_no")
    @ApiModelProperty(value = "员工工号")
    private String busEmployeeNo;

    @Column(name = "bus_employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String busEmployeeName;
    
    @Column(name = "bus_org_code")
    @ApiModelProperty(value = "员工所属机构编码")
    private String busOrgCode;
    
    @Column(name = "bus_dept_id")
    @ApiModelProperty(value = "员工所属科室ID")
    private String busDeptId;

    @Column(name = "bus_dept_code")
    @ApiModelProperty(value = "员工所属科室编码")
    private String busDeptCode;

    @Column(name = "bus_dept_name")
    @ApiModelProperty(value = "员工所属科室名称")
    private String busDeptName;
    
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 0=否; 1=是;")
    private String isEnable;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

}
