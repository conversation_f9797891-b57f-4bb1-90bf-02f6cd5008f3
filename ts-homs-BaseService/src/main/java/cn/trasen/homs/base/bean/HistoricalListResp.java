package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 11:12
 * @description
 */

@Data
public class HistoricalListResp {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String text;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    private String auditStatus;


    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private String auditResult;


    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private String createDate;

    @ApiModelProperty(hidden = true)
    private String workflowName;

    @ApiModelProperty(hidden = true)
    private String workflowNumber;

    @ApiModelProperty(hidden = true)
    private String wfInstanceId;

    @ApiModelProperty(hidden = true)
    private String wfCurrentStepName;
}
