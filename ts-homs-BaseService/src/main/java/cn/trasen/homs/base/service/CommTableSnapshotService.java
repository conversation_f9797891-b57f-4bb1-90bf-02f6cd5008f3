package cn.trasen.homs.base.service;

import cn.trasen.homs.base.model.CommTableSnapshot;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommTableSnapshotService
 * @Description TODO
 * @date 2024年6月6日 上午9:05:30
 */
public interface CommTableSnapshotService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年6月6日 上午9:05:30
     * <AUTHOR>
     */
    Integer save(CommTableSnapshot record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年6月6日 上午9:05:30
     * <AUTHOR>
     */
    Integer update(CommTableSnapshot record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年6月6日 上午9:05:30
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return CommTableSnapshot
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年6月6日 上午9:05:30
     * <AUTHOR>
     */
    CommTableSnapshot selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<CommTableSnapshot>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年6月6日 上午9:05:30
     * <AUTHOR>
     */
    DataSet<CommTableSnapshot> getDataSetList(Page page, CommTableSnapshot record);


    void asyncSaveTableSnapshot(String tableName, String rowPkValue, Object o, Object n);
}
