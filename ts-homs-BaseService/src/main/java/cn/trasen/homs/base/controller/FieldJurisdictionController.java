/**
 * @Title: FieldJurisdictionController.java  
 * @Package: cn.trasen.homs.base.controller  
 * @Date: 2021年6月21日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommGroupJurisdiction;
import cn.trasen.homs.base.service.FieldJurisdictionService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
* @ClassName: FieldJurisdictionController  
 * @Author: 86189
 * @Date: 2021年6月21日
 */
@Api(tags = "字段权限控制Controller")
@RestController
public class FieldJurisdictionController {
	
	@Autowired
	private FieldJurisdictionService fieldJurisdictionService;
	
    @ApiOperation(value = "新增权限设置", notes = "新增权限设置")
    @PostMapping("/fieldJurisdiction/insert")
    public PlatformResult<String> insert(@RequestBody List<CommGroupJurisdiction> record) {
        try {
        		
        	int count = fieldJurisdictionService.insert(record);
        	
        	if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    @ApiOperation(value = "修改权限设置", notes = "修改权限设置")
    @PostMapping("/fieldJurisdiction/update")
    public PlatformResult<String> update(@RequestBody CommGroupJurisdiction record) {
        try {
        	
        	int count = fieldJurisdictionService.update(record);
        	if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
        	
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure(e.getMessage());
        }
    }
    
    
    @ApiOperation(value = "获取权限设置列表", notes = "获取权限设置列表")
    @PostMapping("/fieldJurisdiction/getList")
    @ApiImplicitParams({
    	@ApiImplicitParam(name="fieldGroupId",value="权限分组id",required=false,paramType="query",dataType="String")
    })
    public PlatformResult<List<CommGroupJurisdiction>> getList(CommGroupJurisdiction record) {
        try {
        	List<CommGroupJurisdiction> list = fieldJurisdictionService.getList(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
        	return PlatformResult.failure(e.getMessage());
        }
    }
}
