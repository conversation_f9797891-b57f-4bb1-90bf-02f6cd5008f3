package cn.trasen.homs.base.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON>hu<PERSON>
 * @Description: 字典项目
 * @Date: 2020/4/29 10:31
 * @Param:
 * @return:
 **/
@Setter
@Getter
public class DictItemResp {
    /**
     * ID
     */

    private String id;

    /**
     * 字典类型ID
     */
    private String dicTypeId;

    /**
     * 字典项目编码
     */

    private String itemCode;

    /**
     * 字典项目名称
     */

    private String itemName;

    /**
     * 字典项目字段值
     */

    private String itemNameValue;


    /**
     * 字典项目图片
     */

    private String itemImg;

    /**
     * 备注
     */

    private String remark;

    /**
     * 排序
     */

    private Short sort;

    /**
     * 系统编码
     */

    private String sysCode;

    /**
     * 创建人ID
     */

    private String createUser;

    /**
     * 创建人姓名
     */

    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人ID
     */

    private String updateUser;

    /**
     * 更新人姓名
     */

    private String updateUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */

    private String isDeleted;

    /**
     * 机构编码
     */

    private String orgCode;

    /**
     * 院区编码
     */

    private String hospCode;

    /**
     * 创建部门编号
     */

    private String createDept;

    /**
     * 创建部门名称
     */

    private String createDeptName;

    /**
     * 项目中是否有图片（0：否，1：是）
     */

    private Short isItemImg;

    /**
     * 字典类型名称
     */

    private String dictTypeName;

    /**
     * 类型编码
     */

    private String dictTypeCode;
}