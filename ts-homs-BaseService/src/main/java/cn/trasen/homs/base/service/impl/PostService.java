package cn.trasen.homs.base.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.base.bean.PostListReq;
import cn.trasen.homs.base.bean.PostListResp;
import cn.trasen.homs.base.bean.PostSaveReq;
import cn.trasen.homs.base.contants.CommonContants;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.mapper.PostMapper;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.HrmsPostWage;
import cn.trasen.homs.base.model.Post;
import cn.trasen.homs.base.service.CommTableSnapshotService;
import cn.trasen.homs.base.service.HrmsPostWageService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IPostService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @description: 岗位级别
 * @return:
 * @author: liyuan
 * @createTime: 2021/8/6 9:41
 */
@Service
@Primary
public class PostService implements IPostService {
    @Autowired
    PostMapper postMapper;

    @Autowired
    private CustomEmployeeInfoService customEmployeeInfoService;

    @Autowired
    CommTableSnapshotService commTableSnapshotService;

    @Autowired
    HrmsPostWageService hrmsPostWageService;

    @Autowired
    IDictItemService dictItemService;


    @Override
    /**
     * @description: 是否存在
     * @param: employeeReq
     * @return: boolean
     * @author: liyuan
     * @createTime: 2021/8/6 14:40
     */
    public boolean exists(PostListReq postListReq) {
        Example example = new Example(Post.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if (!StringUtils.isBlank(postListReq.getPostCategory())) {
            criteria.andEqualTo("postCategory", postListReq.getPostCategory());
        }

        int count = postMapper.selectCountByExample(example);
        if (count > 0) {
            return true;
        }
        return false;
    }


    @Override
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    public List<PostListResp> getList(PostListReq postListReq) {
        //根据当前登录账号机构编码过滤查询数据
        postListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        postListReq.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        List<PostListResp> posts = postMapper.getList(postListReq);
        for (PostListResp post : posts) {
            post.setIsEnableLable(EnableEnum.getValByKey(post.getIsEnable()));
        }
        return posts;
    }


    @Override
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    public DataSet getPageList(PostListReq postListReq, Page page) {
        //根据当前登录账号机构编码过滤查询数据
//        postListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<PostListResp> posts = postMapper.getList(page, postListReq);
        for (PostListResp post : posts) {
            post.setIsEnableLable(EnableEnum.getValByKey(post.getIsEnable()));
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), posts);
    }


    @Override
    /**
     * @description: 新增等级
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(PostSaveReq postSaveReq) {
        Post post = new Post();
        BeanUtil.copyProperties(postSaveReq, post, "postId");
        post.setCreateDate(new Date());
        post.setUpdateDate(post.getCreateDate());
        post.setPostId(String.valueOf(IdWork.id.nextId()));
        post.setCreateUser(UserInfoHolder.getCurrentUserCode());
        post.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        post.setIsDeleted(Contants.IS_DELETED_FALSE);
        post.setIsEnable(EnableEnum.Y.getKey());
        post.setUpgradeSystem("0");
        if (post.getUpgradeTime() != null) {
            post.setUpgradeSystem("1");
        }
        post.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

        postMapper.insertSelective(post);

        // 每次新增的时候同步一行到薪酬配置
        HrmsPostWage hrmsPostWage = new HrmsPostWage();
        hrmsPostWage.setPostId(post.getPostId());
        hrmsPostWage.setPostWage(BigDecimal.ZERO);
        hrmsPostWage.setPerformanceWage(BigDecimal.ZERO);
        hrmsPostWage.setAwardWage(BigDecimal.ZERO);
        hrmsPostWage.setIsEnable("1");
        hrmsPostWageService.insert(hrmsPostWage);

        // 增加快照 这里塞入一个PostListResp对象
        PostListResp plr = new PostListResp();
        BeanUtil.copyProperties(post, plr);
        plr.setIsEnableLable(EnableEnum.getValByKey(post.getIsEnable()));
        fillDescField(plr);
        commTableSnapshotService.asyncSaveTableSnapshot("comm_post", post.getPostId(), null, plr);

    }


    private void fillDescField(PostListResp postListResp) {

        // 填充 postUpgradePostName
        if (StringUtils.isNotBlank(postListResp.getUpgradePostId())) {
            Post upgradePost = findById(postListResp.getUpgradePostId());
            if (upgradePost != null) {
                postListResp.setUpgradePostName(upgradePost.getPostName());
            }
        }

        // 填充isEnableLable
        if (StringUtils.isNotBlank(postListResp.getIsEnable())) {
            postListResp.setIsEnableLable(EnableEnum.getValByKey(postListResp.getIsEnable()));
        }

        // 填充类型名称
        if (StringUtils.isNotBlank(postListResp.getPostCategory())) {
//            PlatformResult<DictItemResp> ret = dictItemFeignService.getDictItemByDictTypeIdAndItemNameValue("post_category", postListResp.getPostCategory());
//            if (ret.isSuccess() && ret.getObject() != null && ret.getObject().getItemName() != null) {
//                postListResp.setPostCategoryName(ret.getObject().getItemName());
//            }
            DictItem dictItem = dictItemService.getDictItemByDictTypeIdAndItemNameValue("post_category", postListResp.getPostCategory(), UserInfoHolder.getCurrentUserCorpCode());
            if (dictItem != null) {
                postListResp.setPostCategoryName(dictItem.getItemName());
            }

        }

    }


    @Override
    /**
     * @description: 修改等级
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(PostSaveReq postSaveReq) {
        // 增加快照
        String id = postSaveReq.getPostId();
        Post op = selectById(id);


        Post postUpdate = new Post();
        BeanUtil.copyProperties(postSaveReq, postUpdate);
        postUpdate.setUpdateDate(new Date());
        postUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());

        postUpdate.setUpgradeSystem("0");
        if (postUpdate.getUpgradeTime() != null) {
            postUpdate.setUpgradeSystem("1");
        }

        postMapper.updateByPrimaryKeySelective(postUpdate);


        Post np = selectById(id);
        // 替换成resp
        PostListResp opr = new PostListResp();
        BeanUtil.copyProperties(op, opr);
        fillDescField(opr);

        PostListResp npr = new PostListResp();
        BeanUtil.copyProperties(np, npr);
        fillDescField(npr);

        commTableSnapshotService.asyncSaveTableSnapshot("comm_post", id, opr, npr);

    }


    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<PostSaveReq> postSaveReqList) {
        for (PostSaveReq postSaveReq : postSaveReqList) {
            Post postUpdate = new Post();
            postUpdate.setPostId(postSaveReq.getPostId());
            postUpdate.setUpdateDate(new Date());
            postUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            postUpdate.setSortNo(postSaveReq.getSortNo());
            postMapper.updateByPrimaryKeySelective(postUpdate);

            // 暂时业务上没看到修改排序 不做快照
        }
    }


    @Override
    /**
     * @description: 删除等级
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        verify(id);
        Post postUpdate = new Post();
        postUpdate.setPostId(id);
        postUpdate.setIsDeleted(Contants.IS_DELETED_TURE);
        postUpdate.setUpdateDate(new Date());
        postUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        postMapper.updateByPrimaryKeySelective(postUpdate);
    }


    private Post selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return postMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id, String enable) {

//        if (enable.equals(EnableEnum.N.getKey())) {
//            verify(id);
//        }
        // 增加快照
        Post op = selectById(id);

        Post postUpdate = new Post();
        postUpdate.setPostId(id);
        postUpdate.setIsEnable(enable);
        postUpdate.setUpdateDate(new Date());
        postUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        postMapper.updateByPrimaryKeySelective(postUpdate);

        Post np = new Post();
        BeanUtil.copyProperties(op, np);
        np.setIsEnable(enable);
        np.setUpdateDate(new Date());
        np.setUpdateUser(UserInfoHolder.getCurrentUserCode());

        // 替换成resp
        PostListResp opr = new PostListResp();
        BeanUtil.copyProperties(op, opr);
        fillDescField(opr);

        PostListResp npr = new PostListResp();
        BeanUtil.copyProperties(np, npr);
        fillDescField(npr);

        commTableSnapshotService.asyncSaveTableSnapshot("comm_post", id, opr, npr);

    }


    /**
     * @description: 验证
     * @param: orgId
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 11:17
     */
    @Override
    public void verify(String id) {

        Post post = postMapper.selectByPrimaryKey(id);
        if (post == null) {
            throw new BusinessException("数据不存在！");
        }
        CustomEmployeeInfo employeeReq = new CustomEmployeeInfo();
        employeeReq.setGwdj(post.getPostId());
        employeeReq.setPlgw(post.getPostCategory());
        if (customEmployeeInfoService.exists(employeeReq)) {
            throw new BusinessException("岗位等级已被使用，不能禁用和删除！");
        }
    }


    @Override
    public Post findById(String id) {
        return postMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Map<String,Object>> getPostLevel() {
        List<Map<String, Object>> postList = postMapper.getPostType();
        List<Map<String, String>> postLevelList = postMapper.getPostLevel();
        List<Map<String,String>> list = null;
        for (Map<String, Object> postMap : postList) {
            list = new ArrayList<>();
            for (Map<String, String> postLevelMap : postLevelList) {
                if(StrUtil.equals(String.valueOf(postMap.get("postId")),postLevelMap.get("postId"))){
                    list.add(postLevelMap);
                }
            }
            postMap.put("postLevel",list);
        }
        return postList;
    }
}