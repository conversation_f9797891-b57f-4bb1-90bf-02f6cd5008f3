package cn.trasen.homs.base.enums;

import lombok.Getter;

/**   
 * @Title: GenderTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: EnableEnum
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月10日 上午11:04:02 
 * @version V1.0   
 */
@Getter
public enum EnableEnum {

	Y("1", "启用"),

	N("0", "禁用");


	private final String key;
	private final String val;

	private EnableEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (EnableEnum item : EnableEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "N";
	}

	/**
	 * @Title: getKeyByVal
	 * @Description: 根据val获得key值
	 * @param val
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:30:51
	 */
	public static String getKeyByVal(String val) {
		for (EnableEnum item : EnableEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return "禁用";
	}

}
