package cn.trasen.homs.base.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.CommInterfaceLogs;
import cn.trasen.homs.base.service.CommInterfaceLogsService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommInterfaceLogsController
 * @Description TODO
 * @date 2024��8��22�� ����7:03:36
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CommInterfaceLogsController")
public class CommInterfaceLogsController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommInterfaceLogsController.class);

	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;

	/**
	 * @Title saveCommInterfaceLogs
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/interfaceLogs/save")
	public PlatformResult<String> saveCommInterfaceLogs(@RequestBody CommInterfaceLogs record) {
		try {
			commInterfaceLogsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommInterfaceLogs
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/interfaceLogs/update")
	public PlatformResult<String> updateCommInterfaceLogs(@RequestBody CommInterfaceLogs record) {
		try {
			commInterfaceLogsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommInterfaceLogsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommInterfaceLogs>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/interfaceLogs/{id}")
	public PlatformResult<CommInterfaceLogs> selectCommInterfaceLogsById(@PathVariable String id) {
		try {
			CommInterfaceLogs record = commInterfaceLogsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommInterfaceLogsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/interfaceLogs/delete/{id}")
	public PlatformResult<String> deleteCommInterfaceLogsById(@PathVariable String id) {
		try {
			commInterfaceLogsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommInterfaceLogsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommInterfaceLogs>
	 * @date 2024��8��22�� ����7:03:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/interfaceLogs/list")
	public DataSet<CommInterfaceLogs> selectCommInterfaceLogsList(Page page, CommInterfaceLogs record) {
		return commInterfaceLogsService.getDataSetList(page, record);
	}
}
