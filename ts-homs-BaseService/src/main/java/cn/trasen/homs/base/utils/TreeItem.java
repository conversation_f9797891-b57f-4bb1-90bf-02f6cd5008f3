package cn.trasen.homs.base.utils;

import java.util.ArrayList;
import java.util.List;

/**    
  * <P> @Description: 树对象</p>
  * <P> @Date: 2020年8月28日  上午10:36:44 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
public class TreeItem {
	
	private String id;

	private String parentId;

	private String name;
	
	private Boolean open;
	
	private Boolean nocheck;

	private  Integer type;

	private List<TreeItem> children = new ArrayList<>();

	public TreeItem(String id, String parentId, String name, Boolean open) {
		this.id = id;
		this.parentId = parentId;
		this.name = name;
		this.open = open;
	}
	public TreeItem() {

	}

	public TreeItem(String id, String parentId, String name) {
		this.id = id;
		this.parentId = parentId;
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<TreeItem> getChildren() {
		return children;
	}

	public void setChildren(List<TreeItem> children) {
		this.children = children;
	}

	public Boolean getOpen() {
		return open;
	}

	public void setOpen(Boolean open) {
		this.open = open;
	}
	
	public Boolean getNocheck() {
		return nocheck;
	}

	public void setNocheck(Boolean nocheck) {
		this.nocheck = nocheck;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	
}
