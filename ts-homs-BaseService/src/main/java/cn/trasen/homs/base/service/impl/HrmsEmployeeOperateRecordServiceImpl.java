package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.mapper.HrmsEmployeeOperateRecordMapper;
import cn.trasen.homs.base.model.HrmsEmployeeOperateRecord;
import cn.trasen.homs.base.service.HrmsEmployeeOperateRecordService;

/**   
 * @Title: HrmsEmployeeOperateRecordServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工操作记录 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月29日 下午5:09:14 
 * @version V1.0   
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEmployeeOperateRecordServiceImpl implements HrmsEmployeeOperateRecordService {

	@Autowired
	HrmsEmployeeOperateRecordMapper hrmsEmployeeOperateRecordMapper;

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月29日 下午5:10:07
	 */
	@Override
	public int batchInsert(List<HrmsEmployeeOperateRecord> list) {
		return hrmsEmployeeOperateRecordMapper.batchInsert(list);
	}

}
