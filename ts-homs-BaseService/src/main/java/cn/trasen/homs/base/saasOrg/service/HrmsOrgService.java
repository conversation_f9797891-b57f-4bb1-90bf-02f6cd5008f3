package cn.trasen.homs.base.saasOrg.service;

import java.util.List;

import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.vo.HrmsOrgTreeVo;
import cn.trasen.homs.base.saasOrg.vo.OrgData;

/**
 * @ClassName HrmsOrgService
 * @Description 机构服务类
 * @date 2025-07-12 11:30:00
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsOrgService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	Integer saveOrUpdate(HrmsOrg record);

	/**
	 * 
	 * @Title deleteByIdOrgCode
	 * @Description 根据机构编码或者编码删除
	 * @param id
	 * @param orgCode
	 * @return Integer
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	Integer deleteByIdOrgCode(String id, String orgCode);
	
	/**
	 * 
	 * @Title detailByIdOrgCode
	 * @Description 根据机构编码或者编码查询
	 * @param id
	 * @param orgCode
	 * @return Integer
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	HrmsOrg detailByIdOrgCode(String id, String orgCode);
	
	/**
	 * 
	 * @Title selectList
	 * @Description 查询机构列表
	 * @param record
	 * @return List<HrmsOrg>
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	List<HrmsOrg> selectList(HrmsOrg record);
	
	/**
	 * 
	 * @Title selectChildren
	 * @Description 根据机构编码或机构ID查询机构列表
	 * @param orgCode
	 * @return List<HrmsOrg>
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	List<HrmsOrg> selectChildren(String orgCode);
	
	/**
	 * 
	 * @Title selectOrgTree
	 * @Description 查询机构树形列表
	 * @param orgCode
	 * @param orgId
	 * @return List<HrmsOrgTreeVo>
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	List<HrmsOrgTreeVo> selectOrgTree(String orgCode, String orgId);
	
	/**
	 * 
	 * @Title syncSsoOrg
	 * @Description 同步sso的机构
	 * @date 2025-07-12 17:03:36
	 * <AUTHOR>
	 */
	void syncSsoOrg();
	
	/**
	 * 
	 * @Title addOrgData
	 * @Description 新增的机构的其他信息：sso的机构、科室、人员、人事的组织机构、全局参数配置等等
	 * @param data
	 * @date 2025-07-15 9:03:36
	 * <AUTHOR>
	 */
	void addOrgData(OrgData data);
	
	/**
	 * 
	 * @Title updateOrgData
	 * @Description 更新机构的其他信息：sso的机构、科室、人员、人事的组织机构、全局参数配置等等
	 * @param data
	 * @date 2025-07-15 9:03:36
	 * <AUTHOR>
	 */
	void updateOrgData(OrgData data);
	
	/**
	 * 
	 * @Title updateOrgData
	 * @Description 删除的机构的其他信息：sso的机构、科室、人员、人事的组织机构、全局参数配置等等
	 * @param data
	 * @date 2025-07-15 9:03:36
	 * <AUTHOR>
	 */
	void deleteOrgData(OrgData data);
	
}
