//package cn.trasen.homs.base.webSocket;
//
//import java.io.IOException;
//import java.util.Collection;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//import javax.websocket.OnClose;
//import javax.websocket.OnError;
//import javax.websocket.OnOpen;
//import javax.websocket.Session;
//import javax.websocket.server.PathParam;
//import javax.websocket.server.ServerEndpoint;
//
//import org.apache.commons.collections4.MapUtils;
//import org.springframework.beans.factory.config.ConfigurableBeanFactory;
//import org.springframework.context.annotation.EnableAspectJAutoProxy;
//import org.springframework.context.annotation.Scope;
////import org.apache.commons.collections.MapUtils;
//import org.springframework.stereotype.Component;
//
//import lombok.extern.log4j.Log4j;
//
//@Component
////@EnableAspectJAutoProxy(proxyTargetClass = false)
////@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
//@ServerEndpoint(value = "/messagewebsocket/{userCode}")
//@Log4j
//public class MessageWebSocket {
//    
//    private static int onlineCount = 0;
//
//
//    private static ConcurrentHashMap<String,ConcurrentHashMap<String,Session> > webSocketSet = new ConcurrentHashMap<>();
//
//
//    public  ConcurrentHashMap<String,ConcurrentHashMap<String,Session> > getMessageWebSocket()
//    {
//        return webSocketSet;
//    }
//
//    @OnOpen
//    public void onOpen(@PathParam(value = "userCode") String userCode,Session session) {
//        synchronized(MessageWebSocket.class) {
//            if (webSocketSet.containsKey(userCode)) {
//                ConcurrentHashMap<String, Session> concurrentHashMap = webSocketSet.get(userCode);
//                if (concurrentHashMap == null) {
//                    concurrentHashMap=new ConcurrentHashMap<>();
//                }
//                concurrentHashMap.put(session.getId(), session);
//            } else {
//                ConcurrentHashMap<String, Session> sessionConcurrentHashMap = new ConcurrentHashMap<>();
//                sessionConcurrentHashMap.put(session.getId(), session);
//                webSocketSet.put(userCode, sessionConcurrentHashMap);
//                addOnlineCount();           //在线数加1
//            }
//        }
//        System.out.println("有新连接加入！当前在线人数为" + getOnlineCount() + ",用户userCode为=======" + userCode);
//        try {
//            sendMessage(session, "链接成功");
//        } catch (IOException e) {
//            System.out.println("IO异常");
//        }
//    }
//    
//    @OnClose
//    public void onClose(@PathParam(value = "userCode") String userCode,Session session) {
//        synchronized (MessageWebSocket.class) {
//            if (webSocketSet.containsKey(userCode)) {
//                ConcurrentHashMap<String, Session> concurrentHashMap = webSocketSet.get(userCode);
//                if (concurrentHashMap != null) {
//                    concurrentHashMap.remove(session.getId());
//                }
//                if (MapUtils.isEmpty(concurrentHashMap)) {
//                    webSocketSet.remove(userCode);
//                }
//            }
//        }
//        System.out.println("有一连接关闭！当前在线人数为" + getOnlineCount());
//    }
//    
//    
//
//    /**
//    * 发送给用户
//    * @param message
//    * @return void
//    * <AUTHOR>
//    * @date 2021/11/4 17:04
//    */
//    public void sendtoUser(String message,String userCode) throws IOException {
//        System.out.println("发送消息"+userCode+"----"+message);
//        ConcurrentHashMap<String, Session> concurrentHashMap = webSocketSet.get(userCode);
//        if (concurrentHashMap != null) {
//            for (Map.Entry<String, Session> v : concurrentHashMap.entrySet()) {
//                System.out.println("发送消息"+userCode+"---"+v.getKey()+"----"+message);
//                sendMessage(v.getValue(), message);
//            }
//        }
//    }
//
//    /**
//     * 发送给用户
//     * @param message
//     * @return void
//     * <AUTHOR>
//     * @date 2021/11/4 17:04
//     */
//    public void sendtoUser(String message) throws IOException {
//        for (Map.Entry<String, ConcurrentHashMap<String, Session>> v : webSocketSet.entrySet()) {
//            for (Map.Entry<String, Session> d : v.getValue().entrySet()) {
//                sendMessage(d.getValue(), message);
//            }
//        }
//    }
// 
//    
//    @OnError
//    public void onError(Session session, Throwable error) {
//        System.out.println("发生错误");
//        error.printStackTrace();
//    }
//
//    public void sendMessage(Session session,String message) throws IOException {
//    	synchronized (session) {
//        session.getBasicRemote().sendText(message);
//    	}
//    }
//    
//    public void sendMessage(String sendUserId, String message) {
//        Collection<Session> sessions = webSocketSet.get(sendUserId).values();
//        sessions.forEach(temp -> {
//            synchronized (temp) {
//                try {
//                    temp.getBasicRemote().sendText(message);
//                } catch (IOException e) {
//                    e.printStackTrace();
//                    log.error("推送信息失败：" + e.getMessage());
//                }
//            }
//        });
//        //this.session.getAsyncRemote().sendText(message);
//    }
//
//    public static synchronized int getOnlineCount() {
//        return onlineCount;
//    }
//
//    public static synchronized void addOnlineCount() {
//        MessageWebSocket.onlineCount++;
//    }
//
//    public static synchronized void subOnlineCount() {
//        MessageWebSocket.onlineCount--;
//    }
//}
