package cn.trasen.homs.base.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.Version;
import cn.trasen.homs.base.model.VersionRecord;
import cn.trasen.homs.base.service.VersionRecordService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CommVersionRecordController
 * @Description TODO
 * @date 2023��1��11�� ����10:50:23
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CommVersionRecordController")
public class VersionRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(VersionRecordController.class);

	@Autowired
	private VersionRecordService commVersionRecordService;

	/**
	 * @Title saveCommVersionRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��1��11�� ����10:50:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/versionRecord/save")
	public PlatformResult<String> saveCommVersionRecord(@RequestBody VersionRecord record) {
		try {
			commVersionRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommVersionRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��1��11�� ����10:50:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/versionRecord/update")
	public PlatformResult<String> updateCommVersionRecord(@RequestBody VersionRecord record) {
		try {
			commVersionRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommVersionRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommVersionRecord>
	 * @date 2023��1��11�� ����10:50:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/versionRecord/{id}")
	public PlatformResult<VersionRecord> selectCommVersionRecordById(@PathVariable String id) {
		try {
			VersionRecord record = commVersionRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommVersionRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��1��11�� ����10:50:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/versionRecord/delete/{id}")
	public PlatformResult<String> deleteCommVersionRecordById(@PathVariable String id) {
		try {
			commVersionRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommVersionRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommVersionRecord>
	 * @date 2023��1��11�� ����10:50:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/versionRecord/list")
	public DataSet<VersionRecord> selectCommVersionRecordList(Page page, VersionRecord record) {
		return commVersionRecordService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @MethodName: selectVersionRecord
	 * @Description: TODO
	 * <AUTHOR>
	 * @return PlatformResult<VersionRecord>
	 * @date 2023-01-11 11:23:36
	 */
	@ApiOperation(value = "查询版本信息", notes = "查询版本信息")
	@GetMapping("/api/versionRecord/selectVersionRecord")
	public PlatformResult<Version> selectVersionRecord() {
		try {
			return PlatformResult.success(commVersionRecordService.selectVersionRecord());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
