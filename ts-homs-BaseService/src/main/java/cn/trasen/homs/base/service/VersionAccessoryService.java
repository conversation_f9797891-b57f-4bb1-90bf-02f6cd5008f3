package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.VersionAccessory;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 版本管理附件信息Service层
 * @Date: 2020/4/27 16:38
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface VersionAccessoryService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int insert(VersionAccessory entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int update(VersionAccessory entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    int deleted(VersionAccessory entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月27日  下午1:49:22 </p>
     */
    List<VersionAccessory> getDataList(Page page, VersionAccessory entity);

}
