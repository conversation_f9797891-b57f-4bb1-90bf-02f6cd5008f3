package cn.trasen.homs.base.model;

import javax.persistence.Column;
import javax.persistence.Table;

import cn.trasen.homs.core.model.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @createTime 2021/6/17 13:25
 * @description
 */
@Getter
@Setter
@Table(name = "comm_file_attachment")
public class FileAttachment extends BaseBean {

    @ApiModelProperty(value = "模块名字")
    @Column(name = "module_name")

    String moduleName;

    @ApiModelProperty(value = "文件名称")
    @Column(name = "original_name")

    String originalName;


    @ApiModelProperty(value = "文件扩展名")
    @Column(name = "file_extension")

    String fileExtension;
    @ApiModelProperty(value = "文件大小")
    @Column(name = "file_size")

    Long fileSize;
    @ApiModelProperty(value = "预览地址")
    @Column(name = "real_path")

    String realPath;


    //文件地址
    String filePath;

    //业务ID
    String businessId;


    String createUserName;

    /**
     * 范围 1私有 2公用
     */
    Integer scope;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}