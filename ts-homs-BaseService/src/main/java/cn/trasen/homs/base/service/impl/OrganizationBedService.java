package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.OrganizationAllocationSaveNumReq;
import cn.trasen.homs.base.mapper.OrganizationBedMapper;
import cn.trasen.homs.base.model.OrganizationBed;
import cn.trasen.homs.base.service.IOrganizationBedService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 11:38
 * @description
 */
@Service
public class OrganizationBedService implements IOrganizationBedService {


    @Autowired
    OrganizationBedMapper organizationBedMapper;


    @Override
    public List<OrganizationBed> getList() {
        Example example = new Example(OrganizationBed.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        return organizationBedMapper.selectByExample(example);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /** 
    * @description: 删除所有数据
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/5 9:34
    */
    public  void delAll()
    {
        Example example = new Example(OrganizationBed.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIsNotNull("orgId");
        organizationBedMapper.deleteByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 保存编制
     * @param: organizationAllocationSaveNumReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/4 13:34
     */
    public void saveNum(OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq) {
        Example example = new Example(OrganizationBed.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", organizationAllocationSaveNumReq.getOrgId());
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        OrganizationBed organizationBed = organizationBedMapper.selectOneByExample(example);
        if (organizationBed != null) {
            OrganizationBed organizationBedUpdate = new OrganizationBed();
            organizationBedUpdate.setId(organizationBed.getId());
            organizationBedUpdate.setNum(organizationAllocationSaveNumReq.getBedNum());
            organizationBedMapper.updateByPrimaryKeySelective(organizationBedUpdate);
        } else {
            OrganizationBed organizationBedAdd = BeanUtils.InitBean(OrganizationBed.class);
            organizationBedAdd.setOrgId(organizationAllocationSaveNumReq.getOrgId());
            organizationBedAdd.setNum(organizationAllocationSaveNumReq.getBedNum());
            organizationBedAdd.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            organizationBedMapper.insertSelective(organizationBedAdd);
        }

    }
}