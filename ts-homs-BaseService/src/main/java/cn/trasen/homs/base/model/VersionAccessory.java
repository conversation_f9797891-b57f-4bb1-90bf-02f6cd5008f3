package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 版本管理附件信息
 * @Date: 2020/4/27 15:31
 * @Param:
 * @return:
 **/
@Table(name = "COMM_VERSION_ACCESSORY")
@Setter
@Getter
public class VersionAccessory {
    /**
     * 附件ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "附件ID")
    private String id;

    /**
     * 版本ID
     */
    @Column(name = "VERSION_ID")
    @ApiModelProperty(value = "版本ID")
    private String versionId;

    /**
     * 是否为图片
     */
    @Column(name = "ISIMAGE")
    @ApiModelProperty(value = "是否为图片")
    private Short isimage;

    /**
     * 附件类型
     */
    @Column(name = "TYPE")
    @ApiModelProperty(value = "附件类型")
    private String type;

    /**
     * 附件名称
     */
    @Column(name = "NAME")
    @ApiModelProperty(value = "附件名称")
    private String name;

    /**
     * 保存名称
     */
    @Column(name = "SAVE_NAME")
    @ApiModelProperty(value = "保存名称")
    private String saveName;

    /**
     * 文件大小
     */
    @Column(name = "FILE_SIZE")
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;
}