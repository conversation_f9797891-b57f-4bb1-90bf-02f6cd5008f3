/**
 * @Title: CommGroupJurisdictionService.java  
 * @Package: cn.trasen.homs.base.service  
 * @Date: 2021年6月21日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.CommGroupJurisdiction;

/**
* @ClassName: CommGroupJurisdictionService  
 * @Author: 86189
 * @Date: 2021年6月21日
 */
public interface FieldJurisdictionService {
	
	
	/**
	 * 
	* @Title: insert  
	* @Description: 新增人员字段权限
	* @Params: @param record
	* @Params: @return      
	* @Return: int
	* <AUTHOR>
	* @date:2021年6月16日
	* @Throws
	 */
	int insert(List<CommGroupJurisdiction> record);
    
	/**
	 * 
	* @Title: update  
	* @Description: 修改人员字段权限
	* @Params: @param record
	* @Params: @return      
	* @Return: int
	* <AUTHOR>
	* @date:2021年6月16日
	* @Throws
	 */
    int update(CommGroupJurisdiction record);
    
    /**
     * 
    * @Title: deleted  
    * @Description: 删除人员字段权限
    * @Params: @param id
    * @Params: @return      
    * @Return: int
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    int deleted(String id);
    
    /**
     * 
    * @Title: getList  
    * @Description: 获取人员字段权限
    * @Params: @param page
    * @Params: @param record
    * @Params: @return      
    * @Return: List<CommGroupJurisdiction>
    * <AUTHOR>
    * @date:2021年6月16日
    * @Throws
     */
    List<CommGroupJurisdiction> getList(CommGroupJurisdiction record);
    
}
