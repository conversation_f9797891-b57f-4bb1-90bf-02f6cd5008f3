package cn.trasen.homs.base.mapper;

import java.util.List;

import cn.trasen.homs.base.bean.JobtitleBasicListReq;
import cn.trasen.homs.base.model.JobtitleBasic;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

/** 
* @description:
* @return: 
* @author: liyuan
* @createTime: 2021/8/7 10:29
*/
public interface JobtitleBasicMapper extends Mapper<JobtitleBasic> {

	List<JobtitleBasic> getCategoryPageList(JobtitleBasicListReq jobtitleBasicListReq, Page page);
	

}