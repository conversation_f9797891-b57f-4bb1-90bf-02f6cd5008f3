package cn.trasen.homs.base.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.base.bean.WeekDaysNumReq;
import cn.trasen.homs.base.mapper.HolidayInfoMapper;
import cn.trasen.homs.base.model.HolidayInfo;
import cn.trasen.homs.base.service.IHolidayInfoService;
import cn.trasen.homs.core.contants.Contants;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/9/6 15:47
 * @description
 */



@Service
public class HolidayInfoService  implements IHolidayInfoService {
    @Autowired
    HolidayInfoMapper holidayInfoMapper;


    @Override
    /**
     * 获取工作日天数
     *
     * @param weekDaysNumReq
     * @return java.lang.Integer
     * @description
     * <AUTHOR>
     * @createTime 2021/9/6 15:50
     */
    public Integer getWeekDaysNum(WeekDaysNumReq weekDaysNumReq) {
        List<String> months = getBetweenMonths(weekDaysNumReq.getBeginTime(), weekDaysNumReq.getEndTime());
        List<String> days = getBetweenDates(weekDaysNumReq.getBeginTime(), weekDaysNumReq.getEndTime());
        List<String> monthsmm = new ArrayList<>();
        for (String month : months) {
            monthsmm.add(DateUtil.format(DateUtil.parse(month, "yyyy-MM"), "yyyy-M"));
        }
        Example example = new Example(HolidayInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andIn("month", monthsmm);
        List<HolidayInfo> holidayInfoList = holidayInfoMapper.selectByExample(example);

        //周末工作日
        List<String> weekendList = new ArrayList<>();
        for (HolidayInfo holidayInfo : holidayInfoList) {
            if (!StringUtils.isBlank(holidayInfo.getWorkDay())) {
                for (String s : holidayInfo.getWorkDay().split(",")) {
                    weekendList.add(s);
                }
            }
        }

        //j
        List<String> holidayList = new ArrayList<>();
        for (HolidayInfo holidayInfo : holidayInfoList) {
            if (!StringUtils.isBlank(holidayInfo.getWorkDay())) {
                for (String s : holidayInfo.getHoliday().split(",")) {
                    holidayList.add(s);
                }
            }
        }

        List<String> workDayList = new ArrayList<>();
        //去除周末
        for (String s : days) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtil.parse(s, "yyyy-MM-dd"));
            if (!(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY)) {
                workDayList.add(s);
            } else if (weekendList.contains(s)) {
                workDayList.add(s);
            }
        }
        workDayList.removeAll(holidayList);
        return workDayList.size();
    }



    /**
     * 获取两个日期之间的所有月(字符串格式, 按月计算)
     *
     * @param start
     * @param end
     * @return
     */
    private List<String> getBetweenMonths(Date start, Date end) {
        List<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        // 加了一个月
        tempStart.add(Calendar.MONTH, 1);
        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        tempEnd.add(Calendar.MONTH, 1);
        result.add(sdf.format(start));
        while (tempStart.before(tempEnd)) {
            result.add(sdf.format(tempStart.getTime()));
            tempStart.add(Calendar.MONTH, 1);
        }
        return result;
    }


    /**
     * @param start
     * @param end
     * @return java.util.List<java.lang.String>
     * @description 获取2个日期区间所有天
     * <AUTHOR>
     * @createTime 2021/9/6 16:24
     */
    private List<String> getBetweenDates(Date start, Date end) {
        List<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        while (tempStart.before(tempEnd) || tempStart.equals(tempEnd)) {
            result.add(sdf.format(tempStart.getTime()));
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        Collections.reverse(result);
        return result;
    }
}