package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 11:12
 * @description
 */
@Data
public class JobtitleBasicListReq {

    @ApiModelProperty(value = "名称")
    private String jobtitleBasicName;


    @ApiModelProperty(value = "父ID")
    String  jobtitleBasicPid;

    @ApiModelProperty(value = "等于父ID")
    String  eqJobtitleBasicPid;
    
    @ApiModelProperty(value = "等级")
    String  jobtitleBasicGrade;

    @ApiModelProperty(value = "主键")
    String jobtitleBasicId;
    
    private String ssoOrgCode;
    
    private String ssoOrgName;
}
