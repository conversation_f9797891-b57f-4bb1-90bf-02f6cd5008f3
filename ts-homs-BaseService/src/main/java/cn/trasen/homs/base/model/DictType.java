package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @Author: Liz<PERSON>huo
 * @Description: 字典类型
 * @Date: 2020/4/29 10:31
 * @Param:
 * @return:
 **/
@Table(name = "COMM_DICT_TYPE")
@Setter
@Getter
public class DictType {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 类型编码
     */
    @Column(name = "TYPE_CODE")
    @ApiModelProperty(value = "类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @Column(name = "TYPE_NAME")
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 系统编码
     */
    @Column(name = "SYS_CODE")
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编号")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    @ApiModelProperty("机构名称")
    private String ssoOrgName;

    /**
     * 模糊筛选
     */
    @Transient
    private String fuzzyScreening;

    /**
     * 当前用户机构编号
     */
    @Transient
    private String ssoOrgCodeUser;

    /**
     * 多机构字典公共库引用去除已引用
     */
    @Transient
    private String belonging;

    /**
     * 配合belonging
     */
    @Transient
    private List<String> typeCodeList;
}