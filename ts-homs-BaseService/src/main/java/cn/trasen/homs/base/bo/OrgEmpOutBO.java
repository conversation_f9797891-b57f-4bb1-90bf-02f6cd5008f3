package cn.trasen.homs.base.bo;

import java.util.List;

import cn.trasen.homs.base.bean.EmployeeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/2/14 9:38
 */
@Setter
@Getter
public class OrgEmpOutBO {


    String orgId;
    String orgName;
    String orgCode;

    @ApiModelProperty(value = "机构列表")
    List<Org> orgList;

    @Setter
    @Getter
    public static  class Org
    {
        String orgId;
        String orgName;
        String orgCode;

        List<Org> children;

    }


    /**
     * 员工列表
     */
    List<EmployeeBean> empList;

}
