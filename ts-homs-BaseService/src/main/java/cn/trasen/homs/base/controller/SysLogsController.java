package cn.trasen.homs.base.controller;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.utils.OS;
import cn.trasen.homs.base.model.SysLogs;
import cn.trasen.homs.base.service.SysLogsService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "系统日志")
@RestController
@Slf4j
public class SysLogsController {


	 static ThreadPoolExecutor executor = new ThreadPoolExecutor(
			1,
			OS.getAvailableProcessors(),
			60,
			TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(10000),

			new ThreadFactory() {
				private AtomicInteger threadNum = new AtomicInteger(0);
				@Override
				public Thread newThread(Runnable r) {
					return new Thread(r, "sysLogs-thread-pool-" + threadNum.incrementAndGet());
				}
			}, new ThreadPoolExecutor.AbortPolicy()

	);

	@Resource
	SysLogsService sysLogsService;

	@ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/sysLogs/list")
    public DataSet<SysLogs> getDataList(Page page, SysLogs sysLogs) {
    	try {
	        List<SysLogs> list = sysLogsService.getDataList(page, sysLogs);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		}
    }
	
	@ApiOperation(value = "新增日志", notes = "新增日志")
    @PostMapping("/sysLogs/save")
    public PlatformResult<String> save(@RequestBody SysLogs sysLogs) {
    	try {
			executor.execute(
					() -> sysLogsService.save(sysLogs)
			);
	       return PlatformResult.success("新增日志成功");
	    }catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.success("新增日志失败，失败原因：" +e.getMessage());
		}
    }
}

