package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.service.HrmsOrganizationService;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.MyTreeModel;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.util.StringUtil;

/**   
 * @Title: HrmsOrganizationController.java 
 * @Package cn.trasen.hrms.controller 
 * @Description: 组织机构Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月13日 下午2:20:22 
 * @version V1.0   
 */
@Slf4j
@Api(tags = "组织机构Controller")
@RestController
public class HrmsOrganizationController {

	@Autowired
	HrmsOrganizationService hrmsOrganizationService;

//	/**
//	 * @Title: insert
//	 * @Description: 新增组织机构
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月28日 下午5:14:55
//	 */
//	@ApiOperation(value = "新增组织机构", notes = "新增组织机构")
//	@PostMapping(value = "/organization/save")
//	public PlatformResult<String> insert(@RequestBody HrmsOrganization entity) {
//		try {
//			return hrmsOrganizationService.insert(entity);
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}
//
//	/**
//	 * @Title: insert
//	 * @Description: 修改组织机构
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月28日 下午5:16:36
//	 */
//	@ApiOperation(value = "修改组织机构", notes = "修改组织机构")
//	@PostMapping(value = "/organization/update")
//	public PlatformResult<String> update(@RequestBody HrmsOrganization entity) {
//		try {
//			return hrmsOrganizationService.update(entity);
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}
//
//	/**
//	 * @Title: updateEnableStatus
//	 * @Description: 修改组织机构启用/停用状态
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月30日 下午5:22:09
//	 */
//	@ApiOperation(value = "修改组织机构启用/停用状态", notes = "修改组织机构启用/停用状态")
//	@PostMapping(value = "/organization/updateEnableStatus")
//	public PlatformResult<String> updateEnableStatus(@RequestBody HrmsOrganization entity) {
//		try {
//			if (hrmsOrganizationService.updateEnableStatus(entity) > 0) {
//				return PlatformResult.success();
//			}
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}
//
//	/**
//	 * @Title: mergeOrganization
//	 * @Description: 合并组织机构
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年6月29日 上午9:18:44
//	 */
//	@ApiOperation(value = "合并组织机构", notes = "合并组织机构")
//	@PostMapping(value = "/organization/mergeOrganization")
//	public PlatformResult<String> mergeOrganization(@RequestBody HrmsOrganization entity) {
//		try {
//			return hrmsOrganizationService.mergeOrganization(entity);
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}

	/**
	 * @Title: getOrganizationList
	 * @Description: 查询组织机构列表
	 * @param page
	 * @param entity
	 * @Return DataSet<HrmsOrganization>
	 * <AUTHOR>
	 * @date 2020年4月13日 下午2:30:25
	 */
	@ApiOperation(value = "查询组织机构列表", notes = "查询组织机构列表")
	@RequestMapping(value = "/organization/list", method = {RequestMethod.GET, RequestMethod.POST})
	public DataSet<HrmsOrganization> getOrganizationList(Page page, HrmsOrganization entity) {
		return hrmsOrganizationService.getDataSetList(page, entity);
	}



	/**
	 * @Title: getTree
	 * @Description: 查询组织机构树(支持get、post)
	 * @Return Result
	 * <AUTHOR>
	 * @date 2020年4月13日 下午4:15:05
	 */
	@ApiOperation(value = "查询组织机构树（没权限）", notes = "查询组织机构树（没权限）")
	@RequestMapping(value = "/organization/getTree")
	public PlatformResult<List<TreeModel>> getTree(HttpServletRequest request, HttpServletResponse response) {
		try {
			
			String levelObj=request.getParameter("level");
			int level=0;
			if(!StringUtils.isBlank(levelObj))
			{
				level=Integer.parseInt(levelObj);
			}
			
			return PlatformResult.success(hrmsOrganizationService.getOrgTree(level));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	/**
	 * @Title: getTree
	 * @Description: 查询组织机构树(支持get、post)
	 * @Return Result
	 * <AUTHOR>
	 * @date 2020年4月13日 下午4:15:05
	 */
	@ApiOperation(value = "查询组织机构树(不包含禁用的)", notes = "查询组织机构树(不包含禁用的)")
	@RequestMapping(value = "/organization/getTree2")
	public PlatformResult<List<TreeModel>> getTree2(HttpServletRequest request, HttpServletResponse response) {
		try {
			String levelObj=request.getParameter("level");
			int level = 0;
			if(!StringUtils.isBlank(levelObj))
			{
				level = Integer.parseInt(levelObj);
			}
			
			return PlatformResult.success(hrmsOrganizationService.getOrgTree2(level));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	/**
	 * @Title: getTree
	 * @Description: 查询组织机构树(支持get、post)
	 * @Return Result
	 * <AUTHOR>
	 * @date 2025年5月12日 下午4:15:05
	 */
	@ApiOperation(value = "查询组织机构树(不包含禁用的)", notes = "查询组织机构树(不包含禁用的)")
	@RequestMapping(value = "/organization/getTreeWithMapping")
	public PlatformResult<List<TreeModel>> getTreeWithMapping(HttpServletRequest request, HttpServletResponse response) {
		try {
			String syscode = request.getParameter("syscode");
			String showAllNode = request.getParameter("showAllNode");
			List<TreeModel> tree = hrmsOrganizationService.getTreeWithMapping(syscode);
			//是否显示“全部”节点，Y-显示，否则不显示
			if(!ObjectUtils.isEmpty(showAllNode) && showAllNode.equals("Y")){
				TreeModel allNode = new TreeModel();
				allNode.setId("0");
				allNode.setName("全部");
				tree.add(0, allNode);
			}
			return PlatformResult.success(tree);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	/**
	 * 返回带人数
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "查询组织机构树(不包含禁用的)", notes = "查询组织机构树(不包含禁用的)")
	@RequestMapping(value = "/organization/getTree3")
	public PlatformResult<List<MyTreeModel>> getTree3(HttpServletRequest request, HttpServletResponse response) {
		try {
			String levelObj = request.getParameter("level");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType"); //档案类型 ，用于区分非人事档案员工数据的过滤
			String isAll = request.getParameter("isAll"); //是否查看全部数据，Y-是，否则不是
			int level = 0;
			if(!StringUtils.isBlank(levelObj))
			{
				level=Integer.parseInt(levelObj);
			}
			//@TODO 添加参数用来区别是否为临时员工使用组织数 Y-是 其他值都是否 add ni.jiang
			String istmp = request.getParameter("istmp");
			boolean isTmpEmp = false;
			if(StringUtil.isNotEmpty(istmp) && "Y".equals(istmp)){
				isTmpEmp =true;
			}
			//判断是否查看全部
	        boolean isAllData = false;
	        if(!ObjectUtils.isEmpty(isAll) && "Y".equals(isAll)){
	        	isAllData = true;
	        }
			return PlatformResult.success(hrmsOrganizationService.getOrgTree3(level,isTmpEmp,employeeStatus,archivesType, isAllData));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	
	/**
	 * @Title: getTree
	 * @Description: 查询组织机构树(支持get、post)(只查询允许会诊排版科室)
	 * @Return Result
	 * <AUTHOR>
	 * @date 2020年4月13日 下午4:15:05
	 */
	@ApiOperation(value = "查询组织机构树(不包含禁用的)(只查询允许会诊排版科室)", notes = "查询组织机构树(不包含禁用的)(只查询允许会诊排版科室)")
	@RequestMapping(value = "/organization/getTreeScduDept")
	public PlatformResult<List<TreeModel>> getTreeScduDept(HttpServletRequest request, HttpServletResponse response) {
		try {
			String levelObj=request.getParameter("level");
			Integer level=0;
			if(!StringUtils.isBlank(levelObj))
			{
				level=Integer.parseInt(levelObj);
			}
			
			return PlatformResult.success(hrmsOrganizationService.getTreeScduDept(level));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

//	/**
//	 * @Title: refreshOrgCache
//	 * @Description: 刷新组织机构缓存
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月13日 下午5:25:46
//	 */
//	@PostMapping(value = "/organization/cache/refresh")
//	@ApiOperation(value = "刷新组织机构缓存", notes = "刷新机构缓存")
//	public PlatformResult<String> refreshOrgCache() {
//		List<HrmsOrganization> records = hrmsOrganizationService.getOrgAllList();
//		if (CollectionUtils.isNotEmpty(records)) {
//			records.stream().forEach(item -> {
//				OrgCacheManager.set(item);
//			});
//		}
//		return PlatformResult.success();
//	}
//
//	/**
//	 * @Title: refreshOrgCache
//	 * @Description: 刷新组织机构缓存
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月13日 下午5:25:46
//	 */
//	@PostMapping(value = "/organization/byId")
//	@ApiOperation(value = "刷新组织机构缓存", notes = "刷新机构缓存")
//	public PlatformResult<String> getOrganizationById() {
//		List<HrmsOrganization> records = hrmsOrganizationService.getOrgAllList();
//		if (CollectionUtils.isNotEmpty(records)) {
//			records.stream().forEach(item -> {
//				OrgCacheManager.set(item);
//			});
//		}
//		return PlatformResult.success();
//	}

	/**
	 * @Title: getOrgInfoByOrgId  
	 * @Description: 通过Id查询机构信息
	 * @param organizationId
	 * @return    参数  
	 * PlatformResult<HrmsTrainNeeds>    返回类型  
	 * @throws
	 */
	@ApiOperation(value = "通过Id查询机构信息", notes = "通过Id查询机构信息")
	@GetMapping(value = "/organization/byOrgId/{organizationId}")
	public PlatformResult<HrmsOrganization> getOrgInfoByOrgId(@PathVariable String organizationId) {
		try {
			HrmsOrganization hrmsOrganization = hrmsOrganizationService.findById(organizationId);
			return PlatformResult.success(hrmsOrganization);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

//	/**
//	 * @Title: excelImportOrganization
//	 * @Description: excel导入组织机构
//	 * @param file
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年6月16日 下午5:12:43
//	 */
//	@SuppressWarnings("unchecked")
//	@ApiOperation(value = "excel导入组织机构", notes = "excel导入组织机构")
//	@PostMapping(value = "/organization/excelImportOrganization")
//	public PlatformResult<String> excelImportOrganization(@RequestParam("file") MultipartFile file) {
//		try {
//			List<HrmsOrganization> list = (List<HrmsOrganization>) ImportExcelUtil.getExcelDatas(file, HrmsOrganization.class);
//			return hrmsOrganizationService.excelImportOrganization(list);
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}


	 @ApiOperation(value = "查询组织机构树和人员(不带权限)", notes = "查询组织机构树和人员(不带权限)")
    @PostMapping("/organization/getOrgTreeAndEmployeeAll")
    public Result getOrgTreeAndEmployeeAll() {
        Result result = new Result();
        try {
            List<TreeModel> tree = hrmsOrganizationService.getOrgTreeAndEmployeeAll();
            result.setSuccess(true);
            result.setObject(tree);
        } catch (Exception e) {
            result.setMessage("操作失败.");
            result.setSuccess(false);
        }
        return result;
    }

	@ApiOperation(value = "获取指定组织以及下层所有组织", notes = "获取指定组织以及下层所有组织")
	@PostMapping("/organization/getHrmsOrganizationAndNextList")
	public PlatformResult<List<String>> getHrmsOrganizationAndNextList(String orgIds) {
		PlatformResult result = new PlatformResult();
		try {
			List<String> list = hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds);
			result.setSuccess(true);
			result.setObject(list);
		} catch (Exception e) {
			result.setMessage("操作失败.");
			result.setSuccess(false);
		}
		return result;
	}


	@ApiOperation(value = "获取指定组织以及下层所有组织", notes = "获取指定组织以及下层所有组织")
	@PostMapping("/organization/getHrmsOrganizationBeanAndNextList")
	public PlatformResult<List<HrmsOrganization>> getHrmsOrganizationBeanAndNextList(@RequestBody List<String> orgIds) {
		PlatformResult result = new PlatformResult();
		try {
			List<HrmsOrganization> list = hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds);
			result.setSuccess(true);
			result.setObject(list);
		} catch (Exception e) {
			result.setMessage("操作失败.");
			result.setSuccess(false);
		}
		return result;
	}


	@PostMapping(value = "/organization/getOrgAllList")
	@ApiOperation(value = "获取所有组织机构", notes = "获取所有组织机构")
	public PlatformResult<List<HrmsOrganization>> getOrgAllList() {

		return PlatformResult.success(hrmsOrganizationService.getOrgAllList());
	}
	
	
	@ApiOperation(value = "查询本组织树", notes = "查询本组织树")
	@RequestMapping(value = "/organization/selectOrganizationTree")
	public PlatformResult<List<TreeModel>> selectOrganizationTree(HttpServletRequest request, HttpServletResponse response) {
		try {
			String organizationId = request.getParameter("organizationId");
			log.info("organizationId的值：" + organizationId);
			return PlatformResult.success(hrmsOrganizationService.selectOrganizationTree(organizationId));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "查询本组织及上级组织树", notes = "查询本组织及上级组织树")
	@RequestMapping(value = "/organization/selectUpOrganizationTree")
	public PlatformResult<List<TreeModel>> selectUpOrganizationTree(HttpServletRequest request, HttpServletResponse response) {
		try {
			String organizationId = request.getParameter("organizationId");
			log.info("organizationId的值：" + organizationId);
			return PlatformResult.success(hrmsOrganizationService.selectUpOrganizationTree(organizationId));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "查询本组织及下级组织树", notes = "查询本组织及下级组织树")
	@RequestMapping(value = "/organization/selectDownOrganizationTree")
	public PlatformResult<List<TreeModel>> selectDownOrganizationTree(HttpServletRequest request, HttpServletResponse response) {
		try {
			String organizationId = request.getParameter("organizationId");
			log.info("organizationId的值：" + organizationId);
			return PlatformResult.success(hrmsOrganizationService.selectDownOrganizationTree(organizationId));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "根据部门code查询人员信息", notes = "根据部门code查询人员信息")
	@RequestMapping(value = "/organization/selectUserListByDeptCode")
	public PlatformResult<List<ThpsUser>> selectUserListByDeptCode(@RequestBody List<String> organizationIdList) {
		try {
			log.info("organizationIdList的值：" + organizationIdList);
			return PlatformResult.success(hrmsOrganizationService.selectUserListByDeptCode(organizationIdList));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
	
	@ApiOperation(value = "根据部门code查询人员信息", notes = "根据部门code查询人员信息")
	@RequestMapping(value = "/organization/selectEmpListByDeptCode")
	public PlatformResult<List<HrmsEmployeeResp>> selectEmpListByDeptCode(@RequestBody List<String> organizationIdList) {
		try {
			return PlatformResult.success(hrmsOrganizationService.selectEmpListByDeptCode(organizationIdList));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "查询机构下级组织树", notes = "查询机构下级组织树")
	@RequestMapping(value = "/organization/selectOrgOrganizationTree")
	public PlatformResult<List<TreeModel>> selectOrgOrganizationTree(HttpServletRequest request, HttpServletResponse response) {
		try {
			String ssoOrgCode = request.getParameter("ssoOrgCode");
			return PlatformResult.success(hrmsOrganizationService.selectOrgOrganizationTree(ssoOrgCode));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return PlatformResult.failure();
	}
	
}
