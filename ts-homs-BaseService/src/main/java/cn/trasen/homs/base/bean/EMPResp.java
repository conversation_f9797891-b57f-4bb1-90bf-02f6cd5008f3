package cn.trasen.homs.base.bean;

//import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

//import javax.persistence.Column;
//import java.util.Date;

/** 
* @description:
* @return: 
* @author: liyuan
* @createTime: 2021/7/5 14:33
*/
@Setter
@Getter
public class EMPResp {

    String userAccounts;
    String empName;
    String empDeptName;
    String id;
    Short empSex;
    String empCode;
    Short empStatus;

    private String avatar;


    private String empPhoneSecond;
    @ApiModelProperty(value = "移动短号")
    private String empBusinessPhone;

    @ApiModelProperty(value = "电信短号")
    private String empTelecomBusinessPhone;

    @ApiModelProperty(value = "联通短号")
    private String empUnicomBusinessPhone;

    /**
     * 座机号码
     */
    @ApiModelProperty(value = "座机号码")
    private String landlineNumber;


    private String empShortPhone;


    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String positionId;

    @ApiModelProperty(value = "职务名称")
    private String positionName;
    
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String empEmail;
    
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String empPhone;

}