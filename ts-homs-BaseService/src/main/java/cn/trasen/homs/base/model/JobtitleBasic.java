package cn.trasen.homs.base.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 职称表
 *
 */
@Table(name = "comm_jobtitle_basic")
@Data
public class JobtitleBasic {
    /**
     * 主键ID
     */
    @Id
    private String  jobtitleBasicId;

    /**
     * 名称
     */
    private String  jobtitleBasicName;

    /**
     * 等级
     */
    private Integer jobtitleBasicGrade;

    /**
     * 父级ID
     */
    private String  jobtitleBasicPid;

    /**
     * 分类名称
     */
    private String  classificationName;

    /**
     * 树ID
     */
    private String  treeIds;

    /**
     * 备注
     */
    private String  remark;

    /**
     * 企业ID
     */
    private String  enterpriseId;

    /**
     * 创建时间
     */
    private Date    createDate;

    /**
     * 创建者ID
     */
    private String  createUser;

    /**
     * 创建者姓名
     */
    private String  createUserName;

    /**
     * 更新时间
     */
    private Date    updateDate;

    /**
     * 更新者ID
     */
    private String  updateUser;

    /**
     * 更新者姓名
     */
    private String  updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    private String  isDeleted;

    /**
     * 创建者姓名
     */
    private double  jobtitleSalary;

    private Integer jobtitleUpgradeTime;

    /**
     * 职称工资
     */
    private double  baseZcgz;

    /**
     * 院龄工资
     */
    private double  baseYlgz;

    /**
     * 是否禁用
     */
    private String  isEnable;

    /**
     * 排序小到大
     */
    private Integer sortNo;

    private String  ssoOrgCode;

}