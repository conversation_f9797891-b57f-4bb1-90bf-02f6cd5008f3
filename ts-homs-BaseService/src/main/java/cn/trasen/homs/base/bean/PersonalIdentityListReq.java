package cn.trasen.homs.base.bean;

import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:41
 * @description
 */

@Data
public class PersonalIdentityListReq {

    @ApiModelProperty(value = "名称")
    String personalIdentityName;
    
    @Transient
    @ApiModelProperty(value = "是否管理页面：0-否，1-是，用于标识职务的管理维护界面")
    private String  isManagerPage;

}
