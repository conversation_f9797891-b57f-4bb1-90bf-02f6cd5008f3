package cn.trasen.homs.base.service;

import cn.trasen.homs.base.model.CommModesUses;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CommModesUsesService
 * @Description TODO
 * @date 2024��12��18�� ����5:19:08
 * <AUTHOR>
 * @version 1.0
 */
public interface CommModesUsesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	Integer save(CommModesUses record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	Integer update(CommModesUses record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommModesUses
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	CommModesUses selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommModesUses>
	 * @date 2024��12��18�� ����5:19:08
	 * <AUTHOR>
	 */
	DataSet<CommModesUses> getDataSetList(Page page, CommModesUses record);
}
