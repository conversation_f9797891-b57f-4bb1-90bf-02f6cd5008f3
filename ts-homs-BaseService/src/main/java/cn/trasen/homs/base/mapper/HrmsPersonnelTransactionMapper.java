package cn.trasen.homs.base.mapper;

import java.util.List;

import cn.trasen.homs.base.model.HrmsPersonnelTransaction;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsPersonnelTransactionMapper extends Mapper<HrmsPersonnelTransaction> {

	int batchInsert(List<HrmsPersonnelTransaction> list);

	String getBatchNumber(String ym);

	/**  
	 * <p> @Title: getDataList</p>
	 * <p> @Description: TODO</p>
	 * <p> @Param: </p>
	 * <p> @Return: List<HrmsPersonnelTransaction></p>
	 * <P> @Date: 2021年3月18日  上午11:42:28 </p>
	 * <p> <AUTHOR>
	 */  
	List<HrmsPersonnelTransaction> getDataList(Page page, HrmsPersonnelTransaction entity);

	/**  
	 * <p> @Title: loadChangeSelect</p>
	 * <p> @Description: 异动类型下拉框</p>
	 * <p> @Param: </p>
	 * <p> @Return: List<String></p>
	 * <P> @Date: 2021年4月15日  上午10:55:21 </p>
	 * <p> <AUTHOR>
	 */  
	List<String> loadChangeSelect();

	List<HrmsPersonnelTransaction> getList(HrmsPersonnelTransaction entity);

    List<HrmsPersonnelTransaction> getCycleList(HrmsPersonnelTransaction personnelTransaction);
}