package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: Excel导入个人联系人扩展类
 * @Date: 2020/1/14 14:56
 * @Param:
 * @return:
 **/
@Setter
@Getter
public class LinkmanPo {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "员工姓名")
    @Excel(name = "姓名")
    private String linkmanName;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String linkmanUnit;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Excel(name = "部门")
    private String linkmanDepart;

    /**
     * 职业
     */
    @ApiModelProperty(value = "岗位")
    @Excel(name = "岗位")
    private String linkmanProfession;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    @Excel(name = "职务")
    private String linkmanDuty;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "电子邮件")
    @Excel(name = "电子邮件")
    private String linkmanEmail;

    /**
     * 手机
     */
    @ApiModelProperty(value = "联系方式")
    @Excel(name = "联系方式")
    private String mobilePhone;

    /**
     * 联系人类型
     */
    @ApiModelProperty(value = "联系人类型")
    @Excel(name = "联系人类型")
    private String className;    /**
     * 联系人类型
     */
    @ApiModelProperty(value = "性别")
    @Excel(name = "性别")
    private String linkmanSex;

    @Column(name = "备注")
    @Excel(name = "备注")
    private String linkmanDescribe;

}