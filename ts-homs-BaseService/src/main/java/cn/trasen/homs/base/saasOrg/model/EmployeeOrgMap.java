package cn.trasen.homs.base.saasOrg.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 员工与机构的关系映射表
 * 
 * @update 2025-06-28 11:20:22 
 * <AUTHOR>
 *
 */
@Table(name = "cust_emp_org_map")
@Setter
@Getter
public class EmployeeOrgMap {
	
	@Id
    @Column(name = "id")
    @ApiModelProperty(value = "员工与机构映射表ID")
	private String id;
	
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;
    
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Column(name = "is_default")
    @ApiModelProperty(value = "是否默认机构：N-否，Y-是，一个员工只能有一个默认机构")
    private String isDefault;
    
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date effectiveDate;
    
    @Column(name = "dead_date")
    @ApiModelProperty(value = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deadDate;

    @Column(name = "hire_date")
    @ApiModelProperty(value = "入职时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date hireDate;

    @Column(name = "default_dept_code")
    @ApiModelProperty(value = "默认科室编码", required = true)
    private String defaultDeptCode;

    @Column(name = "default_dept_id")
    @ApiModelProperty(value = "默认科室ID", required = true)
    private String defaultDeptId;

    @Column(name = "status")
    @ApiModelProperty(value = "状态(0-禁用，1-启用)", required = true)
    private Integer status;

    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识：N-否，Y-是")
    private String isDeleted;
}
