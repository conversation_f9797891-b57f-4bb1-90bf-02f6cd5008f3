package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @createTime 2021/8/27 11:04
 * @description
 */
@Data
public class MessageWebPushReq {

    @Data
    public static class WebMessageTemplate {
        @ApiModelProperty(value = "业务ID")

        private String id;//业务ID
        @ApiModelProperty(value = "内容")

        private String content;  //内容
        @ApiModelProperty(value = "标题")

        private String title;  //标题
        @ApiModelProperty(value = "要跳转的url如：/information/messageRead")

        private String url; //要跳转的url如：/information/messageRead
    }

    /**
     * 接收人,多个用逗号隔开,发送所有人传all
     */
    @ApiModelProperty(value = "接收人,多个用逗号隔开,发送所有人传all")
    private String receiver;

    /**
     * 接收人,多个用逗号隔开,发送所有人传all
     */
    @ApiModelProperty(value = "接收人,多个用逗号隔开,发送所有人传all")
    private WebMessageTemplate webMessageTemplate;
}
