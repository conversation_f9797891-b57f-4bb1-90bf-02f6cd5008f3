package cn.trasen.homs.base.saasOrg.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.base.bean.OrganizationSaveReq;
import cn.trasen.homs.base.bean.PlatformBody;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.service.HrmsOrgService;
import cn.trasen.homs.base.saasOrg.vo.HrmsOrgTreeVo;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName HrmsOrgController
 * @Description 员工机构映射控制器
 * @date 2025-07-14 11:30:00
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "机构管理控制器")
public class HrmsOrgController {
	
	@Autowired
	private HrmsOrgService hrmsOrgService;
	
	@Autowired
	private IDictItemService dictItemService;

	/**
	 * @Title saveOrUpdate
	 * @Description 机构新增/更新
	 * @param record
	 * @return PlatformResult<String>
	 * @date 025-07-14 11:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "机构新增/更新", notes = "机构新增/更新")
	@PostMapping("/api/hrmsOrg/saveOrUpdate")
	public PlatformResult<String> saveOrUpdate(@RequestBody HrmsOrg record) {
		try {
			hrmsOrgService.saveOrUpdate(record);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("操作失败");
		}
	}
    
    /**
	 * @Title deleteByIdOrgCode
	 * @Description 根据编码或ID删除
	 * @param orgCode
	 * @param orgId
	 * @return PlatformResult<String>
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "根据编码或ID删除", notes = "根据编码或ID删除")
    @GetMapping("/api/hrmsOrg/deleteByIdOrgCode")
    public PlatformResult<String> deleteByIdOrgCode(@RequestParam(value = "orgCode", required = false )@ApiParam(value = "机构编码") String orgCode,
    		@RequestParam(value = "orgId", required = false )@ApiParam(value = "机构ID") String orgId){
    	if(ObjectUtils.isEmpty(orgCode) && ObjectUtils.isEmpty(orgId)){
    		return PlatformResult.failure("机构编码和机构ID不能同时为空。");
    	}
        try {
        	hrmsOrgService.deleteByIdOrgCode(orgId, orgCode);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
    }
    
    /**
	 * @Title detailByIdOrgCode
	 * @Description 根据编码或ID查询
	 * @param orgCode
	 * @param orgId
	 * @return PlatformResult<String>
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "根据编码或ID查询", notes = "根据编码或ID查询")
    @GetMapping("/api/hrmsOrg/detailByIdOrgCode")
    public PlatformResult<HrmsOrg> detailByIdOrgCode(@RequestParam(value = "orgCode", required = false )@ApiParam(value = "机构编码") String orgCode,
    		@RequestParam(value = "orgId", required = false )@ApiParam(value = "机构ID") String orgId){
    	if(ObjectUtils.isEmpty(orgCode) && ObjectUtils.isEmpty(orgId)){
    		return PlatformResult.failure("机构编码和机构ID不能同时为空。");
    	}
        try {
			return PlatformResult.success(hrmsOrgService.detailByIdOrgCode(orgId, orgCode));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("操作失败");
		}
    }

	/**
	 * @Title selectOrgList
	 * @Description 查询机构列表
	 * @return PlatformResult<List<HrmsOrg>>
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "机构列表", notes = "机构列表")
    @GetMapping("/api/hrmsOrg/list")
    public PlatformResult<List<HrmsOrg>> selectOrgList(HrmsOrg record){
    	try {
			return PlatformResult.success(hrmsOrgService.selectList(record));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
    }

	/**
	 * @Title selectOrgTree
	 * @Description 查询机构树形列表
     * @param orgId 非必填，不为空时则查询该机构下的子机构，为空时则查询所有数据
	 * @return PlatformResult<List<HrmsOrgTreeVo>>
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "机构树形列表", notes = "机构树形列表")
    @GetMapping("/api/hrmsOrg/orgTree")
    public PlatformResult<List<HrmsOrgTreeVo>> selectOrgTree(@RequestParam(value = "orgCode", required = false )@ApiParam(value = "机构编码")String orgCode){
    	try {
			return PlatformResult.success(hrmsOrgService.selectOrgTree(orgCode, null));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
    }

	/**
	 * @Title syncSsoOrg
	 * @Description 同步sso的机构
	 * @return PlatformResult<String>
	 * @date 2025-07-14 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "同步sso的机构", notes = "同步sso的机构")
    @GetMapping("/api/hrmsOrg/syncSsoOrg")
    public PlatformResult<String> syncSsoOrg(){
    	try {
    		hrmsOrgService.syncSsoOrg();
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("操作失败");
		}
    }

	/**
	 * @Title syncHrmsOrgByPlatform
	 * @Description 同步平台的机构
	 * @return PlatformResult<String>
	 * @date 2025-07-29 11:30:00
	 * <AUTHOR>
	 */
    @ApiOperation(value = "同步平台的机构", notes = "同步平台的机构")
    @PostMapping("/hrmsOrg/syncHrmsOrgByPlatform")
    public PlatformResult<String> syncHrmsOrgByPlatform(@RequestBody PlatformBody platformBody){
    	try {
	    	
		    UserLoginService.loginContext("admin");
		    
		    Map<String, Object> bodyData = platformBody.getBody();
		    
//		    Map<String, Object> bodyData = (Map<String, Object>) body.get("body");
		    log.info("集成平台获取到机构的platformBody：" + bodyData);
		    
		    HrmsOrg hrmsOrg = hrmsOrgService.detailByIdOrgCode(null, (String) bodyData.get("orgCode"));
	    	//先判断获取数据字典，平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构，默认安单机构处理
		    String platformOrgType = "1";
		    List<DictItem> itemList = dictItemService.getDictItemByTypeCode("PLATFORM_ORG_TYPE");
		    if(CollUtil.isNotEmpty(itemList)){
		    	for(DictItem item : itemList){
		    		if(item.getItemCode().equals("ORG_TYPE")){
		    			platformOrgType = item.getItemNameValue();
		    		}
		    	}
		    }
		    
		    String orgType = (String) bodyData.get("orgType");//0机构1院区
		    String isDelete = (String) bodyData.get("isDelete");//0-否 1-是
	    	if(null != hrmsOrg) {
	    		if("1".equals(isDelete)){
	    			//删除
	    			hrmsOrgService.deleteByIdOrgCode(null, (String) bodyData.get("orgCode"));
	    			return PlatformResult.success();
	    		}
	    		
	    		if(platformOrgType.equals("2")){ //紧密型，平台院区对应OA的机构-比如益阳三
	    			hrmsOrg.setOrgName((String) bodyData.get("orgName"));
	    		} else {
	    			//非紧密型的院区数据不同步
	    			if("1".equals(orgType)){
	    				return PlatformResult.success();
	    			}
	    			
	    		}
	    		hrmsOrg.setStatus(Integer.valueOf((String) bodyData.get("status")));
	    		hrmsOrg.setParentCode((String) bodyData.get("parentCode"));
	    		hrmsOrgService.saveOrUpdate(hrmsOrg);
	    	} else if(null == hrmsOrg && "0".equals(isDelete)) {
	    		hrmsOrg = new HrmsOrg();
	    		if(!platformOrgType.equals("2") && "1".equals(orgType)){ //非紧密型不同步院区
	    			hrmsOrg.setOrgName((String) bodyData.get("orgName"));
	    		}
	    		hrmsOrg.setOrgId((String) bodyData.get("orgId"));
	    		hrmsOrg.setOrgCode((String) bodyData.get("orgCode"));
	    		hrmsOrg.setStatus(Integer.valueOf((String) bodyData.get("status")));
	    		hrmsOrg.setParentCode((String) bodyData.get("parentCode"));
	    		hrmsOrgService.saveOrUpdate(hrmsOrg);
	    	} 
	    	return PlatformResult.success();
	 } catch (Exception e) {
         e.printStackTrace();
         return PlatformResult.failure("同步机构数据失败,失败原因：" + e.getMessage());
     }
    }
}
