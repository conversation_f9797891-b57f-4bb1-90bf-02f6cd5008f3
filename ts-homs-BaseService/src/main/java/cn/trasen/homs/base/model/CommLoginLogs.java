package cn.trasen.homs.base.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

/**系统登录密码安全日志**/

@Table(name = "comm_login_logs") 
@Setter
@Getter
public class CommLoginLogs {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 用户编码
     */
    @Column(name = "user_code")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 是否弱密码
     */
    @Column(name = "password_type")
    @ApiModelProperty(value = "密码类型  1强口令  2弱口令")
    private String passwordType;

    /**
     * 最后登录时间
     */
    @Column(name = "login_time")
    @ApiModelProperty(value = "最近登录时间")
    private Date loginTime;
    
    @Column(name = "login_ip")
    @ApiModelProperty(value = "最后登录ip")
    private String loginIp;
    
    @Column(name = "login_type")
    @ApiModelProperty(value = "最后登录类型  PC WX")
    private String loginType;

    /**
     * 是否整改 
     */
    @ApiModelProperty(value = "是否整改  0待整改  1正常")
    private String rectification;

    /**
     * 整改时间
     */
    @Column(name = "rectification_date")
    @ApiModelProperty(value = "最近密码修改时间")
    private Date rectificationDate;

    /**
     * 整改人
     */
    @Column(name = "rectification_user")
    @ApiModelProperty(value = "整改人")
    private String rectificationUser;

    /**
     * 整改人名称
     */
    @Column(name = "rectification_user_name")
    @ApiModelProperty(value = "整改人名称")
    private String rectificationUserName;
    
    /**
     * 
     */
    @Column(name = "rectification_type")
    @ApiModelProperty(value = "整改类型  1自主修改   2管理员重置")
    private String rectificationType;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "员工id")
    private String employeeId;
    
    @Transient
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;
    
    @Transient
    @ApiModelProperty(value = "账户状态 0停用  1启用")
    private String disableStatus;
    
    @Transient
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;
    
    @Transient
    @ApiModelProperty(value = "科室id")
    private String orgId;
    
    @Transient
    @ApiModelProperty(value = "最近登录时间")
    private String loginTimeType;
    
    @Transient
    @ApiModelProperty(value = "最近登录开始时间")
    private String loginTimeStart;
    
    @Transient
    @ApiModelProperty(value = "最近登录结束时间")
    private String loginTimeEnd;
    
    @Transient
    @ApiModelProperty(value = "密码修改开始时间")
    private String rectificationDateStart;
    
    @Transient
    @ApiModelProperty(value = "密码修改结束时间")
    private String rectificationDateEnd;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
    
    @Transient
    private List<String> employeeIds;
    
    @Transient
    private String orgName;
    
}