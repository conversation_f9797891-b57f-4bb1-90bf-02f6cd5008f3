package cn.trasen.homs.base.mapping.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 员工映射表
 * @date 2025-04-28 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "cust_emp_mapping")
@Setter
@Getter
public class CustEmpMapping {
	
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Column(name = "syscode")
    @ApiModelProperty(value = "所属系统编码")
    private String syscode;

    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    @Column(name = "phone_number")
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;
    
    @Column(name = "base_employee_id")
    @ApiModelProperty(value = "员工ID")
    private String baseEmployeeId;

    @Column(name = "base_employee_no")
    @ApiModelProperty(value = "员工工号")
    private String baseEmployeeNo;

    @Column(name = "base_employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String baseEmployeeName;
    
    @Column(name = "base_org_code")
    @ApiModelProperty(value = "员工所属机构编码")
    private String baseOrgCode;
    
    @Column(name = "base_dept_id")
    @ApiModelProperty(value = "员工所属科室ID")
    private String baseDeptId;

    @Column(name = "base_dept_code")
    @ApiModelProperty(value = "员工所属科室编码")
    private String baseDeptCode;

    @Column(name = "base_dept_name")
    @ApiModelProperty(value = "员工所属科室名称")
    private String baseDeptName;
    
    /**********************映射数据*****************************/
    
    @Column(name = "map_employee_id")
    @ApiModelProperty(value = "映射员工ID")
    private String mapEmployeeId;

    @Column(name = "map_employee_no")
    @ApiModelProperty(value = "映射员工工号")
    private String mapEmployeeNo;

    @Column(name = "map_employee_name")
    @ApiModelProperty(value = "映射员工姓名")
    private String mapEmployeeName;
    
    @Column(name = "map_dept_id")
    @ApiModelProperty(value = "映射员工所属科室ID")
    private String mapDeptId;

    @Column(name = "map_dept_code")
    @ApiModelProperty(value = "映射员工所属科室编码")
    private String mapDeptCode;

    @Column(name = "map_dept_name")
    @ApiModelProperty(value = "映射员工所属科室名称")
    private String mapDeptName;
    
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 0=否; 1=是;")
    private String isEnable;
    
    /**********************审计信息*****************************/

    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;


}
