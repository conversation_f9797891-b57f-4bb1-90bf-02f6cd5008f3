package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.JobtitleBasicUpgradeSaveReq;
import cn.trasen.homs.base.model.JobtitleBasicUpgrade;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 15:52
 * @description
 */
public interface IJobtitleBasicUpgradeService {


    @Transactional(rollbackFor = Exception.class)

    /**
    * @description: 保存
* @param: jobtitleBasicUpgradeList
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/7 16:02
    */
    void save(String jobtitleBasicId,List<JobtitleBasicUpgradeSaveReq> jobtitleBasicUpgradeList);

    /**
     * @description: 获取列表
     * @param: jobtitleBasicId
     * @return: java.util.List<cn.trasen.homs.base.bean.JobtitleBasicUpgradeListResp>
     * @author: liyuan
     * @createTime: 2021/8/7 16:05
     */
    List<JobtitleBasicUpgrade> getBaseList(String jobtitleBasicId);
}
