package cn.trasen.homs.base.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/** 
* @ClassName: CommEmployeeShow 
* @Description: 自定义列展示bean 
* <AUTHOR>  
* @date 2022年12月5日 下午3:03:33 
*  
*/
@Table(name = "comm_employee_show")
@Setter
@Getter
public class CommEmployeeShow {
	
    @Id
    private String id;
    
    /**
     * 分组名称
     */
    @Column(name = "field_id")
    @ApiModelProperty(value = "字段id")
    private String fieldId;
    
    @Column(name = "field_lable")
    @ApiModelProperty(value = "字段Lable")
    private String fieldLable;
    
    @Column(name = "field_index")
    @ApiModelProperty(value = "字段index")
    private String fieldIndex;
    
    @Column(name = "field_name")
    @ApiModelProperty(value = "字段name")
    private String fieldName;
    
    
    @Column(name = "field_width")
    @ApiModelProperty(value = "字段id")
    private String fieldWidth;
    
    
    @Column(name = "field_align")
    @ApiModelProperty(value = "显示位置")
    private String fieldAlign;
    
    
    @Column(name = "field_sort")
    @ApiModelProperty(value = "排序号")
    private Integer fieldSort;
    
    
    @Column(name = "field_sortable")
    @ApiModelProperty(value = "字段是否可以排序")
    private Integer fieldSortable;
    
    @Column(name = "field_show")
    @ApiModelProperty(value = "字段是否显示 是否展示 0否 1展示")
    private Integer fieldShow;
    
    @Column(name = "field_fixed")
    @ApiModelProperty(value = "固定的 0否 1是（为1的不能删除）")
    private Integer fieldFixed;
    
    @Column(name = "field_export")
    @ApiModelProperty(value = "是否导出")
    private Integer fieldExport;
    
    @Column(name = "field_export_sort")
    @ApiModelProperty(value = "导出排序")
    private Integer fieldExportSort;
    
    
    
    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}
