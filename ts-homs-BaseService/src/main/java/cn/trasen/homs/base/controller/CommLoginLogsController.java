package cn.trasen.homs.base.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.trasen.BootComm.utils.MD5;
import cn.trasen.BootComm.utils.PasswordHash;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.service.CommLoginLogsService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.core.bean.CommErrorLogs;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * @ClassName CommLoginLogsController
 * @Description TODO
 * @date 2023��9��20�� ����10:30:27
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CommLoginLogsController")
@Log4j2
public class CommLoginLogsController {

	private transient static final Logger logger = LoggerFactory.getLogger(CommLoginLogsController.class);

	@Autowired
	private CommLoginLogsService commLoginLogsService;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;
	
	@Autowired
	private GlobalSettingsService globalSettingsService;
	
	@Value("${changePwdByUserCodePlatformUrl}")
	private String changePwdByUserCodePlatformUrl;

	/**
	 * @Title saveCommLoginLogs
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��20�� ����10:30:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/loginLogs/save")
	public PlatformResult<String> saveCommLoginLogs(@RequestBody CommLoginLogs record) {
		try {
			GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
			
			commLoginLogsService.save(record,globalSetting.getOrgCode());
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCommLoginLogs
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��20�� ����10:30:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/loginLogs/update")
	public PlatformResult<String> updateCommLoginLogs(@RequestBody CommLoginLogs record) {
		try {
			commLoginLogsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCommLoginLogsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CommLoginLogs>
	 * @date 2023��9��20�� ����10:30:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/loginLogs/{id}")
	public PlatformResult<CommLoginLogs> selectCommLoginLogsById(@PathVariable String id) {
		try {
			CommLoginLogs record = commLoginLogsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCommLoginLogsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��9��20�� ����10:30:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/loginLogs/delete/{id}")
	public PlatformResult<String> deleteCommLoginLogsById(@PathVariable String id) {
		try {
			commLoginLogsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCommLoginLogsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CommLoginLogs>
	 * @date 2023��9��20�� ����10:30:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/loginLogs/list")
	public DataSet<CommLoginLogs> selectCommLoginLogsList(Page page, CommLoginLogs record) {
		return commLoginLogsService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @MethodName: batchUpdatePwd
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023-09-23 02:34:44
	 */
	@ApiOperation(value = "重置密码为初始密码", notes = "重置密码为初始密码")
	@PostMapping("/api/loginLogs/batchUpdatePwd")
	public PlatformResult<String> batchUpdatePwd(@RequestBody List<CommLoginLogs> commLoginLogsList) {
		try {
			
			GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
			
			if (StringUtils.isBlank(globalSetting.getPasswordPreset())) {
				Assert.isTrue(true, "初始密码为空，请先维护初始密码");
			}
			
			//弱密码规则校验  至少8个字符，至少1个字母，1个数字和1个特殊字符
			//String regex = "^(?=.*[a-z])(?=.*\\d)(?=.*[@#.~$!%*^?&_+-])[A-Za-z\\d@#.~$!%*^?&_+-]{8,50}$";
			String regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";
			
			for (CommLoginLogs commLoginLogs : commLoginLogsList) {
				 ThpsUserReq thpsUser = new ThpsUserReq();
			        thpsUser.setId(commLoginLogs.getEmployeeId());
			        thpsUser.setUsercode(commLoginLogs.getUserCode());
			        
			        if (StringUtils.isNotBlank(globalSetting.getPasswordPreset())) {
			        	thpsUser.setPassword(PasswordHash.createHash(globalSetting.getPasswordPreset()));
				        String md5Password = MD5.string2MD5(globalSetting.getPasswordPreset());//md5 加密
				        thpsUser.setOldpassword(md5Password.toUpperCase());//将md5解密转换为大写
			        }
			        
			        systemUserFeignService.pwdReset(commLoginLogs.getEmployeeId(),globalSetting.getPasswordPreset());
			        //systemUserFeignService.saveOrUpdate(thpsUser);
			        
			        //更新登录记录表
			    	CommLoginLogs record = new CommLoginLogs();
			    	record.setUserCode(commLoginLogs.getUserCode());
			    	record.setRectificationDate(new Date());
			    	record.setRectificationType("2");
			    	
			        if(!globalSetting.getPasswordPreset().matches(regex)){
			            record.setPasswordType("2");
			            record.setRectification("0");
			        }else{
			        	record.setPasswordType("1");
			        	record.setRectification("1");
			        }
			        
					commLoginLogsService.updateByUserCode(record);
			        
			        if(StringUtils.isNotBlank(changePwdByUserCodePlatformUrl)) {
			        	Map<String,String> requestJson = new HashMap<>();
			            requestJson.put("userCode", commLoginLogs.getUserCode());
			            String newpassword = new String(Base64.encode(globalSetting.getPasswordPreset().trim().getBytes()));
			            requestJson.put("password", newpassword);
			            String result2 = HttpRequest.post(changePwdByUserCodePlatformUrl)
			            .body(JSON.toJSONString(requestJson))
			            .execute().body();
			            
			            log.info("同步密码到集成平台返回的数据：" + result2);
			        }
			}
	       
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "停用或启用", notes = "停用或启用")
	@PostMapping("/api/loginLogs/disableOrEnable")
	public PlatformResult<String> disableOrEnable(@RequestBody List<CommLoginLogs> commLoginLogsList,String status) {
		try {
			 
			for (CommLoginLogs commLoginLogs : commLoginLogsList) {
				ThpsUserReq thpsUserReq = new ThpsUserReq();
				thpsUserReq.setId(commLoginLogs.getEmployeeId());
				thpsUserReq.setStatus(Integer.valueOf(status));
				PlatformResult<String> newDisable = systemUserFeignService.newDisable(thpsUserReq);
				 
				if(newDisable.isSuccess()) {
					commLoginLogsService.updateDisable(commLoginLogs.getEmployeeId(),status);
				}
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "批量解锁", notes = "批量解锁")
	@PostMapping("/api/loginLogs/batchUnlock")
	public PlatformResult<String> batchUnlock(@RequestBody List<CommLoginLogs> commLoginLogsList) {
		try {
			
			for (CommLoginLogs commLoginLogs : commLoginLogsList) {
				systemUserFeignService.unlock(commLoginLogs.getEmployeeId());
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "错误异常信息传输", notes = "错误异常信息传输")
	@PostMapping("/api/errorLogs/trans")
	public PlatformResult<String> SaveSyncErrorLogs(String requestBody){
		//先拿到数据后再Base64解码
		byte[] body = Base64Utils.decodeFromString(requestBody);
		JSON.parseObject(body.toString(), new TypeReference<List<CommErrorLogs>>(){});
		return PlatformResult.success();
	}
}
