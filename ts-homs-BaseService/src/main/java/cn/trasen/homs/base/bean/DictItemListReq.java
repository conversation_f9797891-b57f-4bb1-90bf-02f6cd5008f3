package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/16 14:57
 * @description
 */
@Data
public class DictItemListReq {

    @ApiModelProperty(value = "类别编码")
    String typeCode;

    @ApiModelProperty(value = "字典名称")
    String itemName;

    @ApiModelProperty(value = "机构编码")
    String ssoOrgCode;

    @ApiModelProperty(value = "是否启用: 1=是; 0=否;")
    private  String isEnable;
}
