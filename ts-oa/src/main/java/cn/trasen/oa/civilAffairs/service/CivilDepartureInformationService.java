package cn.trasen.oa.civilAffairs.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilDepartureInformation;

/**
 * @ClassName CivilDepartureInformationService
 * @Description TODO
 * @date 2024��1��25�� ����2:23:40
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilDepartureInformationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	Integer save(CivilDepartureInformation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	Integer update(CivilDepartureInformation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilDepartureInformation
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	CivilDepartureInformation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilDepartureInformation>
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	DataSet<CivilDepartureInformation> getDataSetList(Page page, CivilDepartureInformation record);
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilDepartureInformation>
	 * @date 2024��1��25�� ����2:23:40
	 * <AUTHOR>
	 */
	DataSet<CivilDepartureInformation> getPageList(Page page, CivilDepartureInformation record);
	
	List<Map<String, Object>> selectDepartureByMonthly (CivilDepartureInformation record);
	
	List<Map<String, Object>> selectDepartureByMonthlyObjectType (CivilDepartureInformation record);
	
	CivilDepartureInformation  selectByIdMap(CivilDepartureInformation record);
	
}
