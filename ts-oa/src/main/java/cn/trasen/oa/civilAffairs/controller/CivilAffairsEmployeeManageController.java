package cn.trasen.oa.civilAffairs.controller;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsEmpModel;
import cn.trasen.oa.civilAffairs.model.CivilAffairsPersonnelInfo;
import cn.trasen.oa.civilAffairs.service.CivilAffairsEmployeeManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsEmployeeManageController民政对象人员档案相关接口
 * @Description TODO
 * @date 2023��12��18�� ����4:59:45
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsEmployeeManageController")
public class CivilAffairsEmployeeManageController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsFieldController.class);

	@Autowired
	private CivilAffairsEmployeeManageService civilAffairsEmployeeManageService;
	
	@ApiOperation(value = "查询人员信息列表(分页)", notes = "查询人员信息列表(分页)")
	@PostMapping(value = "/api/CivilAffairsEmployeeManage/getPageListByPersonnelInfo")
	public DataSet<Map<String, String>> getPageListByPersonnelInfo(Page page, CivilAffairsPersonnelInfo record) {
		List<Map<String, String>> list = civilAffairsEmployeeManageService.getEmployeePageListByCustom(page, record);
		return new DataSet<Map<String, String>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}
	
	
	@ApiOperation(value = "根据id、对象状态查询自定义人员档案", notes = "根据id、对象状态查询自定义人员档案")
	@PostMapping("/api/CivilAffairsEmployeeManage/findByIdAndDetails/{id}")//人员主表ID
	public PlatformResult<Map<String, List<Map<String, String>>>> findByIdAndDetails(CivilAffairsPersonnelInfo record) {
		try {
			return PlatformResult.success(civilAffairsEmployeeManageService.findByIdAndDetails(record));
		} catch (Exception e) {
			e.printStackTrace();
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	@ApiOperation(value = "新增/修改自定义人员档案表数据", notes = "新增/修改自定义人员档案表数据")
	@PostMapping("/api/CivilAffairsEmployeeManage/save")
	public PlatformResult<String> save(@RequestBody CivilAffairsEmpModel record) {
		try {
			String message = civilAffairsEmployeeManageService.uniqueCheck(record);
			String personnelId = "";
			boolean isAdd = true;
			if(StringUtils.isBlank(record.getPersonnelId())) { //新增
				isAdd = true;
			}else {   //update
				isAdd = false;
			}
			if (StringUtils.isBlank(message)) {
				personnelId = civilAffairsEmployeeManageService.save(record);
			} else {
				return PlatformResult.failure(message);
			}
			return PlatformResult.success("操作成功");
		} catch (Exception e) {
			e.printStackTrace();
			return PlatformResult.failure(e.getMessage());
		}
		
	}
}
