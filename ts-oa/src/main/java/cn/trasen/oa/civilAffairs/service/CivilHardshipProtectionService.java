package cn.trasen.oa.civilAffairs.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilHardshipProtection;

/**
 * @ClassName CivilHardshipProtectionService
 * @Description TODO
 * @date 2024��1��25�� ����2:28:24
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilHardshipProtectionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	Integer save(CivilHardshipProtection record);
	
	Integer copyLastMonthSave(CivilHardshipProtection record);
	

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	Integer update(CivilHardshipProtection record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilHardshipProtection
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	CivilHardshipProtection selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	DataSet<CivilHardshipProtection> getDataSetList(Page page, CivilHardshipProtection record);
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	DataSet<CivilHardshipProtection> getPageList(Page page, CivilHardshipProtection record);
	
	
	List<Map<String , Object>> selectHardshipCategoriesCount(CivilHardshipProtection record);
	
	Map<String , Object> selectHardshipMonthDetails(CivilHardshipProtection record);
	
}
