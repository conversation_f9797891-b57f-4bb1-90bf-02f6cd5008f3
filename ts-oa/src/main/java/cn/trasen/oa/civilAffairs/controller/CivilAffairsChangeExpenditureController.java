package cn.trasen.oa.civilAffairs.controller;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.nacos.common.utils.CollectionUtils;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsChangeExpenditureMapper;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsChangeLedgerMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeExpenditure;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeExpenditureChild;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeManage;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeExpenditureService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeLedgerService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeManageService;
import cn.trasen.oa.civilAffairs.util.LeftAlignExportParams;
import cn.trasen.oa.civilAffairs.util.VueTableEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsChangeExpenditureController 零钱支出管理
 * @Description TODO
 * @date 2024��5��22�� ����4:48:28
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsChangeExpenditureController")
public class CivilAffairsChangeExpenditureController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsChangeExpenditureController.class);

	 private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
	 private static final AtomicInteger SEQ = new AtomicInteger(0);
	    
	@Autowired
	private CivilAffairsChangeExpenditureService civilAffairsChangeExpenditureService;
	
	@Autowired
	private CivilAffairsChangeExpenditureMapper civilAffairsChangeExpenditureMapper;
	
	@Autowired
	private CivilAffairsChangeManageService changeManageService;
	
	@Autowired
	private CivilAffairsChangeLedgerService civilAffairsChangeLedgerService;
	
	@Autowired
	private CivilAffairsChangeLedgerMapper changeLedgerMapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	/**
	 * @Title saveCivilAffairsChangeExpenditure
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsChangeExpenditure/save")
	public PlatformResult<String> saveCivilAffairsChangeExpenditure(@RequestBody CivilAffairsChangeExpenditure record) {
		try {
			//为保证流水号不重复，流水号从后台生成
			record.setSerialNumber(civilAffairsChangeExpenditureMapper.selectSerialNumberMax());
			
			if(CollectionUtils.isNotEmpty(record.getChangeExpenditureChildList())) {
				for(CivilAffairsChangeExpenditureChild changeExpenditureChild :record.getChangeExpenditureChildList()) {
					record.setGoodsId(changeExpenditureChild.getGoodsId());
					record.setGoodsName(changeExpenditureChild.getGoodsName());
					record.setPrice(changeExpenditureChild.getPrice());
					record.setNumber(changeExpenditureChild.getNumber());
					record.setTotalPrice(changeExpenditureChild.getTotalPrice());
					record.setRecipient(changeExpenditureChild.getRecipient());
					record.setModel(changeExpenditureChild.getModel());
					record.setSupplierName(changeExpenditureChild.getSupplierName());
					record.setRemarks(changeExpenditureChild.getRemarks());//新增备注
					civilAffairsChangeExpenditureService.save(record);
				}
				CivilAffairsChangeExpenditure changeExpenditure = civilAffairsChangeExpenditureService.selectBySerialNumberHj(record);//根据对象id和票据，汇总金额；产生台账，扣除支出费用
				if(changeExpenditure != null) {
					saveRegisterLedger(changeExpenditure);//产生台账
					updateManageChangeBalance(changeExpenditure,"2");//更新余额，扣除支出
				}
			}else {
				PlatformResult.failure("无商品数据");
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title cancelStatus
	 * @Description 作废
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "作废", notes = "作废")
	@PostMapping("/api/CivilAffairsChangeExpenditure/updateStatus")
	public PlatformResult<String> updateStatus(@RequestBody CivilAffairsChangeExpenditure record) {
		try {
			CivilAffairsChangeExpenditure changeExpenditure = civilAffairsChangeExpenditureService.selectBySerialNumberHj(record);//根据对象id和票据，汇总金额；
			civilAffairsChangeExpenditureService.updateStatusBySerialNumber(record);
			if(StringUtils.isNotBlank(record.getStatus()) && "2".equals(record.getStatus())) {
				changeLedgerMapper.updateByBillNumber(record.getManageId(),record.getSerialNumber());//根据对象id和票据号,作废台账
				updateManageChangeBalance(changeExpenditure,"1");//更新余额，扣除支出
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title updateCivilAffairsChangeExpenditure
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsChangeExpenditure/update")
	public PlatformResult<String> updateCivilAffairsChangeExpenditure(@RequestBody CivilAffairsChangeExpenditure record) {
		try {
			CivilAffairsChangeExpenditure changeExpenditureBefore = civilAffairsChangeExpenditureService.selectBySerialNumberHj(record);//根据对象id和票据，汇总金额；更新前数据；
			
			civilAffairsChangeExpenditureService.update(record);
			
			CivilAffairsChangeExpenditure changeExpenditureAfter  = civilAffairsChangeExpenditureService.selectBySerialNumberHj(record);//根据对象id和票据，汇总金额；更新后数据；
			changeExpenditureAfter.setTotalPriceBefore(changeExpenditureBefore.getTotalPrice());
			if(changeExpenditureAfter != null) {
				updateRegisterLedger(changeExpenditureAfter);//更新台账
				updateManageChangeBalance(changeExpenditureAfter,"3");//更新余额，扣除差额
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsChangeExpenditureById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsChangeExpenditure>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsChangeExpenditure/{id}")
	public PlatformResult<CivilAffairsChangeExpenditure> selectCivilAffairsChangeExpenditureById(@PathVariable String id) {
		try {
			CivilAffairsChangeExpenditure record = civilAffairsChangeExpenditureService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsChangeExpenditureById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsChangeExpenditure/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsChangeExpenditureById(@PathVariable String id) {
		try {
			civilAffairsChangeExpenditureService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsChangeExpenditureList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsChangeExpenditure>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsChangeExpenditure/list")
	public DataSet<CivilAffairsChangeExpenditure> selectCivilAffairsChangeExpenditureList(Page page, CivilAffairsChangeExpenditure record) {
		return civilAffairsChangeExpenditureService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsChangeExpenditure>
	 * @date 2024��5��22�� ����4:48:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/CivilAffairsChangeExpenditure/pageList")
	public DataSet<CivilAffairsChangeExpenditure> selectPageList(Page page, CivilAffairsChangeExpenditure record) {
		return civilAffairsChangeExpenditureService.selectPageList(page, record);
	}
	
	
	/**
	 * @Title saveCivilAffairsChangeRegister
	 * @Description 查询已经产生的流水号(用于新增台账)
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询已经产生的流水号(用于新增台账)", notes = "查询已经产生的流水号(用于新增台账)")
	@GetMapping("/api/CivilAffairsChangeExpenditure/selectSerialNumber")
	public PlatformResult<List<CivilAffairsChangeExpenditure>> selectSerialNumber(CivilAffairsChangeExpenditure record) {
		try {
			//record.setType("1");//模糊检索票据号，并且过滤掉已经新增台账的
			List<CivilAffairsChangeExpenditure> changeRegisterList = civilAffairsChangeExpenditureService.selectSerialNumber(record);
			return PlatformResult.success(changeRegisterList);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	
	/**
	 * @Title saveCivilAffairsChangeRegister
	 * @Description 查询已经产生的流水号(用于新增台账)
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "服务对象零用钱支出单导出数据接口", notes = "服务对象零用钱支出单导出数据接口")
	@GetMapping("/api/CivilAffairsChangeExpenditure/selectExportChangeExpenditureData")
	public PlatformResult<Map<String, Object>> selectExportChangeExpenditureData(CivilAffairsChangeExpenditure record) {
		try {
			//record.setType("1");//模糊检索票据号，并且过滤掉已经新增台账的
			Map<String, Object> map = new HashMap<String, Object>();
			//查询表头
			List<VueTableEntity> vueTableEntityList = civilAffairsChangeExpenditureService.selectExportChangeExpenditureHeader(record);
			//查询数据
			List<Map<String, Object>> records = civilAffairsChangeExpenditureService.getExportChangeExpenditure(record);//查询导出数据
			 // 遍历列表并处理每个映射、将为0处理为null
	        for (Map<String, Object> resultMap : records) {
	            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
	                Object value = entry.getValue();
	                if (value instanceof BigDecimal && ((BigDecimal) value).compareTo(BigDecimal.ZERO) == 0 || value instanceof Integer && (int) value == 0 || (value instanceof Double && (Double) value == 0.00) ) {
	                	resultMap.put(entry.getKey(), null);
	                }
	            }
	        }
			map.put("vueTableEntityList", vueTableEntityList);
			map.put("records", records);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	//不确定字段、导出接口
	@ApiOperation(value = "导出服务对象零用钱支出单", notes = "导出服务对象零用钱支出单")
	@GetMapping("/api/CivilAffairsChangeExpenditure/exportChangeExpenditure")
	public void exportChangeExpenditure(HttpServletRequest request, HttpServletResponse response, CivilAffairsChangeExpenditure record) {
		// 导出文件名称
		String filename = "服务对象零用钱支出单";
	
		// 导出数据列表
		try {
			//查询表头
			List<VueTableEntity> exportList = civilAffairsChangeExpenditureService.selectExportChangeExpenditureHeader(record);
			//查询数据
			List<Map<String, Object>> records = civilAffairsChangeExpenditureService.getExportChangeExpenditure(record);//查询导出数据
			//添加序号
			  int index = 1;
			  for(int i = 0;i <records.size();i++) {
				  Map<String,Object> map =records.get(i);
				  //将数据为0的值，置空
				 /* for (Map.Entry<String, Object> entry : map.entrySet()) {
			            if (entry.getValue() != null && entry.getValue().equals(0)) {
			                map.put(entry.getKey(), null);
			            }
			        } */
				  map.put("no", String.valueOf(index)); 
				  index++; 
				  }
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
			//添加序号
			colList.add(new ExcelExportEntity("序号", "no",6));
			if (exportList != null && exportList.size() > 0) {
				for(int i =0 ;i < exportList.size();i++) {
					colList.add(new ExcelExportEntity(exportList.get(i).getLabel(), exportList.get(i).getProp()));
				}
			}
			
			try {
				Date now = new Date();
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
				 //ExportParams params = new ExportParams(filename, "服务对象零用钱支出单", ExcelType.XSSF);
		            //params.setAddIndex(true);
				LeftAlignExportParams params = new LeftAlignExportParams("服务对象零用钱支出单");
				 if(StringUtils.isNotBlank(record.getManageId())) {
					 CivilAffairsChangeManage changeManage = changeManageService.selectById(record.getManageId());
					 //getItemName("civil_admission_ward",changeManage.getWard());//获取字典值
					params.setSecondTitle("病区:"+getItemName("civil_admission_ward",changeManage.getWard())+"        "+formatter.format(now));		
				 }else {
					params.setSecondTitle(formatter.format(now));
				 }
				 params.setAlignment(HorizontalAlignment.LEFT);
		            
	            Workbook workbook = ExcelExportUtil
	                    .exportExcel(params, colList, records);
	            response.setContentType("application/vnd.ms-excel");
	            response.setCharacterEncoding("UTF-8");
	            response.setHeader("Content-disposition", "attachment; filename="
	                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

	            OutputStream fos = response.getOutputStream();
	            
	            workbook.write(fos);
	            fos.close();

	        } catch (FileNotFoundException e) {
	        	logger.error(e.getMessage(),e);
	        } catch (IOException e) {
	        	logger.error(e.getMessage(),e);
	        }
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}
	
	
	
	@Transactional(readOnly = false)//更新零钱余额
	public void updateManageChangeBalance(CivilAffairsChangeExpenditure record,String  status) {
		// TODO Auto-generated method stub
		CivilAffairsChangeManage changeManage = changeManageService.selectById(record.getManageId());
		if(changeManage !=null) {
			if("1".equals(status)) {
				changeManage.setChangeBalance(changeManage.getChangeBalance().add(record.getTotalPrice()));//现有余额+支出(作废退还)
			}else if ("2".equals(status)) {
				changeManage.setChangeBalance(changeManage.getChangeBalance().subtract(record.getTotalPrice()));//现有余额-支出
			}else if ("3".equals(status)) {
				changeManage.setChangeBalance(changeManage.getChangeBalance().subtract(record.getTotalPrice().subtract(record.getTotalPriceBefore())));//现有余额-差额
			}
			changeManageService.update(changeManage);
		}
	}
	
	@Transactional(readOnly = false)//新增产生台账
	public void saveRegisterLedger(CivilAffairsChangeExpenditure record) {
		//SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		CivilAffairsChangeManage changeManage = changeManageService.selectById(record.getManageId());
		
		CivilAffairsChangeLedger  civilAffairsChangeLedger = new  CivilAffairsChangeLedger();//台账对象
		civilAffairsChangeLedger.setSerialNumber(record.getSerialNumber());//流水号
		//civilAffairsChangeLedger.setBillNumber(record.getBillNumber());//票据号
		civilAffairsChangeLedger.setManageId(record.getManageId());//对象ID
		civilAffairsChangeLedger.setRegisterDate(new Date());//登记时间
		civilAffairsChangeLedger.setAmountCollected(BigDecimal.valueOf(0));//收款金额
		civilAffairsChangeLedger.setExpenditureAmount(record.getTotalPrice());//支出金额
		civilAffairsChangeLedger.setBalanceAmount(changeManage.getChangeBalance());//结存金额--零钱余额
		civilAffairsChangeLedger.setLedgerItems(record.getGoodsName());//台账事项--备注--支出商品名称
		//civilAffairsChangeLedger.setRelativesAutograph(record.getChangeGiver());//家属签名--零钱给予者
		civilAffairsChangeLedgerService.save(civilAffairsChangeLedger);//新增台账  
	}
	
	@Transactional(readOnly = false)//编辑支出、更新台账
	public void updateRegisterLedger(CivilAffairsChangeExpenditure record) {
		CivilAffairsChangeLedger  civilAffairsChangeLedger = new  CivilAffairsChangeLedger();//台账对象
		civilAffairsChangeLedger.setSerialNumber(record.getSerialNumber());//流水号
		//civilAffairsChangeLedger.setBillNumber(record.getBillNumber());//票据号
		civilAffairsChangeLedger.setManageId(record.getManageId());//对象ID
		civilAffairsChangeLedger.setExpenditureAmount(record.getTotalPrice());//支出金额
		civilAffairsChangeLedger.setLedgerItems(record.getGoodsName());//台账事项--备注--支出商品名称
		//civilAffairsChangeLedger.setRelativesAutograph(record.getChangeGiver());//家属签名--零钱给予者
		changeLedgerMapper.updateByNumber(civilAffairsChangeLedger);//新增台账  
	}
	
	public  String getItemName(String dictType, String itemNameValue) {
		Assert.notNull(dictType, "dictType must not be null.");
		String itemName = "";
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				if(StringUtils.isNotBlank(itemNameValue) && itemNameValue.equals(d.getItemNameValue())) {
					itemName = d.getItemName();
				}
			}
		}
		return itemName;
	}
	
	
	
	/**
	 * 
	 * @Title selectArchivalNumberMax
	 * @Description 查询当年最大流水号
	 * @param id
	 * @return PlatformResult<CivilArchivalInformation>
	 * @date 2024��3��18�� ����2:21:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询当年最大流水号", notes = "查询当年最大流水号")
	@GetMapping("/api/CivilArchivalInformation/selectSerialNumberMax")
	public PlatformResult<String> selectSerialNumberMax(String year) {
		try {
			String archivalNumberMax = civilAffairsChangeExpenditureService.selectSerialNumberMax();
			return PlatformResult.success(archivalNumberMax);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title selectArchivalNumberMax
	 * @Description 查询当年最大流水号
	 * @param id
	 * @return PlatformResult<CivilArchivalInformation>
	 * @date 2024��3��18�� ����2:21:02
	 * <AUTHOR>
	 */
	/*@ApiOperation(value = "查询当年最大流水号", notes = "查询当年最大流水号")
	@GetMapping("/api/CivilArchivalInformation/selectSerialNumberMax")
	public PlatformResult<String> selectSerialNumberMax(String year) {
		try {
			        String datePart = DATE_FORMAT.format(new Date());
			        int seqNumber = SEQ.incrementAndGet();
			        return PlatformResult.failure(datePart + String.format("%05d", seqNumber));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	} */
	
	 public static synchronized String generate() {//生成编码
	        String datePart = DATE_FORMAT.format(new Date());
	        int seqNumber = SEQ.incrementAndGet();
	        return datePart + String.format("%05d", seqNumber);
	    }
	 
	    public static void main(String[] args) {
	        for (int i = 0; i < 1; i++) {
	            System.out.println(generate());
	        }
	    }
}
