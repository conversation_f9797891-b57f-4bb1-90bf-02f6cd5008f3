package cn.trasen.oa.civilAffairs.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilOrphanProtection;

/**
 * @ClassName CivilOrphanProtectionService
 * @Description TODO
 * @date 2024��1��25�� ����2:25:01
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilOrphanProtectionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	Integer save(CivilOrphanProtection record);
	
	Integer copyLastMonthSave(CivilOrphanProtection record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	Integer update(CivilOrphanProtection record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilOrphanProtection
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	CivilOrphanProtection selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilOrphanProtection>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	DataSet<CivilOrphanProtection> getDataSetList(Page page, CivilOrphanProtection record);
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilOrphanProtection>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	DataSet<CivilOrphanProtection> getPageList(Page page, CivilOrphanProtection record);
	
}
