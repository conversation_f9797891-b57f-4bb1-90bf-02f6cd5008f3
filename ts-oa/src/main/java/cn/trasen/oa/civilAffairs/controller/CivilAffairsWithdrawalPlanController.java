package cn.trasen.oa.civilAffairs.controller;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.civilAffairs.model.CivilAffairsMessageSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsWithdrawalPlan;
import cn.trasen.oa.civilAffairs.service.CivilAffairsMessageSetService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsWithdrawalPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsWithdrawalPlanController支取计划
 * @Description TODO
 * @date 2023��12��11�� ����4:11:01
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsWithdrawalPlanController")
public class CivilAffairsWithdrawalPlanController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsWithdrawalPlanController.class);

	@Autowired
	private CivilAffairsWithdrawalPlanService civilAffairsWithdrawalPlanService;

	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	@Autowired
    private CivilAffairsMessageSetService  civilAffairsMessageSetService;
	
	/**
	 * @Title saveCivilAffairsWithdrawalPlan
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:11:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/civilAffairsWithdrawalPlan/save")
	public PlatformResult<String> saveCivilAffairsWithdrawalPlan(@RequestBody CivilAffairsWithdrawalPlan record) {
		try {
			civilAffairsWithdrawalPlanService.save(record);
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//日期格式化
			String  plannedDate  = "";
			if(record.getPlannedDate() !=null) {
				plannedDate = formatter.format(record.getPlannedDate());
			}
			
			String content ="计划于" +plannedDate+ "从"+record.getName()+"账号:"+record.getAccount()+"支取"+
					record.getPlannedAmount()+"元,请及时处理";
		    String subject = "民政对象——支取提醒";
		    String url =appConfigProperties.getWxDomain() + "";//微信端地址
		    
		    CivilAffairsMessageSet civilAffairsMessageSet  =  civilAffairsMessageSetService.getCivilAffairsMessageSetOne(new CivilAffairsMessageSet());	
		    if (civilAffairsMessageSet != null &&  StringUtils.isNotEmpty(civilAffairsMessageSet.getWithdrawalUser())){//支取提醒人员
		    	sendMessage(content, Arrays.asList(civilAffairsMessageSet.getWithdrawalUser().split(",")), url, subject,"3");
		    }
			
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	 private void sendMessage(String content,List<String> receiverList,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                //.receiver(receiver)  //接收人
	                .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-oa/civil-affairs-objects/planned-withdrawal-records").source("民政对象")//跳转前端地址
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }

	/**
	 * @Title updateCivilAffairsWithdrawalPlan
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:11:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/civilAffairsWithdrawalPlan/update")
	public PlatformResult<String> updateCivilAffairsWithdrawalPlan(@RequestBody CivilAffairsWithdrawalPlan record) {
		try {
			civilAffairsWithdrawalPlanService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsWithdrawalPlanById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsWithdrawalPlan>
	 * @date 2023��12��11�� ����4:11:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/civilAffairsWithdrawalPlan/{id}")
	public PlatformResult<CivilAffairsWithdrawalPlan> selectCivilAffairsWithdrawalPlanById(@PathVariable String id) {
		try {
			CivilAffairsWithdrawalPlan record = civilAffairsWithdrawalPlanService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsWithdrawalPlanById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:11:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/civilAffairsWithdrawalPlan/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsWithdrawalPlanById(@PathVariable String id) {
		try {
			civilAffairsWithdrawalPlanService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsWithdrawalPlanList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsWithdrawalPlan>
	 * @date 2023��12��11�� ����4:11:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/civilAffairsWithdrawalPlan/list")
	public DataSet<CivilAffairsWithdrawalPlan> selectCivilAffairsWithdrawalPlanList(Page page, CivilAffairsWithdrawalPlan record) {
		return civilAffairsWithdrawalPlanService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsWithdrawalRecords>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/civilAffairsWithdrawalPlan/PageList")
	public DataSet<CivilAffairsWithdrawalPlan> getPageList(Page page, CivilAffairsWithdrawalPlan record) {
		return civilAffairsWithdrawalPlanService.getPageList(page, record);
	}
}
