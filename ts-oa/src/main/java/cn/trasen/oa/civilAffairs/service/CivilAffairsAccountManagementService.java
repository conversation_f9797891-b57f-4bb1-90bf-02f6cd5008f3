package cn.trasen.oa.civilAffairs.service;

import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsAccountManagement;

/**
 * @ClassName CivilAffairsAccountManagementService
 * @Description TODO
 * @date 2023��12��11�� ����4:04:36
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsAccountManagementService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��12��11�� ����4:04:36
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsAccountManagement record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��12��11�� ����4:04:36
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsAccountManagement record);
	
	
	Integer returnAccount(CivilAffairsAccountManagement record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��12��11�� ����4:04:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsAccountManagement
	 * @date 2023��12��11�� ����4:04:36
	 * <AUTHOR>
	 */
	CivilAffairsAccountManagement selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAccountManagement>
	 * @date 2023��12��11�� ����4:04:36
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsAccountManagement> getDataSetList(Page page, CivilAffairsAccountManagement record);
	
	DataSet<CivilAffairsAccountManagement> getPageList(Page page, CivilAffairsAccountManagement record);
	
	Map<String , Object> selectCountByDate(CivilAffairsAccountManagement record);
	
}
