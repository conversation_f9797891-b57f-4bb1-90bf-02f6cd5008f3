package cn.trasen.oa.civilAffairs.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsField;
import cn.trasen.oa.civilAffairs.service.CivilAffairsFieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsFieldController自定义字段
 * @Description TODO
 * @date 2023��12��18�� ����4:59:45
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsFieldController")
public class CivilAffairsFieldController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsFieldController.class);

	@Autowired
	private CivilAffairsFieldService civilAffairsFieldService;

	/**
	 * @Title saveCivilAffairsField
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增自定义档案字段")
	@PostMapping("/api/CivilAffairsField/save")
	public PlatformResult<String> saveCivilAffairsField(@RequestBody CivilAffairsField record) {
		try {
			int count = civilAffairsFieldService.save(record);
			if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsField
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "修改自定义档案字段")
	@PostMapping("/api/CivilAffairsField/update")
	public PlatformResult<String> updateCivilAffairsField(@RequestBody CivilAffairsField record) {
		try {
			int count = civilAffairsFieldService.update(record);
			if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	  @ApiOperation(value = "批量修改档案字段", notes = "根据列表修改自定义人员档案字段")
	    @PostMapping("/api/CivilAffairsField/updateList")
	    public PlatformResult<CivilAffairsField> updateList(@RequestBody List<CivilAffairsField> records) {
	        try {
	        	civilAffairsFieldService.updateList(records);
	            return PlatformResult.success();
	        } catch (Exception e) {
	        	return PlatformResult.failure(e.getMessage());
	        }
	    }

	/**
	 * 
	 * @Title selectCivilAffairsFieldById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsField>
	 * @date 2023��12��18�� ����4:59:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "根据id查询自定义档案字段")
	@GetMapping("/api/CivilAffairsField/{id}")
	public PlatformResult<CivilAffairsField> selectCivilAffairsFieldById(@PathVariable String id) {
		try {
			CivilAffairsField record = civilAffairsFieldService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	   @ApiOperation(value = "获取档案列表头", notes = "获取档案列表头")
	    @PostMapping("/api/CivilAffairsField/getCivilAffairsFieldTitel")
	    public PlatformResult<List<CivilAffairsField>> getCivilAffairsFieldTitel() {
	        try {
	        	List<CivilAffairsField> list = civilAffairsFieldService.getCivilAffairsFieldTitel();
	            return PlatformResult.success(list);
	        } catch (Exception e) {
	        	return PlatformResult.failure(e.getMessage());
	        }
	    }
	
	/**
	 * 
	 * @Title deleteCivilAffairsFieldById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsField/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsFieldById(@PathVariable String id) {
		try {
			civilAffairsFieldService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsFieldList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsField>
	 * @date 2023��12��18�� ����4:59:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsField/list")
	public DataSet<CivilAffairsField> selectCivilAffairsFieldList(Page page, CivilAffairsField record) {
		return civilAffairsFieldService.getDataSetList(page, record);
	}
}
