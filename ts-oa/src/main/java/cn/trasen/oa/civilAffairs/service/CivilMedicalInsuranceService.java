package cn.trasen.oa.civilAffairs.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilMedicalInsurance;
import cn.trasen.oa.civilAffairs.model.CivilSocialSecurity;

/**
 * @ClassName CivilMedicalInsuranceService
 * @Description TODO
 * @date 2024��5��8�� ����5:17:47
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilMedicalInsuranceService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��5��8�� ����5:17:47
	 * <AUTHOR>
	 */
	Integer save(CivilMedicalInsurance record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��5��8�� ����5:17:47
	 * <AUTHOR>
	 */
	Integer update(CivilMedicalInsurance record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��5��8�� ����5:17:47
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilMedicalInsurance
	 * @date 2024��5��8�� ����5:17:47
	 * <AUTHOR>
	 */
	CivilMedicalInsurance selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilMedicalInsurance>
	 * @date 2024��5��8�� ����5:17:47
	 * <AUTHOR>
	 */
	DataSet<CivilMedicalInsurance> getDataSetList(Page page, CivilMedicalInsurance record);
	
	DataSet<CivilMedicalInsurance> getPageList(Page page, CivilMedicalInsurance record);
}
