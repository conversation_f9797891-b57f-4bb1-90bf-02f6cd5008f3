package cn.trasen.oa.civilAffairs.controller;

import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import org.apache.commons.collections.CollectionUtils;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsGoodsManagementMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsGoodsManagement;
import cn.trasen.oa.civilAffairs.model.CivilAffairsPersonnelInfo;
import cn.trasen.oa.civilAffairs.service.CivilAffairsGoodsManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilAffairsGoodsManagementController  供应商物品管理
 * @Description TODO
 * @date 2024��5��20�� ����9:37:24
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsGoodsManagementController")
public class CivilAffairsGoodsManagementController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsGoodsManagementController.class);

	@Autowired
	private CivilAffairsGoodsManagementService civilAffairsGoodsManagementService;
	
	@Autowired
	private CivilAffairsGoodsManagementMapper civilAffairsGoodsManagementMapper;

	/**
	 * @Title saveCivilAffairsGoodsManagement
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsGoodsManagement/save")
	public PlatformResult<String> saveCivilAffairsGoodsManagement(@RequestBody CivilAffairsGoodsManagement record) {
		try {
			Example example = new Example(CivilAffairsGoodsManagement.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("goodsName", record.getGoodsName());
			//criteria.andEqualTo("supplierName",record.getSupplierName());
			List<CivilAffairsGoodsManagement> records = civilAffairsGoodsManagementMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(records)) {
				//return PlatformResult.failure("已经存在该品牌商品:"+record.getSupplierName()+":"+record.getGoodsName());
				return PlatformResult.failure("已经存在该品名:"+record.getGoodsName());
			}
			civilAffairsGoodsManagementService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsGoodsManagement
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsGoodsManagement/update")
	public PlatformResult<String> updateCivilAffairsGoodsManagement(@RequestBody CivilAffairsGoodsManagement record) {
		try {
			Example example = new Example(CivilAffairsGoodsManagement.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("goodsName", record.getGoodsName());
			criteria.andEqualTo("supplierName",record.getSupplierName());
			criteria.andNotEqualTo("id", record.getId());
			List<CivilAffairsGoodsManagement> records = civilAffairsGoodsManagementMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(records)) {
				return PlatformResult.failure("已经存在该供应商物品:"+record.getSupplierName()+":"+record.getGoodsName());
			}
			civilAffairsGoodsManagementService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsGoodsManagementById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsGoodsManagement>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsGoodsManagement/{id}")
	public PlatformResult<CivilAffairsGoodsManagement> selectCivilAffairsGoodsManagementById(@PathVariable String id) {
		try {
			CivilAffairsGoodsManagement record = civilAffairsGoodsManagementService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsGoodsManagementById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsGoodsManagement/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsGoodsManagementById(@PathVariable String id) {
		try {
			civilAffairsGoodsManagementService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsGoodsManagementList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsGoodsManagement>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsGoodsManagement/list")
	public DataSet<CivilAffairsGoodsManagement> selectCivilAffairsGoodsManagementList(Page page, CivilAffairsGoodsManagement record) {
		return civilAffairsGoodsManagementService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsGoodsManagementList
	 * @Description 分页列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsGoodsManagement>
	 * @date 2024��5��20�� ����9:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/CivilAffairsGoodsManagement/pageList")
	public DataSet<CivilAffairsGoodsManagement> selectPageList(Page page, CivilAffairsGoodsManagement record) {
		return civilAffairsGoodsManagementService.selectPageList(page, record);
	}
	
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出供应商物品列表", notes = "导出供应商物品列表")
	@GetMapping("/api/CivilAffairsGoodsManagement/exportPageList")
	public void exportList(HttpServletRequest request, HttpServletResponse response, Page page, CivilAffairsGoodsManagement record) {
		
	List<CivilAffairsGoodsManagement> records = civilAffairsGoodsManagementMapper.selectPageList(page, record);
		
	// 导出文件名称
	String name = "供应商物品管理.xls";
	
	// 模板位置
	String templateUrl = "template/civilAffairsGoodsManagement.xls";
	// 导出数据列表
	try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
