package cn.trasen.oa.civilAffairs.controller;

import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsBillMaintenanceMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsBillMaintenance;
import cn.trasen.oa.civilAffairs.service.CivilAffairsBillMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsBillMaintenanceController 收款单据维护
 * @Description TODO
 * @date 2024��5��21�� ����10:50:38
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsBillMaintenanceController")
public class CivilAffairsBillMaintenanceController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsBillMaintenanceController.class);

	@Autowired
	private CivilAffairsBillMaintenanceService civilAffairsBillMaintenanceService;

	@Autowired
	private CivilAffairsBillMaintenanceMapper civilAffairsBillMaintenanceMapper;
	
	/**
	 * @Title saveCivilAffairsBillMaintenance
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsBillMaintenance/save")
	public PlatformResult<String> saveCivilAffairsBillMaintenance(@RequestBody CivilAffairsBillMaintenance record) {
		try {
			List<CivilAffairsBillMaintenance> billMaintenancelist = civilAffairsBillMaintenanceMapper.selectCrossDataByCoding(Long.valueOf(record.getBeginCoding()),Long.valueOf(record.getEndCodeing()));
			if(CollectionUtils.isNotEmpty(billMaintenancelist)) {
				return PlatformResult.failure("新增票据号存在交叉,新增失败");
			}
			civilAffairsBillMaintenanceService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsBillMaintenance
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsBillMaintenance/update")
	public PlatformResult<String> updateCivilAffairsBillMaintenance(@RequestBody CivilAffairsBillMaintenance record) {
		try {
			civilAffairsBillMaintenanceService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsBillMaintenanceById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsBillMaintenance>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsBillMaintenance/{id}")
	public PlatformResult<CivilAffairsBillMaintenance> selectCivilAffairsBillMaintenanceById(@PathVariable String id) {
		try {
			CivilAffairsBillMaintenance record = civilAffairsBillMaintenanceService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsBillMaintenanceById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsBillMaintenance/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsBillMaintenanceById(@PathVariable String id) {
		try {
			civilAffairsBillMaintenanceService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsBillMaintenanceList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsBillMaintenance>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsBillMaintenance/list")
	public DataSet<CivilAffairsBillMaintenance> selectCivilAffairsBillMaintenanceList(Page page, CivilAffairsBillMaintenance record) {
		return civilAffairsBillMaintenanceService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Title updateStatus
	 * @Description 作废更新
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "作废", notes = "作废")
	@PostMapping("/api/CivilAffairsBillMaintenance/updateStatus")
	public PlatformResult<String> updateStatus(@RequestBody CivilAffairsBillMaintenance record) {
		try {
			civilAffairsBillMaintenanceService.updateStatus(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title selectCivilAffairsBillMaintenanceList
	 * @Description 查询分页列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsBillMaintenance>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/CivilAffairsBillMaintenance/pageList")
	public DataSet<CivilAffairsBillMaintenance> selectPageList(Page page, CivilAffairsBillMaintenance record) {
		return civilAffairsBillMaintenanceService.selectPageList(page, record);
	}
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出收款单据列表", notes = "导出收款单据列表")
	@GetMapping("/api/CivilAffairsBillMaintenance/exportPageList")
	public void exportList(HttpServletRequest request, HttpServletResponse response, Page page, CivilAffairsBillMaintenance record) {
		
	List<CivilAffairsBillMaintenance> records = civilAffairsBillMaintenanceMapper.selectPageList(page, record);
		
	// 导出文件名称
	String name = "收款单据.xls";
	
	// 模板位置
	String templateUrl = "template/civilAffairsBillMaintenance.xls";
	// 导出数据列表
	try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	
}
