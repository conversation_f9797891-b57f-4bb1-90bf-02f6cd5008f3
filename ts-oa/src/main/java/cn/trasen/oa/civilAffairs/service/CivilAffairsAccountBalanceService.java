package cn.trasen.oa.civilAffairs.service;


import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsAccountBalance;

/**
 * @ClassName CivilAffairsAccountBalanceService
 * @Description TODO
 * @date 2024��7��2�� ����2:13:58
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsAccountBalanceService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��7��2�� ����2:13:58
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsAccountBalance record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��7��2�� ����2:13:58
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsAccountBalance record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��7��2�� ����2:13:58
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsAccountBalance
	 * @date 2024��7��2�� ����2:13:58
	 * <AUTHOR>
	 */
	CivilAffairsAccountBalance selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAccountBalance>
	 * @date 2024��7��2�� ����2:13:58
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsAccountBalance> getDataSetList(Page page, CivilAffairsAccountBalance record);
}
