package cn.trasen.oa.civilAffairs.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsWithdrawalRecords;

/**
 * @ClassName CivilAffairsWithdrawalRecordsService
 * @Description TODO
 * @date 2023��12��11�� ����4:12:04
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsWithdrawalRecordsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��12��11�� ����4:12:04
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsWithdrawalRecords record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��12��11�� ����4:12:04
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsWithdrawalRecords record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��12��11�� ����4:12:04
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsWithdrawalRecords
	 * @date 2023��12��11�� ����4:12:04
	 * <AUTHOR>
	 */
	CivilAffairsWithdrawalRecords selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsWithdrawalRecords>
	 * @date 2023��12��11�� ����4:12:04
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsWithdrawalRecords> getDataSetList(Page page, CivilAffairsWithdrawalRecords record);
	
	DataSet<CivilAffairsWithdrawalRecords> getPageList(Page page, CivilAffairsWithdrawalRecords record);
	
	List<Map<String, Object>>   selectByYear(CivilAffairsWithdrawalRecords record);
}
