package cn.trasen.oa.civilAffairs.controller;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsAuthority;
import cn.trasen.oa.civilAffairs.service.CivilAffairsAuthorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsAuthorityController权限设置
 * @Description TODO
 * @date 2023��12��18�� ����4:59:05
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsAuthorityController")
public class CivilAffairsAuthorityController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsAuthorityController.class);

	@Autowired
	private CivilAffairsAuthorityService civilAffairsAuthorityService;

	/**
	 * @Title saveCivilAffairsAuthority
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsAuthority/save")
	public PlatformResult<String> saveCivilAffairsAuthority(@RequestBody CivilAffairsAuthority record) {
		try {
			civilAffairsAuthorityService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsAuthority
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsAuthority/update")
	public PlatformResult<String> updateCivilAffairsAuthority(@RequestBody CivilAffairsAuthority record) {
		try {
			civilAffairsAuthorityService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsAuthorityById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsAuthority>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsAuthority/{id}")
	public PlatformResult<CivilAffairsAuthority> selectCivilAffairsAuthorityById(@PathVariable String id) {
		try {
			CivilAffairsAuthority record = civilAffairsAuthorityService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsAuthorityById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsAuthority/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsAuthorityById(@PathVariable String id) {
		try {
			civilAffairsAuthorityService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsAuthorityList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAuthority>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsAuthority/list")
	public DataSet<CivilAffairsAuthority> selectCivilAffairsAuthorityList(Page page, CivilAffairsAuthority record) {
		return civilAffairsAuthorityService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsAuthorityList
	 * @Description 查询个人权限
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAuthority>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询个人权限", notes = "查询个人权限")
	@GetMapping("/api/CivilAffairsAuthority/selectCivilAffairsAuthorityByUserId")
	public PlatformResult<Map<String,Object>> selectCivilAffairsAuthorityByUserId(CivilAffairsAuthority record) {
		Map<String,Object> map =  civilAffairsAuthorityService.selectCivilAffairsAuthorityByUserId(record);
		return PlatformResult.success(map);
	}
}
