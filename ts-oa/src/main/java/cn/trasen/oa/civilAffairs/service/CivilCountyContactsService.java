package cn.trasen.oa.civilAffairs.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilCountyContacts;

/**
 * @ClassName CivilCountyContactsService
 * @Description TODO
 * @date 2024��1��25�� ����2:27:22
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilCountyContactsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	Integer save(CivilCountyContacts record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	Integer update(CivilCountyContacts record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilCountyContacts
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	CivilCountyContacts selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilCountyContacts>
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	DataSet<CivilCountyContacts> getDataSetList(Page page, CivilCountyContacts record);
	
	/**
	 * @Title getPageList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilCountyContacts>
	 * @date 2024��1��25�� ����2:27:22
	 * <AUTHOR>
	 */
	DataSet<CivilCountyContacts> getPageList(Page page, CivilCountyContacts record);
	
}
