package cn.trasen.oa.civilAffairs.controller;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsChangeLedgerMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeManage;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeRegister;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeLedgerService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeManageService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsChangeRegisterController 零钱登记
 * @Description TODO
 * @date 2024��5��21�� ����4:08:31
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsChangeRegisterController")
public class CivilAffairsChangeRegisterController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsChangeRegisterController.class);

	@Autowired
	private CivilAffairsChangeRegisterService civilAffairsChangeRegisterService;

	@Autowired
	private CivilAffairsChangeManageService   changeManageService;
	
	@Autowired
	private CivilAffairsChangeLedgerService civilAffairsChangeLedgerService;
	
	@Autowired
	private CivilAffairsChangeLedgerMapper changeLedgerMapper;
	
	
			
	/**
	 * @Title saveCivilAffairsChangeRegister
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsChangeRegister/save")
	public PlatformResult<String> saveCivilAffairsChangeRegister(@RequestBody CivilAffairsChangeRegister record) {
		try {
			civilAffairsChangeRegisterService.save(record);
			updateManageChangeBalance(record,"1");//更新对象余额,收款
			saveRegisterLedger(record);//新增产生台账
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsChangeRegister
	 * @Description 作废
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "作废", notes = "作废")
	@PostMapping("/api/CivilAffairsChangeRegister/updateStatus")
	public PlatformResult<String> updateStatus(@RequestBody CivilAffairsChangeRegister record) {
		try {
			civilAffairsChangeRegisterService.updateStatus(record);
			if(StringUtils.isNotBlank(record.getStatus()) && "2".equals(record.getStatus())) {
				changeLedgerMapper.updateByBillNumber(record.getManageId(),record.getBillNumber());//根据对象id和票据号,作废台账
				updateManageChangeBalance(record,"2");//更新对象余额,退还
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title updateCivilAffairsChangeRegister
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsChangeRegister/update")
	public PlatformResult<String> updateCivilAffairsChangeRegister(@RequestBody CivilAffairsChangeRegister record) {
		try {
			civilAffairsChangeRegisterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsChangeRegisterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsChangeRegister>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsChangeRegister/{id}")
	public PlatformResult<CivilAffairsChangeRegister> selectCivilAffairsChangeRegisterById(@PathVariable String id) {
		try {
			CivilAffairsChangeRegister record = civilAffairsChangeRegisterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsChangeRegisterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsChangeRegister/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsChangeRegisterById(@PathVariable String id) {
		try {
			civilAffairsChangeRegisterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsChangeRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsChangeRegister>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsChangeRegister/list")
	public DataSet<CivilAffairsChangeRegister> selectCivilAffairsChangeRegisterList(Page page, CivilAffairsChangeRegister record) {
		return civilAffairsChangeRegisterService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsChangeRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsChangeRegister>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/CivilAffairsChangeRegister/pageList")
	public DataSet<CivilAffairsChangeRegister> selectPageList(Page page, CivilAffairsChangeRegister record) {
		return civilAffairsChangeRegisterService.selectPageList(page, record);
	}
	
	
	/**
	 * @Title checkBillNumber
	 * @Description 校验票据是否已使用、以及是否在维护范围内
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "票据校验", notes = "票据校验")
	@GetMapping("/api/CivilAffairsChangeRegister/checkBillNumber")
	public PlatformResult<String> checkBillNumber(CivilAffairsChangeRegister record) {
		try {
			String status = civilAffairsChangeRegisterService.checkBillNumber(record);
			return PlatformResult.success(status);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title saveCivilAffairsChangeRegister
	 * @Description 查询已经登记使用的票据(用于新增台账)
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��5��21�� ����4:08:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询已经登记使用的票据(用于新增台账)", notes = "查询已经登记使用的票据")
	@GetMapping("/api/CivilAffairsChangeRegister/selectAllByBillNumber")
	public PlatformResult<List<CivilAffairsChangeRegister>> selectAllByBillNumber(CivilAffairsChangeRegister record) {
		try {
			record.setType("1");//模糊检索票据号，并且过滤掉已经新增台账的
			List<CivilAffairsChangeRegister> changeRegisterList = civilAffairsChangeRegisterService.selectAllByBillNumber(record);
			return PlatformResult.success(changeRegisterList);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	public void updateManageChangeBalance(CivilAffairsChangeRegister record,String  status) {
		// TODO Auto-generated method stub
		CivilAffairsChangeManage changeManage = changeManageService.selectById(record.getManageId());
		if(changeManage != null) {
			if("1".equals(status)) {
				changeManage.setChangeBalance(changeManage.getChangeBalance().add(record.getChangeAmount()));//现有余额+收款
			}else if ("2".equals(status)) {
				changeManage.setChangeBalance(changeManage.getChangeBalance().subtract(record.getChangeAmount()));//现有余额-收款(作废退还)
			}
			changeManageService.update(changeManage);
		}
	}
	
	public void saveRegisterLedger(CivilAffairsChangeRegister record) {
		CivilAffairsChangeLedger  civilAffairsChangeLedger = new  CivilAffairsChangeLedger();//台账对象
		civilAffairsChangeLedger.setBillNumber(record.getBillNumber());//票据号
		civilAffairsChangeLedger.setManageId(record.getManageId());//对象ID
		civilAffairsChangeLedger.setRegisterDate(record.getRegisterDate());//登记时间
		civilAffairsChangeLedger.setAmountCollected(record.getChangeAmount());//收款金额——零钱金额
		civilAffairsChangeLedger.setExpenditureAmount(BigDecimal.valueOf(0));//支出金额默认0
		civilAffairsChangeLedger.setBalanceAmount(record.getChangeBalance());//结存金额--零钱余额
		
		civilAffairsChangeLedger.setLedgerItems(record.getRemarks());//台账事项--备注
		civilAffairsChangeLedger.setRelativesAutograph(record.getChangeGiver());//家属签名--零钱给予者
		civilAffairsChangeLedgerService.save(civilAffairsChangeLedger);//新增台账  
	}
}
