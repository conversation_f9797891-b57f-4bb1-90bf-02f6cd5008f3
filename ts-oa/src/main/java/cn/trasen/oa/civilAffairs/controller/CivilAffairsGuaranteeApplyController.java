package cn.trasen.oa.civilAffairs.controller;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.oa.civilAffairs.model.CivilAffairsGuaranteeApply;
import cn.trasen.oa.civilAffairs.service.CivilAffairsGuaranteeApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsGuaranteeApplyController  保障申请新增核减
 * @Description TODO
 * @date 2024��2��1�� ����3:34:57
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsGuaranteeApplyController")
public class CivilAffairsGuaranteeApplyController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsGuaranteeApplyController.class);

	@Autowired
	private CivilAffairsGuaranteeApplyService civilAffairsGuaranteeApplyService;

	/**
	 * @Title saveCivilAffairsGuaranteeApply
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsGuaranteeApply/save")
	public PlatformResult<String> saveCivilAffairsGuaranteeApply(@RequestBody CivilAffairsGuaranteeApply record) {
		try {
			civilAffairsGuaranteeApplyService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsGuaranteeApply
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsGuaranteeApply/update")
	public PlatformResult<String> updateCivilAffairsGuaranteeApply(@RequestBody CivilAffairsGuaranteeApply record) {
		try {
			civilAffairsGuaranteeApplyService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title updateCivilAffairsGuaranteeApply
	 * @Description 完成审核
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "完成审核", notes = "完成审核")
	@PostMapping("/api/CivilAffairsGuaranteeApply/examineUpdate")
	public PlatformResult<String> examineUpdate(@RequestBody CivilAffairsGuaranteeApply record) {
		try {
			civilAffairsGuaranteeApplyService.examineUpdate(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsGuaranteeApplyById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsGuaranteeApply>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsGuaranteeApply/{id}")
	public PlatformResult<CivilAffairsGuaranteeApply> selectCivilAffairsGuaranteeApplyById(@PathVariable String id) {
		try {
			CivilAffairsGuaranteeApply record = civilAffairsGuaranteeApplyService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsGuaranteeApplyById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsGuaranteeApply/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsGuaranteeApplyById(@PathVariable String id) {
		try {
			civilAffairsGuaranteeApplyService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsGuaranteeApplyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsGuaranteeApply>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsGuaranteeApply/list")
	public DataSet<CivilAffairsGuaranteeApply> selectCivilAffairsGuaranteeApplyList(Page page, CivilAffairsGuaranteeApply record) {
		return civilAffairsGuaranteeApplyService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsGuaranteeApplyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsGuaranteeApply>
	 * @date 2024��2��1�� ����3:34:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsGuaranteeApply/PageList")
	public DataSet<CivilAffairsGuaranteeApply> selectCivilAffairsGuaranteeApplyPageList(Page page, CivilAffairsGuaranteeApply record) {
		return civilAffairsGuaranteeApplyService.getPageList(page, record);
	}
	
	
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出模板表", notes = "导出模板表")
	@GetMapping("/api/CivilAffairsGuaranteeApply/print")
	public void print(HttpServletRequest request, HttpServletResponse response,CivilAffairsGuaranteeApply record) {
		
	//Map<String,Object> map = civilAffairsGuaranteeApplyService.selectByIdMap(record);
	CivilAffairsGuaranteeApply  civilAffairsGuaranteeApply = civilAffairsGuaranteeApplyService.selectByIdMap(record);
	
	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//日期格式化
	
	Map<String, Object> dataMap = new HashMap();
    dataMap.put("applyDate", civilAffairsGuaranteeApply.getApplyDate()==null ? "" : formatter.format(civilAffairsGuaranteeApply.getApplyDate()));
    dataMap.put("number", StringUtil.isEmpty(civilAffairsGuaranteeApply.getNumber()) ? "" : civilAffairsGuaranteeApply.getNumber());
    dataMap.put("name", StringUtil.isEmpty(civilAffairsGuaranteeApply.getName()) ? "" : civilAffairsGuaranteeApply.getName());
    dataMap.put("alias", StringUtil.isEmpty(civilAffairsGuaranteeApply.getAlias()) ? "" : civilAffairsGuaranteeApply.getAlias());
    dataMap.put("sexText", StringUtil.isEmpty(civilAffairsGuaranteeApply.getSexText()) ? "" : civilAffairsGuaranteeApply.getSexText());
    dataMap.put("birthday", StringUtil.isEmpty(civilAffairsGuaranteeApply.getBirthday()) ? "" : civilAffairsGuaranteeApply.getBirthday());
    dataMap.put("inDate", civilAffairsGuaranteeApply.getInDate()==null ? "" : formatter.format(civilAffairsGuaranteeApply.getInDate()));
    dataMap.put("marriageStatusText", StringUtil.isEmpty(civilAffairsGuaranteeApply.getMarriageStatusText()) ? "" : civilAffairsGuaranteeApply.getMarriageStatusText());
    dataMap.put("healthStatusText", StringUtil.isEmpty(civilAffairsGuaranteeApply.getHealthStatusText()) ? "" : civilAffairsGuaranteeApply.getHealthStatusText());
    dataMap.put("selfCareText", StringUtil.isEmpty(civilAffairsGuaranteeApply.getSelfCareText()) ? "" : civilAffairsGuaranteeApply.getSelfCareText());
    dataMap.put("educationalLevelText", StringUtil.isEmpty(civilAffairsGuaranteeApply.getEducationalLevelText()) ? "" : civilAffairsGuaranteeApply.getEducationalLevelText());
    dataMap.put("identityNumber", StringUtil.isEmpty(civilAffairsGuaranteeApply.getIdentityNumber()) ? "" : civilAffairsGuaranteeApply.getIdentityNumber());
    dataMap.put("address", StringUtil.isEmpty(civilAffairsGuaranteeApply.getAddress()) ? "" : civilAffairsGuaranteeApply.getAddress());
    dataMap.put("reason", StringUtil.isEmpty(civilAffairsGuaranteeApply.getReason()) ? "" : civilAffairsGuaranteeApply.getReason());
    dataMap.put("type", civilAffairsGuaranteeApply.getType() == null ? "" : civilAffairsGuaranteeApply.getType());
		// 导出文件名称
		String name = "长沙市福利事业单位特困人员集中供养申请审批表.xls";
		// 模板位置
		String templateUrl = "template/civilAffairsGuaranteeApplyAdd.xls";
		
	if (!dataMap.isEmpty()) {
		if(dataMap.get("type") != null  && dataMap.get("type").equals(2)) {
			name = "长沙市福利事业单位特困人员终止救助供养审批表.xls";
			templateUrl = "template/civilAffairsGuaranteeApplyDel.xls";
		}
	}
	// 导出表格
		try {
			if (!dataMap.isEmpty()) {
				ExportUtil.export(request, response, dataMap, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
