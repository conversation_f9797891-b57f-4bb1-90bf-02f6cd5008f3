package cn.trasen.oa.civilAffairs.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.civilAffairs.model.CivilAffairsEmpModel;
import cn.trasen.oa.civilAffairs.model.CivilAffairsPersonnelInfo;

public interface CivilAffairsEmployeeManageService {

	/**
	 * 
	* @Title: getEmployeePageListByCustom  
	* @Description: 分页获取自定义人员档案列表
	* @Params: @param page
	* @Params: @param record
	* @Params: @return      
	* @Return: List<Map<String,String>>
	* <AUTHOR>
	* @date:2021年6月19日
	* @Throws
	 */
	List<Map<String,String>> getEmployeePageListByCustom(Page page,CivilAffairsPersonnelInfo record);
	
	/**
	 * 
	* @Title: findById  
	* @Description: 根据人员id查询自定义人员档案所有表数据
	* @Params: @param id
	* @Params: @return      
	* @Return: Map<String,String>
	* @Throws
	 */
	Map<String,List<Map<String,String>>> findByIdAndDetails(CivilAffairsPersonnelInfo record);
	
	/**
	 * 
	* @Title: UniqueCheck  
	* @Description: 新增修改时姓名,工号唯一校验
	* @Params: @param record
	* @Params: @return      
	* @Return: boolean
	* @Throws
	 */
	String uniqueCheck(CivilAffairsEmpModel record);
	
	/**
	 * 
	* @Title: insert  
	* @Description: 新增或修改人员自定义档案
	* @Params: @param records
	* @Params: @return      
	* @Return: String
	* @Throws
	 */
	String save(CivilAffairsEmpModel record);
}
