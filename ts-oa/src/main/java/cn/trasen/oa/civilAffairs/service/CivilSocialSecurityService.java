package cn.trasen.oa.civilAffairs.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilSocialSecurity;

/**
 * @ClassName CivilSocialSecurityService
 * @Description TODO
 * @date 2024��1��25�� ����2:22:29
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilSocialSecurityService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	Integer save(CivilSocialSecurity record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	Integer update(CivilSocialSecurity record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilSocialSecurity
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	CivilSocialSecurity selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilSocialSecurity>
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	DataSet<CivilSocialSecurity> getDataSetList(Page page, CivilSocialSecurity record);
	
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilSocialSecurity>
	 * @date 2024��1��25�� ����2:22:29
	 * <AUTHOR>
	 */
	DataSet<CivilSocialSecurity> getPageList(Page page, CivilSocialSecurity record);
	
}
