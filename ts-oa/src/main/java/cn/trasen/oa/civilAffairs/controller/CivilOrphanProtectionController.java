package cn.trasen.oa.civilAffairs.controller;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.civilAffairs.dao.CivilOrphanProtectionMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsMessageSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsPersonnelInfo;
import cn.trasen.oa.civilAffairs.model.CivilHardshipProtection;
import cn.trasen.oa.civilAffairs.model.CivilNursingInformation;
import cn.trasen.oa.civilAffairs.model.CivilOrphanProtection;
import cn.trasen.oa.civilAffairs.service.CivilAffairsMessageSetService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsPersonnelInfoService;
import cn.trasen.oa.civilAffairs.service.CivilOrphanProtectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilOrphanProtectionController 孤儿保障
 * @Description TODO
 * @date 2024��1��25�� ����2:25:01
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilOrphanProtectionController")
public class CivilOrphanProtectionController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilOrphanProtectionController.class);

	@Autowired
	private CivilOrphanProtectionService civilOrphanProtectionService;
	
	@Autowired
	private CivilOrphanProtectionMapper civilOrphanProtectionMapper;
	
	@Autowired
	private CivilAffairsPersonnelInfoService civilAffairsPersonnelInfoService;

	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	@Autowired
    private CivilAffairsMessageSetService  civilAffairsMessageSetService;

	/**
	 * @Title saveCivilOrphanProtection
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilOrphanProtection/save")
	public PlatformResult<String> saveCivilOrphanProtection(@RequestBody CivilOrphanProtection record) {
		try {
			Example example = new Example(CivilOrphanProtection.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("personnelId",record.getPersonnelId());
			criteria.andEqualTo("orphanDate",record.getOrphanDate());
			List<CivilOrphanProtection> recordList = civilOrphanProtectionMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(recordList)) {
				return PlatformResult.failure("该月份日期已经存在孤儿保障标准,不可重复新增");
			}else {
				civilOrphanProtectionService.save(record);
				return PlatformResult.success();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title copyLastMonthSave
	 * @Description 复制上月标准
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "复制上月标准", notes = "复制上月标准")
	@PostMapping("/api/CivilOrphanProtection/copyLastMonthSave")
	public PlatformResult<String> copyLastMonthSave(@RequestBody CivilOrphanProtection record) {
		try {

			Date now = new Date();
			SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM");
			
			Example example = new Example(CivilOrphanProtection.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("personnelId",record.getPersonnelId());
			criteria.andEqualTo("orphanDate",sdf.format(now));
			List<CivilOrphanProtection> recordList = civilOrphanProtectionMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(recordList)) {
				return PlatformResult.failure("当月已经存在孤儿保障标准,不可复制");
			}else {
				CivilOrphanProtection civilOrphanProtection =  civilOrphanProtectionMapper.selectLastMonthData(record);//查询上月数据
				if(civilOrphanProtection !=null) {
					civilOrphanProtectionService.copyLastMonthSave(civilOrphanProtection);//查询到上月数据、复制
					return PlatformResult.success();
				}else {
					return PlatformResult.failure("未有比当前月份小的数据,无法复制");
				}
			}
			
			
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	 private void sendMessage(String content,List<String> receiverList,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                //.receiver(receiver)  //接收人
	                .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-oa/civil-affairs-objects/civil-affairs-objects").source("民政对象")//跳转前端地址
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }

	/**
	 * @Title updateCivilOrphanProtection
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilOrphanProtection/update")
	public PlatformResult<String> updateCivilOrphanProtection(@RequestBody CivilOrphanProtection record) {
		try {
			if(record != null && StringUtils.isNotBlank(record.getId())){
				civilOrphanProtectionService.update(record);
			}else{
				Example example = new Example(CivilOrphanProtection.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria.andEqualTo("personnelId",record.getPersonnelId());
				criteria.andEqualTo("orphanDate",record.getOrphanDate());
				List<CivilOrphanProtection> recordList = civilOrphanProtectionMapper.selectByExample(example);
				if(CollectionUtils.isNotEmpty(recordList)) {
					return PlatformResult.failure("该月份日期已经存在孤儿保障标准,不可重复新增");
				}else {
					civilOrphanProtectionService.save(record);
				}
				
				CivilAffairsPersonnelInfo civilAffairsPersonnelInfo = civilAffairsPersonnelInfoService.selectById(record.getPersonnelId());
				if(civilAffairsPersonnelInfo !=null) {
					String content = civilAffairsPersonnelInfo.getName()+ "孤儿保障于"+record.getOrphanDate()+"变更为"+record.getOrphanStandard()+"元/月,请知悉";
				    String subject = "民政对象——保障标准提醒";
				    String url =appConfigProperties.getWxDomain() + "";//微信端地址
				    
				    CivilAffairsMessageSet civilAffairsMessageSet  =  civilAffairsMessageSetService.getCivilAffairsMessageSetOne(new CivilAffairsMessageSet());	
				    if (civilAffairsMessageSet != null &&  StringUtils.isNotEmpty(civilAffairsMessageSet.getGuaranteeUser())){//保障标准提醒人员
				    	sendMessage(content, Arrays.asList(civilAffairsMessageSet.getGuaranteeUser().split(",")), url, subject,"3");
				    }
				    //sendMessage(content, civilAffairsPersonnelInfo.getCreateUser(), url, subject,"3");//给创建人发送信息
				}
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilOrphanProtectionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilOrphanProtection>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilOrphanProtection/{id}")
	public PlatformResult<CivilOrphanProtection> selectCivilOrphanProtectionById(@PathVariable String id) {
		try {
			CivilOrphanProtection record = civilOrphanProtectionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilOrphanProtectionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilOrphanProtection/delete/{id}")
	public PlatformResult<String> deleteCivilOrphanProtectionById(@PathVariable String id) {
		try {
			civilOrphanProtectionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilOrphanProtectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilOrphanProtection>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilOrphanProtection/list")
	public DataSet<CivilOrphanProtection> selectCivilOrphanProtectionList(Page page, CivilOrphanProtection record) {
		return civilOrphanProtectionService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilOrphanProtectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilOrphanProtection>
	 * @date 2024��1��25�� ����2:25:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "关联主表分页列表", notes = "关联主表分页列表")
	@GetMapping("/api/CivilOrphanProtection/PageList")
	public DataSet<CivilOrphanProtection> selectCivilOrphanProtectionPageList(Page page, CivilOrphanProtection record) {
		return civilOrphanProtectionService.getPageList(page, record);
	}
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出列表", notes = "导出列表")
	@GetMapping("/api/CivilOrphanProtection/exportPageList")
	public void exportPageList(HttpServletRequest request, HttpServletResponse response, Page page, CivilOrphanProtection record) {
		
	List<CivilOrphanProtection> records = civilOrphanProtectionMapper.getPageList(page,record);
		
	// 导出文件名称
	String name = "孤儿保障.xls";
	
	// 模板位置
	String templateUrl = "template/civilOrphanProtection.xls";
	// 导出数据列表
		try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
